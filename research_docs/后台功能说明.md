# **后台功能说明（管理员角色）**



## 一、仪表盘

（或称首页），主要显示模块如下：

![](.//media/image1.png){width="5.768055555555556in" height="2.9625in"}\
![](.//media/image2.png){width="5.768055555555556in"
height="2.6729166666666666in"}
主要字段释义：

注册人数（平台所有注册用户数量）、

登陆会员（平台当日登陆会员数量，或平台当前在线人数）、

活跃率（该字段可以不显示，意义不大）、

收付差（平台代收金额减去平台代付金额）、

实际收付差（平台实际代收金额减去平台实际代付金额）、

代收金额（平台充值的总金额）、

实际代收（平台代收金额减去支付通道收取的代收手续费）、

代收人数（平台所有充值用户的人数）、

代收笔数（平台用户充值的次数）、

代付金额（平台所有提款的总金额）、

实际代付（平台代付金额减去支付通道收取的代付手续费）、

代付人数（平台提款的总人数）、

代付笔数（平台所有提款的次数）、

首充人数（首次在平台充值的人数统计）、

首充笔数（该字段可以不显示，与首充人数重复）、

首充金额（平台首次充值用户充值的总额统计）、

复充人数（在平台进行再次充值的用户数统计）、

复充笔数（用户在平台进行再次充值次数的统计）、

复充金额（平台再次充值金额的统计）、

代收率【代收率就是平台的注册玩家和充值玩家的比例，如果平台有100位玩家注册，其中50位玩家进行了充值，则代收率为50%（充值玩家/注册玩家百分比）】、

复充率（复充率就是充值玩家，再次进行充值的比例，比如有100位玩家进行了充值，其中30位玩家再次进行了充值，则复充率为30%）、

下单人数（该字段可以不显示，意义不大）、

派彩金额（派彩金额就是平台玩家进行游戏时中奖的总金额。就是玩家玩游戏中奖的总金额）、

游戏盈亏（玩家投注金额总数减去派彩金额总数）、

手动加钱（平台业务员GM，手动给平台用户添加的工资或金额的总数）、

手动扣钱（平台业务员GM，手动给平台用户扣除的工资或金额的总数）。

## 二、支付管理

### 1、金币充值配置

主要模块功能如下

![](.//media/image3.png){width="5.768055555555556in"
height="2.7402777777777776in"}
![](.//media/image4.png){width="5.768055555555556in"
height="2.821527777777778in"}
该功能为配置平台用户端用户充值时可以选择的充值金额，以及配置平台对大额充值用户赠送金额的配置，对应用户端的功能如下：\
![](.//media/image5.png){width="4.18125in"
height="4.143940288713911in"}\

### 2、订单管理

（显示用户充值订单的重要模块），图示如下：

![](.//media/image6.png){width="5.768055555555556in"
height="2.8472222222222223in"}\
![](.//media/image7.png){width="5.768055555555556in"
height="2.9444444444444446in"}
所展示的检索功能全部都需要用到
主要字段：

ID（充值ID，即记录该充值为平台第几笔充值）、

玩家ID（充值玩家的ID）、

上级ID（该充值玩家上级用户的ID）、

玩家类型（对应[玩家管理模块]{.underline}中的[玩家分组管理功能]{.underline}，可将用户进行分组管理，标记为玩家或博主等等）、

状态（该笔充值订单的支付状态，显示是否已经通过渠道充值成功）、

通道名称（支付通道的名称，显示通过哪个支付通道完成的充值）、

渠道ID（显示该笔订单是哪个渠道即代理线的用户充值）、

业务员ID（显示该笔订单为哪个具体业务员的用户充值）、

平台订单号（该充值在平台的订单号，方便与支付通道进行对账查询）、

实际金额（该笔订单的实际充值金额）、

到账金额（该笔订单的实际金额加上平台的赠送金额的总额）、

赠送金额（平台对大额充值的赠送金额）、

充值类型（该字段可以不显示，意义不大）、

是否补单（该字段可以不显示，意义不大）、

支付时间（该笔订单到账的具体时间）、

创建时间（该笔订单的创建时间）\
操作：[通过第三方查询订单支付情况]{.underline}（即通过支付通道查询该比订单的实际支付情况）、[补单]{.underline}（如果订单实际已经支付，可以选择是否讲该订单刷新为支付状态）。

## 三、支出管理

### 1、放款审核（重要模块）图示如下：

![](.//media/image8.png){width="5.768055555555556in"
height="2.9680555555555554in"}\
![](.//media/image9.png){width="5.768055555555556in"
height="2.9493055555555556in"}
所展示的检索功能全部都需要用到
主要字段：

订单号（第三方支付平台的订单号，方便与支付通道核对）、

玩家ID（提款玩家的ID）、

上级ID（该提款玩家上级用户的ID）、

玩家类型（同订单管理所述）、

渠道ID（同订单管理所述）、业务员ID（同订单管理所述）、

渠道名称（同订单管理所述）、

提现金额（提款的金额）、

累计充值（该用户在平台的总充值金额）、

自动出款备注（显示是否为系统自动出款）、

税收（平台对用户收取的提现手续费，目前都没有收取，可暂留备用）、

打款金额（需要支付的金额）、

携带金币（目前用户在平台上剩余的金币总数）、

邮件补偿（该字段可以不显示，意义不大）、

订单状态（显示是否加入审核工单，是否需要审批）、

账户类型（显示该用户提款账户的类型，通常有电话、CPF、邮箱等，取决于支付通道支持的支付类型有哪些）、

姓名（该用户提款时填写的真实姓名，目前支付都需要实名制，可以判断博主是否为本人提款）、

账号（用户提款时候填写的账号）、

第三方订单号（该字段可以不显示，意义不大）、

备注（显示提款的备注信息，通常为业务员对该用户的备注信息）
操作：通过（放款）、虚拟支付（仅在客户端显示放款，实际不会通过支付通道进行支付）、拒绝（拒绝提款，将提款金额退回用户的平台账户）、没收（直接没收该笔提款，不会退回用户的平台账户）。

### 2、转出记录

（重要模块）图示如下：
![](.//media/image10.png){width="5.768055555555556in"
height="2.932638888888889in"}\
![](.//media/image11.png){width="5.768055555555556in"
height="2.935416666666667in"}
![](.//media/image12.png){width="5.768055555555556in"
height="2.9590277777777776in"}\
所展示的检索功能全部都需要用到
主要字段：

参考放款审核的字段，

添加[审核时间]{.underline}（管理人员GM的审核时间）、[到账时间]{.underline}（支付通道的实际到账时间）即可。\
操作：第三方支付状态（如果实际有支付则可以点击查询第三方的支付情况，如果为虚拟支付则标记为虚拟支付），凭证（查询支付通道的支付凭证，付款凭证如下图所示）\
![](.//media/image13.png){width="3.6287882764654418in"
height="4.605555555555555in"}

### 3、邀请充值奖励发放

![](.//media/image14.png){width="5.768055555555556in"
height="3.2666666666666666in"}\
所展示的检索功能全部都需要用到\
勾选某笔奖金，点击奖金发放则为释放该比邀请奖金，邀请奖金会到达用户的平台账户中（奖金规则为，每日统计一次当日邀请的有效充值用户，邀请的下级用户首次充值25雷亚尔以上的用户为有效用户，复充用户不计入在内，奖励金额位每位有效用户10雷亚尔，可根据平台规则调整）；自动发放奖励开关（开通后，邀请奖金无需审核，可以自动释放到用户的平台账户中）\
主要字段：记录ID（该字段可以不显示，意义不大）、日期（邀请奖金统计的日期）、UID（用户再平台的ID）、邀请奖励发放状态（显示该笔奖金是否已经发放）、合计总奖金（该用户所有奖金的总额：已发放+未发放）、已发放奖金（该用户已经发放的奖金）、会员手机号（该字段可以不显示，意义不大）、玩家类型、上级、业务员等如之前所诉。\
奖金页面在用户端如下图所示，只有在业务员GM选择发放奖金后，用户才可以在此页面点看到邀请奖金金额，并点击接收奖金\
![](.//media/image15.png){width="5.431062992125984in"
height="4.219697069116361in"}\

## 四、统计分析

### 1、日况统计管理（重要模块）

如下图所示

![](.//media/image16.png){width="5.768055555555556in"
height="2.7680555555555557in"}
显示整个平台的每日情况数据，主要显示

金币历史库存（当日平台所有用户账户中剩余的金额汇总）、

总充值（每日平台的总充值金额）、

总提现（每日平台的总提款金额）、

总充提差（即每日平台的收付差，红色表示盈利，绿色表示负数亏损）、

新增用户、

首充人数、

复充人数、

游戏中奖（即游戏派彩）等即可，

其余字段意义不大可以取消。

### 2、游戏盈亏结算，如下图所示

![](.//media/image17.png){width="5.768055555555556in"
height="2.922222222222222in"}
显示不同游戏渠道的：

流水（即打码，用户在该游戏渠道下注的总额）、

平台盈亏（游戏流水减去游戏派彩）、

游戏回报率（派彩/流水的比率）、

税收（该字段可以不显示，意义不大）、

杀率（平台盈亏/流水的比率）、

总游戏人数、

总游戏局数。

### 3、渠道对账表（重要模块）

如下图所示

![](.//media/image18.png){width="5.768055555555556in"
height="2.8673611111111112in"}
显示每个渠道方（代理线）的总数据，需要的字段有：

渠道ID（代理线的ID）、

渠道名称（总台为代理线设置的备注）、

总充值、

总提现、

总重提差（总收付差）、

充值手续费（根据总台设置收取）、

提现手续费（根据总台设置收取）、

游戏盈亏（即API，游戏厂商计算的游戏打码量）、

API费用（根据总台设置收取）、

总利润（总利润=[总充提差]{.underline}-[充值手续费]{.underline}-[提现手续费]{.underline}-[API费用]{.underline}）。\

### 4、数据日报管理，非重要模块

如下图所示

![](.//media/image19.png){width="5.768055555555556in"
height="2.902083333333333in"}
显示不同渠道的每日各项数据，不太经常查询此项数据，因为总后端可以直接登陆渠道后端查询不同渠道的日况统计。\

## 五、客服系统

### 1、客服配置（重要模块）

如下图所示：

![](.//media/image20.png){width="5.768055555555556in"
height="2.790277777777778in"}\
![](.//media/image21.png){width="5.768055555555556in"
height="3.2333333333333334in"}\
主要功能是让用户可以通过前端寻找到客服支持人员，或则进入平台的玩家群组，查看平台官方INSTA等，通过插入链接的形式跳转。用户前端展示如下：
![](.//media/image22.png){width="2.8787882764654418in"
height="4.409027777777778in"}![](.//media/image23.png){width="2.7569444444444446in"
height="4.3930391513560805in"}\

### 2、GM上下分（非重要模块）

![](.//media/image24.png){width="5.768055555555556in"
height="2.8041666666666667in"}
主要显示管理员GM给用户手动加钱、手动扣钱等情况。

## 六、活动管理

### 1、充值返利活动（重要模块，即分成模式）

![](.//media/image25.png){width="5.768055555555556in"
height="2.8555555555555556in"}\
![](.//media/image26.png){width="5.768055555555556in"
height="2.908333333333333in"}
字段释义：活动名称（管理员自行备注）、活动封面（可以取消）；
活动参与范围（可以选则给不同的渠道/代理开通分成模式）：\
![](.//media/image27.png){width="5.768055555555556in"
height="1.5180555555555555in"}
活动时间的选择、

到账类型（统一为即时到账，立刻返现至用户的平台账户中）、

最小充值金额（给予分成佣金的最小充值金额）。
一级用户首充返现比例（首次充值的返现比例，例如比例为50%，A用户邀请了B用户，则B用户为A用户的一级用户，如果B用户在平台首次充值了100雷亚尔，则平台按设置比例返给A用户50雷亚尔作为佣金）、

一级用户复充返现比例（再次充值玩家的返现比例，规则同上）。
二级用户首充返现比例（例如比例为10%，A用户邀请了B用户，B用户邀请了C用户，则C用户为A用户的二级用户，如果C用户在平台充值了100雷亚尔，则平台你按设置比例返给A用户10雷亚尔作为佣金）
二级用户复充返现比例（同上）
**三级用户以此类推，并且做多只返现至三级用户。

## 七、玩家代理管理

### 1、代理有效邀请奖励配置管理（重要功能，即宝箱模式）

如下图

![](.//media/image28.png){width="5.768055555555556in"
height="2.6347222222222224in"}
用户前端显示如下图：
![](.//media/image29.png){width="4.606060804899387in"
height="6.5680555555555555in"}
用户完成邀请有效充值用户目标，可以手动点击宝箱，根据完成邀请有效充值用户数量，领取相应的宝箱金额。（默认首次充值25雷亚尔以上为有效充值用户），点击宝箱后，相应金额直接到达用户的平台账户上。

## 八、游戏管理

### 1、供应商管理（与当前平台相似，无需太大改变），如下所示

![](.//media/image30.png){width="5.768055555555556in"
height="2.9194444444444443in"}
可以选择平台是否开启游戏供应商，切换真假PG、PP等功能。

### 2、VIP配置，如下所示

![](.//media/image31.png){width="5.768055555555556in"
height="2.9298611111111112in"}
完成相应充值目标或则流水目标，可以升级VIP等级，获取VIP奖励，不同的VIP等级有不同的提款限制，管理员可以根据不同平台手动修改。
![](.//media/image32.png){width="2.809945319335083in"
height="1.9950120297462817in"}![](.//media/image33.png){width="2.7272725284339456in"
height="1.9916666666666667in"}
以上VIP设置功能都需要，默认设置规则供参考

### 3、打码配置

![](.//media/image34.png){width="5.768055555555556in"
height="2.4118055555555555in"}
此功能主要针对不同方式获取的游戏余额，来设置用户提现所需要完成的打码量，限制用户提款。

## 九、账号管理

### 1、管理员管理，如下所示

![](.//media/image35.png){width="5.768055555555556in"
height="2.873611111111111in"}
该功能主要为添加总后台管理员

### 2、管理员日志，如下所示

![](.//media/image36.png){width="5.768055555555556in"
height="2.942361111111111in"}
该功能主要记录每个管理员账号的操作日志

### 3、角色组，如下图所示

![](.//media/image37.png){width="5.768055555555556in"
height="2.954861111111111in"}
该功能主要配置管理员权限

# 十、玩家管理

### 1、所有玩家（重要核心模块）

如下图所示

![](.//media/image38.png){width="5.768055555555556in"
height="2.970833333333333in"}\
![](.//media/image39.png){width="5.768055555555556in"
height="2.81875in"}\
![](.//media/image40.png){width="5.768055555555556in"
height="2.8152777777777778in"}
![](.//media/image41.png){width="5.768055555555556in"
height="2.7680555555555557in"}
![](.//media/image42.png){width="5.768055555555556in" height="1.7in"}
所展示的检索功能全部都需要用到，包括操作区的功能，此模块需要默认冻结[玩家ID]{.underline}以及[上级ID]{.underline}此两列窗口（不会随着滚动条的移动而被隐藏），操作列也需要固定冻结。冻结金额为用户以及申请提现，但是还没有通过放款审核的金额。
核心功能1：掉绑设置（即偷人设置）
![](.//media/image43.png){width="5.768055555555556in"
height="3.2159722222222222in"}
a.模式介绍：关闭（则为关闭掉绑功能）、渠道模式（较少使用，可以忽略）、个人模式（按照设置内容，掉绑用户个人所有下级注册及充值用户，含所有1级、2级、3级用户）
b.个人注册掉绑设置说明：
掉绑类型：间隔（当个人邀请的注册用户超过XX人时，用户每邀请XX个玩家，隐藏1个玩家，例如用户连续邀请了4为注册用户A、B、C、D。系统会自动隐藏掉D玩家的信息，不会在用户端显示）
比例（当个人邀请的注册用户超过XX人时，后续用户每位按照XX比例进行隐藏，例如用户连续邀请了4位注册用户A、B、C、D。系统会按照设置的偷人概率进行偷取，隐藏用户信息，不会在用户端显示）
c.个人充值掉绑设置说明：
同注册用户，但是只针对有进行充值的玩家进行掉绑。
![](.//media/image44.png){width="5.768055555555556in"
height="3.1506944444444445in"}
\*被隐藏的用户，掉绑状态会显示未亮起，如果被用户发现，则可以手动点击取消\
核心功能2：拉黑设置（即控制玩家胜率）\
团队拉黑：拉黑该用户以及所有的下级用户的胜率（开始拉黑功能后，该用户团队后续进入的所有包括1级、2级、3级用户都会自动拉黑到相同等级）\
个人拉黑：仅拉黑该用户个人的胜率\
核心功能3：点击玩家ID（包含点击玩家上级ID）后会自动进入玩家信息界面，所示界面如下\
![](.//media/image45.png){width="5.768055555555556in"
height="3.147222222222222in"}
如上图基本信息所显示的所有数据都需要显示，需注意设置点击上级ID则会自动跳转到用户上级的用户信息界面（上图上级ID蓝色字体部分可以点击并跳转）。其中团队（含用户本人及其所有下级用户）详情下所显示的[总收付差]{.underline}=该[团队的总充值]{.underline}-[该团队的总提现]{.underline}。\
客服模块实际为修改用户登陆密码的功能，如下图\
![](.//media/image46.png){width="5.768055555555556in"
height="1.0291666666666666in"}
打码变化模块：显示用户的打码信息变化，是否完成打码量，是否可以提款，如下图\
![](.//media/image47.png){width="5.768055555555556in"
height="2.0930555555555554in"}
打码任务模块：显示玩家的打码任务，其中明打码任务为用户可以看到的打码量，暗打码任务为用户看不到的打码量，如下图\
![](.//media/image48.png){width="5.768055555555556in"
height="1.5680555555555555in"}
打码量操作日志模块：显示业务员GM，给玩家手动添加的打码任务，如下图\
![](.//media/image48.png){width="5.768055555555556in"
height="1.5680555555555555in"}
充值记录，显示用户每笔充值数据，如下图\
![](.//media/image49.png){width="5.768055555555556in"
height="1.76875in"}
提现记录，显示用户每笔提现数据，如下图\
![](.//media/image50.png){width="5.768055555555556in"
height="1.3180555555555555in"}
登入日志，显示用户的登入信息，如下图\
![](.//media/image51.png){width="5.768055555555556in" height="1.425in"}
同IP查询，显示所有与用户相同IP的用户数据，如下图\
![](.//media/image52.png){width="5.768055555555556in"
height="1.8819444444444444in"}
玩家备注，为业务员GM给玩家的备注信息，如下图\
![](.//media/image53.png){width="5.768055555555556in"
height="1.7069444444444444in"}
下级数据，显示用户所有的下级用户数据，需要显示每一位下级注册用户的基本数据并备注层级，如下图
![](.//media/image54.png){width="5.768055555555556in"
height="3.1909722222222223in"}
代理佣金，显示用户所有领取的分成佣金，邀请奖励，宝箱奖励等数据，如下图\
![](.//media/image55.png){width="5.768055555555556in"
height="1.5298611111111111in"}
游戏日报（此功能无用），
金币日志，显示用户进行游戏的数据，需要显示每一次的下注及输赢，金币的变化，如下图\
![](.//media/image56.png){width="5.768055555555556in"
height="3.1458333333333335in"}
代理余额日志（此功能无用）
充值分成，针对用户进行充值分成的配置，详细可以参考分成模式的解释，需要同步所有下级选项，如下图\
![](.//media/image57.png){width="5.768055555555556in"
height="2.4208333333333334in"}
偷人设置，参考针对掉绑配置的解释，需要同步所有下级的选项，如下图\
![](.//media/image58.png){width="5.768055555555556in"
height="1.9583333333333333in"}

### 2、虚拟玩家

如下图所示

![](.//media/image59.png){width="5.768055555555556in"
height="2.861111111111111in"}
此功能为添加虚拟账户给用户对平台进行测试使用，可以批量生成虚拟账户（选择数量），设置虚拟账户金额，密码统一默认为123456.

### 4、打码变化

如下图

![](.//media/image60.png){width="5.768055555555556in"
height="2.917361111111111in"}
主要显示每个用户的打码变化，是否完成打码任务，操作选项里面添加控制打码量的功能。

### 5、玩家分组管理（重要功能）

如下图所示

![](.//media/image61.png){width="5.768055555555556in"
height="2.9472222222222224in"}
主要为给玩家进行分组，区分博主和玩家以及骗子。

## 十一、渠道管理

### 1、渠道列表，如下图

![](.//media/image62.png){width="5.768055555555556in"
height="2.8020833333333335in"}\
![](.//media/image63.png){width="5.768055555555556in"
height="2.377083333333333in"}\
![](.//media/image64.png){width="5.768055555555556in"
height="2.904861111111111in"}\
主要为设置渠道（代理），设置每个渠道可以使用的放款额度，各种费用，点击渠道用户名，可以直接登陆该渠道后端。可以勾选是否给渠道放款权限，是否给渠道设置偷人权限，是否给渠道拉黑功能权限，是否给渠道配置宝箱权限，可以选择关闭渠道状态，让渠道无法使用。

### 2、代付额度明细，如下图\

![](.//media/image65.png){width="5.768055555555556in"
height="2.779861111111111in"}
主要为审核通过，渠道的放款额度，记录给每个渠道的放款额度。

## 十二、公告管理\

![](.//media/image66.png){width="5.768055555555556in"
height="2.939583333333333in"}
主要为配置用户前端界面的公告内容。包含前端轮播图、小喇叭等功能。