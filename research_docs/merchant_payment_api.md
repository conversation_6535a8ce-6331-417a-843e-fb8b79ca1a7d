# 接口请求响应说明

## 网关地址请与客服联系

HTTP FORM表单 KV键值对格式，Content-Type 头设置为 application/x-www-form-urlencoded;charset=UTF-8

响应格式: json 对象

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| success | Boolean | 是 | true成功,false失败 |
| errorCode | String | 可选 | 错误编码 |
| message | String | 可选 | 错误信息 |
| data | Object | 可选 | 成功响应业务字段 |

## 签名说明

请注意兼容后续的增减字段

签名串拼接时，请获取所有的非空参数集合(回调中,不包括回调url中包含的参数)

1. 对所有非空请求参数集合按 key asci码升序进行排序，得到有序集合，按key1=value1&key2=value2 拼接
2. 拼接 MD5 签名密钥 &secret= + 密钥
3. 对字符串进行 MD5 签名 sign(32位小写)

## 1. 创建交易

请求地址: /api/open/merchant/trade/create

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| merchantId | String | 是 | 商户ID |
| merchantOrderNo | String | 是 | 商户订单号 |
| amount | Number | 是 | 金额(单位:分) |
| payType | String | 是 | 产品编码 |
| currency | String | 是 | 币种 |
| content | String | 是 | 订单内容 |
| bankCode | String | 否 | 银行编码, 越南网银, 印尼网银 支付方式可以支持的银行编码 |
| kycPayerIdNo | String | 否 | KYC付款人ID, 巴西个人传CPF(纯数字), 巴西公司传CNPJ(纯数字) |
| kycPayerName | String | 否 | KYC付款人名称, 巴西个人传个人姓名, 巴西公司传公司名称 |
| clientIp | String | 是 | 用户Ip |
| callback | String | 是 | 回调地址 |
| redirect | String | 否 | 支付完成跳转地址 |
| sign | String | 是 | 签名 |

### 响应字段

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| merchantId | String | 是 | 商户Id |
| merchantOrderNo | String | 是 | 商户订单号 |
| orderNo | String | 是 | 平台订单号 |
| payUrl | String | 是 | 支付链接 |
| payRaw | String | 否 | 支付信息 |
| amount | Number | 是 | 金额(单位:分) |
| status | String | 是 | 订单状态 |
| currency | String | 是 | 币种 |
| payType | String | 是 | 代收产品编码 |

## 2. 查询交易订单

请求地址: /api/open/merchant/trade/query

### 请求参数

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| merchantId | String | 是 | 商户ID |
| merchantOrderNo | String | 是 | 商户订单号 |
| sign | String | 是 | 签名 |

### 响应字段

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| merchantId | String | 是 | 商户Id |
| merchantOrderNo | String | 是 | 商户订单号 |
| orderNo | String | 是 | 平台订单号 |
| amount | Number | 是 | 金额(单位:分) |
| status | String | 是 | 订单状态 |
| currency | String | 是 | 币种 |
| payType | String | 是 | 代收产品编码 |
| ref_cpf | String | 否 | 付款人CPF |
| ref_name | String | 否 | 付款人名称 |

## 3. 交易回调通知

签名串不包含url中的参数

调用该接口返回字段
回调错误时，可能多次回调，请做好去重操作（防止应用多多次处理）
处理成功后需响应 success 字符串

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| merchantId | String | 是 | 商户Id |
| merchantOrderNo | String | 是 | 商户订单号 |
| orderNo | String | 是 | 平台订单号 |
| amount | Number | 是 | 金额(单位:分) |
| status | String | 是 | 订单状态 |
| currency | String | 是 | 币种 |
| payType | String | 是 | 代收产品编码 |
| ref_cpf | String | 否 | 付款人CPF |
| ref_name | String | 否 | 付款人名称 |
| sign | String | 是 | 签名 |

## 4. 提现

请求地址:/api/open/merchant/payment/create

网络超时问题，需要处理成功才中止，找我方客服或者飞机群咨询机器人二次确认

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| merchantId | String | 是 | 商户ID |
| merchantOrderNo | String | 是 | 商户订单号 |
| amount | Number | 是 | 金额(单位:分) |
| currency | String | 是 | 代付币种 |
| accountType | String | 是 | 账户类型 |
| bankCode | String | 否 | 银行编码 |
| branchBankNo | String | 否 | 支行行号 |
| branchBankName | String | 否 | 支行名称 |
| accountNo | String | 是 | 账号 |
| accountName | String | 是 | 账户名 |
| accountMobile | String | 否 | 账户手机号 |
| accountEmail | String | 否 | 账户邮箱 |
| province | String | 否 | 省份 |
| city | String | 否 | 城市 |
| cpf | String | 否 | 巴西CPF/CPF_CNPJ |
| ifsc | String | 否 | 印度IFSC |
| callback | String | 否 | 回调地址 |
| sign | String | 是 | 签名 |

### 巴西代付说明

accountType支持的类型：PIX_EMAIL, PIX_PHONE, PIX_CPF, PIX_CNPJ, PIX_BANK

- PIX_PHONE: accountNo 请传对应+55手机号, accountName 收款人姓名
- PIX_EMAIL: accountNo 请传对应邮箱, accountName 请传收款人姓名
- PIX_CPF: accountNo 巴西个人税号cpf, accountName 请传收款人姓名
- PIX_CNPJ: accountNo 公司税号CNPJ, accountName 请传收款人姓名
- PIX_RANDOM: accountNo PIX密钥, accountName 请传收款人姓名, cpf 请传对应cpf
- PIX_EVP: accountNo PIX密钥, accountName 请传收款人姓名
- PIX_BANK: accountNo 请传银行账号, bankCode 请传ISPB, branchBankNo 传支行号, accountName 请传收款人姓名

### 响应字段

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| merchantId | String | 是 | 商户Id |
| merchantOrderNo | String | 是 | 商户单号 |
| orderNo | String | 是 | 平台代付单号 |
| amount | Number | 是 | 金额(单位:分) |
| status | String | 是 | 代付状态 |
| currency | String | 是 | 币种 |
| errorMsg | String | 否 | 失败时返回原因 |

## 5. 提现查询

请求地址:/api/open/merchant/payment/query

### 请求参数

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| merchantId | String | 是 | 商户ID |
| merchantOrderNo | String | 是 | 商户单号 |
| sign | String | 是 | 签名 |

### 响应字段

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| merchantId | String | 是 | 商户Id |
| merchantOrderNo | String | 是 | 商户单号 |
| orderNo | String | 是 | 平台代付单号 |
| amount | Number | 是 | 金额(单位:分) |
| status | String | 是 | 代付状态 |
| currency | String | 是 | 币种 |
| errorMsg | String | 否 | 失败原因 |

## 6. 提现回调通知

签名串不包含url中的参数
处理成功后需响应 success 字符串

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| merchantId | String | 是 | 商户Id |
| merchantOrderNo | String | 是 | 商户单号 |
| orderNo | String | 是 | 平台代付单号 |
| amount | Number | 是 | 金额(单位:分) |
| status | String | 是 | 代付状态 |
| currency | String | 是 | 币种 |
| errorMsg | String | 否 | 失败时返回原因 |
| sign | String | 是 | 签名 |

## 7. 余额查询

请求地址:/api/open/merchant/balance/query

### 请求参数

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| merchantId | String | 是 | 商户ID |
| sign | String | 是 | 签名 |

### 响应字段

data 为 List 数组，单条字段为

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| balance | Number | 是 | 余额 |
| unsettledBalance | Number | 是 | 待结算金额 |
| frozenAmount | Number | 是 | 冻结金额 |
| currency | String | 是 | 币种 |

## 8. 提现反查

请求地址在商户后台确定
签名串不包含url中的参数
提交响应 pass 字符串代表通过,其他则失败

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| merchantId | String | 是 | 商户ID |
| merchantOrderNo | String | 是 | 商户单号 |
| amount | Number | 是 | 代付金额 |
| currency | String | 是 | 代付币种 |
| callback | String | 是 | 代付回调地址 |
| sign | String | 是 | 签名 |

## 9. 编码定义

### payType产品编码 商户后台可查看已配置产品

| 编码 | 描述 |
|------|------|
| PIX_QRCODE | 巴西PIX扫码 |
| PKR_EASYPAISA | 巴基斯坦EasyPaisa钱包 |
| PKR_JAZZCASH | 巴基斯坦JazzCash钱包 |
| PHQR | 菲律宾扫码 |
| GCASH | 菲律宾GCASH收款 |

### status订单状态

| 状态码 | 描述 |
|--------|------|
| WAITING_PAY | 待支付 |
| PAYING | 支付中 |
| PAID | 支付成功 |
| PAY_FAILED | 支付失败 |
| REFUND | 已退款 |


