# ThinkPHP DB类使用说明

## 1. 基本配置

数据库配置位于 `application/database.php`，主要配置项包括：

```php
return [
    'type'            => 'mysql',           // 数据库类型
    'hostname'        => '127.0.0.1',       // 服务器地址
    'database'        => 'fastadmin',       // 数据库名
    'username'        => 'root',            // 用户名
    'password'        => '',                // 密码
    'hostport'        => '',                // 端口
    'charset'         => 'utf8mb4',         // 数据库编码
    'prefix'          => 'fa_',             // 数据库表前缀
    'debug'           => false,             // 数据库调试模式
    'deploy'          => 0,                 // 数据库部署方式:0 集中式,1 分布式
    'rw_separate'     => false,             // 读写分离
    'master_num'      => 1,                 // 读写分离后 主服务器数量
    'slave_no'        => '',                // 指定从服务器序号
    'fields_strict'   => true,              // 是否严格检查字段
    'resultset_type'  => 'array',           // 数据集返回类型
    'auto_timestamp'  => false,             // 自动写入时间戳
    'datetime_format' => false,             // 时间字段默认格式
    'sql_explain'     => false,             // 是否进行SQL性能分析
];
```

## 2. 基本用法

### 2.1 数据库连接

```php
// 使用默认配置连接
$db = Db::connect();

// 使用自定义配置连接
$db = Db::connect([
    'type'     => 'mysql',
    'hostname' => '127.0.0.1',
    'database' => 'test',
    'username' => 'root',
    'password' => '',
    'prefix'   => 'fa_'
]);

// 获取PDO对象
$pdo = $db->getPdo();
```

### 2.2 查询构造器

```php
use think\Db;

// 查询单条记录
$user = Db::table('user')->where('id', 1)->find();

// 查询多条记录
$users = Db::table('user')->where('status', 1)->select();

// 条件查询
$users = Db::table('user')
    ->where('status', 1)
    ->where('age', '>', 18)
    ->select();

// 排序
$users = Db::table('user')
    ->order('id', 'desc')
    ->select();

// 分页
$users = Db::table('user')
    ->page(1, 10)
    ->select();

// 字段选择
$users = Db::table('user')
    ->field('id, name, age')
    ->select();

// 分组
$users = Db::table('user')
    ->field('status, count(*) as count')
    ->group('status')
    ->select();

// 限制
$users = Db::table('user')
    ->limit(10)
    ->select();
```

### 2.3 原生SQL查询

```php
// 执行原生SQL查询
$result = Db::query('SELECT * FROM user WHERE id = ?', [1]);

// 执行原生SQL更新
Db::execute('UPDATE user SET status = ? WHERE id = ?', [1, 1]);

// 执行多条SQL
Db::execute("SELECT 1; SELECT 2;");
```

### 2.4 事务处理

```php
Db::startTrans();
try {
    // 执行SQL
    Db::table('user')->insert(['name' => 'test']);
    Db::table('log')->insert(['content' => 'test']);
    Db::commit();
} catch (\Exception $e) {
    Db::rollback();
    throw $e;
}
```

### 2.5 高级查询

```php
// 联表查询
$result = Db::table('user')
    ->alias('u')
    ->join('profile p', 'u.id = p.user_id')
    ->select();

// 子查询
$result = Db::table('user')
    ->where('id', 'in', function($query) {
        $query->table('user_group')->where('status', 1)->column('user_id');
    })
    ->select();

// 聚合查询
$count = Db::table('user')->count();
$sum = Db::table('user')->sum('score');
$avg = Db::table('user')->avg('score');
$max = Db::table('user')->max('score');
$min = Db::table('user')->min('score');

// 获取单列
$ids = Db::table('user')->column('id');
$names = Db::table('user')->column('name', 'id'); // 以id为键
```

### 2.6 数据操作

```php
// 插入数据
Db::table('user')->insert(['name' => 'test', 'age' => 18]);
Db::table('user')->insertAll([
    ['name' => 'test1', 'age' => 18],
    ['name' => 'test2', 'age' => 19]
]);

// 更新数据
Db::table('user')->where('id', 1)->update(['name' => 'test']);
Db::table('user')->where('id', 1)->inc('score', 1); // 递增
Db::table('user')->where('id', 1)->dec('score', 1); // 递减

// 删除数据
Db::table('user')->where('id', 1)->delete();
```

## 3. 条件查询操作符

支持以下条件查询操作符：

- `=` : 等于
- `<>` : 不等于
- `LIKE` : 模糊查询
- `NOT LIKE` : 不模糊查询
- `>` : 大于
- `>=` : 大于等于
- `<` : 小于
- `<=` : 小于等于
- `FINDIN` : FIND_IN_SET
- `IN` : IN查询
- `NOT IN` : NOT IN查询
- `BETWEEN` : BETWEEN查询
- `NOT BETWEEN` : NOT BETWEEN查询
- `NULL` : IS NULL
- `NOT NULL` : IS NOT NULL
- `EXISTS` : EXISTS查询
- `NOT EXISTS` : NOT EXISTS查询

## 4. 链式操作

```php
// 链式操作示例
$result = Db::table('user')
    ->field('id, name, age')
    ->where('status', 1)
    ->where('age', '>', 18)
    ->order('id', 'desc')
    ->group('status')
    ->having('count(*) > 1')
    ->limit(10)
    ->select();
```

## 5. 注意事项

1. 默认使用表前缀，如需禁用表前缀，可以在配置文件中设置 `'prefix' => ''`
2. 查询结果默认返回数组格式，可通过配置 `resultset_type` 修改
3. 支持自动写入时间戳，需要配置 `auto_timestamp` 为 true
4. 支持字段严格检查，需要配置 `fields_strict` 为 true
5. 支持SQL性能分析，需要配置 `sql_explain` 为 true
6. 使用 `Db::name()` 和 `Db::table()` 的区别：
   - `Db::name()` 会自动添加表前缀
   - `Db::table()` 使用完整的表名，不添加前缀

## 6. 最佳实践

1. 使用参数绑定而不是直接拼接SQL，防止SQL注入
2. 合理使用索引，提高查询性能
3. 大量数据操作时使用事务
4. 使用查询构造器而不是原生SQL，提高代码可维护性
5. 合理使用联表查询，避免过多的联表操作
6. 使用 `Db::name()` 而不是 `Db::table()`，以支持表前缀
7. 使用链式操作提高代码可读性
8. 合理使用字段选择，避免查询不必要的字段
9. 使用 `column()` 方法获取单列数据，提高性能
10. 使用 `inc()` 和 `dec()` 方法进行数据递增和递减操作
11. 使用 `Db::connect()` 方法连接数据库，提高数据库连接的灵活性
12. 使用 `getPdo()` 方法获取PDO对象，提高数据库操作的灵活性 