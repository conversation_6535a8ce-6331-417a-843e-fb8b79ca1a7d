# 项目部署步骤

1.  复制项目基本文件夹，切换到目标皮肤分支。
2.  设置 `.env` 文件中的 `APP_STAGE` 变量和数据库连接相关变量。
3.  从上一个 stage 的数据库 dump 出 SQL：
    *   **前提**：假设 `pgd` 是源数据库名，`pgd2` 是新的目标数据库名。
    *   **导出结构**：
        ```bash
        mysqldump pgd --no-data --events --routines | mysql pgd2
        ```
    *   **导出指定表数据**：
        ```bash
        mysqldump --no-create-info pgd announcements betting_task_config cards customer_service_config fa_admin fa_auth_group fa_auth_group_access fa_auth_rule fa_config fa_index_config game_providers game_rooms payment_channels payment_plans player_balance_change_types player_tags treasure_box_config vip_config > pgdata.sql
        ```
    *   **编辑数据文件**：编辑 `pgdata.sql` 文件，对于 `fa_admin` 表和 `fa_auth_group_access` 表，仅保留各自的第一条 INSERT 语句（即第一条记录）。
        可以使用以下 sed 命令完成此操作：
        ```bash
        # 备份原始文件
        cp pgdata.sql pgdata.sql.bak
        
        # 处理 fa_admin 表：提取第一条 INSERT 语句
        sed -n '/^INSERT INTO `fa_admin` VALUES/ {p; n; s/,$/;/; p; q; }' pgdata.sql > fa_admin.tmp
        
        # 处理 fa_auth_group_access 表：提取第一条 INSERT 语句
        sed -n '/^INSERT INTO `fa_auth_group_access` VALUES/ {p; n; s/,$/;/; p; q; }' pgdata.sql > fa_auth_group_access.tmp
        
        # 临时文件用于保存其他表的所有数据
        sed -e '/^INSERT INTO `fa_admin` VALUES/,/);/d' -e '/^INSERT INTO `fa_auth_group_access` VALUES/,/);/d' pgdata.sql > other_tables.tmp
        
        # 合并所有临时文件到最终 SQL 文件
        cat other_tables.tmp fa_admin.tmp fa_auth_group_access.tmp > pgdata.sql
        
        # 清理临时文件
        rm *.tmp
        ```
*   **导入数据**：
        ```
        mysql pgd2 < pgdata.sql
        ```
*   **重置 `deposit_orders` 主键自增值**：
        ```
        mysql pgd2 -e "ALTER TABLE deposit_orders AUTO_INCREMENT=1;"
        ```
4.  配置 Caddyfile：
    *   主域名
    *   后台域名
    *   api域名，用于支付网关回调