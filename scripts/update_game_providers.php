<?php
/**
 * 游戏提供商列表更新脚本
 * 扫描 public/assets/img/frontend/games 目录下所有提供商的游戏图片
 * 更新 game_providers_list.json 文件
 */

// 定义目录和文件路径
$imagesDir = 'public/assets/img/frontend/games';
$jsonFile = 'public/assets/json/frontend/game_providers_list.json';

// 检查目录是否存在
if (!is_dir($imagesDir)) {
    die("Error: 目录 {$imagesDir} 不存在\n");
}

// 读取JSON文件
if (!file_exists($jsonFile)) {
    die("Error: JSON文件 {$jsonFile} 不存在\n");
}

$jsonData = json_decode(file_get_contents($jsonFile), true);
if (json_last_error() !== JSON_ERROR_NONE) {
    die("Error: JSON文件解析错误: " . json_last_error_msg() . "\n");
}

// 列出游戏提供商目录
$providers = array_diff(scandir($imagesDir), array('.', '..', '.DS_Store'));
echo "找到以下游戏提供商目录:\n";
foreach ($providers as $provider) {
    echo "- {$provider}\n";
}

// 游戏名称映射，将文件名转换为更友好的游戏名称
$gameNameMap = [
    'PG' => [
        '100' => 'Fortune Mouse',
        '101' => 'Fortune Ox',
        '102' => 'Fortune Tiger',
        '103' => 'Mahjong Ways',
        '104' => 'Ganesha Fortune',
        '105' => 'Dragon Hatch',
        // 可以继续添加更多名称映射
    ],
    'JILI-FISH' => [
        '001' => 'Fish Hunter',
        '003' => 'Golden Toad',
        '004' => 'Fish Paradise',
    ],
    'JILI-SLOT' => [
        '002' => 'Fortune Tiger',
        '003' => 'Lucky Neko',
    ],
    'JDB-FISH' => [
        '008' => 'Fish Hunter',
        '009' => 'Dragon Treasure',
    ],
    'JDB-SLOT' => [
        '011' => 'Fortune Gods',
        '012' => 'Golden Dragon',
    ],
    'CQ9' => [
        '102' => 'Dragon Boat',
        '103' => 'Fire Chibi',
        '104' => 'Treasure Bowl',
    ],
];

// Popular游戏集合
$popularGames = [];
$maxPopularGames = 10; // 每个提供商最多选择的热门游戏数量
$popularLimit = 2; // 每个提供商选择的游戏数量上限

// 清空所有提供商的游戏列表
foreach ($jsonData as $key => $value) {
    if ($key !== 'popular') { // 不清空popular分类
        $jsonData[$key]['games'] = [];
    }
}

// 处理每个提供商目录
foreach ($providers as $provider) {
    $providerDir = $imagesDir . '/' . $provider;
    if (!is_dir($providerDir)) {
        continue;
    }

    // 确保提供商在JSON中存在
    if (!isset($jsonData[$provider])) {
        echo "提供商 {$provider} 在JSON中不存在，跳过\n";
        continue;
    }

    echo "处理提供商: {$provider}\n";
    
    // 获取所有PNG图片
    $images = glob($providerDir . '/*.png');
    $gameCount = 0;
    $popularCount = 0;
    
    foreach ($images as $image) {
        $filename = basename($image);
        if (strpos($filename, '.DS_Store') !== false) continue;
        
        // 提取游戏ID
        $gameId = pathinfo($filename, PATHINFO_FILENAME);
        $gameId = str_replace('_en', '', $gameId); // 移除_en后缀
        
        // 寻找适合的名称
        $name = '';
        foreach ($gameNameMap as $prefix => $nameMap) {
            if (strpos($gameId, $prefix) === 0) {
                $id = str_replace($prefix . '_', '', $gameId);
                $name = isset($nameMap[$id]) ? $nameMap[$id] : '';
                break;
            }
        }
        
        // 如果没有映射名称，使用默认命名
        if (empty($name)) {
            $name = 'Game ' . $gameId;
        }
        
        $game = [
            'game_id' => $gameId,
            'name' => $name,
            'image' => '/assets/img/frontend/games/' . $provider . '/' . $filename,
            'type' => $provider
        ];
        
        // 添加到提供商的游戏列表
        $jsonData[$provider]['games'][] = $game;
        $gameCount++;
        
        // 添加到热门游戏(仅选择部分游戏)
        if ($popularCount < $popularLimit && count($popularGames) < $maxPopularGames) {
            $popularGames[] = $game;
            $popularCount++;
        }
    }
    
    echo "  添加了 {$gameCount} 个游戏\n";
}

// 设置热门游戏列表
$jsonData['popular']['games'] = $popularGames;
echo "添加了 " . count($popularGames) . " 个热门游戏\n";

// 保存回JSON文件
file_put_contents($jsonFile, json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "JSON文件已更新: {$jsonFile}\n"; 