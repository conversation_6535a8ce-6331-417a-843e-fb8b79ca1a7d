<?php
/**
 * 游戏提供商JSON数据更新脚本
 * 直接读取public/assets/img/frontend/games目录结构，更新JSON文件
 */

// 定义路径
$rootPath = __DIR__;
$imgPath = $rootPath . '/public/assets/img/frontend/games';
$jsonPath = $rootPath . '/public/assets/json/frontend';
$jsonFile = $jsonPath . '/game_providers_list.json';

// 检查游戏图片目录是否存在
if (!is_dir($imgPath)) {
    die("错误：游戏图片目录不存在: $imgPath\n");
}

// 列出所有文件夹
echo "游戏目录 $imgPath 中的文件夹：\n";
echo "==============================\n";

$folders = [];
$items = scandir($imgPath);
foreach ($items as $item) {
    // 跳过特殊目录和文件
    if ($item === '.' || $item === '..' || $item === '.DS_Store') {
        continue;
    }
    
    $itemPath = $imgPath . '/' . $item;
    if (is_dir($itemPath)) {
        $folders[] = $item;
        echo "- $item\n";
    }
}

echo "\n找到 " . count($folders) . " 个游戏提供商文件夹\n";

// 让用户确认是否继续
echo "\n是否继续生成JSON文件？(y/n): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
if(trim(strtolower($line)) != 'y'){
    echo "操作已取消\n";
    exit;
}

// 确保JSON目录存在
if (!is_dir($jsonPath)) {
    mkdir($jsonPath, 0777, true);
    echo "创建目录: $jsonPath\n";
}

// 映射关系：目录名称 => 提供商代码和名称
$providerMapping = [
    '真PG' => ['code' => 'PG', 'name' => 'PG Soft'],
    'PP' => ['code' => 'PP', 'name' => 'Pragmatic Play'],
    'JILI' => ['code' => 'JILI', 'name' => 'JILI Gaming'],
    'JDB' => ['code' => 'JDB', 'name' => 'JDB Gaming'],
    'TADA' => ['code' => 'TADA', 'name' => 'TADA Gaming'],
    'CQ9' => ['code' => 'CQ9', 'name' => 'CQ9 Gaming'],
    'Bgaming' => ['code' => 'BGM', 'name' => 'Bgaming'],
    'FC' => ['code' => 'FC', 'name' => 'FC Gaming'],
    'Habanero' => ['code' => 'HBN', 'name' => 'Habanero'],
    'Hacksaw' => ['code' => 'HCK', 'name' => 'Hacksaw Gaming'],
    'MicroGaming' => ['code' => 'MG', 'name' => 'MicroGaming'],
    'RelaxGaming' => ['code' => 'RLX', 'name' => 'Relax Gaming'],
    'YESBINGO' => ['code' => 'YB', 'name' => 'YESBINGO'],
    '仿JDB' => ['code' => 'FJDB', 'name' => '仿JDB'],
    '仿JILI' => ['code' => 'FJILI', 'name' => '仿JILI'],
    '仿PG' => ['code' => 'FPG', 'name' => '仿PG'],
    '仿PP' => ['code' => 'FPP', 'name' => '仿PP'],
    '仿SPIRIT' => ['code' => 'FSPT', 'name' => '仿SPIRIT'],
    '仿SPRIBE' => ['code' => 'FSPR', 'name' => '仿SPRIBE'],
    '仿TADA' => ['code' => 'FTADA', 'name' => '仿TADA']
];

// 确认映射表完整性
echo "\n检查映射表完整性...\n";
$missingMappings = [];
foreach ($folders as $folder) {
    if (!isset($providerMapping[$folder])) {
        $missingMappings[] = $folder;
        echo "警告：文件夹 '$folder' 在映射表中没有定义\n";
    }
}

if (!empty($missingMappings)) {
    echo "\n发现 " . count($missingMappings) . " 个文件夹没有映射定义，将使用默认命名方式\n";
    
    // 让用户确认是否继续
    echo "是否继续？(y/n): ";
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    if(trim(strtolower($line)) != 'y'){
        echo "操作已取消\n";
        exit;
    }
}

// 初始化结果数组
$result = [];
$stats = [
    'providers' => 0,
    'games' => 0,
    'popular_games' => 0
];

echo "\n开始处理游戏图片目录...\n";

// 读取提供商目录
foreach ($folders as $provider) {
    $providerPath = $imgPath . '/' . $provider;
    
    echo "处理提供商: $provider\n";
    
    // 设置提供商代码和名称
    $code = isset($providerMapping[$provider]['code']) ? $providerMapping[$provider]['code'] : strtoupper(substr($provider, 0, 3));
    $name = isset($providerMapping[$provider]['name']) ? $providerMapping[$provider]['name'] : $provider;
    
    // 初始化提供商数据
    $result[$provider] = [
        'code' => $code,
        'name' => $name,
        'games' => []
    ];
    
    // 读取游戏图片
    $gameImages = glob($providerPath . '/*.png');
    $gameCount = 0;
    
    foreach ($gameImages as $image) {
        $imageName = basename($image);
        
        // 跳过系统文件
        if ($imageName === '.DS_Store') {
            continue;
        }
        
        // 提取游戏ID和名称
        $gameId = pathinfo($imageName, PATHINFO_FILENAME);
        // 移除语言后缀（如_en）
        $gameId = preg_replace('/_[a-z]{2}$/', '', $gameId);
        
        // 格式化游戏名称
        $gameName = str_replace('_', ' ', $gameId);
        $gameName = preg_replace('/([a-z])([A-Z])/', '$1 $2', $gameName); // CamelCase转空格分隔
        $gameName = ucwords(strtolower($gameName)); // 首字母大写
        
        // 相对路径
        $relativePath = '/assets/img/frontend/games/' . $provider . '/' . $imageName;
        
        // 添加到游戏列表
        $result[$provider]['games'][] = [
            'game_id' => $gameId,
            'name' => $gameName,
            'image' => $relativePath
        ];
        
        $gameCount++;
        $stats['games']++;
    }
    
    echo "  添加了 $gameCount 个游戏\n";
    $stats['providers']++;
}

// 创建Popular Games分类
$popularGames = [];
foreach ($result as $provider) {
    if (!empty($provider['games'])) {
        // 从每个提供商选择最多5个游戏
        $selectedGames = array_slice($provider['games'], 0, min(5, count($provider['games'])));
        $popularGames = array_merge($popularGames, $selectedGames);
    }
}

// 随机打乱热门游戏列表
shuffle($popularGames);

// 限制热门游戏数量
$popularGames = array_slice($popularGames, 0, 100);
$stats['popular_games'] = count($popularGames);

// 添加Popular分类
$result = array_merge([
    'popular' => [
        'code' => 'POP',
        'name' => 'Popular Games',
        'games' => $popularGames
    ]
], $result);

// 保存到JSON文件
$jsonContent = json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
if (file_put_contents($jsonFile, $jsonContent)) {
    echo "\n成功更新游戏提供商列表到: $jsonFile\n";
    echo "总计: {$stats['providers']} 个提供商, {$stats['games']} 个游戏, {$stats['popular_games']} 个热门游戏\n";
    
    // 输出每个提供商的游戏数量
    echo "\n每个提供商的游戏数量:\n";
    echo "------------------------\n";
    foreach ($result as $key => $provider) {
        if ($key !== 'popular') {
            $count = count($provider['games']);
            echo sprintf("%-15s: %d 个游戏\n", $key, $count);
        }
    }
    
    echo "\n脚本执行完成\n";
} else {
    echo "错误: 无法写入JSON文件 $jsonFile\n";
} 