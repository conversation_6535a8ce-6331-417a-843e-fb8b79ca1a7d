<?php
/**
 * 充值订单佣金计算脚本
 *
 * 功能：传入deposit_orders的id，脚本自动查询充值订单信息，
 * 并查询充值订单所属玩家的一二三级上线，以及对应的佣金配置，
 * 根据佣金配置计算佣金并添加到上线玩家，同时插入记录到player_balance_logs。
 *
 * 使用方法：php calculate_commission.php [订单ID]
 *
 * 示例：php calculate_commission.php 318
 */

// 定义交易类型常量
const TRANSACTION_TYPE_LEVEL1_COMMISSION = 14; // 邀请充值返利一级贡献
const TRANSACTION_TYPE_LEVEL2_COMMISSION = 15; // 邀请充值返利二级贡献
const TRANSACTION_TYPE_LEVEL3_COMMISSION = 16; // 邀请充值返利三级贡献

// 从.env文件读取数据库配置
function parseEnvFile($filePath) {
    if (!file_exists($filePath)) {
        throw new Exception("配置文件不存在: {$filePath}");
    }

    $config = [];
    $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $currentSection = null;

    foreach ($lines as $line) {
        // 跳过注释行
        if (strpos(trim($line), '#') === 0) {
            continue;
        }

        // 处理节名称 [section]
        if (preg_match('/^\[([^\]]+)\]$/', trim($line), $matches)) {
            $currentSection = $matches[1];
            continue;
        }

        // 处理键值对
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value);

            // 如果在节内，则使用节名作为前缀
            if ($currentSection) {
                $config[$currentSection][$key] = $value;
            } else {
                $config[$key] = $value;
            }
        }
    }

    return $config;
}

// 获取脚本所在目录
$scriptDir = dirname(__FILE__);
// 查找.env文件（先查找当前目录，然后查找上级目录）
$envFile = $scriptDir . '/.env';
if (!file_exists($envFile)) {
    $envFile = dirname($scriptDir) . '/.env';
}
if (!file_exists($envFile)) {
    $envFile = dirname($scriptDir) . '/pgd/.env';
}
if (!file_exists($envFile)) {
    $envFile = dirname(dirname($scriptDir)) . '/pgd/.env';
}

if (!file_exists($envFile)) {
    throw new Exception("找不到.env配置文件");
}

echo "使用配置文件: {$envFile}\n";

// 解析.env文件
$envConfig = parseEnvFile($envFile);

// 设置数据库配置
$dbConfig = [
    'host' => $envConfig['database']['hostname'] ?? '127.0.0.1',
    'port' => $envConfig['database']['hostport'] ?? '3306',
    'dbname' => $envConfig['database']['database'] ?? 'pgd',
    'username' => $envConfig['database']['username'] ?? 'root',
    'password' => $envConfig['database']['password'] ?? '',
    'charset' => 'utf8mb4'
];

echo "数据库配置：主机={$dbConfig['host']}, 端口={$dbConfig['port']}, 数据库={$dbConfig['dbname']}\n";

// 检查命令行参数
if ($argc < 2) {
    echo "错误：缺少订单ID参数\n";
    echo "使用方法：php calculate_commission.php [订单ID]\n";
    exit(1);
}

// 获取订单ID参数
$orderId = intval($argv[1]);
if ($orderId <= 0) {
    echo "错误：无效的订单ID\n";
    exit(1);
}

try {
    // 连接数据库
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];

    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $options);

    // 开始事务
    $pdo->beginTransaction();

    // 1. 查询充值订单信息
    $orderQuery = "SELECT id, player_id, amount, payment_status FROM deposit_orders WHERE id = :order_id";
    $orderStmt = $pdo->prepare($orderQuery);
    $orderStmt->execute(['order_id' => $orderId]);
    $order = $orderStmt->fetch();

    if (!$order) {
        throw new Exception("未找到ID为 {$orderId} 的充值订单");
    }

    // 检查订单状态是否为已支付
    if ($order['payment_status'] != 1) {
        echo "订单ID {$orderId} 的支付状态不是已支付(1)，当前状态为: {$order['payment_status']}\n";
        exit(0);
    }

    $playerId = $order['player_id'];
    $orderAmount = $order['amount'];

    echo "订单信息：ID={$orderId}, 玩家ID={$playerId}, 金额={$orderAmount}\n";

    // 2. 查询玩家的一二三级上线关系
    $relationQuery = "SELECT
        MAX(CASE WHEN relation_level = 1 THEN ancestor_id ELSE NULL END) AS level1_id,
        MAX(CASE WHEN relation_level = 2 THEN ancestor_id ELSE NULL END) AS level2_id,
        MAX(CASE WHEN relation_level = 3 THEN ancestor_id ELSE NULL END) AS level3_id
    FROM player_relations
    WHERE player_id = :player_id
    GROUP BY player_id";

    $relationStmt = $pdo->prepare($relationQuery);
    $relationStmt->execute(['player_id' => $playerId]);
    $relations = $relationStmt->fetch();

    if (!$relations) {
        echo "玩家ID {$playerId} 没有上线关系\n";
        $pdo->commit();
        exit(0);
    }

    $level1Id = $relations['level1_id'];
    $level2Id = $relations['level2_id'];
    $level3Id = $relations['level3_id'];

    echo "上线关系：一级={$level1Id}, 二级={$level2Id}, 三级={$level3Id}\n";

    // 3. 处理一级上线佣金
    if ($level1Id) {
        processCommission($pdo, $level1Id, $orderAmount, $playerId, $orderId, 1);
    }

    // 4. 处理二级上线佣金
    if ($level2Id) {
        processCommission($pdo, $level2Id, $orderAmount, $playerId, $orderId, 2);
    }

    // 5. 处理三级上线佣金
    if ($level3Id) {
        processCommission($pdo, $level3Id, $orderAmount, $playerId, $orderId, 3);
    }

    // 提交事务
    $pdo->commit();
    echo "佣金计算和分配完成\n";

} catch (Exception $e) {
    // 回滚事务
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo "错误：" . $e->getMessage() . "\n";
    exit(1);
}

/**
 * 处理佣金计算和分配
 *
 * @param PDO $pdo 数据库连接
 * @param int $ancestorId 上线玩家ID
 * @param float $orderAmount 订单金额
 * @param int $playerId 下线玩家ID
 * @param int $orderId 订单ID
 * @param int $level 上线级别(1,2,3)
 */
function processCommission($pdo, $ancestorId, $orderAmount, $playerId, $orderId, $level) {
    // 获取对应级别的字段名
    $rateField = "level{$level}_rate";
    $maxAmountField = "level{$level}_max_amount";
    $transactionType = constant("TRANSACTION_TYPE_LEVEL{$level}_COMMISSION");

    // 查询上线玩家的佣金配置
    $configQuery = "SELECT player_id, min_deposit_amount, {$rateField}, {$maxAmountField}
                   FROM player_commission_configs
                   WHERE player_id = :player_id";

    $configStmt = $pdo->prepare($configQuery);
    $configStmt->execute(['player_id' => $ancestorId]);
    $config = $configStmt->fetch();

    if (!$config) {
        echo "玩家ID {$ancestorId} 没有佣金配置\n";
        return;
    }

    $minDepositAmount = floatval($config['min_deposit_amount']);
    $commissionRate = floatval($config[$rateField]);
    $maxCommissionAmount = floatval($config[$maxAmountField]);

    echo "玩家ID {$ancestorId} 的佣金配置：最低充值金额={$minDepositAmount}, 佣金比例={$commissionRate}%, 最大佣金金额={$maxCommissionAmount}\n";

    // 检查是否满足最低充值金额要求
    if ($orderAmount < $minDepositAmount) {
        echo "订单金额 {$orderAmount} 小于最低充值金额要求 {$minDepositAmount}，不发放佣金\n";
        return;
    }

    // 计算佣金金额
    $commissionAmount = $orderAmount * $commissionRate / 100;

    // 检查是否超过最大佣金金额
    if ($maxCommissionAmount > 0 && $commissionAmount > $maxCommissionAmount) {
        $commissionAmount = $maxCommissionAmount;
    }

    // 四舍五入到2位小数
    $commissionAmount = round($commissionAmount, 2);

    if ($commissionAmount <= 0) {
        echo "计算的佣金金额为0或负数，不发放佣金\n";
        return;
    }

    echo "计算的佣金金额：{$commissionAmount}\n";

    // 查询玩家当前余额
    $balanceQuery = "SELECT balance FROM players WHERE id = :player_id";
    $balanceStmt = $pdo->prepare($balanceQuery);
    $balanceStmt->execute(['player_id' => $ancestorId]);
    $playerData = $balanceStmt->fetch();

    if (!$playerData) {
        throw new Exception("未找到ID为 {$ancestorId} 的玩家");
    }

    $balanceBefore = floatval($playerData['balance']);
    $balanceAfter = $balanceBefore + $commissionAmount;

    echo "玩家ID {$ancestorId} 的余额变更：{$balanceBefore} -> {$balanceAfter}\n";

    // 更新玩家余额
    $updateBalanceQuery = "UPDATE players SET balance = :balance_after WHERE id = :player_id";
    $updateBalanceStmt = $pdo->prepare($updateBalanceQuery);
    $updateBalanceStmt->execute([
        'balance_after' => $balanceAfter,
        'player_id' => $ancestorId
    ]);

    // 记录余额变动日志
    $levelText = ["一级", "二级", "三级"][$level - 1];
    $remark = "玩家ID:{$playerId} 充值订单ID:{$orderId} 的{$levelText}佣金";

    $logQuery = "INSERT INTO player_balance_logs (
        player_id, amount, balance_before, balance_after,
        balance_type, transaction_type, remark, created_at
    ) VALUES (
        :player_id, :amount, :balance_before, :balance_after,
        1, :transaction_type, :remark, NOW()
    )";

    $logStmt = $pdo->prepare($logQuery);
    $logStmt->execute([
        'player_id' => $ancestorId,
        'amount' => $commissionAmount,
        'balance_before' => $balanceBefore,
        'balance_after' => $balanceAfter,
        'transaction_type' => $transactionType,
        'remark' => $remark
    ]);

    echo "已成功发放{$levelText}佣金 {$commissionAmount} 给玩家ID {$ancestorId}\n";
}
