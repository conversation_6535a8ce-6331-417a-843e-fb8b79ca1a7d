<?php

use think\Route;

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

// 注册路由到News控制器
Route::rule('index/CustomerService/:action', "index/CustomerService/:action");

// 添加公告相关路由
Route::get('close_notice_popup', 'index/index/markNoticePopupClosed');

// 添加登录状态检查路由
Route::get('index/index/checkLoginStatus', 'index/index/checkLoginStatus');

// 添加通配符路由，捕获所有不存在的前台路由
Route::miss('index/index/index', 'get');

return [
    //别名配置,别名只能是映射到控制器且访问时必须加上请求的方法
    '__alias__'   => [
    ],
    //变量规则
    '__pattern__' => [
    ],
    //路由规则
    '__rule__'    => [
    ],
//        域名绑定到模块
//        '__domain__'  => [
//            'admin' => 'admin',
//            'api'   => 'api',
//        ],
];
