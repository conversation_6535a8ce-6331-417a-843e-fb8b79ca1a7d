<?php

namespace app\common\controller;

use app\common\library\Auth;
use think\Config;
use think\Controller;
use think\Hook;
use think\Lang;
use think\Loader;
use think\Validate;

/**
 * 前台控制器基类
 */
class Frontend extends Controller
{

    /**
     * 布局模板
     * @var string
     */
    protected $layout = '';

    /**
     * 无需登录的方法,同时也就不需要鉴权了
     * @var array
     */
    protected $noNeedLogin = [];

    /**
     * 无需鉴权的方法,但需要登录
     * @var array
     */
    protected $noNeedRight = [];

    /**
     * 权限Auth
     * @var Auth
     */
    protected $auth = null;

    public function _initialize()
    {
        //移除HTML标签
        $this->request->filter('trim,strip_tags,htmlspecialchars');
        $modulename = $this->request->module();
        $controllername = Loader::parseName($this->request->controller());
        $actionname = strtolower($this->request->action());

        // 检测IP是否允许
        check_ip_allowed();

        // 如果有使用模板布局
        if ($this->layout) {
            $this->view->engine->layout('layout/' . $this->layout);
        }
        $this->auth = Auth::instance();

        $path = str_replace('.', '/', $controllername) . '/' . $actionname;

        // 检测是否需要验证登录
        if (!$this->match($this->noNeedLogin, $path)) {
            // 检测是否登录
            $loginStatus = \app\index\model\Player::checkLoginStatus();

            if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
                // 记录未登录访问
                \app\common\library\FrontendLog::warning('未登录用户尝试访问需要登录的页面: ' . $path . ', IP=' . $this->request->ip());

                // 设置提示信息，将在首页显示
                \think\Session::set('toast_message', __('Please login first'));
                \think\Session::set('toast_type', 'warning');

                // 重定向到首页并带上login参数，显示登录弹窗
                $this->redirect(url('/', ['login' => 1, 't' => time()]));
                exit;
            }

            // 将用户信息传递给视图
            $this->view->assign('user', $loginStatus['data']);
        }

        // 将用户信息传递给视图（如果已登录）
        $loginStatus = \app\index\model\Player::checkLoginStatus();
        if ($loginStatus['code'] == 1 && isset($loginStatus['data'])) {
            $this->view->assign('user', $loginStatus['data']);
        }

        // 语言检测
        // 从配置文件获取前台语言设置，默认为中文
        $lang = config('lang.frontend') ?: 'pt-br';
        // 确保语言代码格式正确
        $lang = preg_match("/^([a-zA-Z\-_]{2,10})$/i", $lang) ? $lang : 'pt-br';
        $site = Config::get("site");

        $upload = \app\common\model\Config::upload();

        // 上传信息配置后
        Hook::listen("upload_config_init", $upload);

        // 配置信息
        $config = [
            'site'           => array_intersect_key($site, array_flip(['name', 'cdnurl', 'version', 'timezone', 'languages'])),
            'upload'         => $upload,
            'modulename'     => $modulename,
            'controllername' => $controllername,
            'actionname'     => $actionname,
            'jsname'         => 'frontend/' . str_replace('.', '/', $controllername),
            'moduleurl'      => rtrim(url("/{$modulename}", '', false), '/'),
            'language'       => $lang
        ];
        $config = array_merge($config, Config::get("view_replace_str"));

        Config::set('upload', array_merge(Config::get('upload'), $upload));

        // 配置信息后
        Hook::listen("config_init", $config);
        // 加载当前控制器语言包
        $this->loadlang($controllername);
        $this->assign('site', $site);
        $this->assign('config', $config);
    }

    /**
     * 加载语言文件
     * @param string $name
     */
    protected function loadlang($name)
    {
        $name = Loader::parseName($name);
        $name = preg_match("/^([a-zA-Z0-9_\.\/]+)\$/i", $name) ? $name : 'index';
        // 从配置文件获取前台语言设置，默认为中文
        $lang = config('lang.frontend') ?: 'zh-cn';
        // 确保语言代码格式正确
        $lang = preg_match("/^([a-zA-Z\-_]{2,10})$/i", $lang) ? $lang : 'zh-cn';
        Lang::load(APP_PATH . $this->request->module() . '/lang/' . $lang . '/' . str_replace('.', '/', $name) . '.php');
    }

    /**
     * 渲染配置信息
     * @param mixed $name  键名或数组
     * @param mixed $value 值
     */
    protected function assignconfig($name, $value = '')
    {
        $this->view->config = array_merge($this->view->config ? $this->view->config : [], is_array($name) ? $name : [$name => $value]);
    }

    /**
     * 刷新Token
     */
    protected function token()
    {
        $token = $this->request->param('__token__');

        //验证Token
        if (!Validate::make()->check(['__token__' => $token], ['__token__' => 'require|token'])) {
            $this->error(__('Token verification error'), '', ['__token__' => $this->request->token()]);
        }

        //刷新Token
        $this->request->token();
    }

    /**
     * 检测是否匹配
     * @param array $arr 需要匹配的规则
     * @param string $path 当前请求路径
     * @return boolean
     */
    protected function match($arr, $path)
    {
        $arr = is_array($arr) ? $arr : explode(',', $arr);
        if (in_array('*', $arr) || in_array($path, $arr)) {
            return true;
        }

        $arr2 = array_map(function ($val) {
            return rtrim($val, "/*") . '/';
        }, $arr);

        foreach ($arr2 as $val) {
            if (strpos($path . '/', $val) === 0) {
                return true;
            }
        }

        return false;
    }
}
