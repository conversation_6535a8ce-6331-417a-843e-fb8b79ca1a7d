<?php

namespace app\common\behavior;

use think\Response;
use think\Log;

/**
 * 应用调度行为
 * 用于捕获不存在的路由并重定向到首页
 */
class AppDispatch
{
    /**
     * 应用调度
     * @param $params
     * @return bool|Response
     */
    public function run(&$params)
    {
        // 获取当前模块和控制器
        $module = request()->module();
        $controller = request()->controller();
        $action = request()->action();
        
        // 获取当前请求信息
        $url = request()->url(true);
        
        // 判断是否为前台请求（排除admin和api模块）
        if ($module === 'index' || !in_array($module, ['admin', 'api'])) {
            // 检查是否为search模块（截图中显示的错误）
            if ($module === 'search' || $controller === 'search') {
                // 记录日志
                Log::record("路由不存在，重定向到首页: 模块={$module}, 控制器={$controller}, 操作={$action}, URL={$url}", 'info');
                
                // 重定向到首页
                return redirect('/');
            }
        }
        
        return true;
    }
}
