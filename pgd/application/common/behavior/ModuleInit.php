<?php

namespace app\common\behavior;

use think\Response;
use think\Log;

/**
 * 模块初始化行为
 * 用于捕获不存在的模块并重定向到首页
 */
class ModuleInit
{
    /**
     * 模块初始化
     * @param $params
     * @return bool|Response
     */
    public function run(&$params)
    {
        // 获取当前模块
        $module = request()->module();
        
        // 获取当前请求信息
        $url = request()->url(true);
        
        // 判断是否为前台请求（排除admin和api模块）
        if (!in_array($module, ['index', 'admin', 'api'])) {
            // 记录日志
            Log::record("模块不存在，重定向到首页: 模块={$module}, URL={$url}", 'info');
            
            // 重定向到首页
            return redirect('/');
        }
        
        return true;
    }
}
