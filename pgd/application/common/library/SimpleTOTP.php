<?php

namespace app\common\library;

/**
 * 简化版TOTP实现，兼容谷歌验证器
 */
class SimpleTOTP {
    private string $secret;
    private string $label = '';
    private string $issuer = '';
    private int $digits = 6;
    private int $period = 30;
    private string $algorithm = 'sha1';

    /**
     * 创建SimpleTOTP实例
     *
     * @param string $secret Base32编码的密钥
     * @return self
     */
    public static function create(string $secret): self {
        return new self($secret);
    }

    /**
     * 生成一个新的TOTP实例，使用随机生成的密钥
     *
     * @return self
     */
    public static function generate(): self {
        return new self(self::generateSecret());
    }

    /**
     * 生成一个随机的Base32编码密钥
     *
     * @param int $length 密钥长度，默认为16个字符
     * @return string Base32编码的密钥
     */
    public static function generateSecret(int $length = 16): string {
        // Base32字符集
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $secret = '';

        // 生成随机密钥
        for ($i = 0; $i < $length; $i++) {
            $secret .= $chars[random_int(0, 31)];
        }

        return $secret;
    }

    /**
     * 构造函数
     *
     * @param string $secret Base32编码的密钥
     */
    public function __construct(string $secret) {
        $this->secret = $secret;
    }

    /**
     * 设置标签（用户标识）
     *
     * @param string $label
     * @return self
     */
    public function setLabel(string $label): self {
        $this->label = $label;
        return $this;
    }

    /**
     * 设置发行者
     *
     * @param string $issuer
     * @return self
     */
    public function setIssuer(string $issuer): self {
        $this->issuer = $issuer;
        return $this;
    }

    /**
     * 获取密钥
     *
     * @return string
     */
    public function getSecret(): string {
        return $this->secret;
    }

    /**
     * 生成TOTP验证码
     *
     * @return string 6位数字验证码
     */
    public function now(): string {
        return $this->at(floor(time() / $this->period));
    }

    /**
     * 在指定时间点生成验证码
     *
     * @param int $counter 时间计数器
     * @return string 6位数字验证码
     */
    public function at(int $counter): string {
        // 解码密钥
        $decodedSecret = $this->base32Decode($this->secret);

        // 检查解码是否成功
        if ($decodedSecret === false) {
            // 可以抛出异常或返回错误指示
            throw new \Exception('Invalid base32 secret.');
        }

        // 将计数器转换为二进制字符串
        $binary = pack('N*', 0, $counter);

        // 计算HMAC哈希
        $hash = hash_hmac($this->algorithm, $binary, $decodedSecret, true);

        // 提取动态截断的值
        $offset = ord($hash[strlen($hash) - 1]) & 0x0F;
        $value = (
            ((ord($hash[$offset]) & 0x7F) << 24) |
            ((ord($hash[$offset + 1]) & 0xFF) << 16) |
            ((ord($hash[$offset + 2]) & 0xFF) << 8) |
            (ord($hash[$offset + 3]) & 0xFF)
        ) % pow(10, $this->digits);

        // 格式化为指定位数数字
        return str_pad((string)$value, $this->digits, '0', STR_PAD_LEFT);
    }

    /**
     * 验证用户输入的验证码
     *
     * @param string $code 用户输入的验证码
     * @param int|null $timestamp 时间戳，默认为当前时间
     * @param int $window 时间窗口，允许前后多少个时间周期的验证码
     * @return bool 验证是否成功
     */
    public function verify(string $code, ?int $timestamp = null, int $window = 1): bool {
        $timestamp = $timestamp ?? time();

        // 检查 code 是否为纯数字且长度正确
        if (!preg_match('/^[0-9]{'.$this->digits.'}$/', $code)) {
            return false;
        }

        // 计算当前时间周期
        $currentTimeSlice = floor($timestamp / $this->period);

        // 检查当前时间周期及前后window个时间周期的验证码
        for ($i = -$window; $i <= $window; $i++) {
            try {
                $calculatedCode = $this->at($currentTimeSlice + $i);
                if (hash_equals($calculatedCode, $code)) {
                    return true;
                }
            } catch (\Exception $e) {
                // 处理 base32 解码错误等情况
                // 可以记录日志或根据需要处理
                continue;
            }
        }

        return false;
    }

    /**
     * 生成谷歌验证器兼容的URI
     *
     * @return string
     */
    public function getProvisioningUri(): string {
        if (empty($this->label) || empty($this->issuer)) {
            throw new \Exception('Label and Issuer must be set before generating URI.');
        }
        $label = $this->issuer . ':' . $this->label;
        $params = [
            'secret' => $this->secret,
            'issuer' => $this->issuer,
            'algorithm' => strtoupper($this->algorithm),
            'digits' => $this->digits,
            'period' => $this->period
        ];

        $query = http_build_query($params);
        // 根据RFC3986 对label进行编码，空格编码为%20而不是+
        $labelEncoded = rawurlencode($label);
        return 'otpauth://totp/' . $labelEncoded . '?' . $query;
    }

    /**
     * Base32解码
     *
     * @param string $secret
     * @return string|false 返回解码后的字符串，失败返回false
     */
    private function base32Decode(string $secret): string|false {
        // 简单的Base32解码实现（备用）
        $secret = strtoupper($secret);
        // 移除填充字符
        $secret = rtrim($secret, '=');

        $base32chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $base32charsFlipped = array_flip(str_split($base32chars));
        $paddingCharCount = strlen($secret) % 8;
        $allowedValues = [2, 4, 5, 7, 0]; // 允许的填充后长度模8的余数
        if (!in_array($paddingCharCount, $allowedValues)) {
            return false;
        }

        $bits = '';
        $output = '';

        for ($i = 0; $i < strlen($secret); $i++) {
            $char = $secret[$i];
            if (!isset($base32charsFlipped[$char])) {
                return false; // 无效的Base32字符
            }
            $bits .= str_pad(decbin($base32charsFlipped[$char]), 5, '0', STR_PAD_LEFT);
        }

        // 根据填充情况调整 bits 长度
        $bits = substr($bits, 0, floor(strlen($bits) / 8) * 8);

        for ($i = 0; $i < strlen($bits); $i += 8) {
            $byte = substr($bits, $i, 8);
            if (strlen($byte) < 8) continue; // 避免处理不足8位的片段
            $output .= chr(bindec($byte));
        }

        return $output;
    }
}
