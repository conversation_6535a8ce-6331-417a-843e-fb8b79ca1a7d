<?php

namespace app\common\library\exception;

use Exception;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\RouteNotFoundException;
use think\exception\ClassNotFoundException;
use think\exception\TemplateNotFoundException;
use think\Response;
use think\Config;
use think\Log;
use think\Request;

/**
 * 自定义异常处理类
 *
 * 用于捕获前台路由错误并重定向到主页
 */
class Handler extends Handle
{
    /**
     * 渲染异常
     * @param Exception $e 异常对象
     * @return Response
     */
    public function render(Exception $e)
    {
        // 记录异常日志
        Log::record('捕获到异常: ' . $e->getMessage() . ' [' . get_class($e) . ']', 'error');

        // 获取当前请求信息
        $url = Request::instance()->url(true);
        $module = Request::instance()->module();
        $controller = Request::instance()->controller();
        $action = Request::instance()->action();

        Log::record("请求信息: URL={$url}, 模块={$module}, 控制器={$controller}, 操作={$action}", 'info');

        // 判断是否为前台请求（包括错误的模块名）
        if ($module === 'index' || !in_array($module, ['admin', 'api'])) {
            // 判断是否为任何类型的路由错误
            if ($e instanceof RouteNotFoundException ||
                $e instanceof HttpException ||
                $e instanceof ClassNotFoundException ||
                $e instanceof TemplateNotFoundException ||
                strpos($e->getMessage(), '模块不存在') !== false ||
                strpos($e->getMessage(), '控制器不存在') !== false) {

                // 记录详细信息
                Log::record('前台路由错误，重定向到首页: ' . $url, 'info');

                // 重定向到首页
                return redirect('/');
            }
        }

        // 其他异常交给父类处理
        return parent::render($e);
    }
}
