<?php
namespace app\common\library;

use think\Log as ThinkLog;

/**
 * 前端日志工具类
 * 
 * 对ThinkPHP的Log类进行封装，避免在多处引用think\Log命名空间
 * 提供简洁的静态方法用于记录各种级别的日志
 */
class FrontendLog
{
    /**
     * 记录一般日志信息
     * @param mixed $msg 日志信息
     */
    public static function log($msg)
    {
        ThinkLog::log($msg);
    }
    
    /**
     * 记录错误日志
     * @param mixed $msg 日志信息
     * @param \Exception $e 异常对象(可选)
     */
    public static function error($msg, $e = null)
    {
        ThinkLog::error($msg);
        
        // 如果提供了异常对象，则同时记录异常信息
        if ($e instanceof \Exception) {
            ThinkLog::error('错误详情: ' . $e->getMessage());
            ThinkLog::error('错误堆栈: ' . $e->getTraceAsString());
        }
    }
    
    /**
     * 记录信息日志
     * @param mixed $msg 日志信息
     */
    public static function info($msg)
    {
        ThinkLog::info($msg);
    }
    
    /**
     * 记录SQL日志
     * @param mixed $msg 日志信息
     */
    public static function sql($msg)
    {
        ThinkLog::sql($msg);
    }
    
    /**
     * 记录提示信息
     * @param mixed $msg 日志信息
     */
    public static function notice($msg)
    {
        ThinkLog::notice($msg);
    }
    
    /**
     * 记录警告信息
     * @param mixed $msg 日志信息
     */
    public static function warning($msg)
    {
        ThinkLog::notice($msg); // ThinkPHP默认没有warning，使用notice代替
    }
    
    /**
     * 记录警报信息
     * @param mixed $msg 日志信息
     */
    public static function alert($msg)
    {
        ThinkLog::alert($msg);
    }
    
    /**
     * 记录调试信息
     * @param mixed $msg 日志信息
     */
    public static function debug($msg)
    {
        ThinkLog::debug($msg);
    }
    
    /**
     * 记录异常信息(便捷方法)
     * @param \Exception $e 异常对象
     * @param string $prefix 前缀信息
     */
    public static function exception(\Exception $e, $prefix = '异常捕获')
    {
        ThinkLog::error($prefix . ': ' . $e->getMessage());
        ThinkLog::error('文件位置: ' . $e->getFile() . ' (行: ' . $e->getLine() . ')');
        ThinkLog::error('异常堆栈: ' . $e->getTraceAsString());
    }
} 