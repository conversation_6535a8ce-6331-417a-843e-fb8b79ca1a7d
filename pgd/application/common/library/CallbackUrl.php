<?php

namespace app\common\library;

/**
 * 回调URL工具类
 * 用于生成各种API回调URL
 */
class CallbackUrl
{
    /**
     * 获取支付回调URL
     * 格式: https://api.dev.imgxxx.net/payment
     * 其中 dev.imagxxx.net 是主机名，https 是协议，api 是端点特定前缀
     *
     * @param \think\Request $request 请求对象
     * @param string $endpoint 端点路径，默认为 '/payment'
     * @return string 回调URL
     */
    public static function getCallbackUrl($request, $endpoint = '/payment')
    {
        // 获取当前请求的主机名和协议
        $host = $request->host();
        $scheme = $request->scheme();

        // 将主机名的第一部分替换为 'api'
        $parts = explode('.', $host);
        if (count($parts) > 2) {
            // 如果主机名包含多个部分 (e.g., www.example.com, sub.example.co.uk)
            $parts[0] = 'api';
            $new_host = implode('.', $parts);
        } else {
            // 如果主机名只有一个部分 (e.g., localhost)
            // 或者根据需要决定如何处理这种情况，这里我们简单地加上 api. 前缀
            $new_host = 'api.' . $host;
        }

        // 构建回调URL
        return $scheme . '://' . $new_host . $endpoint;
    }
}
