<?php

namespace app\admin\library;

use think\Db;
use app\admin\library\Auth;

/**
 * SQL查询过滤工具类
 * 用于根据用户角色过滤SQL查询
 */
class SqlFilter
{
    /**
     * 获取当前用户的角色组ID
     * @return int|null 角色组ID，如果不是渠道或业务员则返回null
     */
    public static function getCurrentRoleGroupId()
    {
        $auth = Auth::instance();
        $groupIds = $auth->getGroupIds();

        // 检查用户是否属于渠道(id=4)或业务员(id=6)角色组
        if (in_array(4, $groupIds)) {
            return 4; // 渠道角色
        } elseif (in_array(6, $groupIds)) {
            return 6; // 业务员角色
        }

        return null; // 其他角色
    }

    /**
     * 获取当前用户关联的渠道ID
     * @return int|null 渠道ID，如果不是渠道角色则返回null
     */
    public static function getCurrentChannelId()
    {
        $auth = Auth::instance();
        $groupIds = $auth->getGroupIds();

        // 如果不是渠道角色，返回null
        if (!in_array(4, $groupIds)) {
            return null;
        }

        // 查询当前管理员关联的渠道ID
        $adminId = $auth->id;
        $channel = Db::table('channels')
            ->where('admin_id', $adminId)
            ->field('id')
            ->find();

        return $channel ? $channel['id'] : null;
    }

    /**
     * 获取当前用户关联的业务员ID
     * @return int|null 业务员ID，如果不是业务员角色则返回null
     */
    public static function getCurrentAgentId()
    {
        $auth = Auth::instance();
        $groupIds = $auth->getGroupIds();

        // 如果不是业务员角色，返回null
        if (!in_array(6, $groupIds)) {
            return null;
        }

        // 查询当前管理员关联的业务员ID
        $adminId = $auth->id;
        $agent = Db::table('agents')
            ->where('admin_id', $adminId)
            ->field('id')
            ->find();

        return $agent ? $agent['id'] : null;
    }

    /**
     * 获取渠道下的所有业务员ID
     * @param int $channelId 渠道ID
     * @return array 业务员ID数组
     */
    public static function getAgentIdsByChannelId($channelId)
    {
        if (!$channelId) {
            return [];
        }

        return Db::table('agents')
            ->where('channel_id', $channelId)
            ->column('id');
    }

    /**
     * 获取SQL查询的WHERE子句
     * @param string $table 表名，例如'players'或'withdraw_orders'
     * @param string $prefix WHERE子句前缀，例如'WHERE'或'AND'
     * @return string 构建好的WHERE子句
     */
    public static function getWhereClause($table, $prefix = 'WHERE')
    {
        $roleGroupId = self::getCurrentRoleGroupId();

        // 如果不是特殊角色，不需要过滤
        if (!$roleGroupId) {
            return '';
        }

        $whereClause = [];

        if ($roleGroupId == 4) { // 渠道角色
            $channelId = self::getCurrentChannelId();
            if ($channelId) {
                // 根据表名决定使用哪个字段进行过滤
                if ($table == 'players') {
                    // 对于players表，直接使用channel_id字段
                    $whereClause[] = "channel_id = $channelId";
                } else {
                    // 对于其他表，使用agent_id字段
                    // 获取该渠道下的所有业务员ID
                    $agentIds = self::getAgentIdsByChannelId($channelId);

                    if (!empty($agentIds)) {
                        $agentIdsStr = implode(',', $agentIds);
                        $whereClause[] = "agent_id IN ($agentIdsStr)";
                    } else {
                        // 如果没有业务员，确保不返回任何数据
                        $whereClause[] = "agent_id = -1";
                    }
                }
            }
        } elseif ($roleGroupId == 6) { // 业务员角色
            $agentId = self::getCurrentAgentId();
            if ($agentId) {
                $whereClause[] = "agent_id = $agentId";
            }
        }

        // 如果没有条件，返回空字符串
        if (empty($whereClause)) {
            return '';
        }

        // 构建WHERE子句
        return " $prefix " . implode(' AND ', $whereClause);
    }


}
