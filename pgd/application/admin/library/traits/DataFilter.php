<?php

namespace app\admin\library\traits;

use think\Db;
use think\Session;
use app\admin\library\Auth;

/**
 * 数据过滤特性
 * 用于根据用户角色过滤数据
 */
trait DataFilter
{
    /**
     * 获取当前用户的角色组ID
     * @return int|null 角色组ID，如果不是渠道或业务员则返回null
     */
    protected function getCurrentRoleGroupId()
    {
        $auth = Auth::instance();
        $groupIds = $auth->getGroupIds();

        // 检查用户是否属于渠道(id=4)或业务员(id=6)角色组
        if (in_array(4, $groupIds)) {
            return 4; // 渠道角色
        } elseif (in_array(6, $groupIds)) {
            return 6; // 业务员角色
        }

        return null; // 其他角色
    }

    /**
     * 获取当前用户关联的渠道ID
     * @return int|null 渠道ID，如果不是渠道角色则返回null
     */
    protected function getCurrentChannelId()
    {
        $auth = Auth::instance();
        $groupIds = $auth->getGroupIds();

        // 如果不是渠道角色，返回null
        if (!in_array(4, $groupIds)) {
            return null;
        }

        // 查询当前管理员关联的渠道ID
        $adminId = $auth->id;
        $channel = Db::table('channels')
            ->where('admin_id', $adminId)
            ->field('id')
            ->find();

        return $channel ? $channel['id'] : null;
    }

    /**
     * 获取当前用户关联的业务员ID
     * @return int|null 业务员ID，如果不是业务员角色则返回null
     */
    protected function getCurrentAgentId()
    {
        $auth = Auth::instance();
        $groupIds = $auth->getGroupIds();

        // 如果不是业务员角色，返回null
        if (!in_array(6, $groupIds)) {
            return null;
        }

        // 查询当前管理员关联的业务员ID
        $adminId = $auth->id;
        $agent = Db::table('agents')
            ->where('admin_id', $adminId)
            ->field('id')
            ->find();

        return $agent ? $agent['id'] : null;
    }

    /**
     * 应用数据过滤条件到查询构造器
     * @param \think\db\Query $query 查询构造器
     * @param string $tableAlias 表别名
     * @return \think\db\Query 应用过滤条件后的查询构造器
     */
    protected function applyDataFilter($query, $tableAlias = '')
    {
        $roleGroupId = $this->getCurrentRoleGroupId();

        // 如果不是特殊角色，不需要过滤
        if (!$roleGroupId) {
            return $query;
        }

        // 表别名处理
        $prefix = $tableAlias ? $tableAlias . '.' : '';

        // 根据角色类型应用不同的过滤条件
        if ($roleGroupId == 4) { // 渠道角色
            $channelId = $this->getCurrentChannelId();
            if ($channelId) {
                // 检查表是否有channel_id字段
                if ($this->hasField($query, $tableAlias, 'channel_id')) {
                    $query->where($prefix . 'channel_id', $channelId);
                }
                // 如果没有channel_id字段但有agent_id字段，通过关联查询过滤
                elseif ($this->hasField($query, $tableAlias, 'agent_id')) {
                    // 查询该渠道下的所有业务员ID
                    $agentIds = Db::table('agents')
                        ->where('channel_id', $channelId)
                        ->column('id');

                    if (!empty($agentIds)) {
                        $query->where($prefix . 'agent_id', 'in', $agentIds);
                    } else {
                        // 如果没有业务员，确保不返回任何数据
                        $query->where($prefix . 'agent_id', -1);
                    }
                }
            }
        } elseif ($roleGroupId == 6) { // 业务员角色
            $agentId = $this->getCurrentAgentId();
            if ($agentId && $this->hasField($query, $tableAlias, 'agent_id')) {
                $query->where($prefix . 'agent_id', $agentId);
            }
        }

        return $query;
    }

    /**
     * 检查表是否有指定字段
     * @param \think\db\Query $query 查询构造器
     * @param string $tableAlias 表别名
     * @param string $field 字段名
     * @return bool 是否存在该字段
     */
    protected function hasField($query, $tableAlias, $field)
    {
        // 获取表名
        $tableName = $query->getTable();
        if ($tableAlias) {
            // 如果有别名，需要从别名中提取真实表名
            $tables = $query->getOptions('table');
            // 确保 $tables 是数组再进行遍历
            if (is_array($tables)) {
                foreach ($tables as $table) {
                    if (is_array($table) && isset($table[1]) && $table[1] === $tableAlias) {
                        $tableName = $table[0];
                        break;
                    }
                }
            }
        }

        // 检查字段是否存在
        try {
            $fields = Db::getTableFields($tableName);
            return in_array($field, $fields);
        } catch (\Exception $e) {
            // 如果出错，假设字段存在，让查询继续执行
            return true;
        }
    }
}
