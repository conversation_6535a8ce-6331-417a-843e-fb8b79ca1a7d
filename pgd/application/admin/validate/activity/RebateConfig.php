<?php

namespace app\admin\validate\activity;

use think\Validate;

class RebateConfig extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'activity_name' => 'require',
        'channel_id' => 'require|integer|egt:0',
        'level1_first_deposit_rate' => 'require|float|egt:0|elt:100',
        'level1_first_deposit_max' => 'require|float|egt:0',
        'level2_first_deposit_rate' => 'require|float|egt:0|elt:100',
        'level2_first_deposit_max' => 'require|float|egt:0',
        'min_deposit_amount' => 'require|float|egt:0',
        'start_date' => 'require|date',
        'end_date' => 'require|date|egt:start_date',
        'is_active' => 'require|in:0,1'
    ];

    /**
     * 提示消息
     */
    protected $message = [
        'activity_name.require' => '活动名称不能为空',
        'channel_id.require' => '渠道不能为空',
        'channel_id.integer' => '渠道必须为整数',
        'channel_id.egt' => '渠道ID必须大于等于0',
        'level1_first_deposit_rate.require' => '一级用户首充返利率不能为空',
        'level1_first_deposit_rate.float' => '一级用户首充返利率必须为数字',
        'level1_first_deposit_rate.egt' => '一级用户首充返利率必须大于等于0',
        'level1_first_deposit_rate.elt' => '一级用户首充返利率必须小于等于100',
        'level1_first_deposit_max.require' => '一级用户首充返现最大金额不能为空',
        'level1_first_deposit_max.float' => '一级用户首充返现最大金额必须为数字',
        'level1_first_deposit_max.egt' => '一级用户首充返现最大金额必须大于等于0',
        'level2_first_deposit_rate.require' => '二级用户首充返利率不能为空',
        'level2_first_deposit_rate.float' => '二级用户首充返利率必须为数字',
        'level2_first_deposit_rate.egt' => '二级用户首充返利率必须大于等于0',
        'level2_first_deposit_rate.elt' => '二级用户首充返利率必须小于等于100',
        'level2_first_deposit_max.require' => '二级用户首充返现最大金额不能为空',
        'level2_first_deposit_max.float' => '二级用户首充返现最大金额必须为数字',
        'level2_first_deposit_max.egt' => '二级用户首充返现最大金额必须大于等于0',
        'min_deposit_amount.require' => '最低充值金额不能为空',
        'min_deposit_amount.float' => '最低充值金额必须为数字',
        'min_deposit_amount.egt' => '最低充值金额必须大于等于0',
        'start_date.require' => '开始日期不能为空',
        'start_date.date' => '开始日期格式不正确',
        'end_date.require' => '结束日期不能为空',
        'end_date.date' => '结束日期格式不正确',
        'end_date.egt' => '结束日期必须大于等于开始日期',
        'is_active.require' => '状态不能为空',
        'is_active.in' => '状态必须为0或1'
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => ['activity_name', 'channel_id', 'level1_first_deposit_rate', 'level1_first_deposit_max', 'level2_first_deposit_rate', 'level2_first_deposit_max', 'min_deposit_amount', 'start_date', 'end_date', 'is_active'],
        'edit' => ['activity_name', 'channel_id', 'level1_first_deposit_rate', 'level1_first_deposit_max', 'level2_first_deposit_rate', 'level2_first_deposit_max', 'min_deposit_amount', 'start_date', 'end_date', 'is_active'],
    ];
} 