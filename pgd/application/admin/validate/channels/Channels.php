<?php

namespace app\admin\validate\channels;

use think\Validate;

class Channels extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'name' => 'require|regex:/^[a-zA-Z0-9]+$/',
    ];
    /**
     * 提示消息
     */
    protected $message = [
        'name.require' => '渠道名称不能为空',
        'name.regex' => '渠道名称只能使用英文和数字组合',
    ];
    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => ['name'],
        'edit' => ['name'],
    ];
    
}
