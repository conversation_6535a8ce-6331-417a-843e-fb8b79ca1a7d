<?php

namespace app\admin\service;

use think\Log;

/**
 * 支付API服务类
 * 用于处理与支付网关的交互
 */
class PaymentApiService
{
    /**
     * 查询交易订单
     *
     * @param string $merchantOrderNo 商户订单号
     * @return array 查询结果
     */
    public static function queryOrder($merchantOrderNo)
    {
        // 直接从.env文件中获取支付网关信息
        $merchantId = \think\Env::get('payment.merchant_id');
        $secretKey = \think\Env::get('payment.secret_key');
        $gatewayUrl = \think\Env::get('payment.gateway_url');

        if (empty($merchantId) || empty($secretKey) || empty($gatewayUrl)) {
            Log::error('支付网关配置缺失: merchantId=' . $merchantId . ', secretKey=' . (empty($secretKey) ? '未设置' : '已设置') . ', gatewayUrl=' . $gatewayUrl);
            return ['success' => false, 'message' => '支付网关配置缺失，请检查.env文件中的payment配置'];
        }

        // 准备请求参数
        $params = [
            'merchantId' => $merchantId,
            'merchantOrderNo' => $merchantOrderNo
        ];

        // 生成签名
        $params['sign'] = self::generateSign($params, $secretKey);

        try {
            // 发送请求到支付网关
            $apiUrl = rtrim($gatewayUrl, '/') . '/api/open/merchant/trade/query';
            $response = self::sendRequest($apiUrl, $params);

            // 解析响应
            $responseData = json_decode($response, true);

            if (!$responseData) {
                Log::error('支付网关响应解析失败: ' . $response);
                return ['success' => false, 'message' => '支付网关响应异常，请稍后再试'];
            }

            if (isset($responseData['success']) && $responseData['success'] === true) {
                return ['success' => true, 'data' => $responseData['data'] ?? []];
            } else {
                Log::error('支付网关返回错误: ' . ($responseData['message'] ?? '未知错误') .
                         ', errorCode=' . ($responseData['errorCode'] ?? ''));
                return [
                    'success' => false,
                    'message' => '查询失败: ' . ($responseData['message'] ?? '未知错误'),
                    'errorCode' => $responseData['errorCode'] ?? ''
                ];
            }
        } catch (\Exception $e) {
            Log::error('请求支付网关异常: ' . $e->getMessage());
            return ['success' => false, 'message' => '支付系统异常，请稍后再试'];
        }
    }

    /**
     * 查询提现订单状态
     *
     * @param string $merchantOrderNo 商户订单号
     * @return array 查询结果
     */
    public static function queryWithdrawOrder($merchantOrderNo)
    {
        // 直接从.env文件中获取支付网关信息
        $merchantId = \think\Env::get('payment.merchant_id');
        $secretKey = \think\Env::get('payment.secret_key');
        $gatewayUrl = \think\Env::get('payment.gateway_url');

        if (empty($merchantId) || empty($secretKey) || empty($gatewayUrl)) {
            Log::error('支付网关配置缺失: merchantId=' . $merchantId . ', secretKey=' . (empty($secretKey) ? '未设置' : '已设置') . ', gatewayUrl=' . $gatewayUrl);
            return ['success' => false, 'message' => '支付网关配置缺失，请检查.env文件中的payment配置'];
        }

        // 准备请求参数
        $params = [
            'merchantId' => $merchantId,
            'merchantOrderNo' => $merchantOrderNo
        ];

        // 生成签名
        $params['sign'] = self::generateSign($params, $secretKey);

        try {
            // 发送请求到支付网关
            $apiUrl = rtrim($gatewayUrl, '/') . '/api/open/merchant/payment/query';
            $response = self::sendRequest($apiUrl, $params);

            // 解析响应
            $responseData = json_decode($response, true);

            if (!$responseData) {
                Log::error('提现查询接口响应解析失败: ' . $response);
                return ['success' => false, 'message' => '提现查询接口响应异常，请稍后再试'];
            }

            // 记录完整响应用于调试
            Log::info('提现查询接口响应: ' . $response);

            if (isset($responseData['success']) && $responseData['success'] === true) {
                return [
                    'success' => true,
                    'data' => $responseData['data'] ?? [],
                    'message' => $responseData['message'] ?? '',
                    'errorCode' => $responseData['errorCode'] ?? ''
                ];
            } else {
                Log::error('提现查询接口返回错误: ' . ($responseData['message'] ?? '未知错误') .
                         ', errorCode=' . ($responseData['errorCode'] ?? ''));
                return [
                    'success' => false,
                    'message' => '查询失败: ' . ($responseData['message'] ?? '未知错误'),
                    'errorCode' => $responseData['errorCode'] ?? '',
                    'data' => $responseData['data'] ?? []
                ];
            }
        } catch (\Exception $e) {
            Log::error('请求提现查询接口异常: ' . $e->getMessage());
            return ['success' => false, 'message' => '提现查询系统异常，请稍后再试'];
        }
    }

    /**
     * 生成支付签名
     *
     * @param array $params 请求参数
     * @param string $secretKey 密钥
     * @return string 签名
     */
    private static function generateSign($params, $secretKey)
    {
        // 1. 过滤空值参数
        $filteredParams = array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        });

        // 2. 按键名升序排序
        ksort($filteredParams);

        // 3. 拼接参数
        $signStr = '';
        foreach ($filteredParams as $key => $value) {
            $signStr .= $key . '=' . $value . '&';
        }

        // 4. 添加密钥
        $signStr .= 'secret=' . $secretKey;

        // 5. MD5加密（小写）
        return md5($signStr);
    }

    /**
     * 提现接口
     *
     * @param array $data 提现数据，包含以下字段：
     *        - merchantOrderNo: 商户订单号
     *        - amount: 金额(单位:分)
     *        - currency: 代付币种
     *        - accountType: 账户类型
     *        - bankCode: 银行编码 (可选)
     *        - branchBankNo: 支行行号 (可选)
     *        - branchBankName: 支行名称 (可选)
     *        - accountNo: 账号
     *        - accountName: 账户名
     *        - accountMobile: 账户手机号 (可选)
     *        - accountEmail: 账户邮箱 (可选)
     *        - province: 省份 (可选)
     *        - city: 城市 (可选)
     *        - cpf: 巴西CPF/CPF_CNPJ (可选)
     *        - ifsc: 印度IFSC (可选)
     *        - callback: 回调地址 (可选)
     * @return array 提现结果
     */
    public static function withdraw($data)
    {
        // 直接从.env文件中获取支付网关信息
        $merchantId = \think\Env::get('payment.merchant_id');
        $secretKey = \think\Env::get('payment.secret_key');
        $gatewayUrl = \think\Env::get('payment.gateway_url');

        if (empty($merchantId) || empty($secretKey) || empty($gatewayUrl)) {
            Log::error('支付网关配置缺失: merchantId=' . $merchantId . ', secretKey=' . (empty($secretKey) ? '未设置' : '已设置') . ', gatewayUrl=' . $gatewayUrl);
            return ['success' => false, 'message' => '支付网关配置缺失，请检查.env文件中的payment配置'];
        }

        // 检查必填参数
        $requiredFields = ['merchantOrderNo', 'amount', 'currency', 'accountType', 'accountNo', 'accountName'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                return ['success' => false, 'message' => "缺少必填参数: {$field}"];
            }
        }

        // 准备请求参数
        $params = [
            'merchantId' => $merchantId,
            'merchantOrderNo' => $data['merchantOrderNo'],
            'amount' => $data['amount'],
            'currency' => $data['currency'],
            'accountType' => $data['accountType'],
            'accountNo' => $data['accountNo'],
            'accountName' => $data['accountName'],
        ];

        // 添加可选参数
        $optionalFields = [
            'bankCode', 'branchBankNo', 'branchBankName', 'accountMobile',
            'accountEmail', 'province', 'city', 'cpf', 'ifsc', 'callback'
        ];

        foreach ($optionalFields as $field) {
            if (!empty($data[$field])) {
                $params[$field] = $data[$field];
            }
        }

        // 生成签名
        $params['sign'] = self::generateSign($params, $secretKey);

        try {
            // 发送请求到支付网关
            $apiUrl = rtrim($gatewayUrl, '/') . '/api/open/merchant/payment/create';
            $response = self::sendRequest($apiUrl, $params);
            error_log(print_r($params, true));

            // 解析响应
            $responseData = json_decode($response, true);

            if (!$responseData) {
                Log::error('提现接口响应解析失败: ' . $response);
                return ['success' => false, 'message' => '提现接口响应异常，请稍后再试'];
            }

            // 记录完整响应用于调试
            Log::info('提现接口响应: ' . $response);

            if (isset($responseData['success']) && $responseData['success'] === true) {
                // 注意：这里的success只表示API调用成功，不代表提现成功
                // 实际提现状态需要查看data.status字段
                return [
                    'success' => true,
                    'data' => $responseData['data'] ?? [],
                    'message' => $responseData['message'] ?? '',
                    'errorCode' => $responseData['errorCode'] ?? ''
                ];
            } else {
                Log::error('提现接口返回错误: ' . ($responseData['message'] ?? '未知错误') .
                         ', errorCode=' . ($responseData['errorCode'] ?? ''));
                return [
                    'success' => false,
                    'message' => '提现失败: ' . ($responseData['message'] ?? '未知错误'),
                    'errorCode' => $responseData['errorCode'] ?? '',
                    'data' => $responseData['data'] ?? []
                ];
            }
        } catch (\Exception $e) {
            Log::error('请求提现接口异常: ' . $e->getMessage());
            return ['success' => false, 'message' => '提现系统异常，请稍后再试'];
        }
    }

    /**
     * 发送请求到支付网关
     *
     * @param string $url 请求地址
     * @param array $params 请求参数
     * @return string 响应内容
     */
    private static function sendRequest($url, $params)
    {
        // 使用直接的cURL方式发送请求，避免使用Http::post可能导致的问题
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params)); // 使用http_build_query确保正确编码
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded;charset=UTF-8'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $error = curl_error($ch);

        if ($error) {
            Log::error('CURL请求失败: ' . $error);
            curl_close($ch);
            throw new \Exception('网络请求失败: ' . $error);
        }

        curl_close($ch);

        if (empty($response)) {
            throw new \Exception('网络请求失败，未收到响应');
        }

        return $response;
    }
}
