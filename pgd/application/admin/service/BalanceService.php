<?php

namespace app\admin\service;

use think\Db;
use think\Exception;

/**
 * 余额变更服务类
 *
 * 处理所有与玩家余额变更相关的业务逻辑
 */
class BalanceService
{
    /**
     * 处理玩家余额变更
     *
     * @param int $playerId 玩家ID
     * @param float $amount 变更金额
     * @param int $transactionType 交易类型
     * @param string $remark 备注
     * @param string $operatorName 操作人名称
     * @param int $operatorType 操作人类型：0-管理员 1-渠道 2-业务员
     * @return array 返回变更结果
     * @throws Exception 处理异常
     */
    public static function change($playerId, $amount, $transactionType, $remark, $operatorName = '', $operatorType = 0)
    {
        // 获取玩家信息
        $player = \app\admin\model\players\Players::get($playerId);
        if (!$player) {
            throw new Exception(__('Player not found'));
        }

        // 记录变更前的余额
        $balanceBefore = $player->balance;

        // 获取金额
        $amount = floatval($amount);

        // 判断金额正负来确定操作类型
        if ($amount > 0) { // 上分
            $adjustmentType = 1;
            $balanceAfter = $balanceBefore + $amount;
        } else if ($amount < 0) { // 下分
            $adjustmentType = 2;
            $balanceAfter = $balanceBefore + $amount; // amount已经是负数，所以是加
        } else {
            throw new Exception(__('Amount cannot be zero'));
        }

        // 获取绝对值用于记录
        $absAmount = abs($amount);

        // 确保余额不会变成负数
        if ($balanceAfter < 0) {
            throw new Exception(__('Balance cannot be negative'));
        }

        // 开始事务
        Db::startTrans();
        try {
            // 更新玩家余额
            $player->balance = $balanceAfter;

            // 根据交易类型更新相关统计字段
            self::updateStatisticsFields($player, $transactionType, $amount);

            // 如果是余额增加，更新玩家打码任务
            if ($amount > 0) {
                self::updatePlayerBettingTask($player->id, $transactionType, $amount);
            }

            // 保存玩家信息
            $player->save();

            // 记录余额变更日志
            Db::table('player_balance_logs')->insert([
                'player_id' => $player->id,
                'amount' => ($adjustmentType == 1) ? $absAmount : -$absAmount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'balance_type' => 1, // 1-账户余额
                'transaction_type' => $transactionType,
                'remark' => $remark,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 提交事务
            Db::commit();

            return [
                'player_id' => $player->id,
                'amount' => ($adjustmentType == 1) ? $absAmount : -$absAmount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter
            ];
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 根据交易类型更新玩家统计字段
     *
     * @param \app\admin\model\players\Players $player 玩家模型
     * @param int $transactionType 交易类型
     * @param float $amount 变更金额
     */
    private static function updateStatisticsFields($player, $transactionType, $amount)
    {
        // 根据交易类型更新相关统计字段
        switch ($transactionType) {
            case 6: // 充值本金
                $player->total_deposit = $player->total_deposit + $amount;
                break;

            case 9: // 提现
                if ($amount < 0) { // 提现是负数
                    $player->total_withdraw = $player->total_withdraw + abs($amount);
                }
                break;

            case 18: // 提现驳回
                // 提现驳回不需要更新统计字段，因为之前的提现操作已经被取消
                break;

            case 1: // 玩游戏
                // 游戏相关的统计字段通常由触发器处理
                break;

            // 可以根据需要添加其他交易类型的处理逻辑
        }
    }

    /**
     * 更新玩家打码任务
     *
     * @param int $playerId 玩家ID
     * @param int $transactionType 交易类型
     * @param float $amount 变更金额
     * @throws Exception 处理异常
     */
    private static function updatePlayerBettingTask($playerId, $transactionType, $amount)
    {
        // 查询交易类型对应的打码倍数
        $bettingConfig = Db::table('betting_task_config')
            ->where('type_id', $transactionType)
            ->find();

        // 如果没有找到对应的打码倍数配置，或者打码倍数为0，则不处理
        if (!$bettingConfig || $bettingConfig['betting_multiplier'] <= 0) {
            return;
        }

        // 计算需要增加的打码任务量
        $bettingMultiplier = floatval($bettingConfig['betting_multiplier']);
        $additionalBettingAmount = $amount * $bettingMultiplier;

        // 查询玩家是否已有打码任务记录
        $bettingTask = Db::table('player_betting_tasks')
            ->where('player_id', $playerId)
            ->find();

        if ($bettingTask) {
            // 更新现有记录
            Db::table('player_betting_tasks')
                ->where('player_id', $playerId)
                ->update([
                    'required_betting_amount' => Db::raw('required_betting_amount + ' . $additionalBettingAmount),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        } else {
            // 创建新记录
            Db::table('player_betting_tasks')
                ->insert([
                    'player_id' => $playerId,
                    'required_betting_amount' => $additionalBettingAmount,
                    'completed_betting_amount' => 0,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        }
    }
}
