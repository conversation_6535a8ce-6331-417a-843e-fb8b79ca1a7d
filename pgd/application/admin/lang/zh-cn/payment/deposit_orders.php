<?php

return [
    'Id'             => '订单ID',
    'Player_id'      => '玩家ID',
    'Referrer_id'    => '上级ID',
    'Agent_id'       => '业务员ID',
    'Channel_id'     => '渠道ID',
    'Channel_code'   => '通道代码',
    'Payment_channel_name' => '通道名称',
    'Amount'         => '充值金额',
    'Gift_amount'    => '赠送金额',
    'Service_fee'    => '手续费',
    'Third_order_no' => '支付方订单号',
    'Is_manual_fill' => '是否补单',
    'Payment_status' => '支付状态',
    'Remark'         => '备注',
    'Paid_at'        => '支付时间',
    'Created_at'     => '创建时间',
    'Updated_at'     => '更新日期',
    'Unpaid'         => '待支付',
    'Paid'           => '已支付',
    'Payment failed' => '支付失败',
    'Yes'            => '是',
    'No'             => '否',
    'Player_tag_name' => '玩家标签',
    'Channel_name'   => '渠道名称',
    'Agent_name'     => '业务员名称',
    'Manual fill'    => '补单',
    'Order not found' => '订单不存在',
    'Order already manually filled' => '订单已经补单过',
    'Order already paid' => '订单已经支付',
    'Manual fill successful' => '补单成功',
    'Confirm manual fill?' => '确认补单吗？',
    'Query status' => '查询状态',
    'Query payment status' => '查询支付状态',
    'Query success' => '查询成功',
    'Payment successful' => '支付成功',
    'Payment failed' => '支付失败',
    'Payment pending' => '支付处理中',
    'Unknown status' => '未知状态',
    'Merchant_order_no' => '商户订单号'
];
