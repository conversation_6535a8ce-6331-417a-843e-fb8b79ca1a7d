<?php

return [
    'Two-Factor Authentication Setup'                   => '双因素认证设置',
    'Two-factor authentication adds an extra layer of security to your account. Once enabled, you will need to enter a verification code from your authenticator app when logging in.' => '双因素认证为您的账户增加了额外的安全层。启用后，您在登录时需要输入验证器应用程序中的验证码。',
    'Download an authenticator app'                     => '下载验证器应用',
    'Download and install an authenticator app on your mobile device. We recommend Google Authenticator, Microsoft Authenticator, or Authy.' => '在您的移动设备上下载并安装验证器应用。我们推荐使用谷歌验证器、微软验证器或Authy。',
    'Scan the QR code'                                  => '扫描二维码',
    'Open your authenticator app and scan the QR code below to add your account.' => '打开您的验证器应用并扫描下方的二维码以添加您的账户。',
    'If you cannot scan the QR code, you can manually enter this secret key into your app:' => '如果您无法扫描二维码，可以手动将此密钥输入到您的应用中：',
    'Verify and enable'                                 => '验证并启用',
    'Enter the 6-digit verification code from your authenticator app to enable two-factor authentication.' => '输入验证器应用中的6位验证码以启用双因素认证。',
    'Enter the 6-digit code'                            => '输入6位验证码',
    'Enable Two-Factor Authentication'                  => '启用双因素认证',
    'Cancel'                                            => '取消',
    'Two-factor authentication is currently enabled for your account.' => '您的账户当前已启用双因素认证。',
    'Disable Two-Factor Authentication'                 => '禁用双因素认证',
    'If you want to disable two-factor authentication, please enter the verification code from your authenticator app.' => '如果您想禁用双因素认证，请输入验证器应用中的验证码。',
    'Two-factor authentication has been enabled successfully' => '双因素认证已成功启用',
    'Two-factor authentication has been disabled'       => '双因素认证已禁用',
    'Invalid verification code'                         => '无效的验证码',
    'Please scan the QR code and enter the verification code' => '请扫描二维码并输入验证码',
    'Please enter a valid 6-digit code'                 => '请输入有效的6位验证码',
    'Two-factor authentication is not enabled'          => '双因素认证未启用',
    'Invalid request'                                   => '无效的请求',
];
