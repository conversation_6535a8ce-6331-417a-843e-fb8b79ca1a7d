<?php

return [
    'Url'                                         => '链接',
    'Username'                                    => '用户名',
    'Createtime'                                  => '操作时间',
    'Click to edit'                               => '点击编辑',
    'Admin log'                                   => '操作日志',
    'Leave password blank if dont want to change' => '不修改密码请留空',
    'Please input correct email'                  => '请输入正确的Email地址',
    'Please input correct password'               => '密码长度必须在6-30位之间，不能包含空格',
    'Password must be 6 to 30 characters'         => '密码长度必须在6-30位之间，不能包含空格',
    'Email already exists'                        => '邮箱已经存在',
    // 两因素认证相关
    'Bind Authenticator'                          => '绑定验证器',
    'Verification Code'                           => '验证码',
    'Enter the 6-digit code from your authenticator app' => '输入验证器应用生成的6位验证码',
    'Scan the QR code and enter the 6-digit code from your authenticator app to enable two-factor authentication' => '扫描二维码后，输入验证器应用生成的6位验证码以启用两因素认证',
    'Two-Factor Authentication'                   => '两因素认证',
    'Enabled'                                     => '已启用',
    'Unable to generate QR code'                  => '无法生成二维码',
];
