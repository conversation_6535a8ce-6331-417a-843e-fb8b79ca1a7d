<?php

return [
    'Player not found' => '玩家不存在',
    'Please enter a valid RTP value' => '请输入有效的RTP值',
    'RTP value must be between 0 and 100' => 'RTP值必须在0到100之间',
    'Team RTP value must be between 0 and 100' => '团队RTP值必须在0到100之间',
    'RTP updated successfully' => 'RTP更新成功',
    'Team RTP updated for %d players' => '已更新%d个下级玩家的RTP值',
    'Invalid request' => '无效的请求',
    'Submit' => '提交',
    'Reset' => '重置',
    'RTP Settings' => 'RTP设置',
    'Player Information' => '玩家信息',
    'ID' => 'ID',
    'Username' => '用户名',
    'Current Personal RTP' => '当前个人RTP',
    'Current Team RTP' => '当前团队RTP',
    'Personal RTP Value' => '个人RTP值',
    'Team RTP Value' => '团队RTP值',
    'Enter a value between 0-100' => '请输入0-100之间的数值',
    'RTP Description' => 'RTP (Return To Player) 返奖率是游戏返还给玩家的比例，范围为0-100。值越高，玩家获胜的概率越大。',
    'Team RTP Description' => '团队RTP值将应用于该玩家的所有下级玩家（不包括该玩家本身）。设置后，所有下级玩家的RTP值将被更新。'
];
