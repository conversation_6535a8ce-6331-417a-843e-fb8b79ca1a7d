<?php

return [
    'Toggle all'                                                => '显示全部',
    'Condition'                                                 => '规则条件',
    'Remark'                                                    => '备注',
    'Icon'                                                      => '图标',
    'Alert'                                                     => '警告',
    'Name'                                                      => '名称',
    'Controller/Action'                                         => '控制器名/方法名',
    'Ismenu'                                                    => '菜单',
    'Menutype'                                                  => '菜单类型',
    'Addtabs'                                                   => '选项卡(默认)',
    'Dialog'                                                    => '弹窗',
    'Ajax'                                                      => 'Ajax请求',
    'Blank'                                                     => '链接',
    'Extend'                                                    => '扩展属性',
    'Search icon'                                               => '搜索图标',
    'Toggle menu visible'                                       => '点击切换菜单显示',
    'Toggle sub menu'                                           => '点击切换子菜单',
    'Menu tips'                                                 => '父级菜单无需匹配控制器和方法,子级菜单请使用控制器名',
    'Node tips'                                                 => '控制器/方法名,如果有目录请使用 目录名/控制器名/方法名',
    'Url tips'                                                  => '一般情况下留空即可,如果是外部链接或相对链接请输入',
    'The non-menu rule must have parent'                        => '非菜单规则节点必须有父级',
    'Can not change the parent to child'                        => '父级不能是它的子级',
    'Can not change the parent to self'                         => '父级不能是它自己',
    'Name only supports letters, numbers, underscore and slash' => 'URL规则只能是小写字母、数字、下划线和/组成',
];
