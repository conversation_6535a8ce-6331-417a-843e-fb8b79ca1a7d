<?php

return [
    'Id'                  => '订单ID',
    'Player ID'           => '玩家ID',
    'Referrer ID'         => '上级ID',
    'Agent ID'            => '业务员ID',
    'Channel Code'        => '通道代码',
    'Amount'              => '提现金额',
    'Service Fee'         => '手续费',
    'Balance'             => '账户余额',
    'Total Deposit'       => '总充值',
    'Total Withdraw'      => '总提现',
    'Audit Status'        => '审核状态',
    'Withdraw Account ID' => '提现账户ID',
    'Remark'              => '备注',
    'Order Remark'        => '订单备注',
    'Player Remark'       => '玩家备注',
    'Operator Name'       => '操作人',
    'Operator Type'       => '操作人类型',
    'Third Order No'      => '支付方订单号',
    'Created At'          => '提交时间',
    'Processed At'        => '处理时间',
    'Paid At'             => '支付时间',
    'Approve'             => '审核通过',
    'Reject'              => '审核拒绝',
    'Reject Reason'       => '拒绝原因',
    'Player Tag Name'     => '玩家标签',
    'Channel Name'        => '渠道名称',
    'Channel ID'          => '渠道ID',
    'Agent Name'          => '业务员名称',
    'Are you sure you want to approve these %s items?' => '确定要审核通过这 %s 条记录吗？',
    'Please select at least one row' => '请至少选择一行',
    'Success approve %d orders' => '成功审核通过 %d 个订单',
    'Success reject and refund %d orders' => '成功拒绝并退回 %d 个订单的金币',
    'Success mark %d orders as virtual payment' => '成功将 %d 个订单标记为虚拟支付',
    'Success reject and confiscate %d orders' => '成功拒绝并没收 %d 个订单的金币',
    'Confirm approve this order?' => '确认通过该订单？',
    'Confirm mark this order as virtual payment?' => '确认将该订单标记为虚拟支付？',
    'Confirm reject this order and refund coins?' => '确认拒绝该订单并退回金币？',
    'Confirm reject this order and confiscate coins?' => '确认拒绝该订单并没收金币？',
    'Pass' => '通过',
    'Virtual' => '虚拟',
    'Reject' => '拒绝',
    'Confiscate' => '没收',
    'Virtual payment' => '虚拟支付',
    'Batch virtual payment' => '一键虚拟支付',
    'Reject and refund coins' => '拒绝并退回金币',
    'Reject and confiscate coins' => '拒绝并没收金币',
    'Status pending' => '待加入工单',
    'Status waiting review' => '已加入工单,待审核',
    'Status approved' => '已审核',
    'Status processing' => '三方处理中',
    'Status completed' => '订单完成',
    'Status rejected refund' => '拒绝并退回金币',
    'Status failed refund' => '处理失败并退回金币',
    'Status rejected confiscate' => '拒绝并没收金币',
    'Status virtual' => '虚拟支付',
    'Status unknown' => '未知状态',
    'Status failed' => '处理失败',
    'Query Status' => '查询状态',
    'Query payment status' => '查询支付方状态',
    'Order status' => '订单状态',
    'Message' => '消息',
    'Query successful' => '查询成功',
    'Order not found' => '订单不存在',
    'No third-party order number found' => '未找到第三方订单号'
];
