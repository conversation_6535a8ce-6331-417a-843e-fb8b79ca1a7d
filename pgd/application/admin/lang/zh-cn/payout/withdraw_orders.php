<?php

return [
    'Id'                  => '订单ID',
    'Player ID'           => '玩家ID',
    'Referrer ID'         => '上级ID',
    'Agent ID'            => '业务员ID',
    'Channel Code'        => '通道代码',
    'Amount'              => '提现金额',
    'Service Fee'         => '手续费',
    'Audit Status'        => '审核状态',
    'Withdraw Account ID' => '提现账户ID',
    'Remark'              => '备注',
    'Order remark'        => '订单备注',
    'Player remark'       => '玩家备注',
    'Operator Name'       => '操作人',
    'Operator Type'       => '操作人类型',
    'Third Order No'      => '支付方订单号',
    'Created At'          => '提交时间',
    'Processed At'        => '处理时间',
    'Paid At'             => '支付时间',
    'Player Tag Name'     => '玩家标签',
    'Channel Name'        => '渠道名称',
    'Channel ID'          => '渠道ID',
    'Agent Name'          => '业务员名称',
    'Query status'        => '查询状态',
    'Query payment status' => '查询支付方状态',
    'Query success, payment status: pending' => '查询成功，支付方状态：待处理',
    'Get proof URL success' => '获取凭证URL成功',
    'Status pending' => '待加入工单',
    'Status waiting review' => '已加入工单,待审核',
    'Status approved' => '已审核',
    'Status processing' => '三方处理中',
    'Status completed' => '订单完成',
    'Status rejected refund' => '拒绝并退回金币',
    'Status failed refund' => '处理失败并退回金币',
    'Status rejected confiscate' => '拒绝并没收金币',
    'Status virtual' => '虚拟支付',
    'View Proof' => '查看凭证',
    'View Payment Proof' => '查看支付凭证',
    'Query failed' => '查询失败',
    'Query success' => '查询成功',
    'Status' => '状态',
    'Order number not found' => '订单号未找到',
    'Total Deposit' => '总充值',
    'Total Withdraw' => '总提现'
];
