<?php

namespace app\admin\model\mine;

use think\Model;
use think\Db;

/**
 * 每月财报模型
 */
class MonthBank extends Model
{
    // 表名
    protected $name = 'daily_statistics';

    // 数据库表名，不带前缀
    protected $table = 'daily_statistics';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    /**
     * 根据角色类型获取每月财报数据
     *
     * @param string $month 月份，格式为 YYYY-MM
     * @param int $userId 用户ID (fa_admin.id)
     * @param int $groupId 角色组ID
     * @return array 财报数据
     */
    public function getReportByRole($month, $userId, $groupId)
    {
        // 根据角色组ID选择不同的处理方法
        if ($groupId == 4) { // 渠道角色
            // 根据 fa_admin.id 获取对应的 channels.id
            $channelId = Db::table('channels')
                ->where('admin_id', $userId)
                ->value('id');

            if (!$channelId) {
                error_log("未找到管理员ID {$userId} 对应的渠道ID");
                return $this->getEmptyReport();
            }

            return $this->getChannelMonthlyReport($month, $channelId);
        } else { // 业务员角色或其他角色
            // 根据 fa_admin.id 获取对应的 agents.id
            $agentId = Db::table('agents')
                ->where('admin_id', $userId)
                ->value('id');

            if (!$agentId) {
                error_log("未找到管理员ID {$userId} 对应的业务员ID");
                return $this->getEmptyReport();
            }

            return $this->getAgentMonthlyReport($month, $agentId);
        }
    }

    /**
     * 获取业务员每月财报数据
     *
     * @param string $month 月份，格式为 YYYY-MM
     * @param int $agentId 业务员ID
     * @return array 财报数据
     */
    public function getAgentMonthlyReport($month, $agentId)
    {
        try {
            // 计算月份的起止时间
            $startDate = $month . '-01';
            $endDate = date('Y-m-d', strtotime($month . '-01 +1 month -1 day'));

            // 从daily_statistics表获取汇总数据
            $result = $this->where('agent_id', $agentId)
                ->where('stat_date', 'between', [$startDate, $endDate])
                ->field([
                    'SUM(total_deposit) as total_deposit',
                    'SUM(total_withdraw) as total_withdraw',
                    'SUM(total_game_profit) as total_game_profit'
                ])
                ->find();

            // 获取业务员的费率信息
            $agentInfo = Db::table('agents')
                ->alias('a')
                ->join(['channels' => 'c'], 'a.channel_id = c.id', 'LEFT')
                ->where('a.id', $agentId)
                ->field([
                    'c.deposit_fee_rate',
                    'c.withdraw_fee_rate',
                    'c.api_fee_rate'
                ])
                ->find();

            // 如果没有找到业务员信息，使用默认费率
            $depositFeeRate = $agentInfo['deposit_fee_rate'] ?? 0;
            $withdrawFeeRate = $agentInfo['withdraw_fee_rate'] ?? 0;
            $apiFeeRate = $agentInfo['api_fee_rate'] ?? 0;

            // 计算各项费用
            $totalDeposit = $result['total_deposit'] ?? 0;
            $totalWithdraw = $result['total_withdraw'] ?? 0;
            $totalGameProfit = $result['total_game_profit'] ?? 0;

            $depositFee = $totalDeposit * ($depositFeeRate / 100);
            $withdrawFee = $totalWithdraw * ($withdrawFeeRate / 100);
            $apiFee = $totalGameProfit * ($apiFeeRate / 100);

            // 计算总利润
            $totalProfit = $totalDeposit - $totalWithdraw - $depositFee - $withdrawFee - $apiFee;

            // 返回结果
            return [
                'total_deposit' => $totalDeposit,
                'total_withdraw' => $totalWithdraw,
                'deposit_fee' => $depositFee,
                'withdraw_fee' => $withdrawFee,
                'api_fee' => $apiFee,
                'total_profit' => $totalProfit
            ];
        } catch (\Exception $e) {
            // 记录错误日志
            error_log('获取业务员每月财报数据失败: ' . $e->getMessage());

            return $this->getEmptyReport();
        }
    }

    /**
     * 获取渠道每月财报数据（汇总渠道下所有业务员的数据）
     *
     * @param string $month 月份，格式为 YYYY-MM
     * @param int $channelId 渠道ID
     * @return array 财报数据
     */
    public function getChannelMonthlyReport($month, $channelId)
    {
        try {
            // 计算月份的起止时间
            $startDate = $month . '-01';
            $endDate = date('Y-m-d', strtotime($month . '-01 +1 month -1 day'));

            // 获取渠道下所有业务员ID
            $agentIds = Db::table('agents')
                ->where('channel_id', $channelId)
                ->column('id');

            if (empty($agentIds)) {
                return $this->getEmptyReport();
            }

            // 从daily_statistics表获取汇总数据
            $result = $this->where('agent_id', 'in', $agentIds)
                ->where('stat_date', 'between', [$startDate, $endDate])
                ->field([
                    'SUM(total_deposit) as total_deposit',
                    'SUM(total_withdraw) as total_withdraw',
                    'SUM(total_game_profit) as total_game_profit'
                ])
                ->find();

            // 获取渠道的费率信息
            $channelInfo = Db::table('channels')
                ->where('id', $channelId)
                ->field([
                    'deposit_fee_rate',
                    'withdraw_fee_rate',
                    'api_fee_rate'
                ])
                ->find();

            // 如果没有找到渠道信息，使用默认费率
            $depositFeeRate = $channelInfo['deposit_fee_rate'] ?? 0;
            $withdrawFeeRate = $channelInfo['withdraw_fee_rate'] ?? 0;
            $apiFeeRate = $channelInfo['api_fee_rate'] ?? 0;

            // 计算各项费用
            $totalDeposit = $result['total_deposit'] ?? 0;
            $totalWithdraw = $result['total_withdraw'] ?? 0;
            $totalGameProfit = $result['total_game_profit'] ?? 0;

            $depositFee = $totalDeposit * ($depositFeeRate / 100);
            $withdrawFee = $totalWithdraw * ($withdrawFeeRate / 100);
            $apiFee = $totalGameProfit * ($apiFeeRate / 100);

            // 计算总利润
            $totalProfit = $totalDeposit - $totalWithdraw - $depositFee - $withdrawFee - $apiFee;

            // 返回结果
            return [
                'total_deposit' => $totalDeposit,
                'total_withdraw' => $totalWithdraw,
                'deposit_fee' => $depositFee,
                'withdraw_fee' => $withdrawFee,
                'api_fee' => $apiFee,
                'total_profit' => $totalProfit
            ];
        } catch (\Exception $e) {
            // 记录错误日志
            error_log('获取渠道每月财报数据失败: ' . $e->getMessage());

            return $this->getEmptyReport();
        }
    }

    /**
     * 获取空的报表数据（用于错误处理）
     *
     * @return array 空的报表数据
     */
    private function getEmptyReport()
    {
        return [
            'total_deposit' => 0,
            'total_withdraw' => 0,
            'deposit_fee' => 0,
            'withdraw_fee' => 0,
            'api_fee' => 0,
            'total_profit' => 0
        ];
    }
}
