<?php

namespace app\admin\model\service;

use think\Model;


class GmBalanceAdjustments extends Model
{

    // 表名
    protected $table = 'gm_balance_adjustments';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'adjustment_type_text',
        'status_text',
        'operator_type_text',
        'player_phone_number',
        'player_channel_name',
        'player_agent_name'
    ];

    // 调整类型获取器
    public function getAdjustmentTypeTextAttr($value, $data)
    {
        $types = [
            1 => __('Balance increase'),
            2 => __('Balance decrease')
        ];
        return isset($types[$data['adjustment_type']]) ? $types[$data['adjustment_type']] : $data['adjustment_type'];
    }

    // 状态获取器
    public function getStatusTextAttr($value, $data)
    {
        $statuses = [
            0 => __('Pending review'),
            1 => __('Approved'),
            2 => __('Rejected')
        ];
        return isset($statuses[$data['status']]) ? $statuses[$data['status']] : $data['status'];
    }

    // 操作员类型获取器
    public function getOperatorTypeTextAttr($value, $data)
    {
        $types = [
            0 => __('Administrator'),
            1 => __('Channel'),
            2 => __('Agent')
        ];
        return isset($types[$data['operator_type']]) ? $types[$data['operator_type']] : $data['operator_type'];
    }

    /**
     * 关联玩家表
     */
    public function player()
    {
        return $this->belongsTo('app\admin\model\players\Players', 'player_id', 'id');
    }

    /**
     * 获取玩家手机号
     */
    public function getPlayerPhoneNumberAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            return $player ? $player['phone_number'] : '';
        }
        return '';
    }

    /**
     * 获取玩家所属渠道名称
     */
    public function getPlayerChannelNameAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            if ($player && $player['channel_id']) {
                $channel = \app\admin\model\channels\Channels::get($player['channel_id']);
                return $channel ? $channel['name'] : '';
            }
        }
        return '';
    }

    /**
     * 获取玩家所属业务员名称
     */
    public function getPlayerAgentNameAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            if ($player && $player['agent_id']) {
                $agent = \app\admin\model\agents_config\Agents::get($player['agent_id']);
                if ($agent && $agent['admin_id']) {
                    $admin = \app\admin\model\Admin::get($agent['admin_id']);
                    return $admin ? $admin['nickname'] : '';
                }
            }
        }
        return '';
    }
}
