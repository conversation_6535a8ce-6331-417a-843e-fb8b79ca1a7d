<?php

namespace app\admin\model\service;

use think\Model;
use traits\model\SoftDelete;

class CustomerServiceConfig extends Model
{

    use SoftDelete;

    

    // 表名
    protected $table = 'customer_service_config';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'open_type_text',
        'status_text'
    ];
    

    
    public function getOpenTypeList()
    {
        return ['内部打开' => __('内部打开'), '外部打开' => __('外部打开')];
    }

    public function getStatusList()
    {
        return ['0' => __('Status 0'), '1' => __('Status 1')];
    }


    public function getOpenTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['open_type'] ?? '');
        $list = $this->getOpenTypeList();
        return $list[$value] ?? '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }




}
