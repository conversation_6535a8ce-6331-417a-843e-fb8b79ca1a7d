<?php
/*
 * @Author: ‘guanzh’ ‘<EMAIL>’
 * @Date: 2025-03-17 00:57:07
 * @LastEditors: ‘guanzh’ ‘<EMAIL>’
 * @LastEditTime: 2025-03-17 00:57:46
 * @FilePath: \pgd\application\admin\model\agents\TreasureBoxConfig.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

namespace app\admin\model\agents;

use think\Model;


class TreasureBoxConfig extends Model
{

    

    

    // 表名
    protected $table = 'treasure_box_config';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







}
