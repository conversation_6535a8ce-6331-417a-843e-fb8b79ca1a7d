<?php

namespace app\admin\model\statistic;

use think\Model;
use think\Db;

class ChannelReports extends Model
{
    // 表名
    protected $table = 'channels';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    /**
     * 获取渠道报表数据
     * 
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @param int $channelId 渠道ID
     * @return array 渠道报表数据
     */
    public function getChannelReports($startDate = null, $endDate = null, $channelId = null)
    {
        // 设置默认日期范围为最近7天
        if (!$startDate) {
            $startDate = date('Y-m-d', strtotime('-7 days'));
        }
        if (!$endDate) {
            $endDate = date('Y-m-d');
        }

        // 构建基础查询 - 获取渠道列表
        $query = Db::table('channels')
            ->alias('c')
            ->field([
                'c.id as channel_id',
                'c.name as channel_name',
                'c.deposit_fee_rate',
                'c.withdraw_fee_rate',
                'c.api_fee_rate'
            ]);

        // 如果指定了渠道ID，则添加过滤条件
        if ($channelId) {
            $query->where('c.id', $channelId);
        }

        // 执行查询获取渠道列表
        $channels = $query->select();

        // 如果没有渠道，则返回空数组
        if (empty($channels)) {
            return [];
        }

        // 获取所有渠道ID
        $channelIds = array_column($channels, 'channel_id');

        // 获取每个渠道的业务员ID列表
        $agentsByChannel = [];
        $agentsQuery = Db::table('agents')
            ->field(['id', 'channel_id'])
            ->where('channel_id', 'in', $channelIds);
        
        $agents = $agentsQuery->select();
        
        foreach ($agents as $agent) {
            if (!isset($agentsByChannel[$agent['channel_id']])) {
                $agentsByChannel[$agent['channel_id']] = [];
            }
            $agentsByChannel[$agent['channel_id']][] = $agent['id'];
        }

        // 为每个渠道计算统计数据
        foreach ($channels as &$channel) {
            $channelId = $channel['channel_id'];
            $agentIds = isset($agentsByChannel[$channelId]) ? $agentsByChannel[$channelId] : [];

            // 如果渠道没有业务员，设置默认值并继续
            if (empty($agentIds)) {
                $this->setDefaultStats($channel);
                continue;
            }

            // 获取充值和提现数据
            $depositWithdrawStats = $this->getDepositWithdrawStats($agentIds, $startDate, $endDate);
            
            // 获取游戏盈亏数据
            $gameStats = $this->getGameStats($agentIds, $startDate, $endDate);

            // 计算各项指标
            $totalDeposit = $depositWithdrawStats['total_deposit'] ?? 0;
            $totalWithdraw = $depositWithdrawStats['total_withdraw'] ?? 0;
            $depositWithdrawDiff = $totalDeposit - $totalWithdraw;
            $depositFee = $totalDeposit * ($channel['deposit_fee_rate'] / 100);
            $withdrawFee = $totalWithdraw * ($channel['withdraw_fee_rate'] / 100);
            $gameLoss = $gameStats['total_game_profit'] ?? 0;
            $apiFee = $gameLoss * ($channel['api_fee_rate'] / 100);
            $totalProfit = $depositWithdrawDiff - $depositFee - $withdrawFee - $apiFee;

            // 设置统计数据
            $channel['total_deposit'] = number_format($totalDeposit, 2);
            $channel['total_withdraw'] = number_format($totalWithdraw, 2);
            $channel['deposit_withdraw_diff'] = number_format($depositWithdrawDiff, 2);
            $channel['deposit_fee'] = number_format($depositFee, 2);
            $channel['withdraw_fee'] = number_format($withdrawFee, 2);
            $channel['game_loss'] = number_format($gameLoss, 2);
            $channel['api_fee'] = number_format($apiFee, 2);
            $channel['total_profit'] = number_format($totalProfit, 2);
        }

        return $channels;
    }

    /**
     * 获取充值和提现统计数据
     * 
     * @param array $agentIds 业务员ID列表
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array 充值和提现统计数据
     */
    private function getDepositWithdrawStats($agentIds, $startDate, $endDate)
    {
        $stats = Db::table('daily_statistics')
            ->field([
                'SUM(total_deposit) as total_deposit',
                'SUM(total_withdraw) as total_withdraw'
            ])
            ->where('agent_id', 'in', $agentIds)
            ->where('stat_date', 'BETWEEN', [$startDate, $endDate])
            ->find();

        return $stats ?: ['total_deposit' => 0, 'total_withdraw' => 0];
    }

    /**
     * 获取游戏统计数据
     * 
     * @param array $agentIds 业务员ID列表
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array 游戏统计数据
     */
    private function getGameStats($agentIds, $startDate, $endDate)
    {
        $stats = Db::table('daily_statistics')
            ->field([
                'SUM(total_game_profit) as total_game_profit'
            ])
            ->where('agent_id', 'in', $agentIds)
            ->where('stat_date', 'BETWEEN', [$startDate, $endDate])
            ->find();

        return $stats ?: ['total_game_profit' => 0];
    }

    /**
     * 设置默认统计数据
     * 
     * @param array &$channel 渠道数据
     */
    private function setDefaultStats(&$channel)
    {
        $channel['total_deposit'] = '0.00';
        $channel['total_withdraw'] = '0.00';
        $channel['deposit_withdraw_diff'] = '0.00';
        $channel['deposit_fee'] = '0.00';
        $channel['withdraw_fee'] = '0.00';
        $channel['game_loss'] = '0.00';
        $channel['api_fee'] = '0.00';
        $channel['total_profit'] = '0.00';
    }
}
