<?php

namespace app\admin\model\statistic;

use think\Model;

/**
 * 业务员月度统计模型
 */
class AgentsMonth extends Model
{
    // 表名
    protected $name = 'daily_statistics';

    // 数据库表名，不带前缀
    protected $table = 'daily_statistics';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'month'
    ];

    /**
     * 获取月份
     */
    public function getMonthAttr($value, $data)
    {
        return date('Y-m', strtotime($data['stat_date']));
    }
}
