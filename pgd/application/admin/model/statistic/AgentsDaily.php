<?php

namespace app\admin\model\statistic;

use think\Model;

/**
 * 业务员每日统计模型
 */
class AgentsDaily extends Model
{
    // 表名
    protected $name = 'daily_statistics';

    // 数据库表名，不带前缀
    protected $table = 'daily_statistics';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'new_active_users_text',
        'deposit_info_text',
        'withdraw_info_text',
        'first_repeat_deposit_text',
        'manual_adjust_text',
        'game_info_text',
        'deposit_withdraw_diff',
        'actual_deposit_withdraw_diff'
    ];



    /**
     * 获取新增/活跃用户文本
     */
    public function getNewActiveUsersTextAttr($value, $data)
    {
        return ($data['new_players'] ?? 0) . '/' . ($data['active_players'] ?? 0);
    }

    /**
     * 获取充值信息文本
     */
    public function getDepositInfoTextAttr($value, $data)
    {
        $depositUsers = ($data['first_deposit_players'] ?? 0) + ($data['repeat_deposit_players'] ?? 0);
        return number_format($data['total_deposit'] ?? 0, 2) . '/' . $depositUsers . '/' . ($data['total_deposit_count'] ?? 0);
    }

    /**
     * 获取提现信息文本
     */
    public function getWithdrawInfoTextAttr($value, $data)
    {
        return number_format($data['total_withdraw'] ?? 0, 2) . '/' . ($data['withdraw_players'] ?? 0) . '/' . ($data['total_withdraw_count'] ?? 0);
    }

    /**
     * 获取首充/复充人数文本
     */
    public function getFirstRepeatDepositTextAttr($value, $data)
    {
        return ($data['first_deposit_players'] ?? 0) . '/' . ($data['repeat_deposit_players'] ?? 0);
    }

    /**
     * 获取手动上分/下分文本
     */
    public function getManualAdjustTextAttr($value, $data)
    {
        return number_format($data['manual_increase'] ?? 0, 2) . '/' . number_format($data['manual_decrease'] ?? 0, 2);
    }

    /**
     * 获取游戏信息文本
     */
    public function getGameInfoTextAttr($value, $data)
    {
        return number_format($data['total_bet'] ?? 0, 2) . '/' .
               number_format($data['total_win'] ?? 0, 2) . '/' .
               number_format($data['total_game_profit'] ?? 0, 2);
    }

    /**
     * 获取充提差
     */
    public function getDepositWithdrawDiffAttr($value, $data)
    {
        $deposit = floatval($data['total_deposit'] ?? 0);
        $withdraw = floatval($data['total_withdraw'] ?? 0);
        return $deposit - $withdraw;
    }

    /**
     * 获取实际充提差
     */
    public function getActualDepositWithdrawDiffAttr($value, $data)
    {
        $deposit = floatval($data['total_deposit'] ?? 0);
        $withdraw = floatval($data['total_withdraw'] ?? 0);
        return $deposit * 0.97 - $withdraw;
    }
}
