<?php

namespace app\admin\model\statistic;

use think\Model;
use think\Db;

class GameRecords extends Model
{
    // 表名
    protected $table = 'game_sessions';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'kill_rate',
        'return_rate'
    ];

    /**
     * 获取杀率
     */
    public function getKillRateAttr($value, $data)
    {
        if (isset($data['bet_amount']) && $data['bet_amount'] > 0) {
            $killRate = ($data['platform_profit'] / $data['bet_amount']) * 100;
            return number_format($killRate, 2);
        }
        return '0.00';
    }

    /**
     * 获取回报率
     */
    public function getReturnRateAttr($value, $data)
    {
        if (isset($data['kill_rate'])) {
            $returnRate = 100 - floatval($data['kill_rate']);
            return number_format($returnRate, 2);
        }
        return '100.00';
    }

    /**
     * 获取按日期和供应商分组的统计数据
     */
    public static function getStatsByDateAndProvider($startDate = null, $endDate = null, $providerId = null)
    {
        // 设置默认日期范围为最近7天
        if (!$startDate) {
            $startDate = date('Y-m-d', strtotime('-7 days'));
        }
        if (!$endDate) {
            $endDate = date('Y-m-d');
        }

        // 构建基础查询
        $query = Db::table('game_sessions')
            ->alias('gs')
            ->join(['game_providers'=>'gp'], 'gs.provider_id = gp.id', 'LEFT')
            ->field([
                'DATE(gs.closed_at) as stat_date',
                'gs.provider_id',
                'gp.name as provider_name',
                'SUM(gs.bet_amounts) as bet_amount',
                'SUM(-gs.transfer_amounts) as platform_profit',
                'COUNT(DISTINCT gs.player_id) as player_count',
                'COUNT(gs.session_id) as game_count'
            ])
            ->where('gs.closed_at', 'BETWEEN', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->where('gs.closed_at', 'not null') // 只统计已关闭的会话
            ->group('stat_date, gs.provider_id')
            ->order('stat_date DESC, gs.provider_id ASC');

        // 如果指定了供应商ID，则添加过滤条件
        if ($providerId) {
            $query->where('gs.provider_id', $providerId);
        }

        // 执行查询
        $result = $query->select();

        // 计算杀率和回报率
        foreach ($result as &$item) {
            if ($item['bet_amount'] > 0) {
                $item['kill_rate'] = ($item['platform_profit'] / $item['bet_amount']) * 100;
                $item['return_rate'] = 100 - $item['kill_rate'];

                // 格式化数值
                $item['kill_rate'] = number_format($item['kill_rate'], 2);
                $item['return_rate'] = number_format($item['return_rate'], 2);
            } else {
                $item['kill_rate'] = '0.00';
                $item['return_rate'] = '100.00';
            }
        }

        return $result;
    }

    /**
     * 获取指定时间范围内的充值合计和玩家数
     */
    public static function getDepositStats($startDate = null, $endDate = null, $providerId = null)
    {
        // 设置默认日期范围为最近7天
        if (!$startDate) {
            $startDate = date('Y-m-d', strtotime('-7 days'));
        }
        if (!$endDate) {
            $endDate = date('Y-m-d');
        }

        // 获取时间范围内的游戏会话涉及的玩家ID
        $playerQuery = Db::table('game_sessions')
            ->alias('gs')
            ->where('gs.closed_at', 'BETWEEN', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->where('gs.closed_at', 'not null');

        // 如果指定了供应商ID，则添加过滤条件
        if ($providerId) {
            $playerQuery->where('gs.provider_id', $providerId);
        }

        // 获取玩家ID列表
        $playerIds = $playerQuery->column('DISTINCT gs.player_id');

        // 如果没有玩家，则返回0
        if (empty($playerIds)) {
            return [
                'deposit_amount' => '0.00',
                'player_count' => 0
            ];
        }

        // 直接从players表获取这些玩家的充值总额
        $depositAmount = Db::table('players')
            ->where('id', 'in', $playerIds)
            ->sum('total_deposit');

        return [
            'deposit_amount' => number_format($depositAmount ?: 0, 2),
            'player_count' => count($playerIds)
        ];
    }
}
