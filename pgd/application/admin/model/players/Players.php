<?php

namespace app\admin\model\players;

use think\Model;


class Players extends Model
{





    // 表名
    protected $table = 'players';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'tag_name',
        'agent_name',
        'channel_name',
        'withdrawing_balance',
        'direct_referrals_count',
        'direct_deposit_referrals_count',
        'second_level_referrals_count',
        'second_level_deposit_referrals_count',
        'third_level_referrals_count',
        'third_level_deposit_referrals_count',
        'team_total_deposit',
        'team_total_withdraw',
        'team_deposit_withdraw_diff',
        'latest_remark'
    ];

    /**
     * 获取玩家标签名称
     */
    public function getTagNameAttr($value, $data)
    {
        if (isset($data['tag_id']) && $data['tag_id']) {
            $tag = \app\admin\model\players\PlayerTags::get($data['tag_id']);
            return $tag ? $tag['name'] : '';
        }
        return '';
    }

    /**
     * 获取业务员名称
     */
    public function getAgentNameAttr($value, $data)
    {
        if (isset($data['agent_id']) && $data['agent_id']) {
            $agent = \app\admin\model\agents_config\Agents::get($data['agent_id']);
            if ($agent && $agent['admin_id']) {
                $admin = \app\admin\model\Admin::get($agent['admin_id']);
                if ($admin) {
                    return $data['agent_id'] . '(' . $admin['nickname'] . ')';
                }
            }
            return $data['agent_id'] ? $data['agent_id'] . '(未知)' : '';
        }
        return '';
    }

    /**
     * 获取渠道名称
     */
    public function getChannelNameAttr($value, $data)
    {
        if (isset($data['channel_id']) && $data['channel_id']) {
            $channel = \app\admin\model\channels\Channels::get($data['channel_id']);
            if ($channel) {
                return $data['channel_id'] . '(' . $channel['name'] . ')';
            }
            return $data['channel_id'] ? $data['channel_id'] . '(未知)' : '';
        }
        return '';
    }

    /**
     * 获取提现中余额
     */
    public function getWithdrawingBalanceAttr($value, $data)
    {
        if (isset($data['id'])) {
            // 查询未审核的提现订单金额累加值
            // 未审核状态包括：0-待加入工单，1-已加入工单待审核
            // 使用 table 方法指定完整表名，避免表前缀问题
            $withdrawingAmount = \think\Db::table('withdraw_orders')
                ->where('player_id', $data['id'])
                ->where('audit_status', 'in', [0, 1])
                ->sum('amount');

            return $withdrawingAmount ?: '0.00';
        }
        return '0.00';
    }

    /**
     * 获取直推人数（一级下线数量）
     */
    public function getDirectReferralsCountAttr($value, $data)
    {
        if (isset($data['id'])) {
            // 查询玩家的一级下线数量，无视is_hidden字段
            // 使用 table 方法指定完整表名，避免表前缀问题
            $directReferralsCount = \think\Db::table('player_relations')
                ->where('ancestor_id', $data['id'])
                ->where('relation_level', 1)
                ->count();

            return $directReferralsCount ?: 0;
        }
        return 0;
    }

    /**
     * 获取二级下线人数
     */
    public function getSecondLevelReferralsCountAttr($value, $data)
    {
        if (isset($data['id'])) {
            // 查询玩家的二级下线数量，无视is_hidden字段
            // 使用 table 方法指定完整表名，避免表前缀问题
            $secondLevelReferralsCount = \think\Db::table('player_relations')
                ->where('ancestor_id', $data['id'])
                ->where('relation_level', 2)
                ->count();

            return $secondLevelReferralsCount ?: 0;
        }
        return 0;
    }

    /**
     * 获取直推充值人数（一级下线中有充值记录的人数）
     */
    public function getDirectDepositReferralsCountAttr($value, $data)
    {
        if (isset($data['id'])) {
            // 查询玩家的一级下线中有充值记录的人数
            // 使用原生 SQL 查询，避免表前缀问题
            $sql = "SELECT COUNT(DISTINCT pr.player_id) as count
                   FROM player_relations pr
                   JOIN deposit_orders d ON pr.player_id = d.player_id
                   WHERE pr.ancestor_id = ?
                   AND pr.relation_level = 1
                   AND d.payment_status = 1";

            $result = \think\Db::query($sql, [$data['id']]);
            $directDepositReferralsCount = isset($result[0]['count']) ? $result[0]['count'] : 0;

            return $directDepositReferralsCount ?: 0;
        }
        return 0;
    }

    /**
     * 获取二级下线充值人数（二级下线中有充值记录的人数）
     */
    public function getSecondLevelDepositReferralsCountAttr($value, $data)
    {
        if (isset($data['id'])) {
            // 查询玩家的二级下线中有充值记录的人数
            // 使用原生 SQL 查询，避免表前缀问题
            $sql = "SELECT COUNT(DISTINCT pr.player_id) as count
                   FROM player_relations pr
                   JOIN deposit_orders d ON pr.player_id = d.player_id
                   WHERE pr.ancestor_id = ?
                   AND pr.relation_level = 2
                   AND d.payment_status = 1";

            $result = \think\Db::query($sql, [$data['id']]);
            $secondLevelDepositReferralsCount = isset($result[0]['count']) ? $result[0]['count'] : 0;

            return $secondLevelDepositReferralsCount ?: 0;
        }
        return 0;
    }

    /**
     * 获取三级下线人数
     */
    public function getThirdLevelReferralsCountAttr($value, $data)
    {
        if (isset($data['id'])) {
            // 查询玩家的三级下线数量，无视is_hidden字段
            // 使用 table 方法指定完整表名，避免表前缀问题
            $thirdLevelReferralsCount = \think\Db::table('player_relations')
                ->where('ancestor_id', $data['id'])
                ->where('relation_level', 3)
                ->count();

            return $thirdLevelReferralsCount ?: 0;
        }
        return 0;
    }

    /**
     * 获取三级下线充值人数（三级下线中有充值记录的人数）
     */
    public function getThirdLevelDepositReferralsCountAttr($value, $data)
    {
        if (isset($data['id'])) {
            // 查询玩家的三级下线中有充值记录的人数
            // 使用原生 SQL 查询，避免表前缀问题
            $sql = "SELECT COUNT(DISTINCT pr.player_id) as count
                   FROM player_relations pr
                   JOIN deposit_orders d ON pr.player_id = d.player_id
                   WHERE pr.ancestor_id = ?
                   AND pr.relation_level = 3
                   AND d.payment_status = 1";

            $result = \think\Db::query($sql, [$data['id']]);
            $thirdLevelDepositReferralsCount = isset($result[0]['count']) ? $result[0]['count'] : 0;

            return $thirdLevelDepositReferralsCount ?: 0;
        }
        return 0;
    }

    /**
     * 获取团队总充值（所有下线的充值金额累加）
     */
    public function getTeamTotalDepositAttr($value, $data)
    {
        if (isset($data['id'])) {
            // 查询玩家所有下线的充值金额累加
            // 使用原生 SQL 查询，避免表前缀问题
            $sql = "SELECT SUM(d.amount) as total
                   FROM player_relations pr
                   JOIN deposit_orders d ON pr.player_id = d.player_id
                   WHERE pr.ancestor_id = ?
                   AND d.payment_status = 1";

            $result = \think\Db::query($sql, [$data['id']]);
            $teamTotalDeposit = isset($result[0]['total']) ? $result[0]['total'] : 0;

            return number_format($teamTotalDeposit, 2, '.', '') ?: '0.00';
        }
        return '0.00';
    }

    /**
     * 获取团队总提现（所有下线的提现金额累加）
     */
    public function getTeamTotalWithdrawAttr($value, $data)
    {
        if (isset($data['id'])) {
            // 查询玩家所有下线的提现金额累加
            // 使用原生 SQL 查询，避免表前缀问题
            // 只计算已完成的提现订单（状态为4-订单完成或状态为8-虚拟支付）
            $sql = "SELECT SUM(w.amount) as total
                   FROM player_relations pr
                   JOIN withdraw_orders w ON pr.player_id = w.player_id
                   WHERE pr.ancestor_id = ?
                   AND w.audit_status IN (4, 8)";

            $result = \think\Db::query($sql, [$data['id']]);
            $teamTotalWithdraw = isset($result[0]['total']) ? $result[0]['total'] : 0;

            return number_format($teamTotalWithdraw, 2, '.', '') ?: '0.00';
        }
        return '0.00';
    }

    /**
     * 获取团队充提差（团队总充值减去团队总提现）
     */
    public function getTeamDepositWithdrawDiffAttr($value, $data)
    {
        if (isset($data['id'])) {
            // 获取团队总充值
            $teamTotalDeposit = $this->getTeamTotalDepositAttr(null, $data);
            // 获取团队总提现
            $teamTotalWithdraw = $this->getTeamTotalWithdrawAttr(null, $data);

            // 计算差值
            $diff = floatval(str_replace(',', '', $teamTotalDeposit)) - floatval(str_replace(',', '', $teamTotalWithdraw));

            // 格式化为两位小数
            return number_format($diff, 2, '.', '');
        }
        return '0.00';
    }

    /**
     * 更新玩家标签
     *
     * @param int $player_id 玩家ID
     * @param int $tag_id 标签ID
     * @return bool
     */
    public static function updatePlayerTag($player_id, $tag_id)
    {
        if (!$player_id || !$tag_id) {
            return false;
        }

        $player = self::get($player_id);
        if (!$player) {
            return false;
        }

        $player->tag_id = $tag_id;
        $player->updated_at = date('Y-m-d H:i:s');
        return $player->save();
    }

    /**
     * 获取玩家最新备注
     */
    public function getLatestRemarkAttr($value, $data)
    {
        if (isset($data['id'])) {
            // 查询玩家的最新备注记录
            $latestRemark = \think\Db::table('player_remarks')
                ->where('player_id', $data['id'])
                ->order('created_at', 'desc')
                ->value('content');

            return $latestRemark ?: '';
        }
        return '';
    }










}
