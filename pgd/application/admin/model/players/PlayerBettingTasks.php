<?php

namespace app\admin\model\players;

use fast\Date;
use think\Model;

class PlayerBettingTasks extends Model
{
    // 表名
    protected $table = 'player_betting_tasks';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 追加属性
    protected $append = [
        'channel_name',
        'player_register_time',
        'player_balance',
        'player_total_deposit',
        'player_total_withdraw',
        'completion_percentage'
    ];

    protected static function init()
    {
        // self::beforeInsert(function ($row) {
        //     $row->created_at =  date('Y-m-d H:i:s');
        //     $row->updated_at =  date('Y-m-d H:i:s');
        // });

        // self::beforeUpdate(function ($row) {
        //     $row->updated_at =  date('Y-m-d H:i:s');
        // });
    }

    public function player()
    {
        return $this->belongsTo('app\admin\model\players\Players', 'player_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 获取渠道名称
     */
    public function getChannelNameAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            if ($player && isset($player['channel_id']) && $player['channel_id']) {
                $channel = \app\admin\model\channels\Channels::get($player['channel_id']);
                if ($channel) {
                    return $channel['id'] . '(' . $channel['name'] . ')';
                }
                return $player['channel_id'] ? $player['channel_id'] . '(未知)' : '';
            }
        }
        return '';
    }

    /**
     * 获取玩家注册时间
     */
    public function getPlayerRegisterTimeAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            if ($player && isset($player['created_at'])) {
                return $player['created_at'];
            }
        }
        return '';
    }

    /**
     * 获取玩家余额
     */
    public function getPlayerBalanceAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            if ($player && isset($player['balance'])) {
                return $player['balance'];
            }
        }
        return '0.00';
    }

    /**
     * 获取玩家总充值
     */
    public function getPlayerTotalDepositAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            if ($player && isset($player['total_deposit'])) {
                return $player['total_deposit'];
            }
        }
        return '0.00';
    }

    /**
     * 获取玩家总提现
     */
    public function getPlayerTotalWithdrawAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            if ($player && isset($player['total_withdraw'])) {
                return $player['total_withdraw'];
            }
        }
        return '0.00';
    }

    /**
     * 获取完成比
     */
    public function getCompletionPercentageAttr($value, $data)
    {
        if (isset($data['required_betting_amount']) && $data['required_betting_amount'] > 0 && isset($data['completed_betting_amount'])) {
            $percentage = ($data['completed_betting_amount'] / $data['required_betting_amount']) * 100;
            return round($percentage, 2);
        }
        return 0;
    }
}