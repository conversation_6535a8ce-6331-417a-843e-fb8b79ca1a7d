<?php

namespace app\admin\model\players;

use think\Model;


class PlayerTags extends Model
{





    // 表名
    protected $table = 'player_tags';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];


    /**
     * 获取所有标签列表
     *
     * @param bool $asArray 是否返回关联数组，默认为对象数组
     * @return array 标签列表
     */
    public static function getTagList($asArray = false)
    {
        $tags = self::field('id,name')->order('id', 'asc')->select();
        
        if ($asArray) {
            $result = [];
            foreach ($tags as $tag) {
                $result[$tag['id']] = $tag['name'];
            }
            return $result;
        }

        return $tags;
    }

    /**
     * 获取关联的玩家数量
     */
    public function getPlayerCount()
    {
        return \app\admin\model\players\Players::where('tag_id', $this->id)->count();
    }

    /**
     * 定义与玩家表的关联
     */
    public function players()
    {
        return $this->hasMany('app\admin\model\players\Players', 'tag_id', 'id');
    }









}
