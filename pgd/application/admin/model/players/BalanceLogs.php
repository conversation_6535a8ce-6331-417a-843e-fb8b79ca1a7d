<?php

namespace app\admin\model\players;

use think\Model;

class BalanceLogs extends Model
{
    // 表名
    protected $table = 'player_balance_logs';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 追加属性
    protected $append = [
        'balance_type_text',
        'player_username'
    ];
    
    // 定义关联关系
    public function player()
    {
        return $this->belongsTo('app\admin\model\players\Players', 'player_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
    public function transactionType()
    {
        return $this->belongsTo('app\admin\model\players\BalanceChangeTypes', 'transaction_type', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
    /**
     * 获取余额类型文本
     */
    public function getBalanceTypeTextAttr($value, $data)
    {
        $balanceTypes = [
            1 => __('Account balance'),
            2 => __('Reward balance')
        ];
        return isset($data['balance_type']) ? $balanceTypes[$data['balance_type']] : '';
    }
    
    /**
     * 获取玩家用户名
     */
    public function getPlayerUsernameAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            if ($player) {
                return $player['username'];
            }
        }
        return '';
    }
}
