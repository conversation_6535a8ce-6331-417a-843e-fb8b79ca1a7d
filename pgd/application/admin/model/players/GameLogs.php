<?php

namespace app\admin\model\players;

use think\Model;

class GameLogs extends Model
{
    // 表名
    protected $table = 'game_records';

    protected $connection = [
        'prefix' => ''
    ];

    // 主键
    protected $pk = 'id';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 追加属性
    protected $append = [
        'is_real_text'
    ];

    // 定义关联关系
    public function player()
    {
        return $this->belongsTo('app\admin\model\players\Players', 'player_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    // 不再需要provider关联关系，直接使用game_id关联到game_rooms

    public function gameRoom()
    {
        return $this->belongsTo('app\admin\model\game\Rooms', 'game_id', 'game_room_id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 获取是否真实游戏文本
     */
    public function getIsRealTextAttr($value, $data)
    {
        $isRealList = [
            0 => __('No'),
            1 => __('Yes')
        ];
        return isset($data['is_real']) ? $isRealList[$data['is_real']] : '';
    }


}
