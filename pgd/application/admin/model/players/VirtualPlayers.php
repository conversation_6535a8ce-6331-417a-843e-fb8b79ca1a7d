<?php

namespace app\admin\model\players;

use think\Model;

class VirtualPlayers extends Model
{
    // 表名
    protected $table = 'virtual_players';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 追加属性
    protected $append = [
        
    ];

    // 定义关联关系
    public function channel()
    {
        return $this->belongsTo('app\admin\model\channels\Channels', 'channel_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
