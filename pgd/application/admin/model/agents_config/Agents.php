<?php

namespace app\admin\model\agents_config;

use think\Model;


class Agents extends Model
{





    // 表名
    protected $table = 'agents';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];

    // 字段类型
    protected $type = [
        'game_credit' => 'float',
        'used_game_credit' => 'float',
        'withdraw_permission' => 'boolean',
        'withdraw_credit' => 'float',
        'used_withdraw_credit' => 'float',
        'can_change_password' => 'boolean',
        'leader_id' => 'integer',
    ];

    /**
     * 关联管理员表
     */
    public function admin()
    {
        return $this->belongsTo('app\admin\model\Admin', 'admin_id', 'id');
    }

    /**
     * 获取业务员列表
     * @return array
     */
    public static function getAgentList()
    {
        $list = self::field('id,username')->select();
        $data = [];
        foreach ($list as $item) {
            $data[$item['id']] = $item['username'];
        }
        return $data;
    }

    /**
     * 获取业务员列表（用于API）
     * @return array
     */
    public static function getList()
    {
        return self::getAgentList();
    }

    /**
     * 检查业务员是否有足够的放款额度
     *
     * @param int $agentId 业务员ID
     * @param float $amount 提现金额
     * @return bool 是否有足够的额度
     */
    public static function hasEnoughWithdrawCredit($agentId, $amount)
    {
        $agent = self::get($agentId);
        if (!$agent) {
            return false;
        }

        // 计算可用放款额度
        $availableCredit = floatval($agent['withdraw_credit']) - floatval($agent['used_withdraw_credit']);

        return $availableCredit >= floatval($amount);
    }

    /**
     * 更新业务员已使用放款额度
     *
     * @param int $agentId 业务员ID
     * @param float $amount 变更金额（正数增加已使用额度，负数减少已使用额度）
     * @return bool 是否更新成功
     */
    public static function updateUsedWithdrawCredit($agentId, $amount)
    {
        $agent = self::get($agentId);
        if (!$agent) {
            return false;
        }

        // 计算新的已使用额度
        $newUsedCredit = floatval($agent['used_withdraw_credit']) + floatval($amount);

        // 只确保已使用额度不为负数，允许超过总额度
        if ($newUsedCredit < 0) {
            return false;
        }

        // 更新业务员已使用放款额度
        $agent->used_withdraw_credit = $newUsedCredit;
        return $agent->save() !== false;
    }

    /**
     * 检查业务员是否有足够的游戏额度
     *
     * @param int $agentId 业务员ID
     * @param float $amount 游戏金额
     * @return bool 是否有足够的额度
     */
    public static function hasEnoughGameCredit($agentId, $amount)
    {
        $agent = self::get($agentId);
        if (!$agent) {
            return false;
        }

        // 计算可用游戏额度
        $availableCredit = floatval($agent['game_credit']) - floatval($agent['used_game_credit']);

        return $availableCredit >= floatval($amount);
    }

    /**
     * 更新业务员已使用游戏额度
     *
     * @param int $agentId 业务员ID
     * @param float $amount 变更金额（正数增加已使用额度，负数减少已使用额度）
     * @return bool 是否更新成功
     */
    public static function updateUsedGameCredit($agentId, $amount)
    {
        $agent = self::get($agentId);
        if (!$agent) {
            return false;
        }

        // 计算新的已使用额度
        $newUsedCredit = floatval($agent['used_game_credit']) + floatval($amount);

        // 只确保已使用额度不为负数，允许超过总额度
        if ($newUsedCredit < 0) {
            return false;
        }

        // 更新业务员已使用游戏额度
        $agent->used_game_credit = $newUsedCredit;
        return $agent->save() !== false;
    }
}
