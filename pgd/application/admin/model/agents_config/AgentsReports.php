<?php

namespace app\admin\model\agents_config;

use think\Model;

class AgentsReports extends Model
{
    // 表名
    protected $table = 'daily_statistics';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'new_active_users_text',
        'deposit_info_text',
        'withdraw_info_text'
    ];

    // 定义搜索的权重
    protected static $searchField = [
        'stat_date' => 'RANGE',
        'agent_id' => '='
    ];



    public function getNewActiveUsersTextAttr($value, $data)
    {
        return ($data['new_players'] ?? 0) . '/' . ($data['active_players'] ?? 0);
    }

    public function getDepositInfoTextAttr($value, $data)
    {
        $depositUsers = ($data['first_deposit_players'] ?? 0) + ($data['repeat_deposit_players'] ?? 0);
        return number_format($data['total_deposit'] ?? 0, 2) . '/' . $depositUsers;
    }

    public function getWithdrawInfoTextAttr($value, $data)
    {
        return number_format($data['total_withdraw'] ?? 0, 2) . '/' . ($data['withdraw_players'] ?? 0);
    }

    public static function getSearchField()
    {
        return self::$searchField;
    }
}
