<?php

namespace app\admin\model\activity;

use think\Model;
use think\Db;
use app\admin\model\channels\Channels;

/**
 * 返利配置模型
 */
class RebateConfig extends Model
{
    // 表名
    protected $name = 'rebate_config';
    
    // 数据库表名，不带前缀
    protected $table = 'rebate_config';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    
    // 类型转换
    protected $type = [
        'id' => 'integer',
        'channel_id' => 'integer',
        'level1_first_deposit_rate' => 'float',
        'level1_first_deposit_max' => 'float',
        'level2_first_deposit_rate' => 'float',
        'level2_first_deposit_max' => 'float',
        'min_deposit_amount' => 'float',
        'is_active' => 'integer',
        'start_date' => 'date',
        'end_date' => 'date'
    ];

    // 追加属性
    protected $append = [
        'channel_text'
    ];

    protected function initialize()
    {
        parent::initialize();
    }

    protected static function init()
    {
        self::beforeWrite(function ($model) {
            // 处理渠道ID
            if (isset($model['channel_id']) && $model['channel_id'] != 999999) {
                $channel = Db::table('channels')->where('id', $model['channel_id'])->find();
                if (!$channel) {
                    throw new \Exception('无效的渠道ID');
                }
            }

            // 验证日期
            if (isset($model['start_date']) && isset($model['end_date'])) {
                $start = strtotime($model['start_date']);
                $end = strtotime($model['end_date']);
                if ($start > $end) {
                    throw new \Exception('结束日期不能早于开始日期');
                }
            }

            // 验证返利率和金额
            if (isset($model['level1_first_deposit_rate'])) {
                if ($model['level1_first_deposit_rate'] < 0 || $model['level1_first_deposit_rate'] > 100) {
                    throw new \Exception('一级用户首充返利率必须在0-100之间');
                }
            }
            if (isset($model['level2_first_deposit_rate'])) {
                if ($model['level2_first_deposit_rate'] < 0 || $model['level2_first_deposit_rate'] > 100) {
                    throw new \Exception('二级用户首充返利率必须在0-100之间');
                }
            }

            // 设置时间戳
            $now = date('Y-m-d H:i:s');
            $model->created_at = $now;
            $model->updated_at = $now;
            
            return true;
        });
    }

    /**
     * 关联渠道
     */
    public function channel()
    {
        return $this->belongsTo('app\admin\model\channels\Channels', 'channel_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 获取渠道名称
     */
    public function getChannelTextAttr($value, $data)
    {
        if (isset($data['channel_id']) && $data['channel_id'] == 999999) {
            return '全部渠道';
        }
        
        $channel = \app\admin\model\channels\Channels::get($data['channel_id']);
        return $channel ? $channel['name'] : '';
    }
}
