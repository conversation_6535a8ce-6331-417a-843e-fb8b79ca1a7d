<?php
/*
 * @Author: ‘guanzh’ ‘<EMAIL>’
 * @Date: 2025-03-17 01:25:21
 * @LastEditors: ‘guanzh’ ‘<EMAIL>’
 * @LastEditTime: 2025-03-17 01:26:34
 * @FilePath: \pgd\application\admin\model\payout\WithdrawOrders.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

namespace app\admin\model\payout;

use think\Model;
use app\admin\model\players\Players;
use app\admin\model\players\PlayerTags;
use app\admin\model\agents_config\Agents;
use app\admin\model\channels\Channels;
use app\admin\model\Admin;

class WithdrawOrders extends Model
{

    

    

    // 表名
    protected $table = 'withdraw_orders';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'player_tag_name',
        'channel_name',
        'agent_name',
        'total_deposit',
        'total_withdraw'
    ];
    
    /**
     * 关联玩家表
     */
    public function player()
    {
        return $this->belongsTo('app\admin\model\players\Players', 'player_id', 'id');
    }
    
    /**
     * 关联业务员表
     */
    public function agent()
    {
        return $this->belongsTo('app\admin\model\agents_config\Agents', 'agent_id', 'id');
    }
    
    /**
     * 关联渠道表
     */
    public function channel()
    {
        return $this->belongsTo('app\admin\model\channels\Channels', 'channel_id', 'id');
    }
    
    /**
     * 获取玩家标签名称
     */
    public function getPlayerTagNameAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            if ($player && $player['tag_id']) {
                $tag = \app\admin\model\players\PlayerTags::get($player['tag_id']);
                return $tag ? $tag['name'] : '';
            }
        }
        return '';
    }
    
    /**
     * 获取渠道名称
     */
    public function getChannelNameAttr($value, $data)
    {
        if (isset($data['agent_id'])) {
            $agent = \app\admin\model\agents_config\Agents::get($data['agent_id']);
            if ($agent && $agent['channel_id']) {
                $channel = \app\admin\model\channels\Channels::get($agent['channel_id']);
                if ($channel) {
                    return $agent['channel_id'] . '(' . $channel['name'] . ')';
                }
                return $agent['channel_id'] . '(未知)';
            }
        }
        return '';
    }
    
    /**
     * 获取业务员名称
     */
    public function getAgentNameAttr($value, $data)
    {
        if (isset($data['agent_id'])) {
            $agent = \app\admin\model\agents_config\Agents::get($data['agent_id']);
            if ($agent && $agent['admin_id']) {
                $admin = \app\admin\model\Admin::get($agent['admin_id']);
                if ($admin) {
                    return $data['agent_id'] . '(' . $admin['nickname'] . ')';
                }
            }
        }
        return $data['agent_id'] ? $data['agent_id'] . '(未知)' : '';
    }
    
    /**
     * 获取玩家总充值
     */
    public function getTotalDepositAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            if ($player) {
                return $player['total_deposit'] ?: 0;
            }
        }
        return isset($data['total_deposit']) ? $data['total_deposit'] : 0;
    }
    
    /**
     * 获取玩家总提现
     */
    public function getTotalWithdrawAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            if ($player) {
                return $player['total_withdraw'] ?: 0;
            }
        }
        return isset($data['total_withdraw']) ? $data['total_withdraw'] : 0;
    }
}
