<?php

namespace app\admin\model\payout;

use think\Model;


class InvitationRewards extends Model
{
    // 表名
    protected $table = 'treasure_box_records';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'player_phone_number',
        'player_channel_name',
        'player_agent_name',
        'valid_invited_count',
        'config_invite_count'
    ];

    /**
     * 关联玩家表
     */
    public function player()
    {
        return $this->belongsTo('app\admin\model\players\Players', 'player_id', 'id');
    }

    /**
     * 关联宝箱配置表
     */
    public function config()
    {
        return $this->belongsTo('app\admin\model\agents\TreasureBoxConfig', 'config_id', 'id');
    }

    /**
     * 获取配置中的邀请人数要求
     */
    public function getConfigInviteCountAttr($value, $data)
    {
        if (isset($data['config_id'])) {
            $config = \app\admin\model\agents\TreasureBoxConfig::get($data['config_id']);
            return $config ? $config['invite_count'] : 0;
        }
        return 0;
    }

    /**
     * 获取玩家手机号
     */
    public function getPlayerPhoneNumberAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            return $player ? $player['phone_number'] : '';
        }
        return '';
    }

    /**
     * 获取玩家所属渠道名称
     */
    public function getPlayerChannelNameAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            if ($player && $player['channel_id']) {
                $channel = \app\admin\model\channels\Channels::get($player['channel_id']);
                return $channel ? $channel['name'] : '';
            }
        }
        return '';
    }

    /**
     * 获取玩家所属业务员名称
     */
    public function getPlayerAgentNameAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            $player = \app\admin\model\players\Players::get($data['player_id']);
            if ($player && $player['agent_id']) {
                $agent = \app\admin\model\agents_config\Agents::get($player['agent_id']);
                if ($agent && $agent['admin_id']) {
                    $admin = \app\admin\model\Admin::get($agent['admin_id']);
                    return $admin ? $admin['nickname'] : '';
                }
            }
        }
        return '';
    }

    /**
     * 获取有效邀请人数（一级下线会员首充充值25以上的玩家数量）
     */
    public function getValidInvitedCountAttr($value, $data)
    {
        if (isset($data['player_id'])) {
            try {
                // 获取玩家的所有下线ID（一级关系）
                $subordinateIds = \think\Db::table('player_relations')
                    ->where('ancestor_id', $data['player_id'])
                    ->where('relation_level', 1)
                    ->column('player_id');

                if (empty($subordinateIds)) {
                    return 0;
                }

                /* 方法1：使用子查询确保只统计每个玩家的第一次充值订单
                $validCount = \think\Db::table('deposit_orders as d1')
                    ->whereIn('d1.player_id', $subordinateIds)
                    ->where('d1.payment_status', 1) // 已支付的订单
                    ->where('d1.amount', '>=', 25) // 充值金额大于等于25
                    ->where(function($query) {
                        // 子查询确保这是玩家的第一次充值订单
                        $query->whereRaw('d1.id = (SELECT MIN(d2.id) FROM deposit_orders d2 WHERE d2.player_id = d1.player_id AND d2.payment_status = 1)');
                    })
                    ->count('DISTINCT d1.player_id'); // 计算不重复的玩家数量
                */

                // 方法2：分两步查询，先获取每个玩家的第一笔充值订单，再统计金额大于25的订单数量
                // 先获取每个下线玩家的第一笔充值订单
                $firstDepositOrders = \think\Db::table('deposit_orders')
                    ->whereIn('player_id', $subordinateIds)
                    ->where('payment_status', 1) // 已支付的订单
                    ->group('player_id')
                    ->field('player_id, MIN(id) as first_order_id')
                    ->select();

                // 如果没有任何下线有充值记录，直接返回0
                if (empty($firstDepositOrders)) {
                    return 0;
                }

                // 获取所有第一笔订单的ID
                $firstOrderIds = [];
                foreach ($firstDepositOrders as $order) {
                    $firstOrderIds[] = $order['first_order_id'];
                }

                // 查询这些第一笔订单中金额大于25的数量
                $validCount = \think\Db::table('deposit_orders')
                    ->whereIn('id', $firstOrderIds)
                    ->where('amount', '>=', 25)
                    ->count();

                return $validCount ?: 0;
            } catch (\Exception $e) {
                // 记录错误日志
                \think\Log::error('计算有效邀请人数失败: ' . $e->getMessage());
                return 0;
            }
        }
        return 0;
    }
}
