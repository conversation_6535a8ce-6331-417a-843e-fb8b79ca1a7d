<?php
namespace app\admin\model\commission;

use think\Model;

class PlayerCommissionConfigs extends Model
{
    // 表名
    protected $name = 'player_commission_configs';

    // 设置表前缀为空
    protected $connection = [
        // 数据库表前缀
        'prefix'      => '',
    ];

    // 追加属性
    protected $append = [];

    /**
     * 获取玩家佣金配置
     * 如果不存在则返回空配置
     */
    public static function getPlayerConfig($playerId)
    {
        $config = self::where('player_id', $playerId)->find();

        // 如果不存在，返回空配置
        if (!$config) {
            $config = new self();
            $config->player_id = $playerId;
            $config->min_deposit_amount = 0.00;
            $config->level1_rate = 0.00;
            $config->level1_max_amount = 0.00;
            $config->level2_rate = 0.00;
            $config->level2_max_amount = 0.00;
            $config->level3_rate = 0.00;
            $config->level3_max_amount = 0.00;
            // $config->save();
        }

        return $config;
    }

    /**
     * 同步佣金配置到所有下级玩家
     */
    public function syncToAllSubordinates()
    {
        // 获取所有下级玩家ID
        $subordinateIds = \think\Db::table('player_relations')
            ->where('ancestor_id', $this->player_id)
            ->column('player_id');

        if (empty($subordinateIds)) {
            return true;
        }

        // 批量更新或创建下级玩家的佣金配置
        $datetime = date('Y-m-d H:i:s'); // 使用格式化的日期时间字符串
        $batch = [];

        foreach ($subordinateIds as $playerId) {
            // 检查是否已存在配置
            $exists = self::where('player_id', $playerId)->find();

            if ($exists) {
                // 更新现有配置
                self::where('player_id', $playerId)
                    ->update([
                        'min_deposit_amount' => $this->min_deposit_amount,
                        'level1_rate' => $this->level1_rate,
                        'level1_max_amount' => $this->level1_max_amount,
                        'level2_rate' => $this->level2_rate,
                        'level2_max_amount' => $this->level2_max_amount,
                        'level3_rate' => $this->level3_rate,
                        'level3_max_amount' => $this->level3_max_amount,
                        'sync_next' => 0, // 下级玩家的sync_next默认设为0
                        'updated_at' => $datetime
                    ]);
            } else {
                // 创建新配置
                $batch[] = [
                    'player_id' => $playerId,
                    'min_deposit_amount' => $this->min_deposit_amount,
                    'level1_rate' => $this->level1_rate,
                    'level1_max_amount' => $this->level1_max_amount,
                    'level2_rate' => $this->level2_rate,
                    'level2_max_amount' => $this->level2_max_amount,
                    'level3_rate' => $this->level3_rate,
                    'level3_max_amount' => $this->level3_max_amount,
                    'sync_next' => 0, // 下级玩家的sync_next默认设为0
                    'created_at' => $datetime,
                    'updated_at' => $datetime
                ];
            }
        }

        // 批量插入新配置
        if (!empty($batch)) {
            self::insertAll($batch);
        }

        return true;
    }
}
