<?php

namespace app\admin\model\game;

use think\Model;

class BettingAmountConfig extends Model
{
    // 表名
    protected $table = 'betting_task_config';

    // 追加属性
    protected $append = [];

    // 定义时间戳字段名
    protected $dateFormat = 'Y-m-d H:i:s';

    // 数据完成前的回调
    protected static function init()
    {
        //无这些字段
        // self::beforeInsert(function ($row) {
        //     $row['create_time'] = time();
        //     $row['update_time'] = time();
        // });

        // self::beforeUpdate(function ($row) {
        //     $row['update_time'] = time();
        // });
    }


}