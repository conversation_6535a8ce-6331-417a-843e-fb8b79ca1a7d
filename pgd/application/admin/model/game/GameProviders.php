<?php


namespace app\admin\model\game;

use think\Model;


class GameProviders extends Model
{
    // 表名
    protected $table = 'game_providers';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];

    // 数据表字段信息
    protected $schema = [
        'id'             => 'smallint',
        'name'           => 'string',
        'sort'           => 'int',
        'remark'         => 'string',
        'min_amount'     => 'decimal',
        'is_active'      => 'tinyint',
        'created_at'     => 'timestamp',
        'updated_at'     => 'timestamp'
    ];
}
