<?php

namespace app\admin\model\game;

use think\Model;

class Rooms extends Model
{
    // 表名
    protected $table = 'game_rooms';
    
    // 主键
    protected $pk = 'game_room_id';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义关联关系
    public function provider()
    {
        return $this->belongsTo('app\admin\model\game\Providers', 'provider_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
