<?php

namespace app\admin\model\channels;

use think\Model;


class Channels extends Model
{





    // 表名
    protected $table = 'channels';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'total_credit_limit'
    ];

    /**
     * 关联管理员表
     */
    public function admin()
    {
        return $this->belongsTo('app\admin\model\Admin', 'admin_id', 'id', ['id' => 'fa_admin.id', 'username' => 'fa_admin.username', 'nickname' => 'fa_admin.nickname', 'two_factor_key' => 'fa_admin.two_factor_key'], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 获取渠道列表
     * @param bool $format 是否格式化为id=>name格式
     * @return array
     */
    public static function getChannelList($format = false)
    {
        $list = self::field('id, name')
            ->select();

        if ($format) {
            $data = [];
            foreach ($list as $item) {
                $data[$item['id']] = $item['name'];
            }
            return $data;
        }
        return $list;
    }

    /**
     * 获取渠道总放款额度（虚拟字段）
     * 计算方式：credit_left + 渠道下所有agents.withdraw_credit - 渠道下所有业务员的agents.used_withdraw_credit
     *
     * @param mixed $value 字段值
     * @param array $data 行数据
     * @return string 格式化后的总放款额度
     */
    public function getTotalCreditLimitAttr($value, $data)
    {
        if (!isset($data['id'])) {
            return '0.00';
        }

        $channelId = $data['id'];

        // 获取渠道剩余额度
        $creditLeft = isset($data['credit_left']) ? floatval($data['credit_left']) : 0;

        // 获取渠道下所有业务员的放款额度和已使用放款额度
        $agentsData = \think\Db::table('agents')
            ->where('channel_id', $channelId)
            ->field('SUM(withdraw_credit) as total_withdraw_credit, SUM(used_withdraw_credit) as total_used_withdraw_credit')
            ->find();

        $totalWithdrawCredit = isset($agentsData['total_withdraw_credit']) ? floatval($agentsData['total_withdraw_credit']) : 0;
        $totalUsedWithdrawCredit = isset($agentsData['total_used_withdraw_credit']) ? floatval($agentsData['total_used_withdraw_credit']) : 0;

        // 计算总放款额度
        $totalCreditLimit = $creditLeft + $totalWithdrawCredit - $totalUsedWithdrawCredit;

        return number_format($totalCreditLimit, 2, '.', '');
    }

    /**
     * 检查渠道是否有足够的剩余额度分配给业务员
     *
     * @param int $channelId 渠道ID
     * @param float $requestedCredit 请求的额度
     * @return bool 是否有足够的额度
     */
    public static function hasEnoughCreditLeft($channelId, $requestedCredit)
    {
        $channel = self::get($channelId);
        if (!$channel) {
            return false;
        }

        return floatval($channel['credit_left']) >= floatval($requestedCredit);
    }

    /**
     * 更新渠道剩余额度
     *
     * @param int $channelId 渠道ID
     * @param float $amount 变更金额（正数减少额度，负数增加额度）
     * @return bool 是否更新成功
     */
    public static function updateCreditLeft($channelId, $amount)
    {
        $channel = self::get($channelId);
        if (!$channel) {
            return false;
        }

        // 计算新的剩余额度
        $newCreditLeft = floatval($channel['credit_left']) - floatval($amount);

        // 确保剩余额度不为负数
        if ($newCreditLeft < 0) {
            return false;
        }

        // 更新渠道剩余额度
        $channel->credit_left = $newCreditLeft;
        return $channel->save() !== false;
    }
}
