<?php

namespace app\admin\model\channels;

use think\Model;

class ChannelsPay extends Model
{
    // 表名
    protected $table = 'channels_pay';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 追加属性
    protected $append = [
        'status_text'
    ];
    
    // 定义状态获取器
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            'pending' => __('Pending'),
            'approved' => __('Approved'),
            'rejected' => __('Rejected')
        ];
        return isset($status[$data['status']]) ? $status[$data['status']] : '';
    }

    protected static function init()
    {
        // self::beforeInsert(function ($row) {
        //     $row->create_time =date('Y-m-d H:i:s');
        //     $row->update_time = date('Y-m-d H:i:s');
        // });

        // self::beforeUpdate(function ($row) {
        //     $row->update_time = date('Y-m-d H:i:s');
        // });
    }
}
