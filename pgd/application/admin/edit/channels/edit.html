<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-password" class="form-control" name="row[password]" type="password" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Deposit_fee_rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-deposit_fee_rate" data-rule="required" class="form-control" step="0.01" name="row[deposit_fee_rate]" type="number" value="{$row.deposit_fee_rate|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Withdraw_fee_rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-withdraw_fee_rate" data-rule="required" class="form-control" step="0.01" name="row[withdraw_fee_rate]" type="number" value="{$row.withdraw_fee_rate|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Api_fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-api_fee" class="form-control selectpicker" multiple name="row[api_fee][]">
                {foreach name="gameProviderList" item="provider"}
                <option value="{$provider.id}" {in name="provider.id" value="$row.api_fee"}selected{/in}>{$provider.name}</option>
                {/foreach}
            </select>
            <span class="help-block">{:implode(', ', array_column($gameProviderList, 'name'))}</span>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control" name="row[remark]" rows="5">{$row.remark|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_active')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-is_active" data-rule="required" class="form-control selectpicker" name="row[is_active]">
                <option value="1" {if condition="$row.is_active eq 1"}selected{/if}>{:__('Has_active')}</option>
                <option value="0" {if condition="$row.is_active eq 0"}selected{/if}>{:__('No_active')}</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Allow_withdrawal')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-allow_withdrawal" data-rule="required" class="form-control selectpicker" name="row[allow_withdrawal]">
                <option value="1" {if condition="$row.allow_withdrawal eq 1"}selected{/if}>{:__('Has_active')}</option>
                <option value="0" {if condition="$row.allow_withdrawal eq 0"}selected{/if}>{:__('No_active')}</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Allow_blacklist')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-allow_blacklist" data-rule="required" class="form-control selectpicker" name="row[allow_blacklist]">
                <option value="1" {if condition="$row.allow_blacklist eq 1"}selected{/if}>{:__('Has_active')}</option>
                <option value="0" {if condition="$row.allow_blacklist eq 0"}selected{/if}>{:__('No_active')}</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Allow_hidden_player')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-allow_hidden_player" data-rule="required" class="form-control selectpicker" name="row[allow_hidden_player]">
                <option value="1" {if condition="$row.allow_hidden_player eq 1"}selected{/if}>{:__('Has_active')}</option>
                <option value="0" {if condition="$row.allow_hidden_player eq 0"}selected{/if}>{:__('No_active')}</option>
            </select>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form> 