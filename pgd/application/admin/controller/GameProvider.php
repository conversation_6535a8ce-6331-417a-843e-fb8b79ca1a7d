<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\index\model\GameProvider as GameProviderModel;

class GameProvider extends Backend
{
    /**
     * 刷新游戏提供商JSON数据
     */
    public function refreshJson()
    {
        $result = GameProviderModel::updateGameProvidersJson();
        
        if (isset($result['success']) && $result['success']) {
            $this->success('刷新成功', null, $result);
        } else {
            $this->error($result['error'] ?? '刷新失败');
        }
    }
} 