<?php

namespace app\admin\controller\players;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 玩家打码设置
 *
 * @icon fa fa-money
 */
class BettingSetting extends Backend
{
    protected $model = null;
    protected $noNeedRight = ['index', 'update'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\players\Players;
    }

    /**
     * 查看
     */
    public function index()
    {
        $player_id = $this->request->param('player_id', 0, 'intval');
        if (!$player_id) {
            $this->error(__('Parameter %s can not be empty', 'player_id'));
        }

        // 获取玩家信息
        $player = $this->model->get($player_id);
        if (!$player) {
            $this->error(__('Player not found'));
        }

        // 获取玩家打码任务信息
        $bettingTask = Db::table('player_betting_tasks')
            ->where('player_id', $player_id)
            ->find();

        // 如果没有打码任务记录，创建一个空对象
        if (!$bettingTask) {
            $bettingTask = [
                'player_id' => $player_id,
                'required_betting_amount' => 0,
                'completed_betting_amount' => 0
            ];
        }

        $this->view->assign('player', $player);
        $this->view->assign('bettingTask', $bettingTask);

        return $this->view->fetch();
    }

    /**
     * 更新打码任务
     */
    public function update()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();

            // 验证参数
            if (!isset($params['player_id']) || !$params['player_id']) {
                $this->error(__('Parameter %s can not be empty', 'player_id'));
            }

            if (!isset($params['amount']) || !is_numeric($params['amount']) || $params['amount'] == 0) {
                $this->error(__('Please enter a valid amount'));
            }

            $player_id = $params['player_id'];
            $amount = floatval($params['amount']);
            $remark = $params['remark'] ?? '管理员手动调整';

            try {
                // 开始事务
                Db::startTrans();
                
                // 查询玩家是否已有打码任务记录
                $bettingTask = Db::table('player_betting_tasks')
                    ->where('player_id', $player_id)
                    ->find();

                if ($bettingTask) {
                    // 更新现有记录
                    $newRequiredAmount = $bettingTask['required_betting_amount'] + $amount;
                    
                    // 确保打码任务量不会变成负数
                    if ($newRequiredAmount < 0) {
                        $this->error(__('Betting amount cannot be negative'));
                    }
                    
                    Db::table('player_betting_tasks')
                        ->where('player_id', $player_id)
                        ->update([
                            'required_betting_amount' => $newRequiredAmount,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                } else {
                    // 创建新记录
                    if ($amount < 0) {
                        $this->error(__('Betting amount cannot be negative for new record'));
                    }
                    
                    Db::table('player_betting_tasks')
                        ->insert([
                            'player_id' => $player_id,
                            'required_betting_amount' => $amount,
                            'completed_betting_amount' => 0,
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                }
                
                // 提交事务
                Db::commit();
                
                // 返回成功信息
                $this->success(__('Betting amount updated successfully'));
            } catch (Exception $e) {
                // 回滚事务
                Db::rollback();
                $this->error($e->getMessage());
            }
        }
        $this->error(__('Invalid request'));
    }
}
