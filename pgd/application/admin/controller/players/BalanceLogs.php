<?php

namespace app\admin\controller\players;

use app\common\controller\Backend;
use think\Db;
use think\Model;
use fast\Tree;

/**
 * 玩家余额变动日志
 *
 * @icon fa fa-money
 */
class BalanceLogs extends Backend
{
    /**
     * BalanceLogs模型对象
     * @var \app\admin\model\players\BalanceLogs
     */
    protected $model = null;
    protected $searchFields = 'player_id,remark';
    protected $relationSearch = true;

    /**
     * 禁用自动数据过滤
     * 我们将在index方法中手动实现过滤
     */
    protected function applyDataFilter($query, $tableAlias = '')
    {
        // 不应用自动数据过滤
        return $query;
    }

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\players\BalanceLogs;

        // 获取余额变动类型列表
        $transactionTypeList = \app\admin\model\players\BalanceChangeTypes::column('name', 'id');
        $this->view->assign('transactionTypeList', $transactionTypeList);

        // 获取余额类型列表
        $balanceTypeList = [
            1 => __('Account balance'),
            2 => __('Reward balance')
        ];
        $this->view->assign('balanceTypeList', $balanceTypeList);
    }

    /**
     * 查看
     */
    public function index()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);

        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 获取当前用户角色组ID
            $roleGroupId = $this->getCurrentRoleGroupId();
            $query = $this->model->with(['player', 'transactionType']);

            // 手动应用业务员角色的过滤条件
            if ($roleGroupId == 6) { // 业务员角色
                $agentId = $this->getCurrentAgentId();
                if ($agentId) {
                    // 查询该业务员下的所有玩家ID
                    $playerIds = Db::table('players')
                        ->where('agent_id', $agentId)
                        ->column('id');

                    if (!empty($playerIds)) {
                        $query->where('player_id', 'in', $playerIds);
                    } else {
                        // 如果没有玩家，确保不返回任何数据
                        $query->where('player_id', -1);
                    }
                }
            } elseif ($roleGroupId == 4) { // 渠道角色
                $channelId = $this->getCurrentChannelId();
                if ($channelId) {
                    // 查询该渠道下的所有玩家ID
                    $playerIds = Db::table('players')
                        ->where('channel_id', $channelId)
                        ->column('id');

                    if (!empty($playerIds)) {
                        $query->where('player_id', 'in', $playerIds);
                    } else {
                        // 如果没有玩家，确保不返回任何数据
                        $query->where('player_id', -1);
                    }
                }
            }

            // 应用原始的where条件
            $list = $query->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            // 处理数据，添加交易类型文本
            foreach ($list->items() as &$item) {
                if (isset($item['transaction_type']) && isset($item['transactionType']['name'])) {
                    $item['transaction_type_text'] = $item['transactionType']['name'];
                }
            }

            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 获取交易类型列表
     */
    public function transactionTypeList()
    {
        $transactionTypeList = \app\admin\model\players\BalanceChangeTypes::column('name', 'id');
        return json($transactionTypeList);
    }

    /**
     * 重写selectpage方法，添加基于角色的数据过滤
     */
    protected function selectpage()
    {
        //设置过滤方法
        $this->request->filter(['trim', 'strip_tags', 'htmlspecialchars']);

        //搜索关键词,客户端输入以空格分开,这里接收为数组
        $word = (array)$this->request->request("q_word/a");
        //当前页
        $page = $this->request->request("pageNumber");
        //分页大小
        $pagesize = $this->request->request("pageSize");
        //搜索条件
        $andor = $this->request->request("andOr", "and", "strtoupper");
        //排序方式
        $orderby = (array)$this->request->request("orderBy/a");
        //显示的字段
        $field = $this->request->request("showField");
        //主键
        $primarykey = $this->request->request("keyField");
        //主键值
        $primaryvalue = $this->request->request("keyValue");
        //搜索字段
        $searchfield = (array)$this->request->request("searchField/a");
        //自定义搜索条件
        $custom = (array)$this->request->request("custom/a");
        //是否返回树形结构
        $istree = $this->request->request("isTree", 0);
        $ishtml = $this->request->request("isHtml", 0);
        if ($istree) {
            $word = [];
            $pagesize = 999999;
        }
        $order = [];
        foreach ($orderby as $k => $v) {
            $order[$v[0]] = $v[1];
        }
        $field = $field ? $field : 'name';

        //如果有primaryvalue,说明当前是初始化传值
        if ($primaryvalue !== null) {
            $where = [$primarykey => ['in', $primaryvalue]];
            $pagesize = 999999;
        } else {
            $where = function ($query) use ($word, $andor, $field, $searchfield, $custom) {
                $logic = $andor == 'AND' ? '&' : '|';
                $searchfield = is_array($searchfield) ? implode($logic, $searchfield) : $searchfield;
                $searchfield = str_replace(',', $logic, $searchfield);
                $word = array_filter(array_unique($word));
                if (count($word) == 1) {
                    $query->where($searchfield, "like", "%" . reset($word) . "%");
                } else {
                    $query->where(function ($query) use ($word, $searchfield) {
                        foreach ($word as $index => $item) {
                            $query->whereOr(function ($query) use ($item, $searchfield) {
                                $query->where($searchfield, "like", "%{$item}%");
                            });
                        }
                    });
                }
                if ($custom && is_array($custom)) {
                    foreach ($custom as $k => $v) {
                        if (is_array($v) && 2 == count($v)) {
                            $query->where($k, trim($v[0]), $v[1]);
                        } else {
                            $query->where($k, '=', $v);
                        }
                    }
                }
            };
        }

        // 获取当前用户角色组ID
        $roleGroupId = $this->getCurrentRoleGroupId();
        $query = $this->model;

        // 手动应用业务员角色的过滤条件
        if ($roleGroupId == 6) { // 业务员角色
            $agentId = $this->getCurrentAgentId();
            if ($agentId) {
                // 查询该业务员下的所有玩家ID
                $playerIds = Db::table('players')
                    ->where('agent_id', $agentId)
                    ->column('id');

                if (!empty($playerIds)) {
                    $query->where('player_id', 'in', $playerIds);
                } else {
                    // 如果没有玩家，确保不返回任何数据
                    $query->where('player_id', -1);
                }
            }
        } elseif ($roleGroupId == 4) { // 渠道角色
            $channelId = $this->getCurrentChannelId();
            if ($channelId) {
                // 查询该渠道下的所有玩家ID
                $playerIds = Db::table('players')
                    ->where('channel_id', $channelId)
                    ->column('id');

                if (!empty($playerIds)) {
                    $query->where('player_id', 'in', $playerIds);
                } else {
                    // 如果没有玩家，确保不返回任何数据
                    $query->where('player_id', -1);
                }
            }
        }

        $list = [];
        $total = $query->where($where)->count();
        if ($total > 0) {
            $datalist = $query
                ->where($where)
                ->order($order)
                ->page($page, $pagesize)
                ->select();

            foreach ($datalist as $index => $item) {
                unset($item['password'], $item['salt']);
                if ($this->selectpageFields == '*') {
                    $result = [
                        $primarykey => isset($item[$primarykey]) ? $item[$primarykey] : '',
                        $field      => isset($item[$field]) ? $item[$field] : '',
                    ];
                } else {
                    $result = array_intersect_key(($item instanceof Model ? $item->toArray() : (array)$item), array_flip(explode(',', $this->selectpageFields)));
                }
                $result['pid'] = isset($item['pid']) ? $item['pid'] : (isset($item['parent_id']) ? $item['parent_id'] : 0);
                $result = array_map("htmlspecialchars", $result);
                $list[] = $result;
            }
            if ($istree && !$primaryvalue) {
                $tree = Tree::instance();
                $tree->init(collection($list)->toArray(), 'pid');
                $list = $tree->getTreeList($tree->getTreeArray(0), $field);
                if (!$ishtml) {
                    foreach ($list as &$item) {
                        $item = str_replace('&nbsp;', ' ', $item);
                    }
                    unset($item);
                }
            }
        }
        //这里一定要返回有list这个字段,total是可选的,如果total<=list的数量,则会隐藏分页按钮
        return json(['list' => $list, 'total' => $total]);
    }
}
