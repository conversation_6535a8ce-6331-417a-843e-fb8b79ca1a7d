<?php

namespace app\admin\controller\players;

use app\common\controller\Backend;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;

/**
 * 玩家标签管理
 *
 * @icon fa fa-tags
 */
class PlayerTags extends Backend
{
    protected $model = null;
    protected $searchFields = 'id,name';
    protected $noNeedRight = ['index'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\players\PlayerTags;
    }

    /**
     * 查看
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            [$where, $sort, $order, $offset, $limit] = $this->buildparams();
            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            $result = ['total' => $list->total(), 'rows' => $list->items()];
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 获取标签列表
     */
    public function getList()
    {
        return json($this->model->getTagList());
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post('row/a');
            if ($params) {
                $params = $this->preExcludeFields($params);

                // 验证标签名称长度
                if (mb_strlen($params['name']) > 10) {
                    $this->error(__('Tag name too long'));
                }
                
                // 验证备注长度
                if (isset($params['remarks']) && mb_strlen($params['remarks']) > 10) {
                    $this->error(__('Remarks too long'));
                }

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    // 检查标签名称是否已存在
                    $exists = $this->model->where('name', $params['name'])->find();
                    if ($exists) {
                        throw new Exception(__('Tag name exists'));
                    }
                    
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                        $this->error(__('Tag name exists'));
                    } else {
                        $this->error($e->getMessage());
                    }
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post('row/a');
            if ($params) {
                $params = $this->preExcludeFields($params);
                
                // 验证标签名称长度
                if (mb_strlen($params['name']) > 10) {
                    $this->error(__('Tag name too long'));
                }
                
                // 验证备注长度
                if (isset($params['remarks']) && mb_strlen($params['remarks']) > 10) {
                    $this->error(__('Remarks too long'));
                }
                
                $result = false;
                Db::startTrans();
                try {
                    // 检查标签名称是否已存在（排除当前记录）
                    $exists = $this->model->where('name', $params['name'])->where('id', '<>', $ids)->find();
                    if ($exists) {
                        throw new Exception(__('Tag name exists'));
                    }
                    
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                        $this->error(__('Tag name exists'));
                    } else {
                        $this->error($e->getMessage());
                    }
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign('row', $row);
        return $this->view->fetch();
    }

    /**
     * 删除
     */
    public function del($ids = '')
    {
        if ($ids) {
            $pk = $this->model->getPk();
            $adminIds = $this->getDataLimitAdminIds();
            if (is_array($adminIds)) {
                $this->model->where($this->dataLimitField, 'in', $adminIds);
            }
            $list = $this->model->where($pk, 'in', $ids)->select();
            $count = 0;
            Db::startTrans();
            try {
                foreach ($list as $item) {
                    $count += $item->delete();
                }
                Db::commit();
            } catch (PDOException|Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($count) {
                $this->success();
            } else {
                $this->error(__('Parameter %s can not be empty', 'ids'));
            }
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }
}
