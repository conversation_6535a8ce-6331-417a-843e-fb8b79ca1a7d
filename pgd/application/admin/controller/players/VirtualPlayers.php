<?php

namespace app\admin\controller\players;

use app\common\controller\Backend;
use think\Db;
use think\exception\ValidateException;
use think\exception\PDOException;
use think\Exception;

/**
 * 虚拟玩家管理
 *
 * @icon fa fa-circle-o
 */
class VirtualPlayers extends Backend
{

    /**
     * VirtualPlayers模型对象
     * @var \app\admin\model\players\VirtualPlayers
     */
    protected $model = null;

    /**
     * 无需鉴权的方法,但需要登录
     * @var array
     */
    protected $noNeedRight = ['index', 'player_detail', 'add', 'edit', 'del'];

    /**
     * 禁用数据限制功能
     * 因为virtual_players表没有agent_id字段，我们需要手动实现过滤
     */
    protected $dataLimit = false;

    /**
     * 重写applyDataFilter方法，避免使用通用的数据过滤
     * 我们将在index方法中手动调用applyRoleDataFilter方法
     */
    protected function applyDataFilter($query, $tableAlias = '')
    {
        // 不执行任何过滤，避免使用不存在的agent_id字段
        return $query;
    }

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\players\VirtualPlayers;
        // 获取渠道列表
        $channelList = \app\admin\model\channels\Channels::getChannelList(false);
        $this->view->assign('channelList', $channelList);
    }

    /**
     * 查看虚拟玩家列表
     *
     * @return string|json
     */
    public function index()
    {
        // 启用关联搜索
        $this->relationSearch = true;

        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);

        // 如果不是Ajax请求，直接返回视图
        if (!$this->request->isAjax()) {
            return $this->view->fetch();
        }

        // 如果是Selectpage请求，则转发到selectpage方法
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }

        // 使用buildparams方法获取查询参数
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();

        // 计算页码
        $page = $limit ? intval($offset / $limit) + 1 : 1;

        // 初始化查询构造器并关联渠道表
        $query = $this->model->with('channel')->where($where);

        // 应用基于角色的数据过滤
        $this->applyRoleDataFilter($query);

        // 克隆查询对象，以便不影响后续的数据查询
        $countQuery = clone $query;

        // 获取总记录数（确保只计算符合条件的记录）
        $total = $countQuery->removeOption('order')->removeOption('limit')->removeOption('page')->count();

        // 执行查询并分页
        $list = $query->order($sort, $order)->page($page, $limit)->select();

        // 返回结果
        $result = ["total" => $total, "rows" => $list];
        return json($result);
    }

    /**
     * 根据用户角色应用数据过滤条件
     *
     * @param \think\db\Query $query 查询构造器
     * @return void
     */
    protected function applyRoleDataFilter($query)
    {
        // 获取当前用户角色组ID
        $roleGroupId = $this->getCurrentRoleGroupId();

        // 根据角色类型应用不同的过滤条件
        if ($roleGroupId == 4) { // 渠道角色
            $channelId = $this->getCurrentChannelId();
            if ($channelId) {
                $query->where('virtual_players.channel_id', $channelId);
            }
        } elseif ($roleGroupId == 6) { // 业务员角色
            // 获取业务员所属的渠道ID
            $agentId = $this->getCurrentAgentId();
            if ($agentId) {
                $agent = \think\Db::table('agents')
                    ->where('id', $agentId)
                    ->field('channel_id')
                    ->find();

                if ($agent && isset($agent['channel_id'])) {
                    $query->where('virtual_players.channel_id', $agent['channel_id']);
                } else {
                    // 如果找不到渠道ID，确保不返回任何数据
                    $query->where('virtual_players.channel_id', -1);
                }
            }
        }
    }

    /**
     * 重写selectpage方法，避免使用不存在的agent_id字段
     */
    protected function selectpage()
    {
        //设置过滤方法
        $this->request->filter(['trim', 'strip_tags', 'htmlspecialchars']);

        //搜索关键词,客户端输入以空格分开,这里接收为数组
        $word = (array)$this->request->request("q_word/a");
        //当前页
        $page = $this->request->request("pageNumber");
        //分页大小
        $pagesize = $this->request->request("pageSize");
        //搜索条件
        $andor = $this->request->request("andOr", "and", "strtoupper");
        //排序方式
        $orderby = (array)$this->request->request("orderBy/a");
        //显示的字段
        $field = $this->request->request("showField");
        //主键
        $primarykey = $this->request->request("keyField");
        //主键值
        $primaryvalue = $this->request->request("keyValue");
        //搜索字段
        $searchfield = (array)$this->request->request("searchField/a");
        //自定义搜索条件
        $custom = (array)$this->request->request("custom/a");
        //是否返回树形结构
        $istree = $this->request->request("isTree", 0);
        if ($istree) {
            $word = [];
            $pagesize = 999999;
        }
        $order = [];
        foreach ($orderby as $v) {
            $order[$v[0]] = $v[1];
        }
        $field = $field ? $field : 'name';

        //如果有primaryvalue,说明当前是初始化传值
        if ($primaryvalue !== null) {
            $where = [$primarykey => ['in', $primaryvalue]];
            $pagesize = 999999;
        } else {
            $where = function ($query) use ($word, $andor, $searchfield, $custom) {
                $logic = $andor == 'AND' ? '&' : '|';
                $searchfield = is_array($searchfield) ? implode($logic, $searchfield) : $searchfield;
                $searchfield = str_replace(',', $logic, $searchfield);
                $word = array_filter(array_unique($word));
                if (count($word) == 1) {
                    $query->where($searchfield, "like", "%" . reset($word) . "%");
                } else {
                    $query->where(function ($query) use ($word, $searchfield) {
                        foreach ($word as $item) {
                            $query->whereOr(function ($query) use ($item, $searchfield) {
                                $query->where($searchfield, "like", "%{$item}%");
                            });
                        }
                    });
                }
                if ($custom && is_array($custom)) {
                    foreach ($custom as $k => $v) {
                        if (is_array($v) && 2 == count($v)) {
                            $query->where($k, trim($v[0]), $v[1]);
                        } else {
                            $query->where($k, '=', $v);
                        }
                    }
                }
            };
        }

        $query = $this->model->where($where);

        // 应用我们自定义的基于角色的数据过滤
        $this->applyRoleDataFilter($query);

        $list = [];
        $total = $query->count();
        if ($total > 0) {
            if ($istree) {
                $datalist = $query->order($order)->select();
                $list = $datalist;
            } else {
                $datalist = $query->page($page, $pagesize)->order($order)->select();
                foreach ($datalist as $item) {
                    unset($item['password'], $item['salt']);
                    $list[] = [
                        $primarykey => isset($item[$primarykey]) ? $item[$primarykey] : '',
                        $field      => isset($item[$field]) ? $item[$field] : '',
                    ];
                }
            }
        }
        //这里一定要返回有list这个字段,total是可选的,如果total<=list的数量,则会隐藏分页按钮
        return json(['list' => $list, 'total' => $total]);
    }

    /**
     * 添加
     */
    public function add()
    {
        // 获取当前用户角色组ID
        $roleGroupId = $this->getCurrentRoleGroupId();

        // 如果是渠道角色，获取其渠道ID并预设到视图中
        if ($roleGroupId == 4) {
            $channelId = $this->getCurrentChannelId();
            if ($channelId) {
                // 获取渠道信息
                $channel = \think\Db::table('channels')
                    ->where('id', $channelId)
                    ->field('id, name')
                    ->find();

                if ($channel) {
                    // 直接设置渠道ID和名称，不需要列表
                    $this->view->assign('channelId', $channel['id']);
                    $this->view->assign('channelName', $channel['name']);
                    $this->view->assign('isChannel', true); // 标记为渠道角色
                }
            }
        }
        // 如果是业务员角色，获取其所属渠道ID并预设到视图中
        elseif ($roleGroupId == 6) {
            $agentId = $this->getCurrentAgentId();
            if ($agentId) {
                $agent = \think\Db::table('agents')
                    ->where('id', $agentId)
                    ->field('channel_id')
                    ->find();

                if ($agent && isset($agent['channel_id'])) {
                    // 获取渠道信息
                    $channel = \think\Db::table('channels')
                        ->where('id', $agent['channel_id'])
                        ->field('id, name')
                        ->find();

                    if ($channel) {
                        // 直接设置渠道ID和名称，不需要列表
                        $this->view->assign('channelId', $channel['id']);
                        $this->view->assign('channelName', $channel['name']);
                        $this->view->assign('isAgent', true); // 标记为业务员角色
                    }
                }
            }
        }

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                // 获取当前用户角色组ID
                $roleGroupId = $this->getCurrentRoleGroupId();

                // 如果是渠道角色，强制使用当前渠道ID
                if ($roleGroupId == 4) {
                    $channelId = $this->getCurrentChannelId();
                    if ($channelId) {
                        $params['channel_id'] = $channelId;
                    }
                }
                // 如果是业务员角色，强制使用业务员所属渠道ID
                elseif ($roleGroupId == 6) {
                    $agentId = $this->getCurrentAgentId();
                    if ($agentId) {
                        $agent = \think\Db::table('agents')
                            ->where('id', $agentId)
                            ->field('channel_id')
                            ->find();

                        if ($agent && isset($agent['channel_id'])) {
                            $params['channel_id'] = $agent['channel_id'];
                        }
                    }
                }

                if (empty($params['channel_id'])) {
                    $this->error(__('Parameter %s can not be empty', __('Channel_id')));
                }

                $generateCount = intval($params['generate_count'] ?? 1);
                if ($generateCount < 1) {
                    $generateCount = 1;
                }

                $now = date('Y-m-d H:i:s');
                $defaultData = [
                    'channel_id' => $params['channel_id'],
                    'balance' => floatval($params['balance'] ?? 0),
                    'password' => '123456',  // 默认密码
                    'is_active' => 1,  // 默认状态为激活
                    'join_time' => $now,
                    'login_ip' => $this->request->ip(),
                    'updated_at' => $now
                ];

                Db::startTrans();
                try {
                    $insertData = [];
                    // 获取最大ID
                    $maxId = $this->model->max('id') ?: 0;

                    // 如果表为空，获取AUTO_INCREMENT值
                    if ($maxId == 0) {
                        // 获取表的AUTO_INCREMENT值
                        $autoIncrementResult = Db::query("SELECT `AUTO_INCREMENT` FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'virtual_players'");
                        if (!empty($autoIncrementResult) && isset($autoIncrementResult[0]['AUTO_INCREMENT'])) {
                            $maxId = intval($autoIncrementResult[0]['AUTO_INCREMENT']) - 1;
                        }
                    }

                    for ($i = 1; $i <= $generateCount; $i++) {
                        $currentId = $maxId + $i;
                        $data = array_merge($defaultData, [
                            'username' => (string)$currentId,
                            'phone_number' => (string)$currentId
                        ]);
                        $insertData[] = $data;
                    }

                    $this->model->insertAll($insertData);
                    Db::commit();
                    $this->success();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 获取当前用户角色组ID
        $roleGroupId = $this->getCurrentRoleGroupId();

        // 如果是渠道角色，检查是否有权限编辑该虚拟玩家
        if ($roleGroupId == 4) {
            $channelId = $this->getCurrentChannelId();
            if ($channelId && $row['channel_id'] != $channelId) {
                $this->error(__('You have no permission'));
            }

            // 直接设置渠道ID和名称，不需要列表
            if ($channelId) {
                $channel = \think\Db::table('channels')
                    ->where('id', $channelId)
                    ->field('id, name')
                    ->find();

                if ($channel) {
                    $this->view->assign('channelId', $channel['id']);
                    $this->view->assign('channelName', $channel['name']);
                    $this->view->assign('isChannel', true); // 标记为渠道角色
                }
            }
        }
        // 如果是业务员角色，检查是否有权限编辑该虚拟玩家
        elseif ($roleGroupId == 6) {
            $agentId = $this->getCurrentAgentId();
            if ($agentId) {
                $agent = \think\Db::table('agents')
                    ->where('id', $agentId)
                    ->field('channel_id')
                    ->find();

                if (!$agent || !isset($agent['channel_id']) || $row['channel_id'] != $agent['channel_id']) {
                    $this->error(__('You have no permission'));
                }

                // 直接设置渠道ID和名称，不需要列表
                if ($agent && isset($agent['channel_id'])) {
                    $channel = \think\Db::table('channels')
                        ->where('id', $agent['channel_id'])
                        ->field('id, name')
                        ->find();

                    if ($channel) {
                        $this->view->assign('channelId', $channel['id']);
                        $this->view->assign('channelName', $channel['name']);
                        $this->view->assign('isAgent', true); // 标记为业务员角色
                    }
                }
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    $params['updated_at'] = date('Y-m-d H:i:s');
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if ($ids) {
            // 获取当前用户角色组ID
            $roleGroupId = $this->getCurrentRoleGroupId();

            // 查询要删除的虚拟玩家记录
            $list = $this->model->where('id', 'in', $ids)->select();

            // 如果没有找到记录
            if (empty($list)) {
                $this->error(__('No Records were found'));
            }

            // 如果是渠道角色，检查是否有权限删除这些虚拟玩家
            if ($roleGroupId == 4) {
                $channelId = $this->getCurrentChannelId();
                if ($channelId) {
                    foreach ($list as $item) {
                        if ($item['channel_id'] != $channelId) {
                            $this->error(__('You have no permission to delete virtual player ID: ') . $item['id']);
                        }
                    }
                }
            }
            // 如果是业务员角色，检查是否有权限删除这些虚拟玩家
            elseif ($roleGroupId == 6) {
                $agentId = $this->getCurrentAgentId();
                if ($agentId) {
                    $agent = \think\Db::table('agents')
                        ->where('id', $agentId)
                        ->field('channel_id')
                        ->find();

                    if ($agent && isset($agent['channel_id'])) {
                        foreach ($list as $item) {
                            if ($item['channel_id'] != $agent['channel_id']) {
                                $this->error(__('You have no permission to delete virtual player ID: ') . $item['id']);
                            }
                        }
                    } else {
                        $this->error(__('You have no permission - agent has no channel'));
                    }
                } else {
                    $this->error(__('You have no permission - agent ID not found'));
                }
            }

            $count = 0;
            Db::startTrans();
            try {
                foreach ($list as $item) {
                    $count += $item->delete();
                }
                Db::commit();
            } catch (PDOException|Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($count) {
                $this->success();
            } else {
                $this->error(__('No rows were deleted'));
            }
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }


}
