<?php

namespace app\admin\controller\players;

use app\admin\service\BalanceService;
use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 玩家余额变更管理
 *
 * @icon fa fa-money
 */
class BalanceChange extends Backend
{
    protected $model = null;
    protected $noNeedRight = ['index', 'change']; // 在方法内部自行处理权限检查

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\players\Players;
    }

    /**
     * 查看
     */
    public function index()
    {
        $player_id = $this->request->param('player_id', 0, 'intval');
        if (!$player_id) {
            $this->error(__('Parameter %s can not be empty', 'player_id'));
        }

        // 获取玩家信息
        $player = $this->model->get($player_id);
        if (!$player) {
            $this->error(__('Player not found'));
        }

        // 变更类型已固定为"GM操作增减金币"，不再需要获取类型列表

        $this->view->assign('player', $player);

        return $this->view->fetch();
    }

    /**
     * 执行余额变更
     */
    public function change()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();

            // 验证参数
            if (!isset($params['player_id']) || !$params['player_id']) {
                $this->error(__('Parameter %s can not be empty', 'player_id'));
            }

            if (!isset($params['amount']) || !is_numeric($params['amount']) || $params['amount'] == 0) {
                $this->error(__('Please enter a valid amount'));
            }

            // 获取玩家信息
            $player = $this->model->get($params['player_id']);
            if (!$player) {
                $this->error(__('Player not found'));
            }

            // 获取当前用户所属的权限组ID
            $authGroupIds = $this->auth->getGroupIds();
            $groupId = $authGroupIds[0] ?? 0; // 获取第一个权限组ID

            // 检查权限：如果不是管理员(id=1)或平台管理员(id=2)，则需要验证玩家归属关系
            if ($groupId != 1 && $groupId != 2) {
                // 如果是渠道(id=4)，则只能操作属于自己渠道的玩家
                if ($groupId == 4) {
                    // 获取当前渠道管理员关联的渠道ID
                    $channelId = Db::table('channels')->where('admin_id', $this->auth->id)->value('id');

                    // 检查玩家是否属于该渠道
                    if (!$channelId || $player['channel_id'] != $channelId) {
                        $this->error(__('You have no permission to change balance of this player'));
                    }
                }
                // 如果是业务员(id=6)，则只能操作属于自己的玩家
                elseif ($groupId == 6) {
                    // 获取当前业务员关联的业务员ID
                    $agentId = Db::table('agents')->where('admin_id', $this->auth->id)->value('id');

                    // 检查玩家是否属于该业务员
                    if (!$agentId || $player['agent_id'] != $agentId) {
                        $this->error(__('You have no permission to change balance of this player'));
                    }
                }
                // 其他角色无权操作
                else {
                    $this->error(__('You have no permission to change balance'));
                }
            }

            // 变更类型已固定为"GM操作增减金币"，不再需要验证
            // 确保change_type值为2（GM操作增减金币）
            $params['change_type'] = 2;

            try {
                // 确定调整类型（上分或下分）
                $amount = floatval($params['amount']);
                $adjustmentType = ($amount > 0) ? 1 : 2; // 1-上分 2-下分
                $absAmount = abs($amount);

                // 如果是上分操作且玩家属于业务员，检查业务员是否有足够的游戏额度
                if ($adjustmentType == 1 && !empty($player['agent_id'])) {
                    // 检查业务员是否有足够的游戏额度
                    $hasEnoughCredit = \app\admin\model\agents_config\Agents::hasEnoughGameCredit(
                        $player['agent_id'],
                        $absAmount
                    );

                    if (!$hasEnoughCredit) {
                        $this->error(__('Agent game credit is insufficient for balance increase'));
                    }
                }

                // 开始事务
                Db::startTrans();

                // 确定操作人类型
                $operatorType = 0; // 默认为0-管理员
                if ($groupId == 4) {
                    $operatorType = 1; // 1-渠道
                } elseif ($groupId == 6) {
                    $operatorType = 2; // 2-业务员
                }

                // 使用BalanceService处理余额变更
                $result = BalanceService::change(
                    $params['player_id'],
                    $params['amount'],
                    $params['change_type'],
                    $params['remark'] ?? '后台直接操作',
                    $this->auth->username,
                    $operatorType
                );

                // 如果是上分操作且玩家属于业务员，更新业务员已使用游戏额度
                if ($adjustmentType == 1 && !empty($player['agent_id'])) {
                    // 更新业务员已使用游戏额度
                    $updateResult = \app\admin\model\agents_config\Agents::updateUsedGameCredit(
                        $player['agent_id'],
                        $absAmount // 使用正数表示增加已使用额度
                    );

                    if (!$updateResult) {
                        \think\Log::error('更新业务员已使用游戏额度失败，业务员ID: ' . $player['agent_id'] . '，玩家ID: ' . $player['id'] . '，金额: ' . $absAmount);
                    }
                }

                // 在gm_balance_adjustments表中添加记录，状态直接设为已审核
                Db::table('gm_balance_adjustments')->insert([
                    'player_id' => $params['player_id'],
                    'amount' => $absAmount, // 使用绝对值
                    'adjustment_type' => $adjustmentType,
                    'status' => 1, // 直接设置为已审核
                    'operator_type' => $operatorType, // 根据用户角色设置
                    'operator_name' => $this->auth->username,
                    'remark' => $params['remark'] ?? '后台直接操作',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                // 提交事务
                Db::commit();

                $this->success(__('Balance changed successfully'), null, $result);
            } catch (Exception $e) {
                // 回滚事务
                Db::rollback();
                $this->error($e->getMessage());
            }
        }

        $this->error(__('Invalid request'));
    }
}
