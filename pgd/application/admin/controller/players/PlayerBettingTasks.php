<?php

namespace app\admin\controller\players;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 玩家打码任务管理
 *
 * @icon fa fa-money
 */
class PlayerBettingTasks extends Backend
{
    /**
     * PlayerBettingTasks模型对象
     * @var \app\admin\model\players\PlayerBettingTasks
     */
    protected $model = null;
    protected $searchFields = 'player_id';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\players\PlayerBettingTasks;
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 获取所有符合条件的数据（不分页）
            $query = $this->model
                ->with(['player'])
                ->where($where);

            // 如果是按完成比排序，则获取所有数据并手动排序
            if ($sort == 'completion_percentage' || ($sort == 'id' && $order == 'desc')) {
                // 获取所有数据
                $allItems = $query->select();

                // 按完成比从大到小排序，但将已完成的（进度100%）放到最后
                usort($allItems, function($a, $b) {
                    $a_percentage = ($a['required_betting_amount'] > 0) ?
                        ($a['completed_betting_amount'] / $a['required_betting_amount']) : 0;
                    $b_percentage = ($b['required_betting_amount'] > 0) ?
                        ($b['completed_betting_amount'] / $b['required_betting_amount']) : 0;

                    // 检查是否有任务已完成（进度100%）
                    $a_completed = $a_percentage >= 1;
                    $b_completed = $b_percentage >= 1;

                    // 如果一个已完成而另一个未完成，将已完成的放到后面
                    if ($a_completed && !$b_completed) {
                        return 1; // a已完成，放到后面
                    }
                    if (!$a_completed && $b_completed) {
                        return -1; // b已完成，放到后面
                    }

                    // 如果两者都已完成或都未完成，则按照完成比例降序排列
                    if ($a_percentage == $b_percentage) {
                        return 0;
                    }

                    // 降序排列
                    return ($a_percentage > $b_percentage) ? -1 : 1;
                });

                // 计算总数
                $total = count($allItems);

                // 手动分页
                $items = array_slice($allItems, $offset, $limit);
            } else {
                // 使用数据库排序和分页
                $list = $query->order($sort, $order)->paginate($limit);
                $items = $list->items();
                $total = $list->total();
            }

            $result = array("total" => $total, "rows" => $items);
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 清空打码量
     */
    public function reset_betting($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        Db::startTrans();
        try {
            // 将已完成打码量设置为0
            $row->completed_betting_amount = 0;
            $row->save();

            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }

        $this->success(__('Reset betting amount successfully'));
    }

    /**
     * 批量清空打码量
     */
    public function batch_reset()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post('params', '');
            if (empty($params)) {
                $this->error(__('Parameter %s can not be empty', 'params'));
            }

            $params = json_decode($params, true);
            if (!isset($params['ids'])) {
                $this->error(__('Parameter %s can not be empty', 'ids'));
            }

            $ids = $params['ids'];

            Db::startTrans();
            try {
                // 将已完成打码量设置为0
                $this->model->where('id', 'in', $ids)->update(['completed_betting_amount' => 0]);

                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }

            $this->success(__('Batch reset betting amount successfully'));
        }
        $this->error(__('Invalid parameters'));
    }
}