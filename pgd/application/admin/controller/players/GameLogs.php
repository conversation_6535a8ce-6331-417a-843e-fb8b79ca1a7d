<?php

namespace app\admin\controller\players;

use app\common\controller\Backend;
use think\Db;

/**
 * 玩家游戏日志
 *
 * @icon fa fa-gamepad
 */
class GameLogs extends Backend
{
    /**
     * GameLogs模型对象
     * @var \app\admin\model\players\GameLogs
     */
    protected $model = null;
    protected $searchFields = 'player_id,bet_id';
    protected $relationSearch = true;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\players\GameLogs;
    }

    /**
     * 应用基于角色的数据过滤
     * @param \think\db\Query $query 查询构造器
     * @return \think\db\Query 应用过滤条件后的查询构造器
     */
    protected function applyRoleDataFilter($query)
    {
        $roleGroupId = $this->getCurrentRoleGroupId();

        // 如果不是特殊角色，不需要过滤
        if (!$roleGroupId) {
            return $query;
        }

        // 确保已经关联了player表
        if (!isset($query->getOptions('join')['player'])) {
            $query->join('players player', 'game_logs.player_id = player.id', 'LEFT');
        }

        // 根据角色类型应用不同的过滤条件
        if ($roleGroupId == 6) { // 业务员角色
            $agentId = $this->getCurrentAgentId();
            if ($agentId) {
                $query->where('player.agent_id', $agentId);
            }
        } elseif ($roleGroupId == 4) { // 渠道角色
            $channelId = $this->getCurrentChannelId();
            if ($channelId) {
                $query->where('player.channel_id', $channelId);
            }
        }

        return $query;
    }

    /**
     * 查看
     */
    public function index()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);

        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            // 获取搜索、排序和分页参数，但不使用buildparams来避免自动应用角色过滤
            $search = $this->request->get("search", '');
            $filter = $this->request->get("filter", '');
            $op = $this->request->get("op", '', 'trim');
            $sort = $this->request->get("sort", 'id');
            $order = $this->request->get("order", "DESC");
            $limit = max(0, $this->request->get("limit/d", 0));
            $limit = $limit ?: 999999;
            $page = max(1, $this->request->get("offset/d", 0) / $limit + 1);

            // 处理过滤条件
            $filter = (array)json_decode($filter, true);
            $op = (array)json_decode($op, true);
            $where = [];

            // 处理搜索条件
            if ($search) {
                $where[] = [$this->searchFields, "LIKE", "%{$search}%"];
            }

            // 处理过滤条件
            foreach ($filter as $k => $v) {
                if (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $k)) {
                    continue;
                }
                $sym = isset($op[$k]) ? $op[$k] : '=';
                // 排除agent_id和channel_id字段，因为game_records表中没有这些字段
                if ($k !== 'agent_id' && $k !== 'channel_id') {
                    $where[] = [$k, $sym, $v];
                }
            }

            // 查询游戏记录
            $query = $this->model->with(['gameRoom']);

            // 应用where条件
            if (!empty($where)) {
                $query->where($where);
            }

            // 应用基于角色的数据过滤
            $this->applyRoleDataFilter($query);

            // 执行查询
            $list = $query->order($sort, $order)->paginate($limit, false, ['page' => $page]);

            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }

        return $this->view->fetch();
    }
}
