<?php

namespace app\admin\controller\players;

use app\common\controller\Backend;
use think\Exception;

/**
 * 玩家RTP设置管理
 *
 * @icon fa fa-percent
 */
class Rtp extends Backend
{
    protected $model = null;
    protected $noNeedRight = ['index', 'update'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\players\Players;
    }

    /**
     * 查看
     */
    public function index()
    {
        $player_id = $this->request->param('player_id', 0, 'intval');
        if (!$player_id) {
            $this->error(__('Parameter %s can not be empty', 'player_id'));
        }

        // 获取玩家信息
        $player = $this->model->get($player_id);
        if (!$player) {
            $this->error(__('Player not found'));
        }

        $this->view->assign('player', $player);

        return $this->view->fetch();
    }

    /**
     * 更新RTP设置
     */
    public function update()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();

            // 验证参数
            if (!isset($params['player_id']) || !$params['player_id']) {
                $this->error(__('Parameter %s can not be empty', 'player_id'));
            }

            if (!isset($params['rtp']) || !is_numeric($params['rtp'])) {
                $this->error(__('Please enter a valid RTP value'));
            }

            // 验证个人RTP值范围
            $rtp = floatval($params['rtp']);
            if ($rtp < 0 || $rtp > 100) {
                $this->error(__('RTP value must be between 0 and 100'));
            }

            // 获取玩家信息
            $player = $this->model->get($params['player_id']);
            if (!$player) {
                $this->error(__('Player not found'));
            }

            // 保存原始RTP值用于日志记录
            $oldRtp = $player->rtp;

            // 开始事务
            \think\Db::startTrans();
            try {
                // 更新个人RTP值
                $player->rtp = $rtp;
                $player->save();

                // 处理团队RTP设置
                $teamRtpUpdated = false;
                $teamRtpCount = 0;

                if (isset($params['team_rtp']) && is_numeric($params['team_rtp'])) {
                    $teamRtp = floatval($params['team_rtp']);

                    // 验证团队RTP值范围
                    if ($teamRtp < 0 || $teamRtp > 100) {
                        \think\Db::rollback();
                        $this->error(__('Team RTP value must be between 0 and 100'));
                    }

                    // 保存team_rtp值到当前玩家的team_rtp字段
                    $player->team_rtp = $teamRtp;
                    $player->save();

                    // 获取该玩家的所有下级玩家ID
                    $subordinateIds = \think\Db::table('player_relations')
                        ->where('ancestor_id', $player->id)
                        ->column('player_id');

                    if (!empty($subordinateIds)) {
                        // 批量更新所有下级玩家的RTP值
                        $teamRtpCount = \think\Db::table('players')
                            ->whereIn('id', $subordinateIds)
                            ->update(['rtp' => $teamRtp]);

                        $teamRtpUpdated = true;
                    }
                }

                \think\Db::commit();

                // 构建成功消息
                $successMsg = __('RTP updated successfully');
                if ($teamRtpUpdated) {
                    $successMsg .= ', ' . __('Team RTP updated for %d players', $teamRtpCount);
                }

                // 记录操作日志
                $this->success($successMsg, null, [
                    'player_id' => $player->id,
                    'old_rtp' => $oldRtp,
                    'new_rtp' => $rtp,
                    'team_rtp_updated' => $teamRtpUpdated,
                    'team_rtp_count' => $teamRtpCount
                ]);
            } catch (Exception $e) {
                \think\Db::rollback();
                $this->error($e->getMessage());
            }
        }

        $this->error(__('Invalid request'));
    }
}
