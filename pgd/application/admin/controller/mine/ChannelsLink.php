<?php

namespace app\admin\controller\mine;

use app\common\controller\Backend;

/**
 * 渠道链接
 */
class ChannelsLink extends Backend
{
    protected $model = null;
    protected $searchFields = '';
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $agent_id = $this->auth->id;

            // 动态生成渠道链接，不保存到数据库
            // 使用工具函数获取清理后的域名
            $cleanDomain = get_clean_domain();
            $link = $cleanDomain;

            return json(['link' => $link]);
        }

        return $this->view->fetch();
    }


}