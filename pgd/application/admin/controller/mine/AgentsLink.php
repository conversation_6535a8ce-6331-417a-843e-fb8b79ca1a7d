<?php

namespace app\admin\controller\mine;

use app\common\controller\Backend;

/**
 * 业务员链接
 */
class AgentsLink extends Backend
{
    protected $model = null;
    protected $searchFields = '';
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            // 获取当前用户关联的业务员ID
            $adminId = $this->auth->id;
            $agent = \think\Db::table('agents')
                ->where('admin_id', $adminId)
                ->field('id')
                ->find();

            $agent_id = $agent ? $agent['id'] : 0;

            // 动态生成业务员链接，不保存到数据库
            // 使用工具函数获取清理后的域名
            $cleanDomain = get_clean_domain();
            $link = $cleanDomain . "/?ic=" . $agent_id;

            return json(['link' => $link]);
        }

        return $this->view->fetch();
    }


}