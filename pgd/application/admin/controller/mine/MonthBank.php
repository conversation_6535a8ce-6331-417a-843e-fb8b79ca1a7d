<?php

namespace app\admin\controller\mine;

use app\common\controller\Backend;
use app\admin\model\mine\MonthBank as MonthBankModel;
use think\Exception;

/**
 * 每月财报
 */
class MonthBank extends Backend
{
    protected $model = null;
    protected $searchFields = '';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new MonthBankModel();
    }

    /**
     * 查看
     */
    public function index()
    {
        // 检查是否传入了agent_id参数（从agents_config/agents页面打开）
        $agentId = $this->request->get('agent_id', 0);

        // 获取当前登录用户的ID和角色组ID
        $userId = $this->auth->id;
        $groupIds = $this->auth->getGroupIds();
        $groupId = $groupIds[0] ?? 0;

        // 如果传入了agent_id，则获取该业务员的信息
        if ($agentId) {
            // 获取业务员信息
            $agentInfo = \think\Db::table('agents')
                ->alias('a')
                ->join(['fa_admin' => 'admin'], 'a.admin_id = admin.id', 'LEFT')
                ->where('a.id', $agentId)
                ->field(['a.id', 'admin.username', 'admin.nickname'])
                ->find();

            if ($agentInfo) {
                // 设置业务员名称，优先使用nickname
                $agentName = !empty($agentInfo['nickname']) ? $agentInfo['nickname'] : $agentInfo['username'];
                $this->view->assign('agent_name', $agentName);
                $this->view->assign('agent_id', $agentId);

                // 获取业务员所属渠道的费率信息
                $rateInfo = $this->getAgentRateInfo($agentId);
                $this->view->assign('rateInfo', $rateInfo);
            }
        } else {
            // 获取费率信息
            $rateInfo = $this->getRateInfo($userId, $groupId);
            $this->view->assign('rateInfo', $rateInfo);
        }

        if ($this->request->isAjax()) {
            try {
                // 获取请求参数
                $month = $this->request->get('month', date('Y-m'));

                // 如果传入了agent_id，则获取该业务员的财报数据
                if ($agentId) {
                    $data = $this->model->getAgentMonthlyReport($month, $agentId);
                } else {
                    // 使用模型根据角色获取每月财报数据
                    $data = $this->model->getReportByRole($month, $userId, $groupId);
                }

                return json(['data' => $data]);
            } catch (Exception $e) {
                // 记录错误日志
                error_log('获取每月财报数据失败: ' . $e->getMessage());

                // 返回错误信息
                return json(['code' => 0, 'msg' => __('获取数据失败，请稍后重试'), 'data' => []]);
            }
        }

        return $this->view->fetch();
    }

    /**
     * 获取费率信息
     *
     * @param int $userId 用户ID
     * @param int $groupId 角色组ID
     * @return array 费率信息
     */
    private function getRateInfo($userId, $groupId)
    {
        $rateInfo = [
            'deposit_fee_rate' => 0,
            'withdraw_fee_rate' => 0,
            'api_fee_rate' => 0
        ];

        try {
            if ($groupId == 4) { // 渠道角色
                // 获取渠道ID
                $channelId = \think\Db::table('channels')
                    ->where('admin_id', $userId)
                    ->value('id');

                if ($channelId) {
                    // 获取渠道费率信息
                    $channelInfo = \think\Db::table('channels')
                        ->where('id', $channelId)
                        ->field(['deposit_fee_rate', 'withdraw_fee_rate', 'api_fee_rate'])
                        ->find();

                    if ($channelInfo) {
                        $rateInfo = $channelInfo;
                    }
                }
            } else { // 业务员角色
                // 获取业务员ID
                $agentId = \think\Db::table('agents')
                    ->where('admin_id', $userId)
                    ->value('id');

                if ($agentId) {
                    // 获取业务员所属渠道的费率信息
                    $agentInfo = \think\Db::table('agents')
                        ->alias('a')
                        ->join(['channels' => 'c'], 'a.channel_id = c.id', 'LEFT')
                        ->where('a.id', $agentId)
                        ->field(['c.deposit_fee_rate', 'c.withdraw_fee_rate', 'c.api_fee_rate'])
                        ->find();

                    if ($agentInfo) {
                        $rateInfo = $agentInfo;
                    }
                }
            }
        } catch (\Exception $e) {
            error_log('获取费率信息失败: ' . $e->getMessage());
        }

        return $rateInfo;
    }

    /**
     * 获取指定业务员的费率信息
     *
     * @param int $agentId 业务员ID
     * @return array 费率信息
     */
    private function getAgentRateInfo($agentId)
    {
        $rateInfo = [
            'deposit_fee_rate' => 0,
            'withdraw_fee_rate' => 0,
            'api_fee_rate' => 0
        ];

        try {
            // 获取业务员所属渠道的费率信息
            $agentInfo = \think\Db::table('agents')
                ->alias('a')
                ->join(['channels' => 'c'], 'a.channel_id = c.id', 'LEFT')
                ->where('a.id', $agentId)
                ->field(['c.deposit_fee_rate', 'c.withdraw_fee_rate', 'c.api_fee_rate'])
                ->find();

            if ($agentInfo) {
                $rateInfo = $agentInfo;
            }
        } catch (\Exception $e) {
            error_log('获取业务员费率信息失败: ' . $e->getMessage());
        }

        return $rateInfo;
    }
}