<?php
/*
 * @Author: ‘guanzh’ ‘<EMAIL>’
 * @Date: 2025-03-17 00:57:07
 * @LastEditors: ‘guanzh’ ‘<EMAIL>’
 * @LastEditTime: 2025-03-17 00:57:35
 * @FilePath: \pgd\application\admin\controller\agents\TreasureBoxConfig.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

namespace app\admin\controller\agents;

use app\common\controller\Backend;

/**
 * 宝箱奖励配置管理
 *
 * @icon fa fa-circle-o
 */
class TreasureBoxConfig extends Backend
{

    /**
     * Treasure_box_config模型对象
     * @var \app\admin\model\agents\TreasureBoxConfig
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\agents\TreasureBoxConfig;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
