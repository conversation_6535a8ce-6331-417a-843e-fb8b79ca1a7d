<?php
namespace app\admin\controller\commission;

use app\common\controller\Backend;
use think\Db;
use app\admin\model\commission\PlayerCommissionConfigs;
use app\admin\model\players\Players;

/**
 * 玩家佣金配置管理
 */
class PlayerCommission extends Backend
{
    /**
     * PlayerCommission模型对象
     */
    protected $model = null;
    protected $noNeedRight = ['save'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new PlayerCommissionConfigs;
    }

    /**
     * 查看玩家佣金配置
     */
    public function index()
    {
        $player_id = $this->request->param('player_id', 0, 'intval');
        if (!$player_id) {
            $this->error(__('Parameter %s can not be empty', 'player_id'));
        }

        // 获取玩家信息
        $player = Players::get($player_id);
        if (!$player) {
            $this->error(__('Player not found'));
        }

        // 获取玩家佣金配置
        $config = PlayerCommissionConfigs::getPlayerConfig($player_id);

        $this->view->assign('player', $player);
        $this->view->assign('config', $config);

        return $this->view->fetch();
    }

    /**
     * 保存玩家佣金配置
     */
    public function save()
    {
        if ($this->request->isPost()) {
            $player_id = $this->request->post('player_id', 0, 'intval');
            if (!$player_id) {
                $this->error(__('Parameter %s can not be empty', 'player_id'));
            }

            // 获取玩家信息
            $player = Players::get($player_id);
            if (!$player) {
                $this->error(__('Player not found'));
            }

            // 获取表单数据
            $params = $this->request->post();

            // 验证数据
            $validate = [
                'min_deposit_amount' => 'require|float|egt:0',
                'level1_rate' => 'require|float|egt:0',
                'level1_max_amount' => 'require|float|egt:0',
                'level2_rate' => 'require|float|egt:0',
                'level2_max_amount' => 'require|float|egt:0',
                'level3_rate' => 'require|float|egt:0',
                'level3_max_amount' => 'require|float|egt:0',
            ];

            $result = $this->validate($params, $validate);
            if (true !== $result) {
                $this->error($result);
            }

            // 获取玩家佣金配置
            $config = PlayerCommissionConfigs::getPlayerConfig($player_id);

            // 更新配置
            $config->min_deposit_amount = $params['min_deposit_amount'];
            $config->level1_rate = $params['level1_rate'];
            $config->level1_max_amount = $params['level1_max_amount'];
            $config->level2_rate = $params['level2_rate'];
            $config->level2_max_amount = $params['level2_max_amount'];
            $config->level3_rate = $params['level3_rate'];
            $config->level3_max_amount = $params['level3_max_amount'];

            // 设置同步到下级标志
            $config->sync_next = isset($params['sync_next']) && $params['sync_next'] == 1 ? 1 : 0;

            // 保存配置
            $config->save();

            // 如果需要同步到所有下级
            if ($config->sync_next == 1) {
                $config->syncToAllSubordinates();
            }

            // 获取返回地址，如果有的话
            $referer = $this->request->post('referer', '');
            if ($referer) {
                $this->success(__('Saved successfully'), $referer);
            } else {
                $this->success(__('Saved successfully'));
            }
        }

        $this->error(__('Invalid request'));
    }
}
