<?php

namespace app\admin\controller\service;

use app\common\controller\Backend;

/**
 * GM上下分记录管理
 *
 * @icon fa fa-circle-o
 */
class GmBalanceAdjustments extends Backend
{

    /**
     * Gm_balance_adjustments模型对象
     * @var \app\admin\model\service\GmBalanceAdjustments
     */
    protected $model = null;
    protected $noNeedRight = ['index'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\service\GmBalanceAdjustments;
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 初始化查询构造器
            $query = $this->model->alias('gba');

            // 获取当前用户角色组ID
            $roleGroupId = $this->getCurrentRoleGroupId();

            // 根据角色类型应用不同的过滤条件
            if ($roleGroupId == 4) { // 渠道角色
                $channelId = $this->getCurrentChannelId();
                if ($channelId) {
                    // 关联players表以便过滤
                    $query->join(['players'=>'p'], 'gba.player_id = p.id', 'left');
                    $query->where('p.channel_id', $channelId);
                }
            } elseif ($roleGroupId == 6) { // 业务员角色
                $agentId = $this->getCurrentAgentId();
                if ($agentId) {
                    // 关联players表以便过滤
                    $query->join(['players'=>'p'], 'gba.player_id = p.id', 'left');
                    $query->where('p.agent_id', $agentId);
                }
            }

            // 处理原始的where条件，添加表前缀
            foreach ($where as $condition) {
                if (is_array($condition) && count($condition) >= 3) {
                    // 为字段名添加表前缀，避免歧义
                    $field = $condition[0];
                    $operator = $condition[1];
                    $value = $condition[2];

                    // 如果字段名没有表前缀，添加gba.前缀
                    if (strpos($field, '.') === false) {
                        $field = 'gba.' . $field;
                    }

                    $query->where($field, $operator, $value);
                }
            }

            // 设置排序字段前缀，避免字段歧义
            if(isset($sort)) {
                $sort = "gba.$sort";
            }

            // 执行查询
            $list = $query
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','player_id','amount','adjustment_type','adjustment_type_text','status','status_text','operator_type','operator_type_text','operator_name','remark','created_at','updated_at']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }



    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                // ... 其他代码 ...
            }
            $this->error();
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 同意记录
     */
    public function approve($ids = null)
    {
        // 检查用户是否属于允许的角色组
        $allowedGroups = [1, 2, 4]; // 管理员组、平台管理员、渠道
        $hasPermission = false;
        $groupIds = $this->auth->getGroupIds();

        foreach ($groupIds as $groupId) {
            if (in_array($groupId, $allowedGroups)) {
                $hasPermission = true;
                break;
            }
        }

        if (!$hasPermission) {
            $this->error(__('You have no permission'));
        }

        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 只有未审核的记录才能审核通过
        if ($row['status'] != 0) {
            $this->error(__('Operation failed'));
        }

        try {
            // 更新状态为已审核
            $result = $row->save(['status' => 1, 'updated_at' => date('Y-m-d H:i:s')]);

            if ($result) {
                $this->success(__('Record approved successfully'));
            } else {
                $this->error(__('Operation failed'));
            }
        } catch (\Exception $e) {
            $this->error(__('Database error: ') . $e->getMessage());
        }
    }

    /**
     * 拒绝记录
     */
    public function reject($ids = null)
    {
        // 检查用户是否属于允许的角色组
        $allowedGroups = [1, 2, 4]; // 管理员组、平台管理员、渠道
        $hasPermission = false;
        $groupIds = $this->auth->getGroupIds();

        foreach ($groupIds as $groupId) {
            if (in_array($groupId, $allowedGroups)) {
                $hasPermission = true;
                break;
            }
        }

        if (!$hasPermission) {
            $this->error(__('You have no permission'));
        }

        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 只有未审核的记录才能拒绝
        if ($row['status'] != 0) {
            $this->error(__('Operation failed'));
        }

        try {
            // 更新状态为已拒绝（使用状态2表示已拒绝）
            $result = $row->save(['status' => 2, 'updated_at' => date('Y-m-d H:i:s')]);

            if ($result) {
                $this->success(__('Record rejected successfully'));
            } else {
                $this->error(__('Operation failed'));
            }
        } catch (\Exception $e) {
            $this->error(__('Database error: ') . $e->getMessage());
        }
    }

}
