<?php

namespace app\admin\controller\statistic;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 游戏记录统计
 *
 * @icon fa fa-bar-chart
 */
class GameRecords extends Backend
{
    /**
     * GameRecords模型对象
     * @var \app\admin\model\statistic\GameRecords
     */
    protected $model = null;
    protected $searchFields = 'provider_name';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\statistic\GameRecords;

        // 获取游戏供应商列表
        $providerList = \app\admin\model\game\Providers::column('name', 'id');
        $this->view->assign('providerList', $providerList);
    }

    /**
     * 查看
     */
    public function index()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);

        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            $startDate = $this->request->get('start_date', date('Y-m-d', strtotime('-7 days')));
            $endDate = $this->request->get('end_date', date('Y-m-d'));
            $providerId = $this->request->get('provider_id', '');

            try {
                // 获取统计数据
                $list = $this->model->getStatsByDateAndProvider($startDate, $endDate, $providerId);

                // 获取充值统计数据
                $depositStats = $this->model->getDepositStats($startDate, $endDate, $providerId);

                $result = array(
                    "total" => count($list),
                    "rows" => $list,
                    "deposit_stats" => $depositStats
                );
                return json($result);
            } catch (Exception $e) {
                return json(['total' => 0, 'rows' => [], 'error' => $e->getMessage()]);
            }
        }

        // 设置默认日期范围
        $this->view->assign('startDate', date('Y-m-d', strtotime('-7 days')));
        $this->view->assign('endDate', date('Y-m-d'));

        return $this->view->fetch();
    }

    /**
     * 导出
     */
    public function export()
    {
        $startDate = $this->request->get('start_date', date('Y-m-d', strtotime('-7 days')));
        $endDate = $this->request->get('end_date', date('Y-m-d'));
        $providerId = $this->request->get('provider_id', '');

        try {
            // 获取统计数据
            $list = $this->model->getStatsByDateAndProvider($startDate, $endDate, $providerId);

            // 导出Excel
            $header = [
                'stat_date' => __('Date'),
                'provider_id' => __('Provider_id'),
                'provider_name' => __('Provider_name'),
                'bet_amount' => __('Bet_amount'),
                'platform_profit' => __('Platform_profit'),
                'kill_rate' => __('Kill_rate'),
                'return_rate' => __('Return_rate'),
                'player_count' => __('Player_count'),
                'game_count' => __('Game_count')
            ];

            $filename = '游戏记录统计_' . date('YmdHis');
            $this->exportExcel($filename, $header, $list);
            return;
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }
}
