<?php

namespace app\admin\controller\statistic;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 渠道报表
 *
 * @icon fa fa-bar-chart
 */
class ChannelReports extends Backend
{
    /**
     * ChannelReports模型对象
     * @var \app\admin\model\statistic\ChannelReports
     */
    protected $model = null;
    protected $noNeedRight = ['index'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\statistic\ChannelReports;
    }

    /**
     * 查看
     */
    public function index()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);

        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            $startDate = $this->request->get('start_date', date('Y-m-d', strtotime('-7 days')));
            $endDate = $this->request->get('end_date', date('Y-m-d'));
            $channelId = $this->request->get('channel_id', '');

            try {
                // 获取统计数据
                $list = $this->model->getChannelReports($startDate, $endDate, $channelId);

                $result = array(
                    "total" => count($list),
                    "rows" => $list
                );
                return json($result);
            } catch (Exception $e) {
                return json(['total' => 0, 'rows' => [], 'error' => $e->getMessage()]);
            }
        }
        return $this->view->fetch();
    }
}
