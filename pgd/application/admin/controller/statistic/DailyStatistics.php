<?php
/*
 * @Author: ‘guanzh’ ‘<EMAIL>’
 * @Date: 2025-03-17 01:11:35
 * @LastEditors: ‘guanzh’ ‘<EMAIL>’
 * @LastEditTime: 2025-03-17 01:11:58
 * @FilePath: \pgd\application\admin\controller\statistic\DailyStatistics.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

namespace app\admin\controller\statistic;

use app\common\controller\Backend;
use think\Db;

/**
 * 每日统计管理
 *
 * @icon fa fa-circle-o
 */
class DailyStatistics extends Backend
{

    /**
     * Daily_statistics模型对象
     * @var \app\admin\model\statistic\DailyStatistics
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\statistic\DailyStatistics;
    }

    /**
     * 应用数据过滤条件到查询构造器
     * 由于现在按日期分组，不再需要按业务员过滤，但仍需保留渠道的过滤逻辑
     *
     * @param \think\db\Query $query 查询构造器
     * @param string $tableAlias 表别名
     * @return \think\db\Query 应用过滤条件后的查询构造器
     */
    protected function applyDataFilter($query, $tableAlias = '')
    {
        $roleGroupId = $this->getCurrentRoleGroupId();

        // 如果不是特殊角色，不需要过滤
        if (!$roleGroupId) {
            return $query;
        }

        // 表别名处理
        $prefix = $tableAlias ? $tableAlias . '.' : '';

        // 根据角色类型应用不同的过滤条件
        if ($roleGroupId == 4) { // 渠道角色
            $channelId = $this->getCurrentChannelId();
            if ($channelId) {
                // 查询该渠道下的所有业务员ID
                $agentIds = Db::table('agents')
                    ->where('channel_id', $channelId)
                    ->column('id');

                if (!empty($agentIds)) {
                    $query->where($prefix . 'agent_id', 'in', $agentIds);
                } else {
                    // 如果没有业务员，确保不返回任何数据
                    $query->where($prefix . 'agent_id', -1);
                }
            }
        }
        // 不再需要业务员角色的过滤，因为我们现在显示的是所有业务员的总和

        return $query;
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $_, $limit) = $this->buildparams(); // 使用 $_ 忽略不需要的变量

            // 创建查询构造器
            $query = $this->model->where($where);

            // 应用数据过滤
            $query = $this->applyDataFilter($query);

            // 按日期分组，计算所有业务员的业绩总和
            $list = $query->field([
                'stat_date',
                'SUM(total_deposit) as total_deposit',
                'SUM(total_withdraw) as total_withdraw',
                'SUM(total_deposit) - SUM(total_withdraw) as deposit_withdraw_diff', // 充提差
                'SUM(total_bet) as total_bet',
                'SUM(total_win) as total_win',
                'SUM(total_game_profit) as total_game_profit', // 游戏赢亏
                'SUM(active_players) as active_players', // 活跃玩家数
                'SUM(new_players) as new_players',
                'SUM(first_deposit_players) as first_deposit_players',
                'SUM(repeat_deposit_players) as repeat_deposit_players',
                'SUM(first_deposit_amount) as first_deposit_amount',
                'SUM(repeat_deposit_amount) as repeat_deposit_amount',
                'MAX(updated_at) as updated_at'
            ])
            ->group('stat_date')
            ->order($sort, $order)
            ->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }
}
