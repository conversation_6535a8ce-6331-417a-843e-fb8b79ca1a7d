<?php

namespace app\admin\controller\statistic;

use app\common\controller\Backend;
use think\Db;

/**
 * 业务员月度统计管理
 *
 * @icon fa fa-bar-chart
 */
class AgentsMonth extends Backend
{
    /**
     * AgentsMonth模型对象
     * @var \app\admin\model\statistic\AgentsMonth
     */
    protected $model = null;
    protected $searchFields = 'agent_id';
    protected $noNeedRight = ['index'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\statistic\AgentsMonth;
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 获取查询条件中的日期范围
            $dateRange = $this->request->get('stat_date/a', '');
            $startDate = isset($dateRange[0]) && $dateRange[0] ? $dateRange[0] : date('Y-01-01');
            $endDate = isset($dateRange[1]) && $dateRange[1] ? $dateRange[1] : date('Y-12-31');

            // 构建查询
            $query = $this->model->where($where);

            // 应用数据过滤
            $query = $this->applyDataFilter($query);

            // 添加日期范围过滤
            $query = $query->where('stat_date', 'between', [$startDate, $endDate]);

            // 按月份和业务员分组，计算汇总数据
            $subQuery = $query->field([
                'DATE_FORMAT(stat_date, "%Y-%m") as month',
                'agent_id',
                'SUM(total_deposit) as total_deposit',
                'SUM(total_deposit_count) as total_deposit_count',
                'SUM(total_withdraw) as total_withdraw',
                'SUM(total_withdraw_count) as total_withdraw_count',
                'SUM(total_bet) as total_bet',
                'SUM(total_win) as total_win',
                'SUM(total_game_profit) as total_game_profit',
                'SUM(new_players) as new_players',
                'SUM(active_players) as active_players',
                'SUM(first_deposit_players) as first_deposit_players',
                'SUM(repeat_deposit_players) as repeat_deposit_players',
                'SUM(withdraw_players) as withdraw_players',
                'SUM(manual_increase) as manual_increase',
                'SUM(manual_decrease) as manual_decrease',
                'MIN(stat_date) as stat_date', // 用于关联月份
                'MAX(updated_at) as updated_at'
            ])
            ->group('DATE_FORMAT(stat_date, "%Y-%m"), agent_id')
            ->buildSql();

            // 使用子查询获取分页数据
            $list = Db::table($subQuery . ' a')
                ->order($sort, $order)
                ->paginate($limit);

            // 获取结果项
            $items = $list->items();

            // 获取所有业务员信息
            $agentIds = array_column($items, 'agent_id');
            $agentInfo = [];
            if (!empty($agentIds)) {
                $agentInfo = Db::table('agents')
                    ->alias('a')
                    ->join('fa_admin admin', 'a.admin_id = admin.id', 'LEFT')
                    ->whereIn('a.id', $agentIds)
                    ->field('a.id, admin.username, admin.nickname')
                    ->select();

                // 转换为以id为键的数组
                $agentInfo = array_column($agentInfo ?: [], null, 'id');
            }

            // 手动计算虚拟字段
            foreach ($items as &$item) {
                // 添加业务员名称
                if (isset($agentInfo[$item['agent_id']])) {
                    $agentName = !empty($agentInfo[$item['agent_id']]['nickname'])
                        ? $agentInfo[$item['agent_id']]['nickname']
                        : $agentInfo[$item['agent_id']]['username'];
                    $item['agent_id_text'] = $item['agent_id'] . '(' . $agentName . ')';
                } else {
                    $item['agent_id_text'] = $item['agent_id'];
                }
                // 计算 new_active_users_text
                $item['new_active_users_text'] = ($item['new_players'] ?? 0) . '/' . ($item['active_players'] ?? 0);

                // 计算 deposit_info_text
                $depositUsers = ($item['first_deposit_players'] ?? 0) + ($item['repeat_deposit_players'] ?? 0);
                $item['deposit_info_text'] = number_format($item['total_deposit'] ?? 0, 2) . '/' . $depositUsers . '/' . ($item['total_deposit_count'] ?? 0);

                // 计算 withdraw_info_text
                $item['withdraw_info_text'] = number_format($item['total_withdraw'] ?? 0, 2) . '/' . ($item['withdraw_players'] ?? 0) . '/' . ($item['total_withdraw_count'] ?? 0);

                // 计算 first_repeat_deposit_text
                $item['first_repeat_deposit_text'] = ($item['first_deposit_players'] ?? 0) . '/' . ($item['repeat_deposit_players'] ?? 0);

                // 计算 manual_adjust_text
                $item['manual_adjust_text'] = number_format($item['manual_increase'] ?? 0, 2) . '/' . number_format($item['manual_decrease'] ?? 0, 2);

                // 计算 game_info_text
                $item['game_info_text'] = number_format($item['total_bet'] ?? 0, 2) . '/' .
                                         number_format($item['total_win'] ?? 0, 2) . '/' .
                                         number_format($item['total_game_profit'] ?? 0, 2);

                // 计算 deposit_withdraw_diff
                $deposit = floatval($item['total_deposit'] ?? 0);
                $withdraw = floatval($item['total_withdraw'] ?? 0);
                $item['deposit_withdraw_diff'] = $deposit - $withdraw;

                // 计算 actual_deposit_withdraw_diff
                $item['actual_deposit_withdraw_diff'] = $deposit * 0.97 - $withdraw;
            }

            $result = array("total" => $list->total(), "rows" => $items);
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 应用数据过滤条件到查询构造器
     * 重写父类方法，针对daily_statistics表没有channel_id字段的情况进行特殊处理
     *
     * @param \think\db\Query $query 查询构造器
     * @param string $tableAlias 表别名
     * @return \think\db\Query 应用过滤条件后的查询构造器
     */
    protected function applyDataFilter($query, $tableAlias = '')
    {
        $roleGroupId = $this->getCurrentRoleGroupId();

        // 如果不是特殊角色，不需要过滤
        if (!$roleGroupId) {
            return $query;
        }

        // 获取当前管理员ID
        $adminId = $this->auth->id;

        // 根据角色组ID应用不同的过滤条件
        if ($roleGroupId == 4) { // 渠道角色
            // 获取渠道ID
            $channelId = $this->getChannelIdByAdminId($adminId);
            if ($channelId) {
                // 获取该渠道下的所有业务员ID
                $agentIds = $this->getAgentIdsByChannelId($channelId);
                if (!empty($agentIds)) {
                    $query->where('agent_id', 'in', $agentIds);
                } else {
                    // 如果没有业务员，返回空结果
                    $query->where('1=0');
                }
            } else {
                // 如果没有关联渠道，返回空结果
                $query->where('1=0');
            }
        } elseif ($roleGroupId == 6) { // 代理角色
            // 获取代理关联的业务员ID
            $agentId = $this->getAgentIdByAdminId($adminId);
            if ($agentId) {
                $query->where('agent_id', $agentId);
            } else {
                // 如果没有关联业务员，返回空结果
                $query->where('1=0');
            }
        }

        return $query;
    }

    /**
     * 根据管理员ID获取渠道ID
     *
     * @param int $adminId 管理员ID
     * @return int|null 渠道ID
     */
    protected function getChannelIdByAdminId($adminId)
    {
        $channel = Db::table('channels')
            ->where('admin_id', $adminId)
            ->find();
        return $channel ? $channel['id'] : null;
    }

    /**
     * 根据渠道ID获取所有业务员ID
     *
     * @param int $channelId 渠道ID
     * @return array 业务员ID数组
     */
    protected function getAgentIdsByChannelId($channelId)
    {
        return Db::table('agents')
            ->where('channel_id', $channelId)
            ->column('id');
    }

    /**
     * 根据管理员ID获取业务员ID
     *
     * @param int $adminId 管理员ID
     * @return int|null 业务员ID
     */
    protected function getAgentIdByAdminId($adminId)
    {
        $agent = Db::table('agents')
            ->where('admin_id', $adminId)
            ->find();
        return $agent ? $agent['id'] : null;
    }

    /**
     * 获取当前用户的角色组ID
     *
     * @return int|null 角色组ID
     */
    protected function getCurrentRoleGroupId()
    {
        $adminId = $this->auth->id;
        $adminGroupIds = Db::name('auth_group_access')
            ->where('uid', $adminId)
            ->column('group_id');

        // 检查是否属于渠道或代理角色组
        if (in_array(4, $adminGroupIds)) {
            return 4; // 渠道角色
        } elseif (in_array(6, $adminGroupIds)) {
            return 6; // 代理角色
        }

        return null;
    }
}
