<?php

namespace app\admin\controller;

use app\admin\model\Admin;
use app\admin\model\AdminLog;
use app\common\controller\Backend;
use app\common\library\SimpleTOTP;
use think\Config;
use think\Hook;
use think\Session;
use think\Validate;

/**
 * 后台首页
 * @internal
 */
class Index extends Backend
{

    protected $noNeedLogin = ['login', 'agent_login', 'channel_login'];
    protected $noNeedRight = ['index', 'logout', 'agent_login', 'channel_login'];
    protected $layout = '';

    public function _initialize()
    {
        parent::_initialize();
        //移除HTML标签
        $this->request->filter('trim,strip_tags,htmlspecialchars');
    }

    /**
     * 后台首页
     */
    public function index()
    {
        $cookieArr = ['adminskin' => "/^skin\-([a-z\-]+)\$/i", 'multiplenav' => "/^(0|1)\$/", 'multipletab' => "/^(0|1)\$/", 'show_submenu' => "/^(0|1)\$/"];
        foreach ($cookieArr as $key => $regex) {
            $cookieValue = $this->request->cookie($key);
            if (!is_null($cookieValue) && preg_match($regex, $cookieValue)) {
                config('fastadmin.' . $key, $cookieValue);
            }
        }
        //左侧菜单
        list($menulist, $navlist, $fixedmenu, $referermenu) = $this->auth->getSidebar([
            'dashboard' => 'hot',
            'addon'     => ['new', 'red', 'badge'],
            'auth/rule' => __('Menu'),
        ], $this->view->site['fixedpage']);
        $action = $this->request->request('action');
        if ($this->request->isPost()) {
            if ($action == 'refreshmenu') {
                $this->success('', null, ['menulist' => $menulist, 'navlist' => $navlist]);
            }
        }
        $this->assignconfig('cookie', ['prefix' => config('cookie.prefix')]);
        $this->view->assign('menulist', $menulist);
        $this->view->assign('navlist', $navlist);
        $this->view->assign('fixedmenu', $fixedmenu);
        $this->view->assign('referermenu', $referermenu);
        $this->view->assign('title', __('Home'));
        return $this->view->fetch();
    }

    /**
     * 管理员登录
     */
    public function login()
    {
        $url = $this->request->get('url', '', 'url_clean');
        $url = $url ?: 'index/index';

        if ($this->auth->isLogin()) {
            $this->success(__("You've logged in, do not login again"), $url);
        }
        //保持会话有效时长，单位:小时
        $keeyloginhours = 240;
        if ($this->request->isPost()) {
            $username = $this->request->post('username');
            $password = $this->request->post('password', '', null);
            $totpCode = $this->request->post('totp_code');
            $keeplogin = $this->request->post('keeplogin');
            $token = $this->request->post('__token__');

            // 通用错误消息，不区分是密码错误还是2FA错误
            $genericErrorMsg = __('Login information verification failed. Please check your credentials.');

            $rule = [
                'username'  => 'require|length:3,30',
                'password'  => 'require|length:3,30',
                '__token__' => 'require|token',
            ];
            $data = [
                'username'  => $username,
                'password'  => $password,
                '__token__' => $token,
            ];
            if (Config::get('fastadmin.login_captcha')) {
                $rule['captcha'] = 'require|captcha';
                $data['captcha'] = $this->request->post('captcha');
            }
            $validate = new Validate($rule, [], ['username' => __('Username'), 'password' => __('Password'), 'captcha' => __('Captcha')]);
            $result = $validate->check($data);
            if (!$result) {
                $this->error($validate->getError(), $url, ['token' => $this->request->token()]);
            }

            // 先检查用户是否存在，以及是否启用了2FA
            $admin = Admin::get(['username' => $username]);

            // 如果用户不存在，返回通用错误
            if (!$admin) {
                $this->error($genericErrorMsg, $url, ['token' => $this->request->token()]);
            }

            // 检查用户是否启用了2FA，如果启用了但没有提供验证码，返回通用错误
            if (!empty($admin->two_factor_key) && empty($totpCode)) {
                $this->error($genericErrorMsg, $url, ['token' => $this->request->token()]);
            }

            // 如果用户启用了2FA，验证TOTP码格式
            if (!empty($admin->two_factor_key)) {
                $totpValidate = new Validate(['totp_code' => 'require|integer|length:6'], [], ['totp_code' => __('Authenticator Code')]);
                if (!$totpValidate->check(['totp_code' => $totpCode])) {
                    $this->error($genericErrorMsg, $url, ['token' => $this->request->token()]);
                }
            }

            AdminLog::setTitle(__('Login'));

            // 验证密码
            $result = $this->auth->login($username, $password, $keeplogin ? $keeyloginhours * 3600 : 0);
            if ($result === true) {
                // 密码验证成功，如果启用了2FA，继续验证TOTP码
                if (!empty($admin->two_factor_key)) {
                    try {
                        $totp = SimpleTOTP::create($admin->two_factor_key);
                        if (!$totp->verify($totpCode)) {
                            // TOTP验证失败，登出并返回通用错误
                            $this->auth->logout();
                            $this->error($genericErrorMsg, $url, ['token' => $this->request->token()]);
                        }
                        // TOTP验证通过，继续执行
                    } catch (\Exception $e) {
                        // TOTP验证异常，登出并返回通用错误
                        $this->auth->logout();
                        // 可选：记录日志 $e->getMessage();
                        $this->error($genericErrorMsg, $url, ['token' => $this->request->token()]);
                    }
                }

                Hook::listen("admin_login_after", $this->request);

                // 检查用户是否已设置双因素认证
                if (empty($admin->two_factor_key)) {
                    // 如果未设置双因素认证，则跳转到专门的双因素认证设置页面
                    $twoFactorUrl = url('general/two_factor/index');
                    $this->success(__('Login successful, please set up two-factor authentication'), $twoFactorUrl, ['url' => $twoFactorUrl, 'id' => $this->auth->id, 'username' => $username, 'avatar' => $this->auth->avatar]);
                } else {
                    // 已设置双因素认证，正常跳转
                    $this->success(__('Login successful'), $url, ['url' => $url, 'id' => $this->auth->id, 'username' => $username, 'avatar' => $this->auth->avatar]);
                }
            } else {
                // 密码验证失败，返回通用错误
                $this->error($genericErrorMsg, $url, ['token' => $this->request->token()]);
            }
        }

        // 根据客户端的cookie,判断是否可以自动登录
        if ($this->auth->autologin()) {
            Session::delete("referer");
            $this->redirect($url);
        }
        $background = Config::get('fastadmin.login_background');
        $background = $background ? (stripos($background, 'http') === 0 ? $background : config('site.cdnurl') . $background) : '';
        $this->view->assign('keeyloginhours', $keeyloginhours);
        $this->view->assign('background', $background);
        $this->view->assign('title', __('Login'));

        Hook::listen("admin_login_init", $this->request);
        return $this->view->fetch();
    }

    /**
     * 退出登录
     */
    public function logout()
    {
        if ($this->request->isPost()) {
            $this->auth->logout();
            Hook::listen("admin_logout_after", $this->request);
            $this->success(__('Logout successful'), 'index/login');
        }
        $html = "<form id='logout_submit' name='logout_submit' action='' method='post'>" . token() . "<input type='submit' value='ok' style='display:none;'></form>";
        $html .= "<script>document.forms['logout_submit'].submit();</script>";

        return $html;
    }

    /**
     * 业务员登录处理
     * 通过token验证并自动登录业务员账号
     */
    public function agent_login()
    {
        // 获取token参数
        $token = $this->request->param('token', '');
        if (empty($token)) {
            $this->error(__('Invalid login token'));
        }

        // 从缓存中获取业务员信息
        $agentInfo = \think\Cache::get('agent_login_' . $token);
        if (!$agentInfo) {
            $this->error(__('Login token expired or invalid'));
        }

        // 验证token是否过期（5分钟内有效）
        if (time() - $agentInfo['time'] > 300) {
            \think\Cache::rm('agent_login_' . $token);
            $this->error(__('Login token expired'));
        }

        // 查找业务员管理员账号
        $admin = Admin::get($agentInfo['admin_id']);
        if (!$admin) {
            $this->error(__('Agent account not found'));
        }

        if ($admin['status'] != 'normal') {
            $this->error(__('Agent account is disabled'));
        }

        // 记录日志
        \think\Log::write('业务员登录: admin_id=' . $admin['id'] . ', username=' . $admin['username'], 'info');
        AdminLog::setTitle(__('Login as agent'));

        // 设置管理员会话
        Session::set("admin", $admin->toArray());
        Session::set("admin.safecode", $this->auth->getEncryptSafecode($admin));

        // 清除token
        \think\Cache::rm('agent_login_' . $token);

        // 跳转到业务员后台首页
        $this->redirect('/');
    }

    /**
     * 渠道登录处理
     * 通过token验证并自动登录渠道账号
     */
    public function channel_login()
    {
        // 获取token参数
        $token = $this->request->param('token', '');
        if (empty($token)) {
            $this->error(__('Invalid login token'));
        }

        // 从缓存中获取渠道信息
        $channelInfo = \think\Cache::get('channel_login_' . $token);
        if (!$channelInfo) {
            $this->error(__('Login token expired or invalid'));
        }

        // 验证token是否过期（5分钟内有效）
        if (time() - $channelInfo['time'] > 300) {
            \think\Cache::rm('channel_login_' . $token);
            $this->error(__('Login token expired'));
        }

        // 查找渠道管理员账号
        $admin = Admin::get($channelInfo['admin_id']);
        if (!$admin) {
            $this->error(__('Channel account not found'));
        }

        if ($admin['status'] != 'normal') {
            $this->error(__('Channel account is disabled'));
        }

        // 记录日志
        \think\Log::write('渠道登录: admin_id=' . $admin['id'] . ', username=' . $admin['username'], 'info');
        AdminLog::setTitle(__('Login as channel'));

        // 设置管理员会话
        Session::set("admin", $admin->toArray());
        Session::set("admin.safecode", $this->auth->getEncryptSafecode($admin));

        // 清除token
        \think\Cache::rm('channel_login_' . $token);

        // 跳转到渠道后台首页
        $this->redirect('/');
    }

}
