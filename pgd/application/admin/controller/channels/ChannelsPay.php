<?php

namespace app\admin\controller\channels;

use app\common\controller\Backend;

/**
 * 渠道额度变更管理
 *
 * @icon fa fa-money
 */
class ChannelsPay extends Backend
{
    /**
     * ChannelsPay模型对象
     * @var \app\admin\model\channels\ChannelsPay
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\channels\ChannelsPay;
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            $params = $this->buildparams();
            $where = $params[0];
            $sort = $params[1];
            $order = $params[2];
            // $offset = $params[3]; // 未使用
            $limit = $params[4];
            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }

    // add 和 edit 方法已被移除

    /**
     * 删除
     */
    public function del($ids = null)
    {
        $this->error('不允许删除记录');
    }
}
