<?php

namespace app\admin\controller\channels;

use app\common\controller\Backend;
use app\admin\model\channels\Channels as ChannelsModel;
use app\admin\model\Admin;
use think\Db;
use think\Exception;

/**
 * 渠道管理
 *
 * @icon fa fa-circle-o
 */
class Channels extends Backend
{

    /**
     * Channels模型对象
     * @var \app\admin\model\channels\Channels
     */
    protected $model = null;

    // 开启验证
    protected $modelValidate = true;

    // 开启场景验证
    protected $modelSceneValidate = true;

    // 无需权限的方法
    protected $noNeedRight = ['login', 'updateLimit', 'set_rtp'];

    protected $multiFields = 'allow_treasure_box,is_active,allow_change_player_credit,allow_withdrawal,allow_hidden_player';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new ChannelsModel;
    }

    /**
     * 获取渠道列表
     */
    public function getList()
    {
        $data = ChannelsModel::getChannelList(true);
        return json($data);
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();

        $list = $this->model
            ->alias('c')
            ->field('c.*, a.username as admin_username, a.nickname as admin_nickname, a.two_factor_key')
            ->join('fa_admin a', 'c.admin_id = a.id', 'LEFT')
            ->where($where)
            ->order('c.'.$sort, $order)
            ->paginate($limit);

        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                // 开启事务
                $this->model->startTrans();

                try {
                    // 检查渠道名称是否为空
                    if (empty($params['name'])) {
                        $this->error('渠道名称不能为空');
                    }

                    // 检查渠道名称是否已存在
                    $existChannel = $this->model->where('name', $params['name'])->find();
                    if ($existChannel) {
                        $this->error('渠道名称已存在');
                    }

                    // 处理密码
                    $password = $params['password'];
                    if (empty($password)) {
                        $this->error('密码不能为空');
                    }

                    // 创建Admin用户
                    $admin = new Admin();

                    // 检查用户名是否为空
                    if (empty($params['username'])) {
                        $this->error(__('Username cannot be empty'));
                    }

                    // 检查admin用户名是否已存在
                    $existAdmin = $admin->where('username', $params['username'])->find();
                    if ($existAdmin) {
                        $this->error(__('Admin username already exists'));
                    }

                    // 随机生成salt
                    $salt = \fast\Random::alnum();
                    $currentTime = time();
                    $adminData = [
                        'username' => $params['username'],
                        'nickname' => '渠道-'.$params['name'],
                        'password' => md5(md5($password) . $salt),
                        'salt' => $salt,
                        'avatar' => '/assets/img/avatar.png',
                        'status' => 'normal',
                        'createtime' => $currentTime,
                        'updatetime' => $currentTime
                    ];

                    // 保存Admin数据
                    if (!$admin->save($adminData)) {
                        throw new \Exception('管理员账号创建失败');
                    }

                    // 获取新创建的admin_id
                    $admin_id = $admin->id;

                    // 为新admin用户分配权限组(1超级管理员2平台管理员4渠道6业务员)
                    $groupId = 4; // 渠道组ID是4
                    $groupAccess = [
                        'uid' => $admin_id,
                        'group_id' => $groupId
                    ];

                    // 保存权限组分配
                    if (!Db::name('auth_group_access')->insert($groupAccess)) {
                        throw new \Exception('管理员权限分配失败');
                    }

                    // 设置默认值
                    $params['is_active'] = 1;
                    $params['allow_withdrawal'] = 1;
                    $params['allow_blacklist'] = 1;
                    $params['allow_hidden_player'] = 1;
                    $params['allow_change_player_credit'] = 1;
                    $params['allow_treasure_box'] = 1;

                    // 设置默认额度为0
                    $params['credit_left'] = 0.00;

                    // 关联admin_id
                    $params['admin_id'] = $admin_id;

                    // 渠道表中不需要保存密码

                    // 保存Channel数据
                    $result = $this->model->allowField(true)->save($params);
                    if ($result === false) {
                        throw new \Exception($this->model->getError() ?: '渠道创建失败');
                    }

                    // 提交事务
                    $this->model->commit();

                    // 返回成功响应
                    if ($this->request->isAjax()) {
                        return json([
                            'code' => 1,
                            'msg'  => '渠道创建成功',
                            'data' => '',
                            'url'  => '',
                            'wait' => 3,
                        ]);
                    } else {
                        $this->success('渠道创建成功');
                    }
                } catch (\Exception $e) {
                    // 回滚事务
                    $this->model->rollback();
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            if ($this->request->isAjax()) {
                return json(['code' => 0, 'msg' => __('No Results were found'), 'data' => '', 'url' => '', 'wait' => 3]);
            } else {
                $this->error(__('No Results were found'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                // 处理密码 (只更新fa_admin表的密码，channels表中不存储密码)
                $updateAdminPassword = false;
                $newPassword = '';
                if (isset($params['password']) && $params['password']) {
                    // 标记需要更新admin密码
                    $updateAdminPassword = true;
                    $newPassword = $params['password'];
                }
                // 从channels表参数中移除密码字段
                unset($params['password']);

                // 验证渠道名称是否为空
                if (isset($params['name']) && empty($params['name'])) {
                    if ($this->request->isAjax()) {
                        return json(['code' => 0, 'msg' => '渠道名称不能为空', 'data' => '', 'url' => '', 'wait' => 3]);
                    } else {
                        $this->error('渠道名称不能为空');
                    }
                }

                // 编辑时不允许修改管理员ID和额度
                if (isset($params['admin_id'])) {
                    unset($params['admin_id']);
                }

                // 移除额度相关字段，额度只能通过专门的额度修改功能来管理
                if (isset($params['credit_left'])) {
                    unset($params['credit_left']);
                }

                // 自动设置更新时间
                $params['updated_at'] = date('Y-m-d H:i:s');

                // 保留原创建时间
                unset($params['created_at']);

                // 开始事务
                $this->model->startTrans();

                try {
                    // 更新channels表
                    $result = $row->allowField(true)->save($params);

                    if ($result !== false) {
                        // 同时更新fa_admin表的updatetime字段和密码（如果有修改）
                        $admin_id = $row->admin_id;
                        if ($admin_id) {
                            $adminUpdateData = [
                                'updatetime' => time()
                            ];

                            // 如果需要更新密码
                            if ($updateAdminPassword && !empty($newPassword)) {
                                // 获取admin用户信息
                                $adminInfo = Db::name('admin')->where('id', $admin_id)->find();
                                if ($adminInfo) {
                                    // 使用原有的salt重新加密密码
                                    $adminUpdateData['password'] = md5(md5($newPassword) . $adminInfo['salt']);
                                }
                            }

                            Db::name('admin')->where('id', $admin_id)->update($adminUpdateData);
                        }

                        // 提交事务
                        $this->model->commit();
                    } else {
                        // 回滚事务
                        $this->model->rollback();
                    }
                } catch (\Exception $e) {
                    // 回滚事务
                    $this->model->rollback();
                    $result = false;
                    // 记录错误信息
                    \think\Log::write('渠道编辑失败: ' . $e->getMessage(), 'error');
                }
                if ($result !== false) {
                    if ($this->request->isAjax()) {
                        return json(['code' => 1, 'msg' => '更新成功', 'data' => '', 'url' => '', 'wait' => 3]);
                    } else {
                        $this->success('更新成功');
                    }
                } else {
                    if ($this->request->isAjax()) {
                        return json(['code' => 0, 'msg' => $row->getError(), 'data' => '', 'url' => '', 'wait' => 3]);
                    } else {
                        $this->error($row->getError());
                    }
                }
            }
            if ($this->request->isAjax()) {
                return json(['code' => 0, 'msg' => __('Parameter %s can not be empty', ''), 'data' => '', 'url' => '', 'wait' => 3]);
            } else {
                $this->error(__('Parameter %s can not be empty', ''));
            }
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 更新放款额度
     */
    public function updateLimit($ids = null)
    {
        $channel = $this->model->get($ids);
        if (!$channel) {
            $this->error(__('渠道不存在'));
        }

        // 计算已分配额度
        $totalCredit = floatval($channel->getTotalCreditLimitAttr(null, $channel->toArray()));
        $currentCreditLeft = floatval($channel->credit_left);
        $allocatedCredit = $totalCredit - $currentCreditLeft;

        if ($this->request->isPost()) {
            $creditChange = floatval($this->request->post('credit_change', 0));

            // 如果是减少额度（负数），检查是否超过可减额度
            if ($creditChange < 0 && abs($creditChange) > $currentCreditLeft) {
                $this->error(__('减少的额度不能超过可减额度 ') . number_format($currentCreditLeft, 2));
            }

            // 计算新的额度
            $newCreditLeft = $currentCreditLeft + $creditChange;

            // 开始事务
            $this->model->startTrans();

            // 更新渠道额度
            $channel->credit_left = $newCreditLeft;
            $result = $channel->save();

            if ($result !== false) {
                // 同时更新fa_admin表的updatetime字段
                $admin_id = $channel->admin_id;
                if ($admin_id) {
                    Db::name('admin')->where('id', $admin_id)->update([
                        'updatetime' => time()
                    ]);
                }

                // 提交事务
                $this->model->commit();

                // 直接返回成功响应
                $this->success('额度更新成功', 'channels/channels/index');
            } else {
                // 回滚事务
                $this->model->rollback();
                $this->error(__('更新失败'));
            }
        }

        // 将数据传递给视图
        $this->view->assign('channel', $channel);
        $this->view->assign('allocated_credit', number_format($allocatedCredit, 2));
        return $this->view->fetch();
    }

    /**
     * 解绑谷歌验证
     */
    public function unbindGoogle()
    {
        $params = $this->request->post();
        $id = $params['id'] ?? 0;

        if (!$id) {
            return json(['code' => 0, 'msg' => __('参数错误'), 'data' => '', 'url' => '', 'wait' => 3]);
        }

        $channel = ChannelsModel::get($id);
        if (!$channel) {
            return json(['code' => 0, 'msg' => __('渠道不存在'), 'data' => '', 'url' => '', 'wait' => 3]);
        }

        // 获取关联的admin_id
        $admin_id = $channel->admin_id;
        if (!$admin_id) {
            return json(['code' => 0, 'msg' => __('管理员不存在'), 'data' => '', 'url' => '', 'wait' => 3]);
        }

        // 开始事务
        $this->model->startTrans();

        try {
            // 更新admin表中的two_factor_key字段为null，同时更新updatetime字段
            $result = Db::name('admin')->where('id', $admin_id)->update([
                'two_factor_key' => null,
                'updatetime' => time()
            ]);

            if ($result !== false) {
                // 更新channels表的updated_at字段
                $channel->updated_at = date('Y-m-d H:i:s');
                $channel->save();

                // 提交事务
                $this->model->commit();
                return json(['code' => 1, 'msg' => '解绑成功', 'data' => '', 'url' => '', 'wait' => 3]);
            } else {
                // 回滚事务
                $this->model->rollback();
                return json(['code' => 0, 'msg' => __('解绑失败'), 'data' => '', 'url' => '', 'wait' => 3]);
            }
        } catch (\Exception $e) {
            // 回滚事务
            $this->model->rollback();
            // 记录错误信息
            \think\Log::write('解绑谷歌验证失败: ' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => __('解绑失败'), 'data' => '', 'url' => '', 'wait' => 3]);
        }
    }

    /**
     * 设置渠道RTP值
     */
    public function set_rtp()
    {
        // 获取当前用户角色组ID
        $roleGroupIds = $this->auth->getGroupIds();

        // 只允许渠道角色(4)访问
        if (!in_array(4, $roleGroupIds)) {
            $this->error(__('No permission'));
        }

        // 获取当前渠道ID
        $channelId = \app\admin\library\SqlFilter::getCurrentChannelId();
        if (!$channelId) {
            $this->error(__('Channel not found'));
        }

        // 获取渠道信息
        $channel = $this->model->get($channelId);
        if (!$channel) {
            $this->error(__('Channel not found'));
        }

        if ($this->request->isPost()) {
            $rtp = $this->request->post('rtp');

            // 验证RTP值
            if (!is_numeric($rtp) || $rtp < 0 || $rtp > 100) {
                $this->error(__('RTP value must be between 0 and 100'));
            }

            // 更新渠道RTP值
            $channel->rtp = intval($rtp);
            $result = $channel->save();

            if ($result !== false) {
                // 返回JSON响应
                return json([
                    'code' => 1,
                    'msg' => __('RTP updated successfully'),
                    'data' => ['rtp' => $channel->rtp]
                ]);
            } else {
                // 返回JSON错误响应
                return json([
                    'code' => 0,
                    'msg' => __('Failed to update RTP'),
                    'data' => null
                ]);
            }
        }

        // 渲染视图
        $this->view->assign('channel', $channel);
        return $this->view->fetch();
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 以渠道身份登录
     * @param int $ids 渠道ID
     */
    public function login($ids = null)
    {
        if (!$this->auth->check('channels/channels/edit')) {
            $this->error(__('You have no permission'));
        }

        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 获取关联的管理员信息
        $admin = Admin::get($row['admin_id']);

        if (!$admin) {
            $this->error(__('Associated admin account not found'));
        }

        if ($admin['status'] != 'normal') {
            $this->error(__('This account is disabled'));
        }

        // 生成一个唯一的令牌，用于跨域登录验证
        $token = md5(uniqid(mt_rand(), true));

        // 记录管理员操作日志
        $channelName = $row['name'];
        \think\Log::write('管理员(' . $this->auth->id . ')以渠道身份登录: channel_id=' . $ids . ', username=' . $admin['username'] . ', name=' . $channelName, 'info');

        // 获取清理后的域名（去除admin、channel或agent前缀）
        $cleanDomain = get_clean_domain();

        // 构建渠道域名
        $urlParts = parse_url($cleanDomain);
        $scheme = isset($urlParts['scheme']) ? $urlParts['scheme'] : 'http';
        $host = isset($urlParts['host']) ? $urlParts['host'] : '';

        // 确保主机名有效
        if (empty($host)) {
            $host = request()->host();
            // 移除可能的前缀
            $hostParts = explode('.', $host);
            if (count($hostParts) > 2 && in_array($hostParts[0], ['admin', 'channel', 'agent'])) {
                array_shift($hostParts);
                $host = implode('.', $hostParts);
            }
        }

        $channelDomain = $scheme . '://channel.' . $host;

        // 将渠道信息和令牌存储在缓存中，设置较短的过期时间（例如5分钟）
        \think\Cache::set('channel_login_' . $token, [
            'admin_id' => $admin['id'],
            'username' => $admin['username'],
            'time' => time()
        ], 300);

        // 构建登录URL，包含token参数
        $loginUrl = $channelDomain . '/index/channel_login?token=' . $token;

        // 返回成功信息和登录URL
        $this->success(__('Preparing to login as channel'), null, ['url' => $loginUrl]);
    }
}
