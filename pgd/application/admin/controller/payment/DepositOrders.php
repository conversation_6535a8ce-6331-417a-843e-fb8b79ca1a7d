<?php

namespace app\admin\controller\payment;

use app\common\controller\Backend;
use app\admin\service\BalanceService;
use think\Db;
use think\Exception;

/**
 * 充值订单管理
 *
 * @icon fa fa-circle-o
 */
class DepositOrders extends Backend
{

    /**
     * Deposit_orders模型对象
     * @var \app\admin\model\payment\DepositOrders
     */
    protected $model = null;

    /**
     * 无需鉴权的方法,但需要登录
     * @var array
     */
    protected $noNeedRight = ['query_status'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\payment\DepositOrders;
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            // 获取所有玩家标签
            $playerTagList = \app\admin\model\players\PlayerTags::all();
            $this->view->assign('playerTagList', $playerTagList);

            // 获取所有渠道
            $channelList = \app\admin\model\channels\Channels::all();
            $this->view->assign('channelList', $channelList);

            // 获取所有业务员
            $agentList = \app\admin\model\agents_config\Agents::with(['admin'])->select();
            $this->view->assign('agentList', $agentList);

            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();

        // 获取筛选参数
        $filter_str = $this->request->get('filter', '');
        $filter = $filter_str ? json_decode($filter_str, true) : [];
        $tag_id = isset($filter['tag_id']) ? $filter['tag_id'] : null;
        $channel_id = isset($filter['channel_id']) ? $filter['channel_id'] : null;

        // 初始化查询构造器
        $query = $this->model;

        // 初始化别名和字段，关联支付通道表获取通道名称
        $query->alias('do')
              ->join(['payment_channels'=>'pc'], 'do.channel_code = pc.code', 'left')
              ->field('do.*, pc.name as payment_channel_name');

        // 如果有玩家标签筛选，使用联合查询
        if ($tag_id && $tag_id != 'all') {
            $query->join(['players'=>'p'], 'do.player_id = p.id')
                  ->where('p.tag_id', $tag_id);
        }

        // 如果有渠道筛选，使用联合查询
        if ($channel_id && $channel_id != 'all') {
            $query->join(['agents'=>'ag'], 'do.agent_id = ag.id', 'left')
                  ->where('ag.channel_id', $channel_id);
        }

        // 获取当前用户角色组ID
        $roleGroupId = $this->getCurrentRoleGroupId();

        // 根据角色类型应用不同的过滤条件
        if ($roleGroupId == 4) { // 渠道角色
            $channelId = $this->getCurrentChannelId();
            if ($channelId) {
                // 确保已经关联agents表
                if (!$channel_id || $channel_id == 'all') {
                    $query->join(['agents'=>'ag'], 'do.agent_id = ag.id', 'left');
                }
                $query->where('ag.channel_id', $channelId);
            }
        } elseif ($roleGroupId == 6) { // 业务员角色
            $agentId = $this->getCurrentAgentId();
            if ($agentId) {
                $query->where('do.agent_id', $agentId);
            }
        }

        // 设置排序字段前缀
        if(isset($sort)) $sort = "do.$sort";

        // 应用原始的where条件
        $query->where($where);

        // 执行查询
        $list = $query->order($sort, $order)->paginate($limit);
        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    /**
     * 补单操作
     */
    public function manual_fill()
    {
        $ids = $this->request->param('ids');
        if (!$ids) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $order = $this->model->where('id', $ids)->find();
        if (!$order) {
            $this->error(__('Order not found'));
        }

        // 如果订单已经是补单状态，则不需要再次补单
        if ($order['is_manual_fill'] == 1) {
            $this->error(__('Order already manually filled'));
        }

        // 如果订单已经支付，则不需要补单
        if ($order['payment_status'] == 1) {
            $this->error(__('Order already paid'));
        }

        // 开始事务
        \think\Db::startTrans();
        try {
            // 更新订单状态
            $order->is_manual_fill = 1;
            $order->payment_status = 1;
            $order->paid_at = date('Y-m-d H:i:s');
            $order->remark = $order->remark ? $order->remark . ' | ' . __('Admin manual fill') : __('Admin manual fill');
            $order->save();

            // 获取玩家信息
            $player = \app\admin\model\players\Players::get($order['player_id']);
            if (!$player) {
                throw new Exception(__('Player not found'));
            }

            // 处理充值本金的余额变更
            BalanceService::change(
                $order['player_id'],
                $order['amount'],
                6, // 6-充值本金
                '管理员手动补单，订单ID: ' . $order['id'],
                $this->auth->username,
                0 // 0-管理员
            );

            // 如果有赠送金额，处理赠送金额的余额变更
            if ($order['gift_amount'] > 0) {
                BalanceService::change(
                    $order['player_id'],
                    $order['gift_amount'],
                    3, // 3-一般是首充返现
                    '首充赠送金额，订单ID: ' . $order['id'],
                    $this->auth->username,
                    0 // 0-管理员
                );
            }

            \think\Db::commit();
            // 返回成功响应，确保 code 为 1
            return json(["code" => 1, "msg" => __('Manual fill successful'), "data" => null]);
        } catch (\Exception $e) {
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
    }

    /**
     * 查询支付状态
     * 调用支付方API查询订单支付状态
     */
    public function query_status()
    {
        $ids = $this->request->param('ids');
        if (!$ids) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $order = $this->model->where('id', $ids)->find();
        if (!$order) {
            $this->error(__('Order not found'));
        }

        // 商户订单号必须存在，不再需要后向兼容代码

        // 调用查询交易订单方法
        $result = $this->queryTradeOrder($order['merchant_order_no']);

        if ($result['success']) {
            // 如果查询成功，更新订单状态
            // $this->updateOrderStatus($order, $result['data']);
            return json(["code" => 1, "msg" => __('Query success') . ': ' . $this->getStatusText($result['data']['status']), "data" => $result['data']]);
        } else {
            // 查询失败
            $this->error($result['message']);
        }
    }

    /**
     * 查询交易订单
     * 调用支付网关API查询订单状态
     *
     * @param string $merchantOrderNo 商户订单号
     * @return array 查询结果
     */
    protected function queryTradeOrder($merchantOrderNo)
    {
        // 调用支付API服务
        return \app\admin\service\PaymentApiService::queryOrder($merchantOrderNo);
    }

    /**
     * 更新订单状态
     *
     * @param object $order 订单对象
     * @param array $apiData API返回的数据
     * @return bool 是否更新成功
     */
    protected function updateOrderStatus($order, $apiData)
    {
        // 如果订单已支付，不需要更新
        if ($order['payment_status'] == 1) {
            return true;
        }

        // 检查API返回的状态
        if (isset($apiData['status'])) {
            $updateData = [];

            // 根据API返回的状态更新订单
            switch ($apiData['status']) {
                case 'PAID': // 实际返回的支付成功状态
                    $updateData = [
                        'payment_status' => 1,
                        'paid_at' => date('Y-m-d H:i:s'),
                        'third_order_no' => $apiData['orderNo'] ?? '',
                        'remark' => ($order['remark'] ? $order['remark'] . ' | ' : '') . '自动查询更新: 支付成功'
                    ];
                    break;

                case 'FAIL': // 支付失败
                    $updateData = [
                        'payment_status' => 2, // 2表示支付失败
                        'remark' => ($order['remark'] ? $order['remark'] . ' | ' : '') . '自动查询更新: 支付失败'
                    ];
                    break;

                case 'PENDING': // 处理中
                    // 不更新状态，只添加备注
                    $updateData = [
                        'remark' => ($order['remark'] ? $order['remark'] . ' | ' : '') . '自动查询更新: 处理中'
                    ];
                    break;

                default: // 未知状态
                    // 记录未知状态，但不更新订单状态
                    $updateData = [
                        'remark' => ($order['remark'] ? $order['remark'] . ' | ' : '') . '自动查询更新: 未知状态(' . $apiData['status'] . ')'
                    ];
                    break;
            }

            // 如果有需要更新的数据
            if (!empty($updateData)) {
                // 更新订单
                $result = $this->model->where('id', $order['id'])->update($updateData);

                // 如果订单状态变为已支付，处理玩家余额变更
                if (isset($updateData['payment_status']) && $updateData['payment_status'] == 1) {
                    // 处理充值本金的余额变更
                    BalanceService::change(
                        $order['player_id'],
                        $order['amount'],
                        6, // 6-充值本金
                        '支付成功，订单ID: ' . $order['id'],
                        'System',
                        0 // 0-系统
                    );

                    // 如果有赠送金额，处理赠送金额的余额变更
                    if ($order['gift_amount'] > 0) {
                        BalanceService::change(
                            $order['player_id'],
                            $order['gift_amount'],
                            4, // 4-单笔充值返利奖励
                            '充值赠送金额，订单ID: ' . $order['id'],
                            'System',
                            0 // 0-系统
                        );
                    }
                }

                return $result !== false;
            }
        }

        return false;
    }

    /**
     * 获取状态文本
     *
     * @param string $status 状态码
     * @return string 状态文本
     */
    protected function getStatusText($status)
    {
        $statusMap = [
            'PAID' => __('Payment successful'), // 实际返回的支付成功状态
            'FAIL' => __('Payment failed'),
            'PENDING' => __('Payment pending'),
        ];

        // 如果状态不在映射表中，返回原始状态和未知状态提示
        if (!isset($statusMap[$status])) {
            return __('Unknown status') . ' (' . $status . ')';
        }

        return $statusMap[$status];
    }
}
