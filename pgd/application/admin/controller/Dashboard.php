<?php

namespace app\admin\controller;

use app\admin\model\Admin;
use app\admin\model\User;
use app\common\controller\Backend;
use app\common\model\Attachment;
use fast\Date;
use think\Db;
use think\Exception;
use app\admin\library\SqlFilter;

/**
 * 控制台
 *
 * @icon   fa fa-dashboard
 * @remark 用于展示当前系统中的统计数据、统计报表及重要实时数据
 */
class Dashboard extends Backend
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

    /**
     * PDO数据库连接对象
     */
    protected $pdo = null;

    /**
     * 数据库配置
     */
    protected $dbConfig = [];

    /**
     * 初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();
        // 获取数据库配置
        $this->dbConfig = config('database');
        // 初始化PDO连接
        $this->initPDO();
    }

    /**
     * 初始化PDO连接
     */
    private function initPDO()
    {
        if ($this->pdo === null) {
            try {
                $dsn = "mysql:host={$this->dbConfig['hostname']};dbname={$this->dbConfig['database']};charset=utf8";
                $options = [
                    \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                    \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                    \PDO::ATTR_EMULATE_PREPARES => false,
                ];
                $this->pdo = new \PDO($dsn, $this->dbConfig['username'], $this->dbConfig['password'], $options);

                // 设置SQL会话时区为America/Sao_Paulo
                $this->pdo->exec("SET time_zone = 'America/Sao_Paulo'");
            } catch (\PDOException $e) {
                throw new Exception("数据库连接失败：" . $e->getMessage());
            }
        }
    }

    /**
     * 执行SQL查询
     *
     * @param string $sql SQL查询语句
     * @param array $params 绑定参数
     * @return \PDOStatement
     */
    private function execute($sql, $params = [])
    {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (\PDOException $e) {
            throw new Exception("SQL执行失败：" . $e->getMessage() . " SQL: $sql");
        }
    }

    /**
     * 查询多条记录
     *
     * @param string $table 表名
     * @param array $conditions 查询条件，关联数组 ['字段名' => '值']
     * @param string $fields 要查询的字段，默认为 *
     * @param string $orderBy 排序条件
     * @param int $limit 限制条数
     * @param int $offset 偏移量
     * @return array 查询结果
     */
    private function db_select($table, $conditions = [], $fields = '*', $orderBy = '', $limit = 0, $offset = 0)
    {
        try {
            // 构建SQL
            $sql = "SELECT $fields FROM $table";
            $params = [];

            // 添加WHERE条件
            if (!empty($conditions)) {
                $whereClause = [];
                foreach ($conditions as $field => $value) {
                    $whereClause[] = "$field = :$field";
                    $params[":$field"] = $value;
                }
                $sql .= " WHERE " . implode(' AND ', $whereClause);
            }

            // 应用基于角色的数据过滤
            $roleGroupId = SqlFilter::getCurrentRoleGroupId();
            $whereAdded = !empty($conditions);

            // 根据角色类型应用不同的过滤条件
            if ($roleGroupId == 4) { // 渠道角色
                $channelId = SqlFilter::getCurrentChannelId();
                if ($channelId) {
                    if ($table == 'daily_statistics') {
                        // 对于daily_statistics表，需要通过agent_id进行过滤
                        // 获取该渠道下的所有业务员ID
                        $agentIds = SqlFilter::getAgentIdsByChannelId($channelId);

                        if (!empty($agentIds)) {
                            $placeholders = [];
                            foreach ($agentIds as $index => $agentId) {
                                $paramName = ":agent_id_$index";
                                $placeholders[] = $paramName;
                                $params[$paramName] = $agentId;
                            }

                            $agentIdClause = "agent_id IN (" . implode(',', $placeholders) . ")";

                            if ($whereAdded) {
                                $sql .= " AND $agentIdClause";
                            } else {
                                $sql .= " WHERE $agentIdClause";
                                $whereAdded = true;
                            }
                        } else {
                            // 如果没有业务员，确保不返回任何数据
                            if ($whereAdded) {
                                $sql .= " AND agent_id = -1";
                            } else {
                                $sql .= " WHERE agent_id = -1";
                                $whereAdded = true;
                            }
                        }
                    }
                }
            } elseif ($roleGroupId == 6) { // 业务员角色
                $agentId = SqlFilter::getCurrentAgentId();
                if ($agentId && $table == 'daily_statistics') {
                    if ($whereAdded) {
                        $sql .= " AND agent_id = :agent_id_filter";
                    } else {
                        $sql .= " WHERE agent_id = :agent_id_filter";
                        $whereAdded = true;
                    }
                    $params[':agent_id_filter'] = $agentId;
                }
            }

            // 添加ORDER BY子句
            if (!empty($orderBy)) {
                $sql .= " ORDER BY $orderBy";
            }

            // 添加LIMIT子句
            if ($limit > 0) {
                $sql .= " LIMIT :offset, :limit";
                $params[':offset'] = (int)$offset;
                $params[':limit'] = (int)$limit;
            }

            // 执行查询
            $stmt = $this->execute($sql, $params);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * 查询单条记录
     *
     * @param string $table 表名
     * @param array $conditions 查询条件
     * @param string $fields 要查询的字段，默认为 *
     * @return array|null 查询结果
     */
    private function db_find($table, $conditions = [], $fields = '*')
    {
        try {
            $result = $this->db_select($table, $conditions, $fields, '', 1);
            return !empty($result) ? $result[0] : null;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * 计数查询
     *
     * @param string $table 表名
     * @param array $conditions 查询条件
     * @return int 记录数量
     */
    private function db_count($table, $conditions = [])
    {
        try {
            // 构建SQL
            $sql = "SELECT COUNT(*) as count FROM $table";
            $params = [];

            // 添加WHERE条件
            $whereClause = [];
            $paramCounter = 0; // 用于生成唯一参数名

            if (!empty($conditions)) {
                foreach ($conditions as $key => $value) {
                    if (is_array($value) && count($value) === 3) {
                        // 支持自定义操作符：['created_at', '>=', '2023-01-01']
                        $field = $value[0];
                        $operator = $value[1];
                        $paramName = ":{$field}_" . $paramCounter++; // 确保参数名唯一
                        $whereClause[] = "{$field} {$operator} {$paramName}";
                        $params[$paramName] = $value[2];
                    } else {
                        // 普通键值对条件
                        $paramName = ":{$key}_" . $paramCounter++; // 确保参数名唯一
                        $whereClause[] = "{$key} = {$paramName}";
                        $params[$paramName] = $value;
                    }
                }
            }

            // 应用基于角色的数据过滤
            $roleGroupId = SqlFilter::getCurrentRoleGroupId();

            // 根据角色类型应用不同的过滤条件
            if ($roleGroupId == 4) { // 渠道角色
                $channelId = SqlFilter::getCurrentChannelId();
                if ($channelId) {
                    if ($table == 'daily_statistics') {
                        // 对于daily_statistics表，需要通过agent_id进行过滤
                        // 获取该渠道下的所有业务员ID
                        $agentIds = SqlFilter::getAgentIdsByChannelId($channelId);

                        if (!empty($agentIds)) {
                            $placeholders = [];
                            foreach ($agentIds as $index => $agentId) {
                                $paramName = ":agent_id_$index";
                                $placeholders[] = $paramName;
                                $params[$paramName] = $agentId;
                            }

                            $whereClause[] = "agent_id IN (" . implode(',', $placeholders) . ")";
                        } else {
                            // 如果没有业务员，确保不返回任何数据
                            $whereClause[] = "agent_id = -1";
                        }
                    } elseif ($table == 'players') {
                        // 对于players表，直接通过channel_id进行过滤
                        $paramName = ":channel_id_filter";
                        $whereClause[] = "channel_id = {$paramName}";
                        $params[$paramName] = $channelId;
                    }
                }
            } elseif ($roleGroupId == 6) { // 业务员角色
                $agentId = SqlFilter::getCurrentAgentId();
                if ($agentId) {
                    if ($table == 'daily_statistics') {
                        $paramName = ":agent_id_filter";
                        $whereClause[] = "agent_id = {$paramName}";
                        $params[$paramName] = $agentId;
                    } elseif ($table == 'players') {
                        $paramName = ":agent_id_filter";
                        $whereClause[] = "agent_id = {$paramName}";
                        $params[$paramName] = $agentId;
                    }
                }
            }

            // 添加WHERE子句
            if (!empty($whereClause)) {
                $sql .= " WHERE " . implode(' AND ', $whereClause);
            }

            // 执行查询
            $stmt = $this->execute($sql, $params);
            $result = $stmt->fetch();

            return (int)($result['count'] ?? 0);
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 求和查询
     *
     * @param string $table 表名
     * @param string $field 要求和的字段
     * @param array $conditions 查询条件
     * @return float 求和结果
     */
    private function db_sum($table, $field, $conditions = [])
    {
        try {
            // 构建SQL
            $sql = "SELECT SUM($field) as total FROM $table";
            $params = [];

            // 添加WHERE条件
            $whereClause = [];
            $paramCounter = 0; // 用于生成唯一参数名

            if (!empty($conditions)) {
                foreach ($conditions as $key => $value) {
                    if (is_array($value) && count($value) === 3) {
                        // 支持自定义操作符：['created_at', '>=', '2023-01-01']
                        $fieldName = $value[0];
                        $operator = $value[1];
                        $paramName = ":{$fieldName}_" . $paramCounter++; // 确保参数名唯一
                        $whereClause[] = "{$fieldName} {$operator} {$paramName}";
                        $params[$paramName] = $value[2];
                    } else {
                        // 普通键值对条件
                        $paramName = ":{$key}_" . $paramCounter++; // 确保参数名唯一
                        $whereClause[] = "{$key} = {$paramName}";
                        $params[$paramName] = $value;
                    }
                }
            }

            // 应用基于角色的数据过滤
            $roleGroupId = SqlFilter::getCurrentRoleGroupId();

            // 根据角色类型应用不同的过滤条件
            if ($roleGroupId == 4) { // 渠道角色
                $channelId = SqlFilter::getCurrentChannelId();
                if ($channelId) {
                    if ($table == 'daily_statistics') {
                        // 对于daily_statistics表，需要通过agent_id进行过滤
                        // 获取该渠道下的所有业务员ID
                        $agentIds = SqlFilter::getAgentIdsByChannelId($channelId);

                        if (!empty($agentIds)) {
                            $placeholders = [];
                            foreach ($agentIds as $index => $agentId) {
                                $paramName = ":agent_id_$index";
                                $placeholders[] = $paramName;
                                $params[$paramName] = $agentId;
                            }

                            $whereClause[] = "agent_id IN (" . implode(',', $placeholders) . ")";
                        } else {
                            // 如果没有业务员，确保不返回任何数据
                            $whereClause[] = "agent_id = -1";
                        }
                    } elseif ($table == 'players') {
                        // 对于players表，直接通过channel_id进行过滤
                        $paramName = ":channel_id_filter";
                        $whereClause[] = "channel_id = {$paramName}";
                        $params[$paramName] = $channelId;
                    }
                }
            } elseif ($roleGroupId == 6) { // 业务员角色
                $agentId = SqlFilter::getCurrentAgentId();
                if ($agentId) {
                    if ($table == 'daily_statistics') {
                        $paramName = ":agent_id_filter";
                        $whereClause[] = "agent_id = {$paramName}";
                        $params[$paramName] = $agentId;
                    } elseif ($table == 'players') {
                        $paramName = ":agent_id_filter";
                        $whereClause[] = "agent_id = {$paramName}";
                        $params[$paramName] = $agentId;
                    }
                }
            }

            // 添加WHERE子句
            if (!empty($whereClause)) {
                $sql .= " WHERE " . implode(' AND ', $whereClause);
            }

            // 执行查询
            $stmt = $this->execute($sql, $params);
            $result = $stmt->fetch();

            return floatval($result['total'] ?? 0);
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 执行原生SQL查询
     *
     * @param string $sql SQL语句
     * @param array $params 绑定参数
     * @return array 查询结果
     */
    private function db_query($sql, $params = [])
    {
        try {
            $stmt = $this->execute($sql, $params);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }


    /**
     * 查看
     */
    public function index()
    {
        try {
            // 获取卡片数据
            $cards = $this->getCards();
            $this->assign('cards', $cards);

            // 获取表格数据
            $tableData = $this->getTableData();
            $this->assign('tableData', $tableData);

            return $this->view->fetch();
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 获取卡片数据
     */
    protected function getCards()
    {
        try {
            $cards = [];
            $cardTypes = $this->db_select(
                'cards',
                ['status' => 1]
            );

            if (!$cardTypes) {
                return [];
            }

            foreach ($cardTypes as $card) {
                $cards[$card['identifier']] = [
                    'name' => $card['name'],
                    'value' => $this->getCardValue($card['identifier']),
                    'stat_name' => $card['stat_name'],
                    'stat_value' => $this->getCardValue($card['identifier'], false, true), // 传递第三个参数，表示获取全部统计
                    'yesterday_value' => $this->getCardValue($card['identifier'], true)
                ];
            }

            return $cards;
        } catch (Exception $e) {
            throw new Exception('获取卡片数据失败：' . $e->getMessage());
        }
    }



    /**
     * 获取表格数据
     */
    protected function getTableData()
    {
        try {
            $data = [];
            // 获取最近15天的数据
            for ($i = 0; $i <= 14; $i++) {
                $date = date('Y-m-d', strtotime("-$i days"));
                $startTime = strtotime($date . ' 00:00:00');
                $endTime = strtotime($date . ' 23:59:59');

                // 获取各项统计数据
                $row = [
                    'date' => $date,
                    'new_players' => $this->getNewPlayer($startTime, $endTime),
                    'active_players' => $this->getActivePlayer($startTime, $endTime),
                    'first_deposit_count' => $this->getFirstDepositCount($startTime, $endTime),
                    'deposit_players' => $this->getDepositPlayers($startTime, $endTime),
                    'deposit_amount' => $this->getDepositAmount($startTime, $endTime),
                    'withdraw_players' => $this->getWithdrawPlayers($startTime, $endTime),
                    'withdraw_amount' => $this->getWithdrawAmount($startTime, $endTime),
                    'deposit_diff' => $this->getDepositDiff($startTime, $endTime),
                    'game_profit' => $this->getGameProfit($startTime, $endTime)
                ];

                $data[] = $row;
            }
            return $data;
        } catch (Exception $e) {
            throw new Exception('获取表格数据失败：' . $e->getMessage());
        }
    }

    /**
     * 根据标识获取卡片值
     */
    protected function getCardValue($identifier, $isYesterday = false, $isTotal = false)
    {
        try {
            $startTime = null;
            $endTime = null;

            // 如果不是获取全部统计数据，则设置时间范围
            if (!$isTotal) {
                // 设置时间范围
                $today = date('Y-m-d');
                $yesterday = date('Y-m-d', strtotime('-1 day'));

                $date = $isYesterday ? $yesterday : $today;
                $startTime = strtotime($date . ' 00:00:00');
                $endTime = strtotime($date . ' 23:59:59');
            }

            // 根据不同的标识返回相应的数据
            switch ($identifier) {
                // 用户相关
                case 'register_count':
                    return $this->getTotalPlayers($startTime, $endTime);
                case 'login_count':
                    return $this->getActivePlayer($startTime, $endTime);

                // 充提差相关
                case 'deposit_diff':
                    return $this->getDepositDiff($startTime, $endTime);
                case 'actual_deposit_diff':
                    return $this->getActualDepositDiff($startTime, $endTime);

                // 充值相关
                case 'deposit_amount':
                    return $this->getDepositAmount($startTime, $endTime);
                case 'actual_deposit':
                    return $this->getActualDepositAmount($startTime, $endTime);
                case 'deposit_players':
                    return $this->getDepositPlayers($startTime, $endTime);
                case 'deposit_count':
                    return $this->getDepositCount($startTime, $endTime);

                // 提现相关
                case 'withdraw_amount':
                    return $this->getWithdrawAmount($startTime, $endTime);
                case 'actual_withdraw':
                    return $this->getActualWithdrawAmount($startTime, $endTime);
                case 'withdraw_players':
                    return $this->getWithdrawPlayers($startTime, $endTime);
                case 'total_withdraw_count':
                    return $this->getWithdrawCount($startTime, $endTime);

                // 首充相关
                case 'first_deposit_count':
                    return $this->getFirstDepositCount($startTime, $endTime);
                case 'first_deposit_amount':
                    return $this->getFirstDepositAmount($startTime, $endTime);

                // 复充相关
                case 'repeat_deposit_players':
                    return $this->getRepeatDepositPlayers($startTime, $endTime);
                case 'repeat_deposit_count':
                    return $this->getDepositCount($startTime, $endTime) - $this->getFirstDepositCount($startTime, $endTime);
                case 'repeat_deposit_amount':
                    return $this->getRepeatDepositAmount($startTime, $endTime);

                // 比率相关
                case 'withdraw_rate':
                    return $this->getWithdrawRate($startTime, $endTime);
                case 'repeat_deposit_rate':
                    return $this->getRepeatDepositRate($startTime, $endTime);
                case 'active_rate':
                    return $this->getActiveRate($startTime, $endTime);

                // 游戏相关
                case 'order_amount':
                    return $this->getOrderAmount($startTime, $endTime);
                case 'payout_amount':
                    return $this->getPayoutAmount($startTime, $endTime);
                case 'game_profit':
                    return $this->getGameProfit($startTime, $endTime);

                // 手动调整相关
                case 'manual_increase':
                    return $this->getManualIncrease($startTime, $endTime);
                case 'manual_decrease':
                    return $this->getManualDecrease($startTime, $endTime);

                default:
                    return '0.00';
            }
        } catch (Exception $e) {
            throw new Exception('获取卡片值失败：' . $e->getMessage());
        }
    }



    /**
     * 获取新增用户数
     */
    protected function getNewPlayer($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取新增用户总数
            $newPlayers = $this->db_sum(
                'daily_statistics',
                'new_players',
                $conditions
            );

            return intval($newPlayers);
        } catch (Exception $e) {
            throw new Exception('获取新增用户数失败：' . $e->getMessage());
        }
    }

    /**
     * 获取活跃用户数
     * 根据game_sessions表统计在指定时间范围内有游戏会话且玩家ID大于180004的用户数量
     * 如果时间参数为null，则查询最近半小时内有游戏会话的用户
     * 根据管理员角色过滤数据
     */
    protected function getActivePlayer($startTime, $endTime)
    {
        try {
            // 如果没有提供时间范围，则查询最近半小时内有游戏会话的用户
            if ($startTime === null || $endTime === null) {
                // 计算半小时前的时间
                $halfHourAgo = date('Y-m-d H:i:s', time() - 1800); // 1800秒 = 30分钟
                $now = date('Y-m-d H:i:s');

                // 基础SQL查询
                $sql = "SELECT COUNT(DISTINCT gs.player_id) as active_count
                        FROM game_sessions gs
                        JOIN players p ON gs.player_id = p.id
                        WHERE gs.created_at >= :start_time
                        AND gs.created_at <= :end_time
                        AND gs.player_id > 180004";

                $params = [
                    ':start_time' => $halfHourAgo,
                    ':end_time' => $now
                ];
            } else {
                // 转换时间戳为日期时间格式
                $startTimeStr = date('Y-m-d H:i:s', $startTime);
                $endTimeStr = date('Y-m-d H:i:s', $endTime);

                // 基础SQL查询
                $sql = "SELECT COUNT(DISTINCT gs.player_id) as active_count
                        FROM game_sessions gs
                        JOIN players p ON gs.player_id = p.id
                        WHERE gs.created_at >= :start_time
                        AND gs.created_at <= :end_time
                        AND gs.player_id > 180004";

                $params = [
                    ':start_time' => $startTimeStr,
                    ':end_time' => $endTimeStr
                ];
            }

            // 应用基于角色的数据过滤
            $roleGroupId = SqlFilter::getCurrentRoleGroupId();

            // 根据角色类型应用不同的过滤条件
            if ($roleGroupId == 4) { // 渠道角色
                $channelId = SqlFilter::getCurrentChannelId();
                if ($channelId) {
                    $sql .= " AND p.channel_id = :channel_id";
                    $params[':channel_id'] = $channelId;
                }
            } elseif ($roleGroupId == 6) { // 业务员角色
                $agentId = SqlFilter::getCurrentAgentId();
                if ($agentId) {
                    $sql .= " AND p.agent_id = :agent_id";
                    $params[':agent_id'] = $agentId;
                }
            }

            // 执行查询
            $result = $this->db_query($sql, $params);

            // 获取结果
            $activePlayers = isset($result[0]['active_count']) ? intval($result[0]['active_count']) : 0;

            return $activePlayers;
        } catch (Exception $e) {
            throw new Exception('获取活跃用户数失败：' . $e->getMessage());
        }
    }

    /**
     * 获取首充用户数（只充值过一次的用户）
     */
    protected function getFirstDepositCount($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取首充用户总数
            $firstDepositPlayers = $this->db_sum(
                'daily_statistics',
                'first_deposit_players',
                $conditions
            );

            return intval($firstDepositPlayers);
        } catch (Exception $e) {
            throw new Exception('获取首充用户数失败：' . $e->getMessage());
        }
    }

    /**
     * 获取充值用户数
     */
    protected function getDepositPlayers($startTime, $endTime)
    {
        try {
            // 计算充值用户总数(首充+复充)
            $firstDepositPlayers = $this->getFirstDepositCount($startTime, $endTime);
            $repeatDepositPlayers = $this->getRepeatDepositPlayers($startTime, $endTime);

            return $firstDepositPlayers + $repeatDepositPlayers;
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 获取复充用户数
     */
    protected function getRepeatDepositPlayers($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取复充用户总数
            $repeatDepositPlayers = $this->db_sum(
                'daily_statistics',
                'repeat_deposit_players',
                $conditions
            );

            return intval($repeatDepositPlayers);
        } catch (Exception $e) {
            throw new Exception('获取复充用户数失败：' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取充值金额
     */
    protected function getDepositAmount($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取充值总金额
            $depositAmount = $this->db_sum(
                'daily_statistics',
                'total_deposit',
                $conditions
            );

            return number_format($depositAmount, 2, '.', '');
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 获取提现用户数
     */
    protected function getWithdrawPlayers($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取提现用户总数
            $withdrawPlayers = $this->db_sum(
                'daily_statistics',
                'withdraw_players',
                $conditions
            );

            return intval($withdrawPlayers);
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 获取提现金额
     */
    protected function getWithdrawAmount($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取提现总金额
            $withdrawAmount = $this->db_sum(
                'daily_statistics',
                'total_withdraw',
                $conditions
            );

            return number_format($withdrawAmount, 2, '.', '');
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 获取净收入（充值减提现）
     */
    protected function getDepositDiff($startTime, $endTime)
    {
        $depositAmount = $this->getDepositAmount($startTime, $endTime);
        $withdrawAmount = $this->getWithdrawAmount($startTime, $endTime);
        return number_format($depositAmount - $withdrawAmount, 2, '.', '');
    }

    /**
     * 获取实际代收金额（考虑支付渠道手续费）
     * 实际代收 = 代收金额 × (1 - payment_channels.percentage_fee/100)
     */
    protected function getActualDepositAmount($startTime, $endTime)
    {
        try {
            // 获取充值金额（已经应用了基于角色的数据过滤）
            $depositAmount = floatval(str_replace(',', '', $this->getDepositAmount($startTime, $endTime)));

            // 从数据库中获取平均手续费率
            $sql = "SELECT AVG(percentage_fee) as avg_fee FROM payment_channels WHERE is_active = 1";
            $result = $this->db_query($sql);

            // 获取平均手续费率，如果没有数据则使用默认值 0
            $avgFeeRate = isset($result[0]['avg_fee']) ? floatval($result[0]['avg_fee']) : 0;

            // 计算实际代收金额
            $actualAmount = $depositAmount * (1 - $avgFeeRate / 100);

            return number_format($actualAmount, 2, '.', '');
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 获取实际净收入（考虑支付渠道手续费）
     */
    protected function getActualDepositDiff($startTime, $endTime)
    {
        $actualDepositAmount = floatval(str_replace(',', '', $this->getActualDepositAmount($startTime, $endTime)));
        $actualWithdrawAmount = floatval(str_replace(',', '', $this->getActualWithdrawAmount($startTime, $endTime)));
        return number_format($actualDepositAmount - $actualWithdrawAmount, 2, '.', '');
    }

    /**
     * 获取实际提现金额（考虑支付渠道手续费）
     * 实际提现 = 提现金额 × (1 - fee/100)
     * 目前手续费率暂时设置为 0
     */
    protected function getActualWithdrawAmount($startTime, $endTime)
    {
        try {
            // 获取提现金额（已经应用了基于角色的数据过滤）
            $withdrawAmount = floatval(str_replace(',', '', $this->getWithdrawAmount($startTime, $endTime)));

            // 手续费率暂时设置为 0
            $feeRate = 0;

            // 计算实际提现金额
            $actualAmount = $withdrawAmount * (1 - $feeRate / 100);

            return number_format($actualAmount, 2, '.', '');
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 获取游戏盈亏
     */
    protected function getGameProfit($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取游戏盈亏总额
            $gameProfit = $this->db_sum(
                'daily_statistics',
                'total_game_profit',
                $conditions
            );

            return number_format($gameProfit, 2, '.', '');
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 获取用户总数
     */
    protected function getTotalPlayers($startTime = null, $endTime = null)
    {
        try {
            // 从 players 表中获取用户总数
            $conditions = [];

            // 如果提供了时间范围，则按创建时间筛选
            if ($startTime && $endTime) {
                $startTimeStr = date('Y-m-d H:i:s', $startTime);
                $endTimeStr = date('Y-m-d H:i:s', $endTime);

                // 使用自定义条件格式，支持操作符
                $conditions[] = ['created_at', '>=', $startTimeStr];
                $conditions[] = ['created_at', '<=', $endTimeStr];
            }

            $totalPlayers = $this->db_count('players', $conditions);
            return intval($totalPlayers);
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 获取活跃率
     */
    protected function getActiveRate($startTime, $endTime)
    {
        try {
            // 获取活跃用户数
            $activePlayers = $this->getActivePlayer($startTime, $endTime);

            // 获取用户总数
            $totalPlayers = $this->getTotalPlayers($startTime, $endTime);

            // 计算活跃率
            if ($totalPlayers > 0) {
                $activeRate = ($activePlayers / $totalPlayers) * 100;
                return number_format($activeRate, 2, '.', '') . '%';
            } else {
                return '0.00%';
            }
        } catch (Exception $e) {
            return '0.00%';
        }
    }

    /**
     * 获取充值笔数
     */
    protected function getDepositCount($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取充值笔数
            $depositCount = $this->db_sum(
                'daily_statistics',
                'total_deposit_count',
                $conditions
            );

            return intval($depositCount);
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 获取提现笔数
     */
    protected function getWithdrawCount($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取提现笔数
            $withdrawCount = $this->db_sum(
                'daily_statistics',
                'total_withdraw_count',
                $conditions
            );

            return intval($withdrawCount);
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 获取首充金额
     */
    protected function getFirstDepositAmount($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取首充金额
            $firstDepositAmount = $this->db_sum(
                'daily_statistics',
                'first_deposit_amount',
                $conditions
            );

            return number_format($firstDepositAmount, 2, '.', '');
        } catch (Exception $e) {
            return '0.00';
        }
    }

    /**
     * 获取复充金额
     */
    protected function getRepeatDepositAmount($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取复充金额
            $repeatDepositAmount = $this->db_sum(
                'daily_statistics',
                'repeat_deposit_amount',
                $conditions
            );

            return number_format($repeatDepositAmount, 2, '.', '');
        } catch (Exception $e) {
            return '0.00';
        }
    }

    /**
     * 获取复充率
     */
    protected function getRepeatDepositRate($startTime, $endTime)
    {
        try {
            // 获取充值用户总数
            $depositPlayers = $this->getDepositPlayers($startTime, $endTime);

            // 获取复充用户数
            $repeatDepositPlayers = $this->getRepeatDepositPlayers($startTime, $endTime);

            // 计算复充率
            if ($depositPlayers > 0) {
                $repeatRate = ($repeatDepositPlayers / $depositPlayers) * 100;
                return number_format($repeatRate, 2, '.', '') . '%';
            } else {
                return '0.00%';
            }
        } catch (Exception $e) {
            return '0.00%';
        }
    }

    /**
     * 获取提现率
     */
    protected function getWithdrawRate($startTime, $endTime)
    {
        try {
            // 获取充值金额
            $depositAmount = $this->getDepositAmount($startTime, $endTime);

            // 获取提现金额
            $withdrawAmount = $this->getWithdrawAmount($startTime, $endTime);

            // 计算提现率
            if ($depositAmount > 0) {
                $withdrawRate = ($withdrawAmount / $depositAmount) * 100;
                return number_format($withdrawRate, 2, '.', '') . '%';
            } else {
                return '0.00%';
            }
        } catch (Exception $e) {
            return '0.00%';
        }
    }

    /**
     * 获取下单金额
     */
    protected function getOrderAmount($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取下单金额
            $orderAmount = $this->db_sum(
                'daily_statistics',
                'total_bet',
                $conditions
            );

            return number_format($orderAmount, 2, '.', '');
        } catch (Exception $e) {
            return '0.00';
        }
    }

    /**
     * 获取派彩金额
     */
    protected function getPayoutAmount($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取派彩金额
            $payoutAmount = $this->db_sum(
                'daily_statistics',
                'total_win',
                $conditions
            );

            return number_format($payoutAmount, 2, '.', '');
        } catch (Exception $e) {
            return '0.00';
        }
    }

    /**
     * 获取手动加钱金额
     */
    protected function getManualIncrease($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取手动加钱金额
            $manualIncrease = $this->db_sum(
                'daily_statistics',
                'manual_increase',
                $conditions
            );

            return number_format($manualIncrease, 2, '.', '');
        } catch (Exception $e) {
            return '0.00';
        }
    }

    /**
     * 获取手动扣钱金额
     */
    protected function getManualDecrease($startTime, $endTime)
    {
        try {
            // 检查时间参数
            $conditions = [];
            if ($startTime !== null && $endTime !== null) {
                $conditions['stat_date'] = date('Y-m-d', $startTime);
            }

            // 从daily_statistics表中获取手动扣钱金额
            $manualDecrease = $this->db_sum(
                'daily_statistics',
                'manual_decrease',
                $conditions
            );

            return number_format($manualDecrease, 2, '.', '');
        } catch (Exception $e) {
            return '0.00';
        }
    }


}
