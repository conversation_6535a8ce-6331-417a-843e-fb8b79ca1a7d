<?php

namespace app\admin\controller\activity;

use app\common\controller\Backend;
use app\admin\model\channels\Channels;
use think\Db;

/**
 * 返利配置管理
 */
class RebateConfig extends Backend
{
    protected $model = null;
    protected $searchFields = 'id,activity_name';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\activity\RebateConfig;
    }

    /**
     * 查看
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $sort = $this->request->get('sort', 'id');
            $order = $this->request->get('order', 'desc');
            $offset = $this->request->get('offset', 0);
            $limit = $this->request->get('limit', 10);
            
            try {
                $total = $this->model->count();
                $list = $this->model
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();
                
                $list = collection($list)->toArray();
                $result = ['total' => $total, 'rows' => $list];
                return json($result);
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => $e->getMessage()]);
            }
        }
        
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        try {
            if ($this->request->isPost()) {
                $params = $this->request->post("row/a");
                
                if (empty($params)) {
                    return json(['code' => 0, 'msg' => '请填写完整的表单数据']);
                }

                // 检查必填字段
                $requiredFields = [
                    'activity_name' => '活动名称',
                    'channel_id' => '渠道',
                    'level1_first_deposit_rate' => '一级用户首充返利率',
                    'level1_first_deposit_max' => '一级用户首充返现最大金额',
                    'level2_first_deposit_rate' => '二级用户首充返利率',
                    'level2_first_deposit_max' => '二级用户首充返现最大金额',
                    'min_deposit_amount' => '最低充值金额',
                    'start_date' => '开始日期',
                    'end_date' => '结束日期'
                ];

                foreach ($requiredFields as $field => $label) {
                    if (!isset($params[$field]) || $params[$field] === '') {
                        return json(['code' => 0, 'msg' => "{$label}不能为空"]);
                    }
                }

                // 处理日期格式
                if (isset($params['start_date'])) {
                    $startTimestamp = strtotime($params['start_date']);
                    if ($startTimestamp === false) {
                        return json(['code' => 0, 'msg' => '开始日期格式不正确']);
                    }
                    $params['start_date'] = date('Y-m-d', $startTimestamp);
                }
                
                if (isset($params['end_date'])) {
                    $endTimestamp = strtotime($params['end_date']);
                    if ($endTimestamp === false) {
                        return json(['code' => 0, 'msg' => '结束日期格式不正确']);
                    }
                    $params['end_date'] = date('Y-m-d', $endTimestamp);
                }

                // 验证日期大小
                if (strtotime($params['start_date']) > strtotime($params['end_date'])) {
                    return json(['code' => 0, 'msg' => '结束日期必须大于等于开始日期']);
                }

                // 数值字段验证
                $floatFields = [
                    'level1_first_deposit_rate' => ['name' => '一级用户首充返利率', 'min' => 0, 'max' => 100],
                    'level1_first_deposit_max' => ['name' => '一级用户首充返现最大金额', 'min' => 0],
                    'level2_first_deposit_rate' => ['name' => '二级用户首充返利率', 'min' => 0, 'max' => 100],
                    'level2_first_deposit_max' => ['name' => '二级用户首充返现最大金额', 'min' => 0],
                    'min_deposit_amount' => ['name' => '最低充值金额', 'min' => 0]
                ];

                foreach ($floatFields as $field => $config) {
                    if (isset($params[$field])) {
                        if (!is_numeric($params[$field])) {
                            return json(['code' => 0, 'msg' => "{$config['name']}必须为数字"]);
                        }
                        $value = (float)$params[$field];
                        if ($value < $config['min']) {
                            return json(['code' => 0, 'msg' => "{$config['name']}不能小于{$config['min']}"]);
                        }
                        if (isset($config['max']) && $value > $config['max']) {
                            return json(['code' => 0, 'msg' => "{$config['name']}不能大于{$config['max']}"]);
                        }
                        $params[$field] = $value;
                    }
                }

                // 验证渠道ID
                $channelIds = Db::table('channels')->column('id');
                $validChannelIds = array_merge([-1, 999999], $channelIds);
                if (!in_array($params['channel_id'], $validChannelIds)) {
                    return json(['code' => 0, 'msg' => '无效的渠道ID']);
                }

                // 添加数据验证
                $this->validateData($params);
                
                // 预处理字段
                $params = $this->preExcludeFields($params);
                $params['is_active'] = isset($params['is_active']) ? $params['is_active'] : 1;

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }

                // 开始事务
                Db::startTrans();
                try {
                    $result = $this->model->allowField(true)->save($params);
                    if ($result !== false) {
                        Db::commit();
                        return json(['code' => 1, 'msg' => '保存成功']);
                    } else {
                        Db::rollback();
                        return json(['code' => 0, 'msg' => '保存失败，请重试']);
                    }
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => $e->getMessage()]);
                }
            }
            return $this->view->fetch();
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 数据验证
     */
    protected function validateData($params)
    {
        $channelIds = Db::table('channels')->column('id');
        $validChannelIds = array_merge([-1, 999999], $channelIds);
        
        $rules = [
            'activity_name' => 'require',
            'channel_id' => 'require|in:' . implode(',', $validChannelIds),
            'level1_first_deposit_rate' => 'require|float|between:0,100',
            'level1_first_deposit_max' => 'require|float|egt:0',
            'level2_first_deposit_rate' => 'require|float|between:0,100',
            'level2_first_deposit_max' => 'require|float|egt:0',
            'min_deposit_amount' => 'require|float|egt:0',
            'start_date' => 'require|date',
            'end_date' => 'require|date'
        ];
        
        $msg = [
            'activity_name.require' => '活动名称不能为空',
            'channel_id.require' => '渠道不能为空',
            'channel_id.in' => '无效的渠道ID',
            'level1_first_deposit_rate.require' => '一级用户首充返利率不能为空',
            'level1_first_deposit_rate.float' => '一级用户首充返利率必须为数字',
            'level1_first_deposit_rate.between' => '一级用户首充返利率必须在0到100之间',
            'level1_first_deposit_max.require' => '一级用户首充返现最大金额不能为空',
            'level1_first_deposit_max.float' => '一级用户首充返现最大金额必须为数字',
            'level1_first_deposit_max.egt' => '一级用户首充返现最大金额必须大于等于0',
            'level2_first_deposit_rate.require' => '二级用户首充返利率不能为空',
            'level2_first_deposit_rate.float' => '二级用户首充返利率必须为数字',
            'level2_first_deposit_rate.between' => '二级用户首充返利率必须在0到100之间',
            'level2_first_deposit_max.require' => '二级用户首充返现最大金额不能为空',
            'level2_first_deposit_max.float' => '二级用户首充返现最大金额必须为数字',
            'level2_first_deposit_max.egt' => '二级用户首充返现最大金额必须大于等于0',
            'min_deposit_amount.require' => '最低充值金额不能为空',
            'min_deposit_amount.float' => '最低充值金额必须为数字',
            'min_deposit_amount.egt' => '最低充值金额必须大于等于0',
            'start_date.require' => '开始日期不能为空',
            'start_date.date' => '开始日期格式不正确',
            'end_date.require' => '结束日期不能为空',
            'end_date.date' => '结束日期格式不正确'
        ];
        
        $validate = new \think\Validate($rules, $msg);
        if (!$validate->check($params)) {
            $errorMsg = $validate->getError();
            if (is_array($errorMsg)) {
                $errorMsg = implode('; ', $errorMsg);
            }
            throw new \Exception($errorMsg);
        }
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 获取渠道列表
     */
    public function get_channel_list()
    {
        $channelModel = new Channels();
        $channels = $channelModel->field('id, name')->select();
        
        $result = [];
        // 添加全渠道选项
        $result[] = ['value' => 999999, 'text' => '全部'];
        
        foreach ($channels as $channel) {
            $result[] = ['value' => $channel['id'], 'text' => $channel['name']];
        }
        
        return json(['code' => 1, 'msg' => 'success', 'data' => $result]);
    }
}
