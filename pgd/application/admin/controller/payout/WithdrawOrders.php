<?php

namespace app\admin\controller\payout;

use app\common\controller\Backend;

/**
 * 提现订单管理
 *
 * @icon fa fa-circle-o
 */
class WithdrawOrders extends Backend
{

    /**
     * Withdraw_orders模型对象
     * @var \app\admin\model\payout\WithdrawOrders
     */
    protected $model = null;
    protected $noNeedRight = ['query_status', 'view_proof'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\payout\WithdrawOrders;
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            // 获取所有渠道
            $channelList = \app\admin\model\channels\Channels::all();
            $this->view->assign('channelList', $channelList);

            // 获取所有业务员
            $agentList = \app\admin\model\agents_config\Agents::with(['admin'])->select();
            $this->view->assign('agentList', $agentList);

            // 获取所有玩家标签
            $playerTagList = \app\admin\model\players\PlayerTags::all();
            $this->view->assign('playerTagList', $playerTagList);

            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();

        // 获取筛选参数
        $filter_str = $this->request->get('filter', '');
        $filter = $filter_str ? json_decode($filter_str, true) : [];
        $channel_id = isset($filter['channel_id']) ? $filter['channel_id'] : null;
        $tag_id = isset($filter['tag_id']) ? $filter['tag_id'] : null;

                // 从 filter 参数中获取玩家ID搜索参数（如果有）
        $player_id = isset($filter['player_id']) ? $filter['player_id'] : null;
        if ($player_id) {
            // 如果有玩家ID搜索参数，记录下来供后续使用
            $player_id = intval($player_id);
        }

        // 初始化查询构造器
        $query = $this->model;

        $query->where('audit_status', 'not in', [0, 1]); // 过滤掉待审核的订单

        // 初始化别名和字段，添加玩家的总充值和总提现字段，以及最新玩家备注
        $query->alias('wo')
              ->join(['players'=>'p'], 'wo.player_id = p.id', 'left')
              ->join(['player_remarks'=>'pr'], 'wo.player_id = pr.player_id AND pr.id = (SELECT MAX(id) FROM player_remarks WHERE player_id = wo.player_id)', 'left')
              ->field('wo.*, p.total_deposit, p.total_withdraw, pr.content as player_remark');

        // 如果有玩家ID搜索参数，直接应用搜索条件
        if (!empty($player_id)) {
            $query->where('wo.player_id', $player_id);
        }

        // 如果有渠道筛选，使用联合查询
        if ($channel_id && $channel_id != 'all') {
            $query->join(['agents'=>'ag'], 'wo.agent_id = ag.id')
                  ->where('ag.channel_id', $channel_id);
        }

        // 获取当前用户角色组ID
        $roleGroupId = $this->getCurrentRoleGroupId();

        // 根据角色类型应用不同的过滤条件
        if ($roleGroupId == 4) { // 渠道角色
            $channelId = $this->getCurrentChannelId();
            if ($channelId) {
                // 确保已经关联agents表
                if (!$channel_id || $channel_id == 'all') {
                    $query->join(['agents'=>'ag'], 'wo.agent_id = ag.id', 'left');
                }
                $query->where('ag.channel_id', $channelId);
            }
        } elseif ($roleGroupId == 6) { // 业务员角色
            $agentId = $this->getCurrentAgentId();
            if ($agentId) {
                $query->where('wo.agent_id', $agentId);
            }
        }

        // 如果有玩家标签筛选，添加标签筛选条件
        if ($tag_id && $tag_id != 'all') {
            $query->where('p.tag_id', $tag_id);
        }

        // 设置排序字段前缀
        if(isset($sort)) $sort = "wo.$sort";

        // 修改where条件中的agent_id字段，添加表前缀避免歧义
        $newWhere = [];
        foreach ($where as $key => $value) {
            // 如果是agent_id字段，添加表前缀
            if (strpos($key, 'agent_id') === 0) {
                $newKey = str_replace('agent_id', 'wo.agent_id', $key);
                $newWhere[$newKey] = $value;
            } else {
                $newWhere[$key] = $value;
            }
        }

        // 应用修改后的where条件
        $query->where($newWhere);

        // 执行查询
        $list = $query->order($sort, $order)->paginate($limit);
        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    /**
     * 查询支付方状态
     * 调用支付方API查询转出付款的状态
     */
    public function query_status()
    {
        $ids = $this->request->param('ids');
        if (!$ids) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $order = $this->model->where('id', $ids)->find();
        if (!$order) {
            $this->error(__('Order not found'));
        }

        // 获取订单号
        $merchantOrderNo = $order['merchant_order_no'];
        if (empty($merchantOrderNo)) {
            $this->error(__('Order number not found'));
        }

        // 调用支付方API查询转出付款的状态
        $result = \app\admin\service\PaymentApiService::queryWithdrawOrder($merchantOrderNo);

        // 记录查询日志
        \think\Log::info('提现订单状态查询: ' . json_encode([
            'order_id' => $ids,
            'order_no' => $merchantOrderNo,
            'result' => $result
        ]));

        if (!$result['success']) {
            $this->error($result['message'] ?: __('Query failed'));
        }

        // 获取支付状态
        $data = $result['data'] ?? [];
        $status = $data['status'] ?? '';
        $statusDesc = $data['statusDesc'] ?? '';


        // 返回查询结果
        $this->success(
            __('Query success') . ': ' . ($statusDesc ?: __('Status') . ': ' . $status),
            null,
            $data
        );
    }

    /**
     * 查看支付凭证
     * 返回支付凭证URL
     */
    public function view_proof()
    {
        $ids = $this->request->param('ids');
        if (!$ids) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $order = $this->model->where('id', $ids)->find();
        if (!$order) {
            $this->error(__('Order not found'));
        }

        // 构建URL
        $url = 'https://front.u2cpay.net/#/payment/prove?paymentSn=' . $order['third_order_no'] . '&currency=BRL';

        // 返回URL
        $this->success(__('Get proof URL success'), null, ['url' => $url]);
    }
}
