<?php

namespace app\admin\controller\payout;

use app\common\controller\Backend;
use app\admin\service\BalanceService;
use app\admin\service\PaymentApiService;
use app\admin\model\payout\WithdrawOrders;
use app\admin\model\agents_config\Agents;
use app\admin\model\channels\Channels;
use app\admin\model\players\PlayerTags;
use think\Db;
use think\Exception;
use think\Log;

/**
 * 放款审核
 *
 * @icon fa fa-arrow-right
 */
class WithdrawOrdersCheck extends Backend
{

    /**
     * WithdrawOrdersCheck模型对象
     * @var \app\admin\model\payout\WithdrawOrders
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new WithdrawOrders;
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            // 获取所有渠道
            $channelList = Channels::all();
            $this->view->assign('channelList', $channelList);

            // 获取所有业务员
            $agentList = Agents::with(['admin'])->select();
            $this->view->assign('agentList', $agentList);

            // 获取所有玩家标签
            $playerTagList = PlayerTags::all();
            $this->view->assign('playerTagList', $playerTagList);

            // 获取当前用户角色组ID
            $roleGroupId = $this->getCurrentRoleGroupId();
            $hasWithdrawPermission = true; // 默认有权限

            // 根据不同角色检查提现权限
            if ($roleGroupId == 4) { // 渠道角色
                $channelId = $this->getCurrentChannelId();
                if ($channelId) {
                    $channel = \app\admin\model\channels\Channels::get($channelId);
                    if ($channel) {
                        $hasWithdrawPermission = (bool)$channel['allow_withdrawal'];
                    }
                }
            } elseif ($roleGroupId == 6) { // 业务员角色
                $agentId = $this->getCurrentAgentId();
                if ($agentId) {
                    $agent = Agents::get($agentId);
                    if ($agent) {
                        $hasWithdrawPermission = (bool)$agent['withdraw_permission'];
                    }
                }
            }

            // 将权限状态传递给视图
            $this->view->assign('hasWithdrawPermission', $hasWithdrawPermission);

            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, , $limit] = $this->buildparams();

        // 获取筛选参数
        $filter_str = $this->request->get('filter', '');
        $filter = $filter_str ? json_decode($filter_str, true) : [];
        $channel_id = isset($filter['channel_id']) ? $filter['channel_id'] : null;
        $tag_id = isset($filter['tag_id']) ? $filter['tag_id'] : null;

        // 从 filter 参数中获取玩家ID搜索参数（如果有）
        $player_id = isset($filter['player_id']) ? $filter['player_id'] : null;
        if ($player_id) {
            // 如果有玩家ID搜索参数，记录下来供后续使用
            $player_id = intval($player_id);
        }

        // 初始化查询构造器
        $query = $this->model;

        $query->where('audit_status', 'in', [0, 1]); // 查询待审核的订单，不包含已审核的订单

        // 初始化别名和字段，添加玩家的余额、总充值和总提现字段，以及最新玩家备注
        $query->alias('wo')
              ->join(['players'=>'p'], 'wo.player_id = p.id', 'left')
              ->join(['player_remarks'=>'pr'], 'wo.player_id = pr.player_id AND pr.id = (SELECT MAX(id) FROM player_remarks WHERE player_id = wo.player_id)', 'left')
              ->field('wo.*, p.balance, p.total_deposit, p.total_withdraw, pr.content as player_remark');

        // 如果有玩家ID搜索参数，直接应用搜索条件
        if (!empty($player_id)) {
            $query->where('wo.player_id', $player_id);
        }

        // 如果有渠道筛选，使用联合查询
        if ($channel_id && $channel_id != 'all') {
            $query->join(['agents'=>'ag'], 'wo.agent_id = ag.id')
                  ->where('ag.channel_id', $channel_id);
        }

        // 获取当前用户角色组ID
        $roleGroupId = $this->getCurrentRoleGroupId();
        $hasWithdrawPermission = true; // 默认有权限

        // 根据角色类型应用不同的过滤条件
        if ($roleGroupId == 4) { // 渠道角色
            $channelId = $this->getCurrentChannelId();
            if ($channelId) {
                // 确保已经关联agents表
                if (!$channel_id || $channel_id == 'all') {
                    $query->join(['agents'=>'ag'], 'wo.agent_id = ag.id', 'left');
                }
                $query->where('ag.channel_id', $channelId);

                // 检查渠道是否有提现权限
                $channel = \app\admin\model\channels\Channels::get($channelId);
                if ($channel) {
                    $hasWithdrawPermission = (bool)$channel['allow_withdrawal'];
                }
            }
        } elseif ($roleGroupId == 6) { // 业务员角色
            $agentId = $this->getCurrentAgentId();
            if ($agentId) {
                $query->where('wo.agent_id', $agentId);

                // 检查业务员是否有提现权限
                $agent = Agents::get($agentId);
                if ($agent) {
                    $hasWithdrawPermission = (bool)$agent['withdraw_permission'];
                }
            }
        }

        // 如果有玩家标签筛选，添加标签筛选条件
        if ($tag_id && $tag_id != 'all') {
            $query->where('p.tag_id', $tag_id);
        }

        // 设置排序字段前缀
        if(isset($sort)) $sort = "wo.$sort";

        // 修改where条件中的agent_id字段，添加表前缀避免歧义
        $newWhere = [];
        foreach ($where as $key => $value) {
            // 如果是agent_id字段，添加表前缀
            if (strpos($key, 'agent_id') === 0) {
                $newKey = str_replace('agent_id', 'wo.agent_id', $key);
                $newWhere[$newKey] = $value;
            } else {
                $newWhere[$key] = $value;
            }
        }

        // 应用修改后的where条件
        $query->where($newWhere);

        // 执行查询
        $list = $query->order($sort, $order)->paginate($limit);
        $result = [
            'total' => $list->total(),
            'rows' => $list->items(),
            'hasWithdrawPermission' => $hasWithdrawPermission // 将权限状态传递给前端
        ];
        return json($result);
    }

    /**
     * 审核通过
     */
    public function approve()
    {
        $ids = $this->request->param('ids');
        if (!$ids) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $list = $this->model->where('id', 'in', $ids)->select();
        if (!$list) {
            $this->error(__('No rows were updated'));
        }

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                // 只有待加入工单和已加入工单待审核状态的订单才能审核通过
                if (in_array($item['audit_status'], [0, 1])) {
                    // 获取提现账户信息
                    $withdrawAccount = Db::table('player_withdraw_accounts')
                        ->where('player_id', $item['player_id'])
                        ->find();

                    if (!$withdrawAccount) {
                        throw new Exception('提现账户不存在，订单ID: ' . $item['id']);
                    }

                    // 根据账户类型映射到支付网关需要的accountType
                    $pixTypeMap = [
                        'CPF' => 'PIX_CPF',
                        'PHONE' => 'PIX_PHONE',
                        'EMAIL' => 'PIX_EMAIL',
                        'CNPJ' => 'PIX_CNPJ'
                    ];

                    $accountType = isset($pixTypeMap[$withdrawAccount['account_type']])
                        ? $pixTypeMap[$withdrawAccount['account_type']]
                        : 'PIX_CPF'; // 默认使用PIX_CPF

                    // 生成回调URL
                    $callbackUrl = \app\common\library\CallbackUrl::getCallbackUrl($this->request, '/payment/withdraw/callback');

                    // 生成商户订单号
                    $merchantOrderNo = $item['id'] . '_' . time(); // 使用订单ID和时间戳生成唯一订单号

                    // 保存商户订单号到withdraw_orders表
                    $item->merchant_order_no = $merchantOrderNo;

                    // 调用第三方支付接口进行提现
                    $withdrawData = [
                        'merchantOrderNo' => $merchantOrderNo,
                        'amount' => intval($item['amount'] * 100), // 转换为分
                        'currency' => 'BRL', // 默认使用巴西币种
                        'accountType' => $accountType,
                        'accountNo' => $withdrawAccount['account_number'],
                        'accountName' => $withdrawAccount['account_name'],
                        'callback' => $callbackUrl, // 添加回调URL
                    ];

                    // 添加CPF参数（如果账户类型不是CPF但有PIX号码）
                    if ($withdrawAccount['account_type'] != 'cpf' && !empty($withdrawAccount['pix_number'])) {
                        $withdrawData['cpf'] = $withdrawAccount['pix_number'];
                    }

                    // 调用支付服务
                    $result = PaymentApiService::withdraw($withdrawData);

                    // API调用结果记录到日志
                    Log::info('提现API调用结果: ' . json_encode($result, JSON_UNESCAPED_UNICODE));

                    if ($result['success']) {
                        // API调用成功，更新订单状态
                        $responseData = $result['data'];

                        // 记录平台订单号
                        if (isset($responseData['orderNo'])) {
                            $item->third_order_no = $responseData['orderNo'];
                        }

                        // 根据状态码更新订单状态
                        if (isset($responseData['status'])) {
                            // 这里可以根据支付网关的状态码映射到系统内部状态
                            // 假设支付网关返回的状态码：PROCESSING表示处理中，PAID表示已支付，FAILED表示失败
                            switch ($responseData['status']) {
                                case 'PAID':
                                    $item->audit_status = 4; // 订单完成
                                    $item->paid_at = date('Y-m-d H:i:s');
                                    break;
                                case 'PAY_FAILED':
                                    // 处理失败，记录错误信息
                                    $errorMsg = isset($responseData['errorMsg']) ? $responseData['errorMsg'] : '支付处理失败';
                                    $item->remark = $errorMsg;
                                    $item->audit_status = 6; // 处理失败并退回金币

                                    // 获取当前用户角色组ID
                                    $roleGroupId = $this->getCurrentRoleGroupId();

                                    // 根据角色设置操作人类型
                                    $operatorType = 0; // 默认为0-管理员
                                    if ($roleGroupId == 4) {
                                        $operatorType = 1; // 1-渠道
                                    } elseif ($roleGroupId == 6) {
                                        $operatorType = 2; // 2-业务员
                                    }

                                    // 设置操作人名称
                                    $operatorName = $this->auth->username;

                                    // 退回金币
                                    BalanceService::change(
                                        $item['player_id'],
                                        $item['amount'],
                                        19, // 19-提现失败
                                        '提现处理失败退回金币，订单ID: ' . $item['id'],
                                        $operatorName,
                                        $operatorType
                                    );
                                    break;
                                case 'WAITING_PAY':
                                case 'PAYING':
                                    $item->audit_status = 3; // 三方处理中状态
                                    break;
                            }
                        } else {
                            // 如果没有状态码，默认设置为处理中
                            $item->audit_status = 3; // 三方处理中状态
                        }

                        // 获取当前用户角色组ID
                        $roleGroupId = $this->getCurrentRoleGroupId();

                        // 根据角色设置操作人类型
                        $operatorType = 0; // 默认为0-管理员
                        if ($roleGroupId == 4) {
                            $operatorType = 1; // 1-渠道
                        } elseif ($roleGroupId == 6) {
                            $operatorType = 2; // 2-业务员
                        }

                        // 设置操作人名称
                        $operatorName = $this->auth->username;

                        $item->processed_at = date('Y-m-d H:i:s');
                        $item->operator_name = $operatorName;
                        $item->operator_type = $operatorType;

                        // 更新业务员已使用放款额度
                        if ($item['agent_id']) {
                            // 只有当订单状态为处理中或已完成时才更新已使用放款额度
                            if (in_array($item->audit_status, [3, 4])) {
                                $updateResult = Agents::updateUsedWithdrawCredit(
                                    $item['agent_id'],
                                    $item['amount']
                                );

                                if (!$updateResult) {
                                    Log::error('更新业务员已使用放款额度失败，业务员ID: ' . $item['agent_id'] . '，订单ID: ' . $item['id']);
                                }
                            }
                        }

                        $item->save();
                        $count += 1;
                    } else {
                        // API调用失败，记录错误信息
                        $errorMessage = ($result['message'] ?? '未知错误');
                        $errorCode = ($result['errorCode'] ?? '');

                        // 记录错误信息到订单备注
                        $item->remark = "API调用失败: {$errorMessage}, 错误码: {$errorCode}";
                        $item->save();

                        // 记录详细错误信息到日志
                        Log::error("提现API调用失败详情: " . json_encode($result, JSON_UNESCAPED_UNICODE));

                        throw new Exception("提现API调用失败: {$errorMessage}, 错误码: {$errorCode}");
                    }
                }
            }

            // 提交事务
            Db::commit();

            if ($count) {
                $this->success(__('Success approve %d orders', $count));
            } else {
                $this->error(__('No rows were updated'));
            }
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->error($e->getMessage());
        }
    }

    /**
     * 审核拒绝
     */
    public function reject()
    {
        $ids = $this->request->param('ids');
        if (!$ids) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $list = $this->model->where('id', 'in', $ids)->select();
        if (!$list) {
            $this->error(__('No rows were updated'));
        }

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                // 只有待加入工单和已加入工单待审核状态的订单才能审核拒绝
                if (in_array($item['audit_status'], [0, 1])) {
                    // 获取当前用户角色组ID
                    $roleGroupId = $this->getCurrentRoleGroupId();

                    // 根据角色设置操作人类型
                    $operatorType = 0; // 默认为0-管理员
                    if ($roleGroupId == 4) {
                        $operatorType = 1; // 1-渠道
                    } elseif ($roleGroupId == 6) {
                        $operatorType = 2; // 2-业务员
                    }

                    // 设置操作人名称
                    $operatorName = $this->auth->username;

                    // 处理余额变更 - 提现驳回退款
                    BalanceService::change(
                        $item['player_id'],
                        $item['amount'],
                        18, // 18-提现驳回
                        '提现拒绝退回金币，订单ID: ' . $item['id'],
                        $operatorName,
                        $operatorType
                    );

                    // 更新提现订单状态
                    $item->audit_status = 5; // 拒绝并退回金币
                    $item->processed_at = date('Y-m-d H:i:s');
                    $item->operator_name = $operatorName;
                    $item->operator_type = $operatorType;

                    // reject 方法不操作业务员额度

                    $item->save();
                    $count += 1;
                }
            }

            // 提交事务
            Db::commit();

            if ($count) {
                $this->success(__('Success reject and refund %d orders', $count));
            } else {
                $this->error(__('No rows were updated'));
            }
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->error($e->getMessage());
        }
    }

    /**
     * 虚拟支付
     */
    public function virtual()
    {
        $ids = $this->request->param('ids/a');  // 使用 /a 修饰符明确指定接收数组
        if (!$ids) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $list = $this->model->where('id', 'in', $ids)->select();
        if (!$list) {
            $this->error(__('No rows were updated'));
        }

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                // 只有待加入工单和已加入工单待审核状态的订单才能标记为虚拟支付
                if (in_array($item['audit_status'], [0, 1])) {
                    // 获取当前用户角色组ID
                    $roleGroupId = $this->getCurrentRoleGroupId();

                    // 根据角色设置操作人类型
                    $operatorType = 0; // 默认为0-管理员
                    if ($roleGroupId == 4) {
                        $operatorType = 1; // 1-渠道
                    } elseif ($roleGroupId == 6) {
                        $operatorType = 2; // 2-业务员
                    }

                    // 设置操作人名称
                    $operatorName = $this->auth->username;

                    $item->audit_status = 8; // 虚拟支付
                    $item->processed_at = date('Y-m-d H:i:s');
                    $item->paid_at = date('Y-m-d H:i:s');
                    $item->operator_name = $operatorName;
                    $item->operator_type = $operatorType;
                    $item->save();
                    $count += 1;
                }
            }

            // 提交事务
            Db::commit();

            if ($count) {
                $this->success(__('Success mark %d orders as virtual payment', $count));
            } else {
                $this->error(__('No rows were updated'));
            }
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->error($e->getMessage());
        }
    }

    /**
     * 拒绝并没收金币
     */
    public function confiscate()
    {
        $ids = $this->request->param('ids');
        if (!$ids) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $list = $this->model->where('id', 'in', $ids)->select();
        if (!$list) {
            $this->error(__('No rows were updated'));
        }

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                // 只有待加入工单和已加入工单待审核状态的订单才能拒绝并没收金币
                if (in_array($item['audit_status'], [0, 1])) {
                    // 获取当前用户角色组ID
                    $roleGroupId = $this->getCurrentRoleGroupId();

                    // 根据角色设置操作人类型
                    $operatorType = 0; // 默认为0-管理员
                    if ($roleGroupId == 4) {
                        $operatorType = 1; // 1-渠道
                    } elseif ($roleGroupId == 6) {
                        $operatorType = 2; // 2-业务员
                    }

                    // 设置操作人名称
                    $operatorName = $this->auth->username;

                    // 更新提现订单状态
                    $item->audit_status = 7; // 拒绝并没收金币
                    $item->processed_at = date('Y-m-d H:i:s');
                    $item->operator_name = $operatorName;
                    $item->operator_type = $operatorType;
                    $item->save();
                    $count += 1;
                }
            }

            // 提交事务
            Db::commit();

            if ($count) {
                $this->success(__('Success reject and confiscate %d orders', $count));
            } else {
                $this->error(__('No rows were updated'));
            }
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->error($e->getMessage());
        }
    }


}
