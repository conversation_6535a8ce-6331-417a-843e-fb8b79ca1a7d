<?php

namespace app\admin\controller\payout;

use app\common\controller\Backend;

/**
 * 邀请奖励发放管理（宝箱奖励）
 *
 * @icon fa fa-circle-o
 */
class InvitationRewards extends Backend
{

    /**
     * Invitation_rewards模型对象
     * @var \app\admin\model\payout\InvitationRewards
     */
    protected $model = null;

    protected $searchFields = 'player_id';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\payout\InvitationRewards;
    }

    /**
     * 查看
     */
    public function index()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            // 获取查询参数
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 初始化查询构造器
            $query = $this->model->alias('ir');

            // 明确选择字段，确保id字段正确
            $query->field('ir.id, ir.player_id, ir.config_id, ir.reward_amount, ir.is_claimed, ir.claimed_at, ir.created_at');

            // 关联宝箱配置表
            $query->join(['treasure_box_config'=>'tbc'], 'ir.config_id = tbc.id', 'left');

            // 根据用户角色应用权限过滤
            $this->applyRoleFilter($query);

            // 处理搜索条件，添加表前缀
            $this->processWhereConditions($query, $where);

            // 设置排序字段前缀，避免字段歧义
            if(isset($sort) && !empty($sort)) {
                $sort = "ir.$sort";
            }

            // 执行查询（使用offset和limit参数）
            $list = $query->order($sort, $order)->limit($offset, $limit)->paginate($limit);

            // 返回结果
            $result = ["total" => $list->total(), "rows" => $list->items()];
            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 根据用户角色应用权限过滤
     * @param \think\db\Query $query 查询构造器
     */
    protected function applyRoleFilter(&$query)
    {
        // 获取当前用户角色组ID
        $roleGroupId = $this->getCurrentRoleGroupId();

        // 根据角色类型应用不同的过滤条件
        if ($roleGroupId == 4) { // 渠道角色
            $channelId = $this->getCurrentChannelId();
            if ($channelId) {
                // 关联players表以便过滤
                $query->join(['players'=>'p'], 'ir.player_id = p.id', 'left');
                $query->where('p.channel_id', $channelId);
            }
        } elseif ($roleGroupId == 6) { // 业务员角色
            $agentId = $this->getCurrentAgentId();
            if ($agentId) {
                // 关联players表以便过滤
                $query->join(['players'=>'p'], 'ir.player_id = p.id', 'left');
                $query->where('p.agent_id', $agentId);
            }
        }
    }

    /**
     * 处理搜索条件，添加表前缀
     * @param \think\db\Query $query 查询构造器
     * @param array $where 搜索条件
     */
    protected function processWhereConditions(&$query, $where)
    {
        foreach ($where as $condition) {
            if (is_array($condition) && count($condition) >= 3) {
                // 为字段名添加表前缀，避免歧义
                $field = $condition[0];
                $operator = $condition[1];
                $value = $condition[2];

                // 如果字段名没有表前缀，添加ir.前缀
                if (strpos($field, '.') === false) {
                    $field = 'ir.' . $field;
                }

                $query->where($field, $operator, $value);
            }
        }
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
}
