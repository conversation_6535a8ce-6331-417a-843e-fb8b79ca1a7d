<?php

namespace app\admin\controller\payout;

use app\common\controller\Backend;

/**
 * 邀请奖励发放管理（宝箱奖励）
 *
 * @icon fa fa-circle-o
 */
class InvitationRewards extends Backend
{

    /**
     * Invitation_rewards模型对象
     * @var \app\admin\model\payout\InvitationRewards
     */
    protected $model = null;

    protected $searchFields = 'id,player_id';
    
    protected $relationSearch = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\payout\InvitationRewards;
    }

    /**
     * 查看
     */
    public function index()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            // 手动获取查询参数
            $search = $this->request->get("search", '');
            $filter = $this->request->get("filter", '');
            $op = $this->request->get("op", '', 'trim');
            $sort = $this->request->get("sort", 'id');
            $order = $this->request->get("order", "DESC");
            $offset = max(0, $this->request->get("offset/d", 0));
            $limit = max(0, $this->request->get("limit/d", 10));
            
            $filter = (array)json_decode($filter, true);
            $op = (array)json_decode($op, true);
            $filter = $filter ? $filter : [];

            // 初始化查询构造器
            $query = $this->model->alias('ir');

            // 明确选择字段，确保id字段正确
            $query->field('ir.id, ir.player_id, ir.config_id, ir.reward_amount, ir.is_claimed, ir.claimed_at, ir.created_at');

            // 关联宝箱配置表
            $query->join(['treasure_box_config'=>'tbc'], 'ir.config_id = tbc.id', 'left');

            // 检查是否需要关联players表（用于权限过滤）
            $needPlayersJoin = false;
            $roleGroupId = $this->getCurrentRoleGroupId();
            if ($roleGroupId == 4 || $roleGroupId == 6) {
                $needPlayersJoin = true;
                $query->join(['players'=>'p'], 'ir.player_id = p.id', 'left');
            }

            // 根据用户角色应用权限过滤
            $this->applyRoleFilter($query, $needPlayersJoin);

            // 处理搜索条件
            if ($search) {
                $query->where(function($query) use ($search) {
                    $query->whereOr('ir.id', '=', $search)
                          ->whereOr('ir.player_id', '=', $search);
                });
            }
            
            // 处理过滤条件
            foreach ($filter as $k => $v) {
                if (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $k)) {
                    continue;
                }
                $sym = isset($op[$k]) ? $op[$k] : '=';
                $v = !is_array($v) ? trim($v) : $v;
                
                // 为字段添加表前缀
                $field = 'ir.' . $k;
                
                switch (strtoupper($sym)) {
                    case '=':
                    case '<>':
                        $query->where($field, $sym, (string)$v);
                        break;
                    case 'LIKE':
                        $query->where($field, 'like', "%{$v}%");
                        break;
                    case '>':
                    case '>=':
                    case '<':
                    case '<=':
                        $query->where($field, $sym, $v);
                        break;
                    case 'IN':
                        $query->where($field, 'in', is_array($v) ? $v : explode(',', $v));
                        break;
                    case 'BETWEEN':
                        $arr = explode(',', $v);
                        if (count($arr) >= 2) {
                            $query->where($field, 'between', [$arr[0], $arr[1]]);
                        }
                        break;
                    case 'RANGE':
                        $v = str_replace(' - ', ',', $v);
                        $arr = explode(',', $v);
                        if (count($arr) >= 2) {
                            $query->where($field, 'between time', [$arr[0], $arr[1]]);
                        }
                        break;
                }
            }

            // 处理排序字段
            $sort = strpos($sort, '.') === false ? "ir.$sort" : $sort;

            // 执行查询（使用offset和limit参数）
            $list = $query->order($sort, $order)->limit($offset, $limit)->paginate($limit);

            // 返回结果
            $result = ["total" => $list->total(), "rows" => $list->items()];
            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 根据用户角色应用权限过滤
     * @param \think\db\Query $query 查询构造器
     * @param bool $playersJoined 是否已经JOIN了players表
     */
    protected function applyRoleFilter(&$query, $playersJoined = false)
    {
        // 获取当前用户角色组ID
        $roleGroupId = $this->getCurrentRoleGroupId();

        // 根据角色类型应用不同的过滤条件
        if ($roleGroupId == 4) { // 渠道角色
            $channelId = $this->getCurrentChannelId();
            if ($channelId) {
                // 如果还没有关联players表，则关联
                if (!$playersJoined) {
                    $query->join(['players'=>'p'], 'ir.player_id = p.id', 'left');
                }
                $query->where('p.channel_id', $channelId);
            }
        } elseif ($roleGroupId == 6) { // 业务员角色
            $agentId = $this->getCurrentAgentId();
            if ($agentId) {
                // 如果还没有关联players表，则关联
                if (!$playersJoined) {
                    $query->join(['players'=>'p'], 'ir.player_id = p.id', 'left');
                }
                $query->where('p.agent_id', $agentId);
            }
        }
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
}
