<?php

namespace app\admin\controller\agents_config;

use app\common\controller\Backend;
use app\admin\model\agents_config\Agents as AgentsModel;
use app\admin\model\channels\Channels;
use app\admin\model\Admin;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\Log;
use Exception;

/**
 * 业务员管理
 *
 * @icon fa fa-user
 */
class Agents extends Backend
{
    protected $model = null;
    protected $searchFields = 'id';
    protected $multiFields = 'withdraw_permission,can_change_password,status';
    protected $noNeedRight = ['login'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new AgentsModel;
    }

    /**
     * 查看
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            [$where, $sort, $order, $offset, $limit] = $this->buildparams();

            // 使用关联查询获取业务员信息，包括渠道信息
            $list = $this->model
                ->alias('a')
                ->join('fa_admin admin', 'a.admin_id = admin.id', 'LEFT')
                ->join(['channels'=>'c'], 'a.channel_id = c.id', 'LEFT')
                ->field('a.*, admin.username, admin.nickname, admin.logintime, admin.status, c.name as channel_name')
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            // 处理结果数据
            $items = $list->items();
            foreach ($items as &$item) {
                // 处理状态显示
                $item['status_text'] = isset($item['status']) ?
                    ($item['status'] == 'normal' ? __('Normal') : __('Hidden')) :
                    __('Unknown');

                // 处理登录时间显示
                if (isset($item['logintime']) && $item['logintime']) {
                    $item['logintime_text'] = date('Y-m-d H:i:s', $item['logintime']);
                } else {
                    $item['logintime_text'] = __('Never');
                }

                // 显示业务员名称，优先使用nickname，如果没有则使用username
                $item['agent_name'] = !empty($item['nickname']) ? $item['nickname'] : $item['username'];

                // 根据leader_id判断业务员类型
                $item['agent_type'] = $item['leader_id'] === null ? __('Team leader') : __('Team member');

                // 动态生成推广链接，不保存到数据库
                // 使用工具函数获取清理后的域名
                $cleanDomain = get_clean_domain();
                $item['promotion_link'] = $cleanDomain . '/?ic=' . $item['id'];
            }
            unset($item);

            $result = ['total' => $list->total(), 'rows' => $items];
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post('row/a');
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    // 创建fa_admin记录
                    $adminModel = new Admin();

                    // 检查用户名是否已存在
                    if ($adminModel->where('username', $params['username'])->find()) {
                        throw new Exception(__('Username already exists'));
                    }

                    // 准备管理员数据
                    $adminData = [
                        'username' => $params['username'],
                        'nickname' => isset($params['nickname']) ? $params['nickname'] : $params['username'],
                        'salt' => \fast\Random::alnum(),
                        'avatar' => '/assets/img/avatar.png',
                        'email' => '',
                        'loginfailure' => 0,
                        'logintime' => null,
                        'createtime' => time(),
                        'updatetime' => time(),
                        'status' => 'normal'
                    ];

                    // 加密密码
                    $adminData['password'] = $this->auth->getEncryptPassword($params['password'], $adminData['salt']);

                    // 保存管理员记录
                    $admin = $adminModel->save($adminData);
                    if (!$admin) {
                        throw new Exception(__('Failed to create admin account'));
                    }

                    // 获取新创建的管理员ID
                    $adminId = $adminModel->id;

                    // 为新admin用户分配权限组(1超级管理员2平台管理员4渠道6业务员)
                    $agentGroupId = 6; // 业务员角色组ID为6
                    $groupAccess = model('AuthGroupAccess')->save(['uid' => $adminId, 'group_id' => $agentGroupId]);
                    if (!$groupAccess) {
                        throw new Exception(__('Failed to assign agent role'));
                    }

                    // 获取当前用户的角色组ID
                    $groupIds = $this->auth->getGroupIds();
                    $channelId = null;

                    // 如果当前用户是渠道角色(id=4)，自动设置channel_id为当前渠道的ID
                    if (in_array(4, $groupIds)) {
                        // 查询当前管理员关联的渠道ID
                            $channel = Db::table('channels')
                            ->where('admin_id', $this->auth->id)
                            ->field('id')
                            ->find();

                        if ($channel) {
                            $channelId = $channel['id'];
                        } else {
                            throw new Exception(__('Current channel not found'));
                        }
                    } else if (in_array(1, $groupIds) || in_array(2, $groupIds)) {
                        // 如果是管理员或平台管理员，需要提供channel_id
                        if (!isset($params['channel_id']) || empty($params['channel_id'])) {
                            throw new Exception(__('Channel ID is required'));
                        }
                        $channelId = $params['channel_id'];
                    } else {
                        throw new Exception(__('You have no permission to add agents'));
                    }

                    // 创建业务员记录
                    $agentData = [
                        'admin_id' => $adminId,
                        'channel_id' => $channelId,
                        'game_credit' => isset($params['game_credit']) ? $params['game_credit'] : 0,
                        'used_game_credit' => 0, // 直接设置为0，不再从表单参数中获取
                        'withdraw_permission' => isset($params['withdraw_permission']) ? $params['withdraw_permission'] : 0,
                        'withdraw_credit' => isset($params['withdraw_credit']) ? $params['withdraw_credit'] : 0,
                        'used_withdraw_credit' => 0, // 直接设置为0，不再从表单参数中获取
                        'can_change_password' => isset($params['can_change_password']) ? $params['can_change_password'] : 0,
                    ];

                    if (isset($params['leader_id']) && !empty($params['leader_id'])) {
                        $agentData['leader_id'] = $params['leader_id'];
                    }

                    // 检查渠道是否有足够的剩余额度
                    $withdrawCredit = floatval($agentData['withdraw_credit']);
                    if ($withdrawCredit > 0) {
                        if (!Channels::hasEnoughCreditLeft($channelId, $withdrawCredit)) {
                            throw new Exception(__('Channel does not have enough credit left'));
                        }

                        // 更新渠道剩余额度
                        if (!Channels::updateCreditLeft($channelId, $withdrawCredit)) {
                            throw new Exception(__('Failed to update channel credit left'));
                        }
                    }

                    $agentResult = $this->model->save($agentData);
                    if (!$agentResult) {
                        throw new Exception(__('Failed to create agent record'));
                    }

                    // 设置结果为成功
                    $result = true;

                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        // 获取组长列表
        $leaderList = $this->getLeaderList();
        $this->view->assign('leaderList', $leaderList);

        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 获取关联的管理员信息
        $admin = Admin::get($row['admin_id']);

        // 如果找到关联的管理员信息，使用管理员表中的用户名和昵称
        if ($admin) {
            $row['username'] = $admin['username'];
            $row['nickname'] = $admin['nickname'];
        }

        if ($this->request->isPost()) {
            $params = $this->request->post('row/a');
            if ($params) {
                $params = $this->preExcludeFields($params);

                // 如果leader_id为0，则移除该字段，让数据库使用默认值null
                if (isset($params['leader_id']) && ($params['leader_id'] === '0' || $params['leader_id'] === 0)) {
                    unset($params['leader_id']);
                }

                $result = false;
                Db::startTrans();
                try {
                    // 查找对应的管理员记录
                    $admin = Admin::get($row['admin_id']);

                    if (!$admin) {
                        throw new Exception(__('Associated admin account not found'));
                    }

                    // 准备更新管理员数据
                    $adminData = [
                        'updatetime' => time()
                    ];

                    // 如果用户名被修改
                    if (isset($params['username']) && $params['username'] != $admin['username']) {
                        // 检查新用户名是否已存在
                        if (Admin::where('username', $params['username'])->where('id', '<>', $row['admin_id'])->find()) {
                            throw new Exception(__('Username already exists'));
                        }
                        $adminData['username'] = $params['username'];
                    }

                    // 如果昵称被修改
                    if (isset($params['nickname'])) {
                        $adminData['nickname'] = $params['nickname'];
                    }

                    // 如果密码被修改
                    if (isset($params['password']) && !empty($params['password'])) {
                        $salt = $admin['salt'] ?: \fast\Random::alnum();
                        $adminData['password'] = $this->auth->getEncryptPassword($params['password'], $salt);
                        $adminData['salt'] = $salt;
                    }

                    // 更新管理员记录
                    $adminResult = $admin->save($adminData);
                    if ($adminResult === false) {
                        throw new Exception(__('Failed to update admin account'));
                    }

                    // 检查渠道剩余额度（如果放款额度有变化）
                    if (isset($params['withdraw_credit']) && $params['withdraw_credit'] != $row['withdraw_credit']) {
                        $channelId = isset($params['channel_id']) ? $params['channel_id'] : $row['channel_id'];
                        $creditDiff = floatval($params['withdraw_credit']) - floatval($row['withdraw_credit']);

                        // 如果放款额度增加，检查渠道是否有足够的剩余额度
                        if ($creditDiff > 0) {
                            if (!Channels::hasEnoughCreditLeft($channelId, $creditDiff)) {
                                throw new Exception(__('Channel does not have enough credit left'));
                            }

                            // 更新渠道剩余额度
                            if (!Channels::updateCreditLeft($channelId, $creditDiff)) {
                                throw new Exception(__('Failed to update channel credit left'));
                            }
                        } else if ($creditDiff < 0) {
                            // 如果放款额度减少，增加渠道剩余额度
                            if (!Channels::updateCreditLeft($channelId, $creditDiff)) {
                                throw new Exception(__('Failed to update channel credit left'));
                            }
                        }
                    }

                    // 更新业务员记录
                    $result = $row->allowField(true)->save($params);
                    if ($result === false) {
                        throw new Exception(__('Failed to update agent record'));
                    }

                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        // 获取渠道列表
        $channelList = Channels::getChannelList(true);
        $this->view->assign('channelList', $channelList);

        // 获取组长列表
        $leaderList = $this->getLeaderList($ids);
        $this->view->assign('leaderList', $leaderList);

        $this->view->assign('row', $row);
        return $this->view->fetch();
    }

    /**
     * 删除
     */
    public function del($ids = '')
    {
        if ($ids) {
            $pk = $this->model->getPk();
            $adminIds = $this->getDataLimitAdminIds();
            if (is_array($adminIds)) {
                $this->model->where($this->dataLimitField, 'in', $adminIds);
            }
            $list = $this->model->where($pk, 'in', $ids)->select();
            $count = 0;
            Db::startTrans();
            try {
                foreach ($list as $item) {
                    // 获取关联的管理员ID
                    $adminId = $item['admin_id'];

                    // 删除业务员记录
                    $count += $item->delete();

                    // 删除对应的管理员账号
                    if ($adminId) {
                        $admin = Admin::get($adminId);
                        if ($admin) {
                            $admin->delete();
                        }
                    }
                }
                Db::commit();
            } catch (PDOException|Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($count) {
                $this->success();
            } else {
                $this->error(__('Parameter %s can not be empty', 'ids'));
            }
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }

    /**
     * 获取组长列表
     * @param int|null $excludeId 排除的ID（编辑时排除自己）
     * @return array 组长列表
     */
    private function getLeaderList($excludeId = null)
    {
        // 获取当前用户的角色组ID
        $groupIds = $this->auth->getGroupIds();
        $isChannel = in_array(4, $groupIds);

        // 查询可以作为组长的业务员
        $query = $this->model->alias('a')
            ->join('fa_admin admin', 'a.admin_id = admin.id', 'LEFT')
            ->field('a.id, a.channel_id, a.leader_id, admin.nickname, admin.username')
            ->where('admin.status', 'normal');

        // 如果是渠道角色，只显示该渠道下的业务员
        if ($isChannel) {
            // 查询当前管理员关联的渠道ID
            $channelId = null;
            $channel = \think\Db::table('channels')
                ->where('admin_id', $this->auth->id)
                ->field('id')
                ->find();

            if ($channel) {
                $channelId = $channel['id'];
                // 只显示该渠道下的leader_id为null的业务员（组长）
                $query->where('a.channel_id', $channelId)
                      ->where('a.leader_id', 'exp', 'IS NULL');
            }
        }

        // 编辑时排除自己，避免自己成为自己的组长
        if ($excludeId) {
            $query->where('a.id', '<>', $excludeId);
        }

        $agents = $query->select();

        $leaderList = [];
        foreach ($agents as $agent) {
            $displayName = $agent['nickname'] ?: $agent['username'];
            $leaderList[$agent['id']] = $displayName;
        }

        return $leaderList;
    }

    /**
     * 以业务员身份登录
     * @param int $ids 业务员ID
     */
    public function login($ids = null)
    {
        if (!$this->auth->check('agents_config/agents/edit')) {
            $this->error(__('You have no permission'));
        }

        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 获取关联的管理员信息
        $admin = Admin::get($row['admin_id']);

        if (!$admin) {
            $this->error(__('Associated admin account not found'));
        }

        if ($admin['status'] != 'normal') {
            $this->error(__('This account is disabled'));
        }

        // 生成一个唯一的令牌，用于跨域登录验证
        $token = md5(uniqid(mt_rand(), true));

        // 记录管理员操作日志
        Log::write('管理员(' . $this->auth->id . ')以业务员身份登录: agent_id=' . $ids . ', username=' . $admin['username'], 'info');

        // 获取清理后的域名（去除admin、channel或agent前缀）
        $cleanDomain = get_clean_domain();

        // 构建业务员域名
        $urlParts = parse_url($cleanDomain);
        $scheme = isset($urlParts['scheme']) ? $urlParts['scheme'] : 'http';
        $host = isset($urlParts['host']) ? $urlParts['host'] : '';

        // 确保主机名有效
        if (empty($host)) {
            $host = request()->host();
            // 移除可能的前缀
            $hostParts = explode('.', $host);
            if (count($hostParts) > 2 && in_array($hostParts[0], ['admin', 'channel', 'agent'])) {
                array_shift($hostParts);
                $host = implode('.', $hostParts);
            }
        }

        $agentDomain = $scheme . '://agent.' . $host;

        // 将业务员信息和令牌存储在缓存中，设置较短的过期时间（例如5分钟）
        \think\Cache::set('agent_login_' . $token, [
            'admin_id' => $admin['id'],
            'username' => $admin['username'],
            'time' => time()
        ], 300);

        // 构建登录URL，包含token参数
        $loginUrl = $agentDomain . '/index/agent_login?token=' . $token;

        // 返回成功信息和登录URL
        $this->success(__('Preparing to login as agent'), null, ['url' => $loginUrl]);
    }
}
