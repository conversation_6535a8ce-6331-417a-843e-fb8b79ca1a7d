<?php

namespace app\admin\controller\agents_config;

use app\common\controller\Backend;
use think\Db;

/**
 * 业务员报表管理
 *
 * @icon fa fa-circle-o
 */
class AgentsReports extends Backend
{
    protected $model = null;
    protected $searchFields = 'stat_date,agent_id';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\agents_config\AgentsReports;
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 应用数据过滤条件到查询构造器
     * 重写父类方法，针对daily_statistics表没有channel_id字段的情况进行特殊处理
     *
     * @param \think\db\Query $query 查询构造器
     * @param string $tableAlias 表别名
     * @return \think\db\Query 应用过滤条件后的查询构造器
     */
    protected function applyDataFilter($query, $tableAlias = '')
    {
        $roleGroupId = $this->getCurrentRoleGroupId();

        // 如果不是特殊角色，不需要过滤
        if (!$roleGroupId) {
            return $query;
        }

        // 表别名处理
        $prefix = $tableAlias ? $tableAlias . '.' : '';

        // 根据角色类型应用不同的过滤条件
        if ($roleGroupId == 4) { // 渠道角色
            $channelId = $this->getCurrentChannelId();
            if ($channelId) {
                // daily_statistics表没有channel_id字段，但有agent_id字段
                // 查询该渠道下的所有业务员ID
                $agentIds = Db::table('agents')
                    ->where('channel_id', $channelId)
                    ->column('id');

                if (!empty($agentIds)) {
                    $query->where($prefix . 'agent_id', 'in', $agentIds);
                } else {
                    // 如果没有业务员，确保不返回任何数据
                    $query->where($prefix . 'agent_id', -1);
                }
            }
        } elseif ($roleGroupId == 6) { // 业务员角色
            $agentId = $this->getCurrentAgentId();
            if ($agentId) {
                $query->where($prefix . 'agent_id', $agentId);
            }
        }

        return $query;
    }
}
