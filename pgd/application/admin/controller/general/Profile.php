<?php

namespace app\admin\controller\general;

use app\admin\model\Admin;
use app\common\controller\Backend;
use app\common\library\SimpleTOTP; // 引入 SimpleTOTP 类
use app\admin\library\traits\DataFilter; // 引入数据过滤特性
use fast\Random;
use think\Session;
use think\Validate;

/**
 * 个人配置
 *
 * @icon fa fa-user
 */
class Profile extends Backend
{
    // 使用数据过滤特性
    use DataFilter;

    protected $searchFields = 'id,title';

    /**
     * 重写数据过滤方法
     * 在个人资料页面中，AdminLog 表不需要应用基于角色的数据过滤
     * @param \think\db\Query $query 查询构造器
     * @param string $tableAlias 表别名
     * @return \think\db\Query
     */
    protected function applyDataFilter($query, $tableAlias = '')
    {
        // 检查当前模型是否为 AdminLog
        if ($this->model && $this->model->getTable() === 'fa_admin_log') {
            // 对于 AdminLog 表，只过滤当前用户的日志，不应用角色过滤
            return $query;
        }

        // 对其他表应用标准的数据过滤
        return parent::applyDataFilter($query, $tableAlias);
    }

    /**
     * 查看
     */
    public function index()
    {
        // 获取当前管理员信息
        $admin = Admin::get($this->auth->id);

        // 生成临时 TOTP 密钥并存储在 Session 中
        $tmpSecretKey = 'tmp_admin_totp_secret_' . $this->auth->id;
        $otpauth_uri = '';

        // 如果用户未设置 two_factor_key 并且 Session 中没有临时密钥，则生成一个
        if (empty($admin->two_factor_key) && !Session::has($tmpSecretKey)) {
            $secret = SimpleTOTP::generateSecret();
            Session::set($tmpSecretKey, $secret);
        }

        // 如果 Session 中有临时密钥，生成 otpauth URI 用于二维码
        if (Session::has($tmpSecretKey)) {
            $secret = Session::get($tmpSecretKey);
            $totp = SimpleTOTP::create($secret)
                ->setLabel($admin->email ?: $admin->username)
                ->setIssuer(request()->host()); // 使用网站主机名作为 Issuer
            $otpauth_uri = $totp->getProvisioningUri();
        }

        // 将 2FA 相关数据传递给视图
        $this->view->assign('otpauth_uri', $otpauth_uri);
        $this->view->assign('two_factor_enabled', !empty($admin->two_factor_key));

        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            $this->model = model('AdminLog');
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 创建查询构造器
            $query = $this->model->where($where)->where('admin_id', $this->auth->id);

            // 获取分页数据
            $list = $query->order($sort, $order)->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 更新个人信息
     */
    public function update()
    {
        if ($this->request->isPost()) {
            $this->token();
            $params = $this->request->post("row/a");

            // 获取验证码
            $totpVerifyCode = $this->request->post('totp_verify');
            $tmpSecretKey = 'tmp_admin_totp_secret_' . $this->auth->id;

            // 如果提交了验证码且不为空，则验证并绑定
            if (!empty($totpVerifyCode) && Session::has($tmpSecretKey)) {
                $tmpSecret = Session::get($tmpSecretKey);

                // 验证验证码格式
                if (!Validate::is($totpVerifyCode, 'integer') || strlen($totpVerifyCode) != 6) {
                    $this->error(__('请输入有效的6位验证码'));
                }

                // 验证 TOTP 码
                $totp = SimpleTOTP::create($tmpSecret);
                if ($totp->verify($totpVerifyCode)) {
                    // 验证成功，将密钥保存到用户记录
                    $params['two_factor_key'] = $tmpSecret;
                    // 清除临时密钥
                    Session::delete($tmpSecretKey);
                } else {
                    // 验证失败
                    $this->error(__('验证码无效'));
                }
            }

            // 允许更新的字段列表，添加 two_factor_key
            $params = array_filter(array_intersect_key(
                $params,
                array_flip(array('email', 'nickname', 'password', 'avatar', 'two_factor_key'))
            ));
            unset($v);
            if (!Validate::is($params['email'], "email")) {
                $this->error(__("Please input correct email"));
            }
            if (isset($params['password'])) {
                if (!Validate::is($params['password'], "/^[\S]{6,30}$/")) {
                    $this->error(__("Please input correct password"));
                }
                $params['salt'] = Random::alnum();
                $params['password'] = md5(md5($params['password']) . $params['salt']);
            }
            $exist = Admin::where('email', $params['email'])->where('id', '<>', $this->auth->id)->find();
            if ($exist) {
                $this->error(__("Email already exists"));
            }
            if ($params) {
                $admin = Admin::get($this->auth->id);
                $admin->save($params);
                //因为个人资料面板读取的Session显示，修改自己资料后同时更新Session
                Session::set("admin", $admin->toArray());
                Session::set("admin.safecode", $this->auth->getEncryptSafecode($admin));
                $this->success();
            }
            $this->error();
        }
        return;
    }
}
