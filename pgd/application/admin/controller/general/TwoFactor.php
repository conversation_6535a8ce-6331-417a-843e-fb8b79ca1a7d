<?php

namespace app\admin\controller\general;

use app\admin\model\Admin;
use app\common\controller\Backend;
use app\common\library\SimpleTOTP;
use think\Session;
use think\Validate;

/**
 * 双因素认证设置
 *
 * @icon fa fa-shield
 */
class TwoFactor extends Backend
{
    protected $noNeedRight = ['*'];

    /**
     * 双因素认证设置页面
     */
    public function index()
    {
        // 获取当前管理员信息
        $admin = Admin::get($this->auth->id);

        // 生成临时 TOTP 密钥并存储在 Session 中
        $tmpSecretKey = 'tmp_admin_totp_secret_' . $this->auth->id;
        $otpauth_uri = '';

        // 如果用户未设置 two_factor_key 并且 Session 中没有临时密钥，则生成一个
        if (empty($admin->two_factor_key) && !Session::has($tmpSecretKey)) {
            $secret = SimpleTOTP::generateSecret();
            Session::set($tmpSecretKey, $secret);
        }

        // 如果 Session 中有临时密钥，生成 otpauth URI 用于二维码
        if (Session::has($tmpSecretKey)) {
            $secret = Session::get($tmpSecretKey);
            $totp = SimpleTOTP::create($secret)
                ->setLabel($admin->email ?: $admin->username)
                ->setIssuer(request()->host()); // 使用网站主机名作为 Issuer
            $otpauth_uri = $totp->getProvisioningUri();
        }

        // 将 2FA 相关数据传递给视图
        $this->view->assign('otpauth_uri', $otpauth_uri);
        $this->view->assign('two_factor_enabled', !empty($admin->two_factor_key));
        $this->view->assign('admin', $admin);
        $this->view->assign('title', __('Two-Factor Authentication Setup'));

        return $this->view->fetch();
    }

    /**
     * 更新双因素认证设置
     */
    public function update()
    {
        if ($this->request->isPost()) {
            $this->token();

            // 获取验证码
            $totpVerifyCode = $this->request->post('totp_verify');
            $tmpSecretKey = 'tmp_admin_totp_secret_' . $this->auth->id;

            // 如果提交了验证码且不为空，则验证并绑定
            if (!empty($totpVerifyCode) && Session::has($tmpSecretKey)) {
                $tmpSecret = Session::get($tmpSecretKey);

                // 验证验证码格式
                if (!Validate::is($totpVerifyCode, 'integer') || strlen($totpVerifyCode) != 6) {
                    $this->error(__('Please enter a valid 6-digit code'));
                }

                // 验证 TOTP 码
                $totp = SimpleTOTP::create($tmpSecret);
                if ($totp->verify($totpVerifyCode)) {
                    // 验证成功，将密钥保存到用户记录
                    $admin = Admin::get($this->auth->id);
                    $admin->two_factor_key = $tmpSecret;
                    $admin->save();

                    // 清除临时密钥
                    Session::delete($tmpSecretKey);

                    // 记录日志
                    \think\Log::write('用户设置双因素认证成功: admin_id=' . $this->auth->id, 'info');

                    $this->success(__('Two-factor authentication has been enabled successfully'), url('index/index'));
                } else {
                    // 验证失败
                    $this->error(__('Invalid verification code'));
                }
            } else {
                $this->error(__('Please scan the QR code and enter the verification code'));
            }
        }
        $this->error(__('Invalid request'));
    }

    /**
     * 禁用双因素认证
     */
    public function disable()
    {
        if ($this->request->isPost()) {
            $this->token();

            // 获取验证码
            $totpVerifyCode = $this->request->post('totp_verify');

            // 获取当前管理员信息
            $admin = Admin::get($this->auth->id);

            if (empty($admin->two_factor_key)) {
                $this->error(__('Two-factor authentication is not enabled'));
            }

            // 验证验证码格式
            if (empty($totpVerifyCode) || !Validate::is($totpVerifyCode, 'integer') || strlen($totpVerifyCode) != 6) {
                $this->error(__('Please enter a valid 6-digit code'));
            }

            // 验证 TOTP 码
            $totp = SimpleTOTP::create($admin->two_factor_key);
            if ($totp->verify($totpVerifyCode)) {
                // 验证成功，清除密钥
                $admin->two_factor_key = null;
                $admin->save();

                // 记录日志
                \think\Log::write('用户禁用双因素认证: admin_id=' . $this->auth->id, 'info');

                $this->success(__('Two-factor authentication has been disabled'), url('index/index'));
            } else {
                // 验证失败
                $this->error(__('Invalid verification code'));
            }
        }
        $this->error(__('Invalid request'));
    }
}
