<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\exception\HttpResponseException;
use think\Response;

/**
 * 二维码生成
 */
class Qrcode extends Backend
{
    protected $noNeedLogin = ['build'];
    protected $noNeedRight = ['build'];

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 生成二维码
     */
    public function build()
    {
        // 获取插件配置
        $config = get_addon_config('qrcode');
        
        // 获取参数
        $params = $this->request->get();
        $params = array_intersect_key($params, array_flip(['text', 'size', 'padding', 'errorlevel', 'foreground', 'background', 'logo', 'logosize', 'logopath', 'label', 'labelmargin', 'labelfontsize', 'labelalignment', 'labelfontcolor']));

        $params['text'] = $this->request->get('text', $config['text'], 'trim');
        $params['label'] = $this->request->get('label', $config['label'], 'trim');

        // 使用插件的Service类生成二维码
        $qrCode = \addons\qrcode\library\Service::qrcode($params);

        $mimetype = $config['format'] == 'png' ? 'image/png' : 'image/svg+xml';

        $response = Response::create()->header("Content-Type", $mimetype);

        // 直接显示二维码
        header('Content-Type: ' . $qrCode->getMimeType());
        $response->content($qrCode->getString());

        // 写入到文件
        if ($config['writefile']) {
            $qrcodePath = ROOT_PATH . 'public/uploads/qrcode/';
            if (!is_dir($qrcodePath)) {
                @mkdir($qrcodePath);
            }
            if (is_really_writable($qrcodePath)) {
                $filePath = $qrcodePath . md5(implode('', $params)) . '.' . $config['format'];
                $qrCode->saveToFile($filePath);
            }
        }

        return $response;
    }
}
