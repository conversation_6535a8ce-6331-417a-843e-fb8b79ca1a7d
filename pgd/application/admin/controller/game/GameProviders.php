<?php

namespace app\admin\controller\game;

use app\common\controller\Backend;
use think\Config;

/**
 * 游戏供应商管理
 *
 * @icon fa fa-circle-o
 */
class GameProviders extends Backend
{

    /**
     * GameProviders模型对象
     * @var \app\admin\model\game\GameProviders
     */
    protected $model = null;
    protected $multiFields = 'is_active';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\game\GameProviders;

    }

    /**
     * 添加
     */
    // public function add()
    // {
    //     dump([
    //         '请求参数' => $this->request->param(),
    //         '模型信息' => $this->model,
    //         '请求方法' => $this->request->method(),
    //         '视图路径' => $this->view->config('view_path'),
    //         '模板引擎' => Config::get('template'),
    //         '视图替换' => Config::get('view_replace_str'),
    //         '当前模块' => $this->request->module(),
    //         '当前控制器' => $this->request->controller(),
    //         '当前方法' => $this->request->action()
    //     ]);
    //     try {
    //         return parent::add();
    //     } catch (\Exception $e) {
    //         dump('错误信息:', $e->getMessage());
    //         dump('错误文件:', $e->getFile());
    //         dump('错误行号:', $e->getLine());
    //         throw $e;
    //     }
    // }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
