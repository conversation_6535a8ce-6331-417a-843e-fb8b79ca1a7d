<?php

namespace app\admin\controller\game;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 打码任务配置
 *
 * @icon fa fa-money
 */
class BettingAmountConfig extends Backend
{
    protected $model = null;
    protected $searchFields = 'id';
    protected $dataList = [];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = model('app\admin\model\game\BettingAmountConfig');
        // 获取类型数据
        $this->dataList = Db::table('player_balance_change_types')
            ->column('name', 'id');
    }

    /**
     * 查看
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            try {
                list($where, $sort, $order, $offset, $limit) = $this->buildparams();

                $list = $this->model
                    ->where($where)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();

                foreach ($list as &$row) {
                    $row['type_name'] = isset($this->dataList[$row['type_id']]) ? $this->dataList[$row['type_id']] : '';
                }

                $total = $this->model
                    ->where($where)
                    ->count();

                return json(['total' => $total, 'rows' => $list]);
            } catch (Exception $e) {
                return json(['code' => 0, 'msg' => $e->getMessage(), 'data' => null]);
            }
        }
        
        $this->view->assign('typeList', $this->dataList);
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                try {
                    $result = $this->model->validate('BettingAmountConfig.add')->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($this->model->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign('typeList', $this->dataList);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                try {
                    $result = $row->validate('BettingAmountConfig.edit')->save($params);
                    if ($result !== false) {
                        $this->success();
                    } else {
                        $this->error($row->getError());
                    }
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign('typeList', $this->dataList);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if ($ids) {
            $pk = $this->model->getPk();
            $list = $this->model->where($pk, 'in', $ids)->select();
            $count = 0;
            Db::startTrans();
            try {
                foreach ($list as $k => $v) {
                    $count += $v->delete();
                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($count) {
                $this->success();
            } else {
                $this->error(__('No rows were deleted'));
            }
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }
} 