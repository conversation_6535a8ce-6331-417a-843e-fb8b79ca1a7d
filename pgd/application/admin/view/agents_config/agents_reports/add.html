<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Report_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-report_date" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[report_date]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" data-rule="required" data-source="agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_name" data-rule="required" class="form-control" name="row[agent_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Leader_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-leader_name" data-rule="required" class="form-control" name="row[leader_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('New_users')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-new_users" data-rule="required" class="form-control" name="row[new_users]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Active_users')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-active_users" data-rule="required" class="form-control" name="row[active_users]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('First_deposit_users')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-first_deposit_users" data-rule="required" class="form-control" name="row[first_deposit_users]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_deposit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_deposit" data-rule="required" class="form-control" step="0.01" name="row[total_deposit]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Deposit_users')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-deposit_users" data-rule="required" class="form-control" name="row[deposit_users]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_withdraw')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_withdraw" data-rule="required" class="form-control" step="0.01" name="row[total_withdraw]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Withdraw_users')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-withdraw_users" data-rule="required" class="form-control" name="row[withdraw_users]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Deposit_withdraw_diff')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-deposit_withdraw_diff" data-rule="required" class="form-control" step="0.01" name="row[deposit_withdraw_diff]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
