<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-username" data-rule="required" class="form-control" name="row[username]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" class="form-control" name="row[nickname]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-password" data-rule="required" class="form-control" name="row[password]" type="password">
        </div>
    </div>


    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Game credits')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-game_credit" class="form-control" name="row[game_credit]" type="number" value="0">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Withdraw credits')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-withdraw_credit" class="form-control" name="row[withdraw_credit]" type="number" value="0">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Withdraw permission')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[withdraw_permission]', ['1'=>__('Yes'), '0'=>__('No')], 0)}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Can change password')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[can_change_password]', ['1'=>__('Yes'), '0'=>__('No')], 0)}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Leader id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-leader_id" class="form-control selectpicker" name="row[leader_id]">
                <option value="0">{:__('None')}</option>
                {foreach name="leaderList" id="leader" key="id"}
                <option value="{$id}">{$leader}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
