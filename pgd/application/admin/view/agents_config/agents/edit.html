<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-username" data-rule="required" class="form-control" name="row[username]" type="text" value="{$row.username|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" class="form-control" name="row[nickname]" type="text" value="{$row.nickname|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-password" class="form-control" name="row[password]" type="password" placeholder="{:__('Leave blank if no change')}">
        </div>
    </div>
    <!-- 所属渠道字段已移除 -->
    <input type="hidden" name="row[channel_id]" value="{$row.channel_id}">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Game credits')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-game_credit" class="form-control" name="row[game_credit]" type="number" value="{$row.game_credit|htmlentities}">
        </div>
    </div>
    <!-- 已使用游戏额度字段已移除 -->
    <input type="hidden" name="row[used_game_credit]" value="{$row.used_game_credit}">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Withdraw credits')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-withdraw_credit" data-rule="required" class="form-control" step="0.01" name="row[withdraw_credit]" type="number" value="{$row.withdraw_credit}">
        </div>
    </div>
    <!-- 已使用放款额度字段已移除 -->
    <input type="hidden" name="row[used_withdraw_quota]" value="{$row.used_withdraw_quota|default=0}">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Withdraw permission')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[withdraw_permission]', ['1'=>__('Yes'), '0'=>__('No')], $row.withdraw_permission)}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Can change password')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[can_change_password]', ['1'=>__('Yes'), '0'=>__('No')], $row.can_change_password)}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Leader id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-leader_id" class="form-control selectpicker" name="row[leader_id]">
                <option value="0">{:__('None')}</option>
                {foreach name="leaderList" id="leader" key="id"}
                <option value="{$id}" {if $row.leader_id==$id}selected{/if}>{$leader}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <input id="c-created_at" type="hidden" name="row[created_at]" value="{:$row.created_at?datetime($row.created_at):''}">
    <input id="c-updated_at" type="hidden" name="row[updated_at]" value="{:$row.updated_at?datetime($row.updated_at):''}">
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
