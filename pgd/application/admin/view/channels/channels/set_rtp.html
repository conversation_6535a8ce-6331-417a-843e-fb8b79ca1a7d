<div class="panel-body">
    <form id="channel-rtp-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
        <div class="form-group">
            <label for="rtp" class="control-label col-xs-12 col-sm-2">{:__('Channel RTP Value')}:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="rtp" class="form-control" name="rtp" type="number" step="1" min="0" max="100" value="{$channel.rtp}" data-rule="required" placeholder="{:__('Enter a value between 0-100')}">
                <span class="help-block">{:__('Channel RTP Description')}</span>
            </div>
        </div>

        <div class="form-group layer-footer">
            <label class="control-label col-xs-12 col-sm-2"></label>
            <div class="col-xs-12 col-sm-8">
                <button type="submit" class="btn btn-primary btn-embossed">{:__('OK')}</button>
                <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
            </div>
        </div>
    </form>
</div>

<script>
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('渠道RTP设置页面加载完成');

        // 表单提交处理
        var form = document.getElementById('channel-rtp-form');
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // 获取RTP值
            var rtp = document.getElementById('rtp').value;

            // 验证RTP值
            if (!rtp || isNaN(rtp) || rtp < 0 || rtp > 100) {
                alert('{:__("Please enter a valid RTP value between 0 and 100")}');
                return false;
            }

            // 提交表单
            var formData = new FormData(form);

            // 发送AJAX请求
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '');
            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.code === 1) {
                            alert('{:__("RTP updated successfully")}');
                            // 关闭弹窗
                            if (typeof parent.Layer !== 'undefined' && parent.Layer.getFrameIndex) {
                                var index = parent.Layer.getFrameIndex(window.name);
                                parent.Layer.close(index);
                            }
                            // 刷新父页面表格
                            parent.$(".btn-refresh").trigger("click");
                        } else {
                            alert('{:__("Failed to update RTP")}: ' + (response.msg || '{:__("Unknown error")}'));
                        }
                    } catch (e) {
                        console.error('解析响应失败:', e);
                        alert('{:__("Failed to update RTP")}: ' + '{:__("Response parsing error")}');
                    }
                } else {
                    console.error('{:__("Failed to update RTP")}:', xhr.statusText);
                    alert('{:__("Failed to update RTP")}: ' + xhr.statusText);
                }
            };
            xhr.onerror = function() {
                console.error('{:__("Failed to update RTP")}:', xhr.statusText);
                alert('{:__("Failed to update RTP")}: ' + '{:__("Network error")}');
            };
            xhr.send(formData);
        });
    });
</script>
