<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-password" class="form-control" name="row[password]" type="password" value="">
            <span class="help-block" style="color:red;">如需修改密码请输入新密码，不修改请留空</span>
        </div>
    </div>



    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Deposit fee rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-deposit_fee_rate" data-rule="required" class="form-control" step="0.01" name="row[deposit_fee_rate]" type="number" value="{$row.deposit_fee_rate|htmlentities}">
                <span class="input-group-addon">%</span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Withdraw fee rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-withdraw_fee_rate" data-rule="required" class="form-control" step="0.01" name="row[withdraw_fee_rate]" type="number" value="{$row.withdraw_fee_rate|htmlentities}">
                <span class="input-group-addon">%</span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Api fee rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-api_fee_rate" data-rule="required" class="form-control" step="0.01" name="row[api_fee_rate]" type="number" value="{$row.api_fee_rate|htmlentities}">
                <span class="input-group-addon">%</span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control" name="row[remark]" rows="5">{$row.remark|htmlentities}</textarea>
        </div>
    </div>


    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
