<form id="update-limit-form" class="form-horizontal" role="form" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-3">{:__('Channel')}:</label>
        <div class="col-xs-12 col-sm-8">
            <p class="form-control-static">{$channel.name}</p>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-3">{:__('Total Credit Limit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <p class="form-control-static">{$channel.total_credit_limit}</p>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-3">{:__('Allocated Credit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <p class="form-control-static">{$allocated_credit}</p>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-3">{:__('Available Credit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <p class="form-control-static" id="available-credit">{$channel.credit_left}</p>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-3">{:__('Credit Change')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-credit_change" class="form-control" step="0.01" name="credit_change" type="number" value="0">
            <span class="help-block">{:__('Positive value to increase credit, negative value to decrease credit. Decrease cannot exceed available credit.')}</span>
            <div class="invalid-feedback" style="display: none; color: red;" id="credit-error"></div>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-3"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed" id="submit-btn">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed" id="reset-btn">{:__('Reset')}</button>
        </div>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取表单和相关元素
    var form = document.getElementById('update-limit-form');
    var creditChangeInput = document.getElementById('c-credit_change');
    var availableCreditElement = document.getElementById('available-credit');
    var errorElement = document.getElementById('credit-error');
    var submitButton = document.getElementById('submit-btn');
    var resetButton = document.getElementById('reset-btn');

    // 获取可用额度
    var availableCredit = parseFloat(availableCreditElement.textContent) || 0;

    // 验证函数
    function validateCreditChange() {
        var value = parseFloat(creditChangeInput.value) || 0;

        // 检查是否为空
        if (creditChangeInput.value.trim() === '') {
            errorElement.textContent = "{:__('Credit change is required')}";
            errorElement.style.display = 'block';
            return false;
        }

        // 检查是否超过可减额度
        if (value < 0 && Math.abs(value) > availableCredit) {
            errorElement.textContent = "{:__('Decrease amount cannot exceed available credit')}: " + availableCredit.toFixed(2);
            errorElement.style.display = 'block';
            return false;
        }

        // 验证通过
        errorElement.style.display = 'none';
        return true;
    }

    // 监听输入变化
    creditChangeInput.addEventListener('input', validateCreditChange);

    // 监听表单提交
    form.addEventListener('submit', function(event) {
        // 阻止默认提交行为
        event.preventDefault();

        // 验证表单
        if (validateCreditChange()) {
            // 验证通过，提交表单
            this.submit();
        }
    });

    // 监听重置按钮
    resetButton.addEventListener('click', function() {
        // 清除错误信息
        errorElement.style.display = 'none';
        // 重置输入值
        creditChangeInput.value = '0';
    });
});
</script>
