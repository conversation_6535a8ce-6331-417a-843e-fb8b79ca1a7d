<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Stat_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-stat_date" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[stat_date]" type="text" value="{$row.stat_date}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" min="0" data-rule="required" data-source="agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="{$row.agent_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_deposit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_deposit" data-rule="required" class="form-control" step="0.01" name="row[total_deposit]" type="number" value="{$row.total_deposit|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_withdraw')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_withdraw" data-rule="required" class="form-control" step="0.01" name="row[total_withdraw]" type="number" value="{$row.total_withdraw|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_bet')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_bet" data-rule="required" class="form-control" step="0.01" name="row[total_bet]" type="number" value="{$row.total_bet|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_win')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_win" data-rule="required" class="form-control" step="0.01" name="row[total_win]" type="number" value="{$row.total_win|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('New_players')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-new_players" data-rule="required" class="form-control" name="row[new_players]" type="number" value="{$row.new_players|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('First_deposit_players')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-first_deposit_players" data-rule="required" class="form-control" name="row[first_deposit_players]" type="number" value="{$row.first_deposit_players|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Repeat_deposit_players')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-repeat_deposit_players" data-rule="required" class="form-control" name="row[repeat_deposit_players]" type="number" value="{$row.repeat_deposit_players|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('First_deposit_amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-first_deposit_amount" data-rule="required" class="form-control" step="0.01" name="row[first_deposit_amount]" type="number" value="{$row.first_deposit_amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Repeat_deposit_amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-repeat_deposit_amount" data-rule="required" class="form-control" step="0.01" name="row[repeat_deposit_amount]" type="number" value="{$row.repeat_deposit_amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Created_at')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-created_at" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[created_at]" type="text" value="{:$row.created_at?datetime($row.created_at):''}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
