<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                    </div>
                    
                    <!-- 搜索表单 -->
                    <div class="panel-body">
                        <form id="search-form" class="form-horizontal" role="form">
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Date_range')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <div class="input-group">
                                        <input type="text" class="form-control datetimepicker" name="start_date" id="start_date" value="{:date('Y-m-d', strtotime('-7 days'))}" data-date-format="YYYY-MM-DD" />
                                        <span class="input-group-addon">-</span>
                                        <input type="text" class="form-control datetimepicker" name="end_date" id="end_date" value="{:date('Y-m-d')}" data-date-format="YYYY-MM-DD" />
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Channel_id')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input type="text" class="form-control" name="channel_id" id="channel_id" placeholder="{:__('Channel_id')}" />
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-xs-12 col-sm-8 col-sm-offset-2">
                                    <button type="button" id="search-btn" class="btn btn-primary">{:__('Search')}</button>
                                    <button type="reset" class="btn btn-default">{:__('Reset')}</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 统计信息卡片 -->
                    <div class="row" style="margin-bottom: 15px; padding: 0 15px;">
                        <div class="col-md-3">
                            <div class="small-box bg-aqua">
                                <div class="inner">
                                    <h3 id="total-deposit">0.00</h3>
                                    <p>{:__('Total_deposit')}</p>
                                </div>
                                <div class="icon">
                                    <i class="fa fa-money"></i>
                                </div>
                                <div class="small-box-footer">
                                    {$Think.lang.Date_range}: <span id="date-range-text">-</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="small-box bg-red">
                                <div class="inner">
                                    <h3 id="total-withdraw">0.00</h3>
                                    <p>{:__('Total_withdraw')}</p>
                                </div>
                                <div class="icon">
                                    <i class="fa fa-credit-card"></i>
                                </div>
                                <div class="small-box-footer">
                                    &nbsp;
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="small-box bg-green" id="profit-box">
                                <div class="inner">
                                    <h3 id="total-diff">0.00</h3>
                                    <p>{:__('Deposit_withdraw_diff')}</p>
                                </div>
                                <div class="icon">
                                    <i class="fa fa-line-chart"></i>
                                </div>
                                <div class="small-box-footer">
                                    &nbsp;
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="small-box bg-yellow">
                                <div class="inner">
                                    <h3 id="total-profit">0.00</h3>
                                    <p>{:__('Total_profit')}</p>
                                </div>
                                <div class="icon">
                                    <i class="fa fa-pie-chart"></i>
                                </div>
                                <div class="small-box-footer">
                                    &nbsp;
                                </div>
                            </div>
                        </div>
                    </div>

                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="false"
                           data-operate-del="false"
                           data-show-export="false"
                           data-show-toggle="false"
                           data-show-columns="false"
                           data-show-refresh="false"
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
