<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Activity_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-activity_name" data-rule="required" class="form-control" name="row[activity_name]" type="text" value="{$row.activity_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Channel_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-channel_id" data-rule="required" class="form-control selectpicker" name="row[channel_id]" value="{$row.channel_id}">
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Level1_first_deposit_rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-level1_first_deposit_rate" data-rule="required" class="form-control" step="0.01" name="row[level1_first_deposit_rate]" type="number" value="{$row.level1_first_deposit_rate|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Level1_first_deposit_max')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-level1_first_deposit_max" data-rule="required" class="form-control" step="0.01" name="row[level1_first_deposit_max]" type="number" value="{$row.level1_first_deposit_max|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Level2_first_deposit_rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-level2_first_deposit_rate" data-rule="required" class="form-control" step="0.01" name="row[level2_first_deposit_rate]" type="number" value="{$row.level2_first_deposit_rate|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Level2_first_deposit_max')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-level2_first_deposit_max" data-rule="required" class="form-control" step="0.01" name="row[level2_first_deposit_max]" type="number" value="{$row.level2_first_deposit_max|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Min_deposit_amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-min_deposit_amount" data-rule="required" class="form-control" step="0.01" name="row[min_deposit_amount]" type="number" value="{$row.min_deposit_amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Start_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-start_date" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[start_date]" type="text" value="{$row.start_date}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('End_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-end_date" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[end_date]" type="text" value="{$row.end_date}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_active')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_active]', ['1'=>__('Yes'), '0'=>__('No')], $row['is_active'])}
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form> 