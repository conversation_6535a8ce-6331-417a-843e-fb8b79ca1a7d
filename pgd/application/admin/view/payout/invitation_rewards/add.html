<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Player_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-player_id" data-rule="required" min="0" data-source="player/index" class="form-control selectpage" name="row[player_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Config_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-config_id" data-rule="required" min="0" data-source="agents/treasure_box_config/index" class="form-control selectpage" name="row[config_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Reward_amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-reward_amount" data-rule="required" class="form-control" step="0.01" name="row[reward_amount]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_claimed')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-is_claimed" data-rule="required" class="form-control" name="row[is_claimed]" type="number" value="0" readonly>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Claimed_at')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-claimed_at" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[claimed_at]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Created_at')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-created_at" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[created_at]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
