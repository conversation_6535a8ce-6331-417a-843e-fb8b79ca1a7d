<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Player_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-player_id" data-rule="required" min="0" data-source="player/index" class="form-control selectpage" name="row[player_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Referrer_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-referrer_id" min="0" data-rule="required" data-source="referrer/index" class="form-control selectpage" name="row[referrer_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" min="0" data-rule="required" data-source="agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Channel_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-channel_code" data-rule="required" class="form-control" name="row[channel_code]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-amount" data-rule="required" class="form-control" step="0.01" name="row[amount]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Service_fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-service_fee" data-rule="required" class="form-control" step="0.01" name="row[service_fee]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Audit_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-audit_status" data-rule="required" class="form-control" name="row[audit_status]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Withdraw_account_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-withdraw_account_id" data-rule="required" min="0" data-source="withdraw/account/index" class="form-control selectpage" name="row[withdraw_account_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-remark" class="form-control" name="row[remark]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Operator_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-operator_name" class="form-control" name="row[operator_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Operator_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-operator_type" data-rule="required" class="form-control" name="row[operator_type]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Third_order_no')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-third_order_no" class="form-control" name="row[third_order_no]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Created_at')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-created_at" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[created_at]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Processed_at')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-processed_at" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[processed_at]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Paid_at')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-paid_at" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[paid_at]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
