{__NOLAYOUT__}
<div class="panel panel-default panel-intro" style="margin-bottom:0;">
    <div class="panel-body" style="padding:0;">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="dashboard-container">
                    <div class="top-actions">
                        <button type="button" class="btn-simple" id="refresh-btn" onclick="this.classList.add('loading'); setTimeout(function() { location.reload(); }, 300);">
                            <i class="fa fa-refresh"></i> <span>{:__('Refresh')}</span>
                        </button>
                    </div>

                    <!-- 卡片区域 -->
                    <div class="card-section">
                        <div class="card-row">
                            {foreach name="cards" item="card" key="identifier"}
                            <div class="card-item">
                                <div class="info-box">
                                    <div class="info-box-title">{$card.name}</div>
                                    <div class="info-box-today">{:__('Today')}</div>
                                    <div class="info-box-number">{$card.value}</div>
                                    <div class="info-box-yesterday">{:__('Yesterday')}: {$card.yesterday_value}</div>
                                    <div class="info-box-stat">{$card.stat_name}</div>
                                    <div class="info-box-stat-value">{$card.stat_value|default=0}</div>
                                </div>
                            </div>
                            {/foreach}
                        </div>
                    </div>



                    <!-- 数据统计表格 -->
                    <div class="panel-body">

                        <table id="table" class="table table-striped table-bordered table-hover table-nowrap" width="100%">
                            <thead>
                                <tr>
                                    <th>{:__('Date')}</th>
                                    <th>{:__('Active players')}</th>
                                    <th>{:__('New players')}</th>
                                    <th>{:__('First deposit count')}</th>
                                    <th>{:__('Deposit players')}</th>
                                    <th>{:__('Deposit amount')}</th>
                                    <th>{:__('Withdraw players')}</th>
                                    <th>{:__('Withdraw amount')}</th>
                                    <th class="text-danger">{:__('Deposit diff')}</th>
                                    <th class="text-danger">{:__('Game profit')}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {foreach name="tableData" item="row"}
                                <tr>
                                    <td>{$row.date}</td>
                                    <td>{$row.active_players}</td>
                                    <td>{$row.new_players}</td>
                                    <td>{$row.first_deposit_count}</td>
                                    <td>{$row.deposit_players}</td>
                                    <td>{$row.deposit_amount}</td>
                                    <td>{$row.withdraw_players}</td>
                                    <td>{$row.withdraw_amount}</td>
                                    <td><span class="text-danger">{$row.deposit_diff}</span></td>
                                    <td>
                                        {if condition="is_numeric($row.game_profit) && $row.game_profit < 0"}
                                        <span class="text-danger">{$row.game_profit}</span>
                                        {else /}
                                        <span class="text-success">{$row.game_profit}</span>
                                        {/if}
                                    </td>
                                </tr>
                                {/foreach}
                                {empty name="tableData"}
                                <tr>
                                    <td colspan="10" class="text-center">{:__('No data')}</td>
                                </tr>
                                {/empty}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .content-wrapper.tab-content.tab-addtabs {
        padding: 0 !important;
    }
    /* 移除顶部空白 */
    .content-wrapper {
        padding: 0 !important;
        margin: 0 !important;
    }
    .panel-default {
        border: none;
        box-shadow: none;
        margin: 0;
    }
    .panel-intro {
        margin: 0 !important;
    }
    .panel-intro > .panel-heading {
        display: none !important;
    }
    .dashboard-container {
        padding: 15px 15px 0;
    }
    .panel-body {
        padding: 0;
    }
    .panel-default {
        margin-bottom: 0;
    }

    .top-actions {
        margin-bottom: 15px;
        display: flex;
        justify-content: flex-end;
    }
    .card-section {
        margin-bottom: 20px;
    }
    .card-row {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -10px;
    }
    .card-item {
        flex: 0 0 20%;
        padding: 10px;
        box-sizing: border-box;
    }

    .info-box {
        position: relative;
        padding: 20px;
        border-radius: 4px;
        background: #fff;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        height: 140px;
    }
    .info-box-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 15px;
    }
    .info-box-today {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #e6f7ff;
        color: #1890ff;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 12px;
    }
    .info-box-number {
        font-size: 24px;
        color: #333;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .info-box-yesterday {
        font-size: 12px;
        color: #999;
        margin-top: 2px;
        margin-bottom: 10px;
    }
    .info-box-stat {
        position: absolute;
        left: 20px;
        bottom: 15px;
        font-size: 13px;
        color: #999;
    }
    .info-box-stat-value {
        position: absolute;
        right: 20px;
        bottom: 15px;
        font-size: 13px;
        color: #333;
        font-weight: 500;
    }
    .box {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .box-header {
        padding: 15px;
        border-bottom: 1px solid #f4f4f4;
    }
    .box-title {
        margin: 0;
        font-size: 16px;
        font-weight: bold;
    }
    .box-body {
        padding: 15px;
    }
    .table-section {
        background: #fff;
        padding: 15px;
        border-radius: 2px;
        box-shadow: none;
    }
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    /* 简单按钮样式 */
    .btn-simple {
        display: inline-block;
        height: 32px;
        line-height: 30px;
        padding: 0 15px;
        background-color: #fff;
        border: 1px solid #dcdee2;
        border-radius: 4px;
        color: #515a6e;
        font-size: 13px;
        cursor: pointer;
        transition: all .2s ease-in-out;
        margin-right: 10px;
        outline: none;
    }

    .btn-simple:hover {
        color: #2d8cf0;
        border-color: #2d8cf0;
        background-color: #f0faff;
    }

    .btn-simple:active {
        color: #2b85e4;
        border-color: #2b85e4;
    }

    .btn-simple i {
        margin-right: 5px;
    }

    /* 按钮加载状态 */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .btn-simple.loading {
        pointer-events: none;
        opacity: 0.8;
    }

    .btn-simple.loading i.fa-refresh {
        animation: spin 1s linear infinite;
    }


    .table {
        margin-bottom: 15px;
    }
    .table > thead > tr > th {
        background: #f8f8f9;
        border-bottom: 1px solid #e8eaec;
        color: #515a6e;
        font-size: 12px;
        padding: 12px 8px;
        font-weight: 500;
    }
    .table > tbody > tr > td {
        border-bottom: 1px solid #e8eaec;
        color: #515a6e;
        font-size: 12px;
        padding: 12px 8px;
    }
    .table-striped > tbody > tr:nth-of-type(odd) {
        background-color: #f8f8f9;
    }
    .table-hover > tbody > tr:hover {
        background-color: #ebf7ff;
    }
    .text-danger {
        color: #ed4014 !important;
    }
    .pagination-info {
        color: #515a6e;
        font-size: 12px;
        padding: 10px 0;
    }
    .page-size {
        display: inline-block;
        width: 60px;
        height: 24px;
        line-height: 24px;
        margin: 0 5px;
        padding: 0 5px;
        border: 1px solid #dcdee2;
        border-radius: 4px;
        color: #515a6e;
        font-size: 12px;
    }



    /* 响应式布局 */
    @media (max-width: 1200px) {
        .card-item {
            flex: 0 0 25%;
        }
    }
    @media (max-width: 992px) {
        .card-item {
            flex: 0 0 33.333%;
        }
    }
    @media (max-width: 768px) {
        .card-item {
            flex: 0 0 50%;
        }

    }
    @media (max-width: 576px) {
        .card-item {
            flex: 0 0 100%;
        }
    }
</style>
