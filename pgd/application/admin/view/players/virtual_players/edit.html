<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Channel_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            {if condition="(isset($isAgent) && $isAgent) || (isset($isChannel) && $isChannel)"}
                <input type="hidden" id="c-channel_id" name="row[channel_id]" value="{$channelId}">
                <input type="text" class="form-control" value="{$channelName}" readonly>
            {else/}
                <select id="c-channel_id" data-rule="required" class="form-control selectpicker" name="row[channel_id]">
                    <option value="">{:__('Please select')}</option>
                    {foreach name="channelList" item="channel"}
                    <option value="{$channel.id}" {if condition="$row.channel_id eq $channel.id"}selected{/if}>{$channel.name}</option>
                    {/foreach}
                </select>
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-username" data-rule="required" class="form-control" name="row[username]" type="text" value="{$row.username|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Phone_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-phone_number" data-rule="required" class="form-control" name="row[phone_number]" type="text" value="{$row.phone_number|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-password" data-rule="required" class="form-control" name="row[password]" type="text" value="{$row.password|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Balance')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-balance" data-rule="required" class="form-control" step="0.01" name="row[balance]" type="number" value="{$row.balance|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_active')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-is_active" name="row[is_active]" type="hidden" value="{$row.is_active}">
            <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-is_active" data-yes="1" data-no="0">
                <i class="fa fa-toggle-on text-success {if condition="$row.is_active neq 1"}fa-flip-horizontal text-gray{/if} fa-2x"></i>
            </a>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Join_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-join_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[join_time]" type="text" value="{:$row.join_time?datetime($row.join_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Last_login_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-last_login_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[last_login_time]" type="text" value="{:$row.last_login_time?datetime($row.last_login_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Login_ip')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-login_ip" class="form-control" name="row[login_ip]" type="text" value="{$row.login_ip|htmlentities}">
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
