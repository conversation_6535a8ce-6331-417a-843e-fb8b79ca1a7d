<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Channel_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            {if condition="(isset($isAgent) && $isAgent) || (isset($isChannel) && $isChannel)"}
                <input type="hidden" id="c-channel_id" name="row[channel_id]" value="{$channelId}">
                <input type="text" class="form-control" value="{$channelName}" readonly>
            {else/}
                <select id="c-channel_id" data-rule="required" class="form-control selectpicker" name="row[channel_id]">
                    <option value="">{:__('Please select')}</option>
                    {foreach name="channelList" item="channel"}
                    <option value="{$channel.id}">{$channel.name}</option>
                    {/foreach}
                </select>
            {/if}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Generate_count')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-generate_count" data-rule="required" class="form-control" name="row[generate_count]" type="number" value="1" min="1">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Balance')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-balance" data-rule="required" class="form-control" step="0.01" name="row[balance]" type="number" value="0.00">
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed">{:__('Generate')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
