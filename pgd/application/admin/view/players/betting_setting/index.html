<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>打码设置</em></div>
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-md-12">
                <form id="betting-setting-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('players/betting_setting/update')}">
                    <input type="hidden" name="player_id" value="{$player.id}">
                    
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3">玩家ID:</label>
                        <div class="col-xs-12 col-sm-8">
                            <p class="form-control-static">{$player.id}</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3">玩家用户名:</label>
                        <div class="col-xs-12 col-sm-8">
                            <p class="form-control-static">{$player.username}</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3">当前打码任务量:</label>
                        <div class="col-xs-12 col-sm-8">
                            <p class="form-control-static">{$bettingTask.required_betting_amount|default='0.00'}</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3">已完成打码量:</label>
                        <div class="col-xs-12 col-sm-8">
                            <p class="form-control-static">{$bettingTask.completed_betting_amount|default='0.00'}</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-3">完成比例:</label>
                        <div class="col-xs-12 col-sm-8">
                            <p class="form-control-static">
                                {if condition="$bettingTask.required_betting_amount > 0"}
                                    {$bettingTask.completed_betting_amount/$bettingTask.required_betting_amount*100|round=2}%
                                {else}
                                    0.00%
                                {/if}
                            </p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="amount" class="control-label col-xs-12 col-sm-3">调整金额:</label>
                        <div class="col-xs-12 col-sm-8">
                            <input id="amount" data-rule="required" class="form-control" name="amount" type="number" step="0.01" value="0">
                            <span class="help-block">正数增加打码任务量，负数减少打码任务量</span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="remark" class="control-label col-xs-12 col-sm-3">备注:</label>
                        <div class="col-xs-12 col-sm-8">
                            <textarea id="remark" class="form-control" name="remark" rows="3"></textarea>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="col-xs-12 col-sm-8 col-sm-offset-3">
                            <button type="submit" class="btn btn-success btn-embossed">{:__('Submit')}</button>
                            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 处理表单提交前的数据处理
        document.getElementById('betting-setting-form').addEventListener('submit', function(e) {
            // 获取金额
            var amount = parseFloat(document.getElementById('amount').value);

            // 验证金额
            if (!amount || amount === 0) {
                Layer.alert('请输入有效的调整金额（不能为0）', {icon: 2});
                e.preventDefault();
                return false;
            }

            // 确认操作
            var confirmMsg = amount > 0 ?
                '确定要增加 ' + Math.abs(amount) + ' 到玩家打码任务量吗？' :
                '确定要从玩家打码任务量中减少 ' + Math.abs(amount) + ' 吗？';

            if (!confirm(confirmMsg)) {
                e.preventDefault();
                return false;
            }
        });
    });
</script>
