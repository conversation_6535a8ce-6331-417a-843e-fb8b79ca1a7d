<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>玩家余额变更</em></div>
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <p><strong>玩家信息：</strong> ID: {$player.id} | 用户名: {$player.username} | 当前余额: {$player.balance}</p>
                </div>
            </div>
        </div>

        <form id="balance-change-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('players/balance_change/change')}">
            <input type="hidden" name="player_id" value="{$player.id}">

            <div class="form-group">
                <label for="amount" class="control-label col-xs-12 col-sm-2">变更金额:</label>
                <div class="col-xs-12 col-sm-8">
                    <input id="amount" class="form-control" name="amount" type="number" step="0.01" data-rule="required" placeholder="输入正数增加余额，输入负数减少余额">
                    <span class="help-block">输入正数增加余额（上分），输入负数减少余额（下分）</span>
                </div>
            </div>

            <!-- 变更类型已固定为"GM操作增减金币"，不再需要选择 -->
            <input type="hidden" name="change_type" value="2"><!-- 2代表"GM操作增减金币" -->

            <div class="form-group">
                <label for="remark" class="control-label col-xs-12 col-sm-2">备注:</label>
                <div class="col-xs-12 col-sm-8">
                    <textarea id="remark" class="form-control" name="remark" rows="3" placeholder="请输入变更原因或备注信息"></textarea>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2"></label>
                <div class="col-xs-12 col-sm-8">
                    <button type="submit" class="btn btn-success btn-embossed">{:__('Submit')}</button>
                    <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// 使用FastAdmin框架的表单处理，移除原生JavaScript的表单处理
// 相关处理逻辑已在balance_change.js中实现
</script>
