<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Phone_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-phone_number" data-rule="required" class="form-control" name="row[phone_number]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-username" data-rule="required" class="form-control" name="row[username]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_name" class="form-control" name="row[user_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-password" data-rule="required" class="form-control" name="row[password]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Player_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-player_type" data-rule="required" class="form-control selectpicker" name="row[player_type]">
                <option value="">{:__('Please select')}</option>
                {foreach name="playerTagsList" id="tag" key="id"}
                <option value="{$tag}">{$tag}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Balance')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-balance" data-rule="required" class="form-control" step="0.01" name="row[balance]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Reward_balance')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-reward_balance" data-rule="required" class="form-control" step="0.01" name="row[reward_balance]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-agent_id" data-rule="required" class="form-control selectpicker" name="row[agent_id]">
                <option value="0" selected>{:__('Please select')}</option>
                {foreach name="agentList" id="agent" key="id"}
                <option value="{$id}">{$agent}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Vip_level')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-vip_level" class="form-control" name="row[vip_level]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_deposit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_deposit" data-rule="required" class="form-control" step="0.01" name="row[total_deposit]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_withdraw')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_withdraw" data-rule="required" class="form-control" step="0.01" name="row[total_withdraw]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_bet')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_bet" data-rule="required" class="form-control" step="0.01" name="row[total_bet]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_win')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_win" data-rule="required" class="form-control" step="0.01" name="row[total_win]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rtp')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-rtp" data-rule="required" class="form-control" step="0.1" name="row[rtp]" type="number" value="0.0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_game_banned')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-is_game_banned" data-rule="required" class="form-control selectpicker" name="row[is_game_banned]">
                <option value="0">{:__('No_active')}</option>
                <option value="1">{:__('Has_active')}</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('No_unbind_subordinate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-no_unbind_subordinate" data-rule="required" class="form-control selectpicker" name="row[no_unbind_subordinate]">
                <option value="0">{:__('No_active')}</option>
                <option value="1">{:__('Has_active')}</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Self_unbind_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-self_unbind_status" data-rule="required" class="form-control selectpicker" name="row[self_unbind_status]">
                <option value="0">{:__('Self_unbind_0')}</option>
                <option value="1">{:__('Self_unbind_1')}</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Team_withdraw_audit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-team_withdraw_audit" data-rule="required" class="form-control selectpicker" name="row[team_withdraw_audit]">
                <option value="0">{:__('No_active')}</option>
                <option value="1">{:__('Has_active')}</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Personal_withdraw_audit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-personal_withdraw_audit" data-rule="required" class="form-control selectpicker" name="row[personal_withdraw_audit]">
                <option value="0">{:__('No_active')}</option>
                <option value="1">{:__('Has_active')}</option>
            </select>
        </div>
    </div>
    <input type="hidden" name="row[referrer_id]" value="">
    <input type="hidden" name="row[last_login_ip]" value="{$Request.server.REMOTE_ADDR}">
    <input type="hidden" name="row[last_login_time]" value="{:date('Y-m-d H:i:s')}">
    <input type="hidden" name="row[created_at]" value="{:date('Y-m-d H:i:s')}">
    <input type="hidden" name="row[updated_at]" value="{:date('Y-m-d H:i:s')}">
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
