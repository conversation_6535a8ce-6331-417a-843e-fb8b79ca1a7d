<style>
/* 标签页加载样式 */
.tab-loading {
    padding: 30px 0;
    text-align: center;
}
.tab-loading i.fa-spinner {
    font-size: 24px;
    margin-right: 10px;
    color: #3c8dbc;
}
</style>

<div class="panel panel-default panel-intro">
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="basic">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="nav-tabs-custom">
                            <ul class="nav nav-tabs text-sm">
                                <li class="active"><a href="#tab-basic" data-toggle="tab">{:__('Basic Info')}</a></li>
                                <li><a href="#tab-customer" data-toggle="tab">{:__('Customer Service')}</a></li>
                                <li><a href="#tab-code-change" data-toggle="tab">{:__('Betting Code Change')} ({:__('Not Implemented')})</a></li>
                                <li><a href="#tab-code-amount" data-toggle="tab">{:__('Betting Amount')}</a></li>
                                <li><a href="#tab-code-amount-detail" data-toggle="tab">{:__('Betting Amount Log')}</a></li>
                                <li><a href="#tab-deposit" data-toggle="tab">{:__('Deposit Records')}</a></li>
                                <li><a href="#tab-withdraw" data-toggle="tab">{:__('Withdraw Records')}</a></li>
                                <li><a href="#tab-login" data-toggle="tab">{:__('Login Log')}</a></li>
                                <li><a href="#tab-same-ip" data-toggle="tab">{:__('Same IP Query')}</a></li>
                                <li><a href="#tab-remark" data-toggle="tab">{:__('Player Remark')}</a></li>
                                <li><a href="#tab-children" data-toggle="tab">{:__('Subordinate Data')}</a></li>
                                <li><a href="#tab-commission" data-toggle="tab">{:__('Agent Commission')} ({:__('Not Implemented')})</a></li>
                                <li><a href="#tab-game-data" data-toggle="tab">{:__('Game Data')}</a></li>
                                <li><a href="#tab-coin-log" data-toggle="tab">{:__('Coin Log')}</a></li>
                                <li><a href="#tab-agent-balance-log" data-toggle="tab">{:__('Agent Balance Log')}</a></li>
                                <li><a href="#tab-commission-configs" data-toggle="tab">{:__('Commission Settings')}</a></li>
                                <li><a href="#tab-unbind-settings" data-toggle="tab">{:__('Unbind Settings')}</a></li>
                            </ul>
                            <div class="tab-content">
                                <!-- 基本信息 -->
                                <div class="tab-pane active" id="tab-basic">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Basic Info data...')}</div>
                                    </div>
                                </div>

                                <!-- 客服标签页 -->
                                <div class="tab-pane" id="tab-customer">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Customer Service data...')}</div>
                                    </div>
                                </div>

                                <!-- 打码变化标签页 -->
                                <div class="tab-pane" id="tab-code-change">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Betting Code Change data...')}</div>
                                    </div>
                                </div>

                                <!-- 打码量标签页 -->
                                <div class="tab-pane" id="tab-code-amount">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Betting Amount data...')}</div>
                                    </div>
                                </div>

                                <!-- 打码量操作日志标签页 -->
                                <div class="tab-pane" id="tab-code-amount-detail">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Betting Amount Log data...')}</div>
                                    </div>
                                </div>

                                <!-- 充值记录标签页 -->
                                <div class="tab-pane" id="tab-deposit">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Deposit Records data...')}</div>
                                    </div>
                                </div>

                                <!-- 提现记录标签页 -->
                                <div class="tab-pane" id="tab-withdraw">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Withdraw Records data...')}</div>
                                    </div>
                                </div>

                                <!-- 登录日志标签页 -->
                                <div class="tab-pane" id="tab-login">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Login Log data...')}</div>
                                    </div>
                                </div>

                                <!-- 同IP查询标签页 -->
                                <div class="tab-pane" id="tab-same-ip">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Same IP Query data...')}</div>
                                    </div>
                                </div>

                                <!-- 玩家备注标签页 -->
                                <div class="tab-pane" id="tab-remark">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Player Remark data...')}</div>
                                    </div>
                                </div>

                                <!-- 下级数据标签页 -->
                                <div class="tab-pane" id="tab-children">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Subordinate Data data...')}</div>
                                    </div>
                                </div>

                                <!-- 分佣记录标签页 -->
                                <div class="tab-pane" id="tab-commission">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Agent Commission data...')}</div>
                                    </div>
                                </div>

                                <!-- 游戏数据标签页 -->
                                <div class="tab-pane" id="tab-game-data">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Game Data data...')}</div>
                                    </div>
                                </div>

                                <!-- 金币日志标签页 -->
                                <div class="tab-pane" id="tab-coin-log">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Coin Log data...')}</div>
                                    </div>
                                </div>

                                <!-- 代理余额日志标签页 -->
                                <div class="tab-pane" id="tab-agent-balance-log">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Agent Balance Log data...')}</div>
                                    </div>
                                </div>

                                <!-- 充值分成设置标签页 -->
                                <div class="tab-pane" id="tab-commission-configs">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Commission Settings data...')}</div>
                                    </div>
                                </div>

                                <!-- 掉绑设置标签页 -->
                                <div class="tab-pane" id="tab-unbind-settings">
                                    <div class="tab-loading">
                                        <div class="text-center"><i class="fa fa-spinner fa-spin"></i> {:__('Loading Unbind Settings data...')}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
/**
 * 通用工具函数和变量
 */

// 使用轮询方式等待jQuery加载完成
function waitForJQuery(callback) {
    if (typeof $ !== 'undefined') {
        // jQuery已加载，执行回调
        callback();
    } else {
        // jQuery未加载，300毫秒后再次检查
        setTimeout(function() {
            waitForJQuery(callback);
        }, 300);
    }
}

// 辅助函数：隐藏元素
function hideElements(elements) {
    if (!elements || !elements.length) return;
    for (var i = 0; i < elements.length; i++) {
        elements[i].style.display = 'none';
    }
}

// 辅助函数：显示元素
function showElements(elements) {
    if (!elements || !elements.length) return;
    for (var i = 0; i < elements.length; i++) {
        elements[i].style.display = '';
    }
}

// 辅助函数：检查元素是否可见
function isVisible(element) {
    return element && element.offsetParent !== null;
}

// 辅助函数：安全地获取DOM元素
function safeQuerySelector(selector) {
    try {
        return document.querySelector(selector);
    } catch (e) {
        console.error('选择器错误:', selector, e);
        return null;
    }
}

// 辅助函数：安全地获取DOM元素集合
function safeQuerySelectorAll(selector) {
    try {
        return document.querySelectorAll(selector);
    } catch (e) {
        console.error('选择器错误:', selector, e);
        return [];
    }
}

// 辅助函数：安全地添加事件监听器
function safeAddEventListener(element, event, handler) {
    if (!element) return false;
    try {
        element.addEventListener(event, handler);
        return true;
    } catch (e) {
        console.error('添加事件监听器失败:', e);
        return false;
    }
}

/**
 * 页面初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，开始初始化...');
    var player_id = '{$player_id}';

    // 检查当前活动的标签页
    var activeTab = safeQuerySelector('.nav-tabs .active a');
    if (activeTab) {
        var target = activeTab.getAttribute('href');
        // 加载当前活动标签页的数据
        loadTabContent(target, player_id);
    }

    // 添加标签页切换事件监听器 - 使用两种方式确保事件被捕获

    // 方式1: 使用原生JavaScript为每个标签添加事件监听器
    var tabLinks = safeQuerySelectorAll('a[data-toggle="tab"]');
    console.log('找到标签页数量:', tabLinks.length);
    for (var i = 0; i < tabLinks.length; i++) {
        (function(tab) {
            safeAddEventListener(tab, 'click', function(e) {
                console.log('标签页点击:', tab.getAttribute('href'));
                // 给事件处理一些时间来切换标签页
                setTimeout(function() {
                    var target = tab.getAttribute('href');
                    loadTabContent(target, player_id);
                }, 100);
            });
        })(tabLinks[i]);
    }

    // 等待jQuery加载完成后初始化依赖jQuery的功能
    waitForJQuery(function() {
        initJQueryDependentFeatures();
    });
});

// 标签页加载函数将在下面定义
// 标签页ID到加载函数的映射将在所有函数定义后初始化
var tabIdToLoader = {};

// 根据标签页ID加载对应内容
function loadTabContent(tabId, playerId) {
    console.log('准备加载标签页内容:', tabId);

    // 检查标签页ID是否有效
    if (!tabId) {
        console.error('标签页ID无效:', tabId);
        return;
    }

    // 确保标签页元素存在
    var tabElement = document.querySelector(tabId);
    if (!tabElement) {
        console.error('找不到标签页元素:', tabId);
        return;
    }

    // 记录加载开始时间，用于性能分析
    var startTime = new Date().getTime();

    try {
        // 查找并执行对应的加载函数
        var loader = tabIdToLoader[tabId];
        if (typeof loader === 'function') {
            console.log('开始加载' + tabId + '标签页数据');
            loader(playerId);
        } else {
            console.warn('未知标签页:', tabId);
        }

        // 记录加载完成时间
        var endTime = new Date().getTime();
        console.log('标签页 ' + tabId + ' 数据加载耗时: ' + (endTime - startTime) + 'ms');
    } catch (error) {
        console.error('加载标签页 ' + tabId + ' 数据时出错:', error);
    }
}

// 表单事件已移至各自的标签页HTML文件中

// 获取标签页的中文名称
function getTabDisplayName(tabName) {
    var tabDisplayNames = {
        'basic': '基本信息',
        'login': '登录日志',
        'same_ip': '同IP查询',
        'remark': '玩家备注',
        'children': '下级数据',
        'commission': '代理佣金',
        'game_data': '游戏数据',
        'coin_log': '金币日志',
        'agent_balance_log': '代理余额日志',
        'commission_configs': '充值分成设置',
        'code_change': '打码变化',
        'code_amount': '打码量',
        'code_amount_detail': '打码量操作日志',
        'deposit': '充值记录',
        'withdraw': '提现记录',
        'customer': '客服',
        'unbind_settings': '掉绑设置'
    };

    return tabDisplayNames[tabName] || tabName;
}

// 初始化依赖jQuery的功能
function initJQueryDependentFeatures() {
    if (typeof $ === 'undefined') {
        console.error('jQuery未加载，无法初始化依赖jQuery的功能');
        return;
    }

    console.log('jQuery已加载，初始化依赖jQuery的功能');

    // 表单验证已移至各自的标签页HTML文件中

    // 初始化搜索按钮事件
    initSearchButtons();
}

/**
 * 初始化搜索按钮事件
 * 注意：搜索按钮事件现在在各自的标签页HTML文件中处理
 */
function initSearchButtons() {
    // 搜索按钮事件已移至各自的标签页HTML文件中
    console.log('搜索按钮事件初始化 - 已移至各自的标签页HTML文件中');
}

/**
 * 掉绑设置相关功能已移至 detail_tabs/unbind_settings.html 文件中
 */

// 定义全局函数，不依赖RequireJS
window.initPlayerUnbindSettings = function(playerId) {
    // 使用通用的标签页加载函数
    loadTabContentWithAjax('#tab-unbind-settings', 'unbind_settings', playerId);
};

/**
 * 数据加载函数
 */

// 构建API URL的辅助函数
function buildApiUrl(endpoint) {
    // 使用ThinkPHP的URL生成函数构建完整的URL
    var baseUrl = '{:url("players/players")}';
    // 移除可能的后缀
    baseUrl = baseUrl.replace(/\.html$/, '');
    return baseUrl + '/' + endpoint;
}

// 将 buildApiUrl 函数添加到全局作用域，确保在标签页内容中可用
window.buildApiUrl = buildApiUrl;

// 通用AJAX数据加载函数
function loadDataWithAjax(endpoint, data, successCallback, errorCallback) {
    waitForJQuery(function() {
        // 构建完整的URL
        var url = buildApiUrl(endpoint);
        console.log('API请求URL:', url, '数据:', data);

        $.ajax({
            url: url,
            type: 'GET',
            data: data,
            dataType: 'json',
            success: function(data) {
                if (typeof successCallback === 'function') {
                    successCallback(data);
                }
            },
            error: function(xhr, status, error) {
                console.error('加载数据失败:', error, xhr.responseText);
                if (typeof errorCallback === 'function') {
                    errorCallback(xhr, status, error);
                }
            }
        });
    });
}

// 加载标签页内容的通用函数
function loadTabContentWithAjax(tabId, tabName, playerId, callback) {
    var tabElement = document.querySelector(tabId);
    if (!tabElement) return;

    // 获取标签页的中文名称
    var tabDisplayName = getTabDisplayName(tabName);

    // 显示加载中
    tabElement.innerHTML = '<div class="tab-loading"><div class="text-center"><i class="fa fa-spinner fa-spin"></i> 正在加载' + tabDisplayName + '数据...</div></div>';

    waitForJQuery(function() {
        // 构建完整的URL
        var url = buildApiUrl('detail_tabs/' + tabName);
        console.log('请求URL:', url);

        $.ajax({
            url: url,
            type: 'GET',
            data: { player_id: playerId },
            success: function(html) {
                // 检查返回的HTML是否包含错误信息
                if (typeof html === 'string' && html.indexOf('class="error"') > -1) {
                    console.error("服务器返回错误:", html);
                    $(tabId).html('<div class="panel-body"><div class="alert alert-danger">服务器返回错误，请查看控制台日志</div></div>');
                    return;
                }

                // 更新标签页内容
                $(tabId).html(html);

                // 执行标签页中的初始化函数
                if (typeof callback === 'function') {
                    callback();
                } else {
                    // 尝试调用标准命名的初始化函数
                    var funcName = 'load' + tabName.split('_').map(function(part) {
                        return part.charAt(0).toUpperCase() + part.slice(1);
                    }).join('') + 'Tab';
                    console.log('尝试调用初始化函数:', funcName, '是否存在:', typeof window[funcName] === 'function');

                    if (tabName === 'unbind_settings') {
                        console.log('特殊处理掉绑设置标签页');
                        if (typeof loadUnbindSettingsTab === 'function') {
                            console.log('直接调用 loadUnbindSettingsTab 函数');
                            loadUnbindSettingsTab();
                        } else {
                            console.error('loadUnbindSettingsTab 函数未定义');
                        }
                    } else if (typeof window[funcName] === 'function') {
                        window[funcName]();
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error("加载" + tabDisplayName + "标签页内容失败:", error, xhr.responseText);
                $(tabId).html('<div class="panel-body"><div class="alert alert-danger">加载' + tabDisplayName + '内容失败，请刷新页面重试</div><div class="small text-muted">错误详情: ' + error + '</div></div>');
            }
        });
    });
}

// 标签页配置已移至文件顶部

/**
 * 标签页加载函数
 */

// 基本信息标签页
function loadBasic() {
    loadTabContentWithAjax('#tab-basic', 'basic', '{$player_id}');
}

// 客服标签页
function loadCustomer() {
    loadTabContentWithAjax('#tab-customer', 'customer', '{$player_id}');
}

// 登录日志标签页
function loadLoginLog() {
    loadTabContentWithAjax('#tab-login', 'login', '{$player_id}');
}

// 同IP查询标签页 - 直接输出方式，不需要额外的JavaScript处理
function loadSameIp() {
    loadTabContentWithAjax('#tab-same-ip', 'same_ip', '{$player_id}');
    // 注意：同IP查询数据现在由后端直接输出，不需要额外的JavaScript处理
}

// 玩家备注标签页 - 直接输出方式，不需要额外的JavaScript处理
function loadRemark() {
    loadTabContentWithAjax('#tab-remark', 'remark', '{$player_id}');
    // 注意：备注数据现在由后端直接输出，不需要额外的JavaScript处理
}

// 下级数据标签页
function loadChildren() {
    loadTabContentWithAjax('#tab-children', 'children', '{$player_id}');
}

// 代理佣金标签页
function loadCommission() {
    loadTabContentWithAjax('#tab-commission', 'commission', '{$player_id}');
}

// 游戏数据标签页
function loadGameData() {
    loadTabContentWithAjax('#tab-game-data', 'game_data', '{$player_id}');
}

// 金币日志标签页
function loadCoinLog() {
    loadTabContentWithAjax('#tab-coin-log', 'coin_log', '{$player_id}');
}

// 代理余额日志标签页
function loadAgentBalanceLog() {
    loadTabContentWithAjax('#tab-agent-balance-log', 'agent_balance_log', '{$player_id}');
}

// 充值分成设置标签页
function loadCommissionConfigs() {
    loadTabContentWithAjax('#tab-commission-configs', 'commission_configs', '{$player_id}');
}

// 打码变化标签页
function loadCodeChange() {
    loadTabContentWithAjax('#tab-code-change', 'code_change', '{$player_id}');
}

// 打码量标签页
function loadCodeAmount() {
    loadTabContentWithAjax('#tab-code-amount', 'code_amount', '{$player_id}');
}

// 打码量操作日志标签页
function loadCodeAmountDetail() {
    loadTabContentWithAjax('#tab-code-amount-detail', 'code_amount_detail', '{$player_id}');
}

// 充值记录标签页
function loadDeposit() {
    loadTabContentWithAjax('#tab-deposit', 'deposit', '{$player_id}');
}

// 提现记录标签页
function loadWithdraw() {
    loadTabContentWithAjax('#tab-withdraw', 'withdraw', '{$player_id}');
}

// 掉绑设置标签页
function loadUnbindSettings() {
    loadTabContentWithAjax('#tab-unbind-settings', 'unbind_settings', '{$player_id}');
}

// 所有标签页加载函数已定义

// 初始化标签页ID到加载函数的映射
tabIdToLoader = {
    '#tab-basic': loadBasic,
    '#tab-customer': loadCustomer,
    '#tab-login': loadLoginLog,
    '#tab-same-ip': loadSameIp,
    '#tab-remark': loadRemark,
    '#tab-children': loadChildren,
    '#tab-commission': loadCommission,
    '#tab-game-data': loadGameData,
    '#tab-coin-log': loadCoinLog,
    '#tab-agent-balance-log': loadAgentBalanceLog,
    '#tab-commission-configs': loadCommissionConfigs,
    '#tab-code-change': loadCodeChange,
    '#tab-code-amount': loadCodeAmount,
    '#tab-code-amount-detail': loadCodeAmountDetail,
    '#tab-deposit': loadDeposit,
    '#tab-withdraw': loadWithdraw,
    '#tab-unbind-settings': loadUnbindSettings
};

// 通用表格初始化函数 - 使用bootstrapTable
function initBootstrapTable(tableId, url, columns) {
    waitForJQuery(function() {
        var tableSelector = '#' + tableId;
        var containerSelector = '#tab-' + tableId.replace('-table', '');

        // 清空并重新创建表格容器
        $(containerSelector).html('<div class="table-responsive"><table id="' + tableId + '" class="table table-striped table-bordered"></table></div>');

        // 初始化表格
        $(tableSelector).bootstrapTable({
            url: url,
            columns: columns,
            pagination: true,
            search: true,
            showRefresh: true,
            showToggle: true,
            showColumns: true,
            showExport: true,
            exportDataType: 'all',
            exportTypes: ['json', 'xml', 'csv', 'txt', 'excel'],
            pageSize: 10,
            pageList: [10, 25, 50, 100, 'All']
        });
    });
}

// 通用表格数据更新函数 - 使用HTML
function updateTableWithHtml(tableId, data, rowRenderer) {
    waitForJQuery(function() {
        var $tbody = $('#' + tableId + ' tbody');
        if (!$tbody.length) {
            console.error('找不到表格:', tableId);
            return;
        }

        if (!data || !data.rows || !data.rows.length) {
            $tbody.html('<tr><td colspan="20" class="text-center">没有找到匹配的记录</td></tr>');
            return;
        }

        var html = '';
        $.each(data.rows, function(i, item) {
            html += rowRenderer(item);
        });

        $tbody.html(html);
    });
}

// 这些函数已经被 loadTabContentWithAjax 通用函数替代
// 如果需要在标签页加载后执行特定逻辑，可以在对应的标签页HTML文件中定义 loadXxxTab 函数
</script>