<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Phone_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-phone_number" data-rule="required" class="form-control" name="row[phone_number]" type="text" value="{$row.phone_number|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-username" data-rule="required" class="form-control" name="row[username]" type="text" value="{$row.username|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_name" class="form-control" name="row[user_name]" type="text" value="{$row.user_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-password" data-rule="required" class="form-control" name="row[password]" type="text" value="{$row.password|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Player_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-player_type" data-rule="required" class="form-control selectpicker" name="row[player_type]">
                <option value="">{:__('Please select')}</option>
                {foreach name="playerTagsList" id="tag" key="id"}
                <option value="{$tag}" {if condition="$row.player_type eq $tag"}selected{/if}>{$tag}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Balance')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-balance" data-rule="required" class="form-control" step="0.01" name="row[balance]" type="number" value="{$row.balance|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Reward_balance')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-reward_balance" data-rule="required" class="form-control" step="0.01" name="row[reward_balance]" type="number" value="{$row.reward_balance|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-agent_id" data-rule="required" class="form-control selectpicker" name="row[agent_id]">
                <option value="0">{:__('Please select')}</option>
                {foreach name="agentList" id="agent" key="id"}
                <option value="{$id}" {if condition="$row.agent_id eq $id"}selected{/if}>{$agent}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Vip_level')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-vip_level" class="form-control" name="row[vip_level]" type="number" value="{$row.vip_level|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_deposit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_deposit" data-rule="required" class="form-control" step="0.01" name="row[total_deposit]" type="number" value="{$row.total_deposit|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_withdraw')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_withdraw" data-rule="required" class="form-control" step="0.01" name="row[total_withdraw]" type="number" value="{$row.total_withdraw|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_bet')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_bet" data-rule="required" class="form-control" step="0.01" name="row[total_bet]" type="number" value="{$row.total_bet|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_win')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_win" data-rule="required" class="form-control" step="0.01" name="row[total_win]" type="number" value="{$row.total_win|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rtp')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-rtp" data-rule="required" class="form-control" step="0.1" name="row[rtp]" type="number" value="{$row.rtp|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_game_banned')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-is_game_banned" data-rule="required" class="form-control selectpicker" name="row[is_game_banned]">
                <option value="0" {if condition="$row.is_game_banned eq 0"}selected{/if}>{:__('No_active')}</option>
                <option value="1" {if condition="$row.is_game_banned eq 1"}selected{/if}>{:__('Has_active')}</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('No_unbind_subordinate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-no_unbind_subordinate" data-rule="required" class="form-control selectpicker" name="row[no_unbind_subordinate]">
                <option value="0" {if condition="$row.no_unbind_subordinate eq 0"}selected{/if}>{:__('No_active')}</option>
                <option value="1" {if condition="$row.no_unbind_subordinate eq 1"}selected{/if}>{:__('Has_active')}</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Self_unbind_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-self_unbind_status" data-rule="required" class="form-control selectpicker" name="row[self_unbind_status]">
                <option value="0" {if condition="$row.self_unbind_status eq 0"}selected{/if}>{:__('Self_unbind_0')}</option>
                <option value="1" {if condition="$row.self_unbind_status eq 1"}selected{/if}>{:__('Self_unbind_1')}</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Team_withdraw_audit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-team_withdraw_audit" data-rule="required" class="form-control selectpicker" name="row[team_withdraw_audit]">
                <option value="0" {if condition="$row.team_withdraw_audit eq 0"}selected{/if}>{:__('No_active')}</option>
                <option value="1" {if condition="$row.team_withdraw_audit eq 1"}selected{/if}>{:__('Has_active')}</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Personal_withdraw_audit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-personal_withdraw_audit" data-rule="required" class="form-control selectpicker" name="row[personal_withdraw_audit]">
                <option value="0" {if condition="$row.personal_withdraw_audit eq 0"}selected{/if}>{:__('No_active')}</option>
                <option value="1" {if condition="$row.personal_withdraw_audit eq 1"}selected{/if}>{:__('Has_active')}</option>
            </select>
        </div>
    </div>
    <input type="hidden" name="row[referrer_id]" value="{$row.referrer_id|htmlentities}">
    <input type="hidden" name="row[last_login_ip]" value="{$row.last_login_ip|htmlentities}">
    <input type="hidden" name="row[last_login_time]" value="{$row.last_login_time|htmlentities}">
    <input type="hidden" name="row[created_at]" value="{$row.created_at|htmlentities}">
    <input type="hidden" name="row[updated_at]" value="{:date('Y-m-d H:i:s')}">
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
