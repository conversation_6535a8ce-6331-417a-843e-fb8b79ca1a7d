<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        {php}
                        // 获取当前用户角色组ID
                        $roleGroupIds = $auth->getGroupIds();
                        // 检查是否是Admin(1)或平台管理员(2)
                        $isAdminOrPlatform = in_array(1, $roleGroupIds) || in_array(2, $roleGroupIds);
                        // 检查是否是渠道(4)
                        $isChannel = in_array(4, $roleGroupIds);
                        // 检查是否是渠道(4)且允许隐藏玩家
                        $isChannelWithHiddenPlayer = false;
                        if ($isChannel) {
                            $channelId = \app\admin\library\SqlFilter::getCurrentChannelId();
                            if ($channelId) {
                                $channel = \think\Db::table('channels')->where('id', $channelId)->find();
                                $isChannelWithHiddenPlayer = $channel && $channel['allow_hidden_player'] == 1;
                            }
                        }
                        // 只有满足条件的用户才显示全局掉绑设置按钮
                        if ($isAdminOrPlatform || $isChannelWithHiddenPlayer):
                        {/php}
                        <a href="javascript:;" class="btn btn-info btn-unbind-setting" title="{:__('Unbind Settings')}"><i class="fa fa-cog"></i> {:__('Unbind Settings')}</a>
                        {php}endif;{/php}

                        {php}
                        // 只有渠道角色才显示渠道RTP设置按钮
                        if ($isChannel):
                        {/php}
                        <a href="javascript:;" class="btn btn-warning btn-channel-rtp" title="{:__('Set Channel RTP')}"><i class="fa fa-sliders"></i> {:__('Set Channel RTP')}</a>
                        {php}endif;{/php}
                        <div class="statistics-panel" style="display: inline-block; margin-left: 10px;">
                            <div id="statistics-info" class="alert alert-info" style="margin-bottom: 0; padding: 6px 12px;">
                                {:__('Loading statistics data...')}
                            </div>
                        </div>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('players/players/edit')}"
                           data-operate-del="{:$auth->check('players/players/del')}"
                           width="100%">
                    </table>
                    <style>
                    /* 优化固定列的样式 */
                    .fixed-table-container .fixed-table-body-columns .table,
                    .fixed-table-container .fixed-table-body-columns .table tr th,
                    .fixed-table-container .fixed-table-body-columns .table tr td {
                        background-color: #fff;
                    }
                    .fixed-table-container .fixed-table-body-columns .table tr th:last-child,
                    .fixed-table-container .fixed-table-body-columns .table tr td:last-child {
                        border-right: 1px solid #ddd;
                    }
                    .fixed-table-container .fixed-table-body-columns {
                        box-shadow: 4px 0 8px -4px rgba(0,0,0,0.1);
                    }
                    .fixed-table-container .fixed-table-body-columns .table tr:hover td {
                        background-color: #f5f5f5;
                    }
                    </style>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/html" id="playertagtemplate">
    <select name="tag_id" class="form-control selectpicker" data-live-search="true">
        <option value="">{:__('Please select')}</option>
        <option value="all">{:__('All')}</option>
        {foreach name="playerTypeList" item="vo"}
        <option value="{$vo.id}">{$vo.name}</option>
        {/foreach}
    </select>
</script>
