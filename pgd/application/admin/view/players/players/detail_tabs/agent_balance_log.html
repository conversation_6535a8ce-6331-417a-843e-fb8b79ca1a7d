<!-- 代理余额日志标签页 -->
<div class="panel-body">
    <form id="agent-balance-log-form" class="form-horizontal" role="form">
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-1">{:__('Player ID')}:</label>
            <div class="col-xs-12 col-sm-2">
                <input type="text" class="form-control" id="player-id-agent" value="{$player_id}" readonly>
            </div>
            <label class="control-label col-xs-12 col-sm-1">{:__('Operation Type')}:</label>
            <div class="col-xs-12 col-sm-2">
                <select class="form-control" id="operation-type-agent">
                    <option value="">{:__('All')}</option>
                    <option value="commission">{:__('Commission')}</option>
                    <option value="withdraw">{:__('Withdraw')}</option>
                    <option value="admin">{:__('Admin Operation')}</option>
                </select>
            </div>
            <label class="control-label col-xs-12 col-sm-1">{:__('Start Time')}:</label>
            <div class="col-xs-12 col-sm-2">
                <input type="text" class="form-control datetimepicker" id="start-time-agent" placeholder="{:__('Start Time')}">
            </div>
            <label class="control-label col-xs-12 col-sm-1">{:__('End Time')}:</label>
            <div class="col-xs-12 col-sm-2">
                <input type="text" class="form-control datetimepicker" id="end-time-agent" placeholder="{:__('End Time')}">
            </div>
            <div class="col-xs-12 col-sm-2">
                <button type="button" id="search-agent-balance" class="btn btn-primary">{:__('Search')}</button>
                <button type="reset" class="btn btn-default">{:__('Reset')}</button>
            </div>
        </div>
    </form>
    
    <div class="table-responsive">
        <table id="agent-balance-log-table" class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>{:__('Player ID')}</th>
                    <th>{:__('Operation Type')}</th>
                    <th>{:__('Amount Before Change')}</th>
                    <th>{:__('Change Amount')}</th>
                    <th>{:__('Amount After Change')}</th>
                    <th>{:__('Remark')}</th>
                    <th>{:__('Operation Time')}</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="8" class="text-center">{:__('No matching records found')}</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
// 当标签页被激活时加载数据
function loadAgentBalanceLogTab() {
    console.log("加载代理余额日志数据");
    
    // 初始化日期选择器
    if ($.fn.datetimepicker) {
        $('.datetimepicker').datetimepicker({
            format: 'YYYY-MM-DD HH:mm:ss',
            locale: 'zh-cn'
        });
    }
    
    // 绑定搜索按钮事件
    $('#search-agent-balance').off('click').on('click', function(e) {
        e.preventDefault();
        
        var operationType = $('#operation-type-agent').val();
        var startTime = $('#start-time-agent').val();
        var endTime = $('#end-time-agent').val();
        
        // 加载代理余额日志数据
        loadAgentBalanceData(operationType, startTime, endTime);
    });
    
    // 初始加载数据
    loadAgentBalanceData();
    
    // 加载代理余额日志数据函数
    function loadAgentBalanceData(operationType, startTime, endTime) {
        var params = {
            player_id: '{$player_id}'
        };
        
        if (operationType) {
            params.operation_type = operationType;
        }
        
        if (startTime) {
            params.start_time = startTime;
        }
        
        if (endTime) {
            params.end_time = endTime;
        }
        
        // 显示加载中
        $('#agent-balance-log-table tbody').html('<tr><td colspan="8" class="text-center"><i class="fa fa-spinner fa-spin"></i> 正在加载数据...</td></tr>');
        
        $.ajax({
            url: 'players/players/get_agent_balance_log',
            type: 'GET',
            data: params,
            dataType: 'json',
            success: function(data) {
                if (data && data.rows && data.rows.length > 0) {
                    var html = '';
                    $.each(data.rows, function(i, item) {
                        html += '<tr>';
                        html += '<td>' + (item.id || '') + '</td>';
                        html += '<td>' + (item.player_id || '') + '</td>';
                        html += '<td>' + (item.operation_type || '') + '</td>';
                        html += '<td>' + (item.before_amount || '0.00') + '</td>';
                        html += '<td>' + (item.change_amount || '0.00') + '</td>';
                        html += '<td>' + (item.after_amount || '0.00') + '</td>';
                        html += '<td>' + (item.remark || '') + '</td>';
                        html += '<td>' + (item.created_at || '') + '</td>';
                        html += '</tr>';
                    });
                    $('#agent-balance-log-table tbody').html(html);
                } else {
                    $('#agent-balance-log-table tbody').html('<tr><td colspan="8" class="text-center">没有找到匹配的记录</td></tr>');
                }
            },
            error: function(xhr, status, error) {
                console.error("加载代理余额日志数据失败:", error);
                $('#agent-balance-log-table tbody').html('<tr><td colspan="8" class="text-center text-danger">加载数据失败，请稍后重试</td></tr>');
            }
        });
    }
}
</script>
