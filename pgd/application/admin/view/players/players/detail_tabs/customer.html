<!-- 客服标签页 -->
<div class="row">
    <div class="col-md-12">
        <form id="customer-form" class="form-horizontal" role="form" data-toggle="validator">
            <div class="form-group">
                <label class="control-label col-sm-2">输入密码:</label>
                <div class="col-sm-8">
                    <input type="password" class="form-control" name="password">
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-offset-2 col-sm-8">
                    <button type="button" class="btn btn-primary">修改密码</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// 当标签页被激活时加载数据
function loadCustomerTab() {
    console.log("加载客服标签页数据");
    
    // 绑定修改密码按钮事件
    $('#customer-form button[type="button"]').off('click').on('click', function(e) {
        e.preventDefault();
        
        var password = $('#customer-form input[name="password"]').val();
        
        if (!password) {
            alert('请输入密码');
            return;
        }
        
        // 提交修改密码请求
        $.ajax({
            url: 'players/players/update_password',
            type: 'POST',
            data: {
                player_id: '{$player_id}',
                password: password
            },
            dataType: 'json',
            success: function(response) {
                if (response.code == 1) {
                    alert('修改密码成功');
                    $('#customer-form input[name="password"]').val('');
                } else {
                    alert('修改密码失败: ' + response.msg);
                }
            },
            error: function(xhr, status, error) {
                alert('修改密码失败，请稍后重试');
                console.error("修改密码失败:", error);
            }
        });
    });
}
</script>
