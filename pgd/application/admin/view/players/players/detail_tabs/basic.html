<!-- 基本信息标签页 -->
<div class="panel-body">
    <div class="panel panel-default margin-bottom-none">
        <div class="panel-heading pad">{:__('Account Information')}</div>
        <div class="panel-body pad">
            <div class="row no-margin">
                <div class="col-md-4">
                    <div class="form-group form-inline no-margin">
                        <label>{:__('Player Account')}: </label>
                        <span>{$row.username|htmlentities}</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-inline">
                        <label>{:__('Game ID')}: </label>
                        <span>{$row.id|htmlentities}</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-inline">
                        <label>{:__('Player Nickname')}: </label>
                        <span>{$row.nickname|htmlentities}</span>
                    </div>
                </div>
            </div>
            <div class="row no-margin">
                <div class="col-md-4">
                    <div class="form-group form-inline">
                        <label>{:__('Phone Number')}: </label>
                        <span>{$row.phone_number|htmlentities}</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-inline">
                        <label>{:__('VIP Level')}: </label>
                        <span>{$row.vip_level|default='0'}</span>
                    </div>
                </div>
            </div>
            <div class="row no-margin">
                <div class="col-md-4">
                    <div class="form-group form-inline">
                        <label>{:__('Agent')}: </label>
                        <span>{$row.agent_name|default='-'}</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-inline">
                        <label>{:__('Channel')}: </label>
                        <span>{$row.channel_name|default='-'}</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-inline">
                        <label>{:__('Referrer_id')}: </label>
                        <span>
                            {if $row.referrer_id}
                                <a href="javascript:;" class="btn-player-details" data-id="{$row.referrer_id}">{$row.referrer_id}</a>
                            {else}
                                -
                            {/if}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default margin-bottom-none">
        <div class="panel-heading pad">{:__('Withdrawal Binding Information')}</div>
        <div class="panel-body pad">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Account Type')}: </label>
                        <span>
                            {if $withdraw_account}
                                {if strtolower($withdraw_account.account_type) == 'phone'}{:__('Phone')}{/if}
                                {if strtolower($withdraw_account.account_type) == 'cpf'}CPF{/if}
                                {if strtolower($withdraw_account.account_type) == 'email'}{:__('Email')}{/if}
                                {if strtolower($withdraw_account.account_type) == 'cnpj'}CNPJ{/if}
                            {else}
                                {:__('Not Bound')}
                            {/if}
                        </span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Name')}: </label>
                        <span>{$withdraw_account.account_name|default=__('Not Bound')}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Account')}: </label>
                        <span>{$withdraw_account.account_number|default=__('Not Bound')}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>PIX: </label>
                        <span>{$withdraw_account.pix_number|default=__('Not Bound')}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default margin-bottom-none">
        <div class="panel-heading pad">{:__('Coin Information')}</div>
        <div class="panel-body pad">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Account Balance')}: </label>
                        <span>{$row.balance|htmlentities}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Reward Balance')}: </label>
                        <span>{$row.reward_balance|htmlentities}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Withdrawing Balance')}: </label>
                        <span>{$row.withdrawing_balance|default='0.00'}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Total Bet Amount')}: </label>
                        <span>{$row.total_bet|default='0.00'}</span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Total Deposit Amount')}: </label>
                        <span>{$row.total_deposit|htmlentities}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Total Withdraw Amount')}: </label>
                        <span>{$row.total_withdraw|htmlentities}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Total Win Amount')}: </label>
                        <span>{$row.total_win|default='0.00'}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>RTP: </label>
                        <span>{$row.rtp|default='0.00'}</span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Direct Referrals Count')}: </label>
                        <span>{$row.direct_referrals_count|default='0'}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Direct Deposit Referrals Count')}: </label>
                        <span>{$row.direct_deposit_referrals_count|default='0'}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Team Total Deposit')}: </label>
                        <span>{$row.team_total_deposit|default='0.00'}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-inline">
                        <label>{:__('Team Total Withdraw')}: </label>
                        <span>{$row.team_total_withdraw|default='0.00'}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default margin-bottom-none">
        <div class="panel-heading pad">{:__('Team Details')}</div>
        <div class="panel-body pad">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group form-inline">
                        <label>{:__('Team Deposit Withdraw Difference')}: </label>
                        <span>{$row.team_deposit_withdraw_diff|default='0.00'}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default margin-bottom-none">
        <div class="panel-heading pad">{:__('IP Information')}</div>
        <div class="panel-body pad">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group form-inline">
                        <label>{:__('Register IP')}: </label>
                        <span>{$row.register_ip|default="--"}</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-inline">
                        <label>{:__('Register Time')}: </label>
                        <span>{$row.created_at ? date('Y-m-d H:i:s', is_numeric($row.created_at) ? $row.created_at : strtotime($row.created_at)) : '--'}</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group form-inline">
                        <label>{:__('Last Login Time')}: </label>
                        <span>{$row.last_login_time ? date('Y-m-d H:i:s', is_numeric($row.last_login_time) ? $row.last_login_time : strtotime($row.last_login_time)) : '--'}</span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group form-inline">
                        <label>{:__('Last Login IP')}: </label>
                        <span>{$row.last_login_ip|default="--"}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 当标签页被激活时加载数据
function loadBasicTab() {
    console.log("基本信息标签页已加载");

    // 绑定上级玩家链接点击事件
    $('.btn-player-details').off('click').on('click', function() {
        var playerId = $(this).data('id');
        if (playerId) {
            // 使用Layer弹窗打开玩家详情页面
            var url = 'players/players/player_detail?ids=' + playerId;
            var title = '玩家详情 (ID: ' + playerId + ')';
            var area = ['95%', '95%']; // 弹窗大小

            // 打开弹窗
            if (typeof Fast !== 'undefined' && Fast.api && Fast.api.open) {
                Fast.api.open(url, title, {area: area});
            } else {
                // 如果Fast.api不可用，使用原生Layer
                if (typeof layer !== 'undefined') {
                    layer.open({
                        type: 2,
                        title: title,
                        area: area,
                        content: url,
                        maxmin: true
                    });
                } else {
                    // 如果Layer也不可用，则使用新窗口打开
                    window.open(url, '_blank');
                }
            }
        }
    });
}
</script>
