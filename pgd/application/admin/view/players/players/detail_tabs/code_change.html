<!-- 打码变化标签页 -->
<div class="panel-body">
    <div class="alert alert-warning">
        <h4><i class="fa fa-exclamation-triangle"></i> {:__('Function not implemented')}</h4>
        <p>{:__('Betting Code Change')} {:__('This feature is not yet implemented')}。{:__('The controller method returns mock data')} <code>get_code_change</code>。</p>
        <p>{:__('To implement this feature, please modify the controller method')} <code>Players.php</code> <code>get_code_change</code>。</p>
    </div>

    <form id="code-change-form" class="form-horizontal" role="form">
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-1">用户id:</label>
            <div class="col-xs-12 col-sm-2">
                <input type="text" class="form-control" id="user-id" value="{$player_id}" readonly>
            </div>
            <div class="col-xs-12 col-sm-2">
                <button type="button" id="search-code-change" class="btn btn-primary" disabled>搜索</button>
                <button type="reset" class="btn btn-default" disabled>重置</button>
            </div>
        </div>
    </form>
    <div class="table-responsive">
        <table id="code-change-table" class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th>用户id</th>
                    <th>需要打码金额</th>
                    <th>需要打码额</th>
                    <th>当前达到的额</th>
                    <th>差额</th>
                    <th>可提现金额</th>
                    <th>添加时间</th>
                    <th>状态</th>
                    <th>随时打码量</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="9" class="text-center text-muted">功能未实现，无法显示数据</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
// 当标签页被激活时加载数据
function loadCodeChangeTab() {
    console.log("打码变化功能未实现");

    // 显示未实现提示
    $('#code-change-table tbody').html('<tr><td colspan="9" class="text-center text-muted">功能未实现，无法显示数据</td></tr>');

    // 禁用搜索和重置按钮
    $('#search-code-change').prop('disabled', true);
    $('#code-change-form button[type="reset"]').prop('disabled', true);
}
</script>
