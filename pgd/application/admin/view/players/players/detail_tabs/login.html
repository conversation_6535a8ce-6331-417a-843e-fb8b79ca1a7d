<!-- 登录日志标签页 -->
<div class="panel-body">
    <form id="login-form" class="form-horizontal" role="form">
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-1">玩家id:</label>
            <div class="col-xs-12 col-sm-2">
                <input type="text" class="form-control" id="player-id-login" value="{$player_id}" readonly>
            </div>
            <div class="col-xs-12 col-sm-2">
                <button type="button" id="search-login" class="btn btn-primary">搜索</button>
                <button type="reset" class="btn btn-default">重置</button>
            </div>
        </div>
    </form>
    <div class="table-responsive">
        <table id="login-table" class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th>玩家id</th>
                    <th>登陆IP</th>
                    <th>注册时间</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{$player_id}</td>
                    <td>{$row.last_login_ip|default="--"}</td>
                    <td>{$row.created_at ? date('Y-m-d H:i:s', is_numeric($row.created_at) ? $row.created_at : strtotime($row.created_at)) : '--'}</td>
                </tr>
            </tbody>
        </table>
        <div class="text-center">显示第 1 到第 1 条记录，总共 1 条记录</div>
    </div>
</div>

<script>
// 当标签页被激活时加载数据
function loadLoginTab() {
    console.log("加载登录日志数据");
    
    // 绑定搜索按钮事件
    $('#search-login').off('click').on('click', function(e) {
        e.preventDefault();
        console.log("点击搜索按钮，重新加载登录日志数据");
        // TODO: 实现搜索功能
    });
}
</script>
