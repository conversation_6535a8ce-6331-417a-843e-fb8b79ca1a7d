<!-- 打码量标签页 -->
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title"><i class="fa fa-info-circle"></i> {:__('Required Betting Amount')} {:__('Information')}</h4>
        </div>
        <div class="panel-body">
            <div class="row">
                <div class="col-md-4">
                    <p><strong>{:__('Player ID')}:</strong> <span id="player-id-display">{$playerInfo.player_id|default='--'}</span></p>
                    <p><strong>{:__('Player Username')}:</strong> <span id="player-username-display">{$playerInfo.player_username|default='--'}</span></p>
                </div>
                <div class="col-md-4">
                    <p><strong>{:__('Balance')}:</strong> <span id="player-balance-display">{$playerInfo.player_balance|default='0.00'}</span></p>
                    <p><strong>{:__('Channel Name')}:</strong> <span id="player-channel-display">{$playerInfo.channel_name|default='--'}</span></p>
                </div>
                <div class="col-md-4">
                    <p><strong>{:__('Required Betting Amount')}:</strong> <span id="required-betting-display">{$playerInfo.required_betting_amount|default='0.00'}</span></p>
                    <p><strong>{:__('Completed Betting Amount')}:</strong> <span id="completed-betting-display">{$playerInfo.completed_betting_amount|default='0.00'}</span></p>
                    <p><strong>{:__('Completion Percentage')}:</strong> <span id="completion-percentage-display">{$playerInfo.completion_percentage|default='0.00%'}</span></p>
                </div>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table id="code-amount-table" class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th>{:__('ID')}</th>
                    <th>{:__('Player ID')}</th>
                    <th>{:__('Player Username')}</th>
                    <th>{:__('Required Betting Amount')}</th>
                    <th>{:__('Completed Betting Amount')}</th>
                    <th>{:__('Remaining Amount')}</th>
                    <th>{:__('Completion Percentage')}</th>
                    <th>{:__('Status')}</th>
                    <th>{:__('Channel Name')}</th>
                    <th>{:__('Created At')}</th>
                    <th>{:__('Updated At')}</th>
                </tr>
            </thead>
            <tbody>
                {notempty name="codeAmountData.rows"}
                {foreach name="codeAmountData.rows" item="row"}
                <tr>
                    <td>{$row.id|default=''}</td>
                    <td>{$row.player_id|default=''}</td>
                    <td>{$playerInfo.player_username|default='--'}</td>
                    <td>{$row.required_betting_amount|default='0.00'}</td>
                    <td>{$row.completed_betting_amount|default='0.00'}</td>
                    <td>{$row.remaining_amount|default='0.00'}</td>
                    <td>{$row.completion_percentage|default='0.00%'}</td>
                    <td>
                        {if condition="isset($row.status) && $row.status eq '已完成'"}
                        <span class="label label-success">{$row.status}</span>
                        {else/}
                        <span class="label label-warning">{$row.status|default='未完成'}</span>
                        {/if}
                    </td>
                    <td>{$playerInfo.channel_name|default='--'}</td>
                    <td>{$row.created_at|default=''}</td>
                    <td>{$row.updated_at|default=''}</td>
                </tr>
                {/foreach}
                {else/}
                <tr>
                    <td colspan="11" class="text-center">{:__('No matching records found')}</td>
                </tr>
                {/notempty}
            </tbody>
        </table>
    </div>
</div>

<!-- <script>
// 当标签页被激活时加载数据
function loadCodeAmountTab() {
    console.log("开始加载打码量数据...");

    // 等待jQuery加载完成后再初始化表格
    waitForJQuery(function() {
        console.log("jQuery已加载，初始化打码量表格");

        // 定义表格列
        var columns = [
            {field: 'id', title: 'ID'},
            {field: 'player_id', title: '玩家ID'},
            {field: 'player_username', title: '玩家账号'},
            {field: 'required_betting_amount', title: '打码任务量'},
            {field: 'completed_betting_amount', title: '已完成打码量'},
            {field: 'remaining_amount', title: '剩余打码量'},
            {field: 'completion_percentage', title: '完成比例'},
            {field: 'status', title: '状态', formatter: function(value) {
                if (value === '已完成') {
                    return '<span class="label label-success">' + value + '</span>';
                } else {
                    return '<span class="label label-warning">' + value + '</span>';
                }
            }},
            {field: 'channel_name', title: '渠道'},
            {field: 'created_at', title: '创建时间'},
            {field: 'updated_at', title: '更新时间'}
        ];

        try {
            // 初始化表格
            $('#code-amount-table').bootstrapTable({
                url: 'players/players/get_code_amount?player_id=' + '{$player_id}',
                columns: columns,
                pagination: true,
                search: false,
                showRefresh: true,
                showToggle: false,
                showColumns: true,
                pageSize: 10,
                pageList: [10, 25, 50, 100, 'All'],
                onLoadSuccess: function(data) {
                    console.log("加载打码量数据成功:", data);

                    // 更新打码量信息区域
                    if (data && data.rows && data.rows.length > 0) {
                        var firstRow = data.rows[0];
                        $('#player-username-display').text(firstRow.player_username || '--');
                        $('#player-balance-display').text(firstRow.player_balance || '0.00');
                        $('#player-channel-display').text(firstRow.channel_name || '--');
                        $('#required-betting-display').text(firstRow.required_betting_amount || '0.00');
                        $('#completed-betting-display').text(firstRow.completed_betting_amount || '0.00');
                        $('#completion-percentage-display').text(firstRow.completion_percentage || '0%');
                    }
                },
                onLoadError: function(status, res) {
                    console.error("加载打码量数据失败:", status, res);
                    $('#code-amount-table tbody').html('<tr><td colspan="11" class="text-center text-danger">加载数据失败，请稍后重试</td></tr>');
                }
            });

        } catch (error) {
            console.error("初始化打码量表格出错:", error);
            $('#code-amount-table tbody').html('<tr><td colspan="11" class="text-center text-danger">初始化表格失败: ' + error.message + '</td></tr>');
        }
    });
}
</script> -->
