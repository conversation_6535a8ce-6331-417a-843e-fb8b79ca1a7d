<!-- 金币日志标签页 -->
<div class="panel-body">
    <!-- 统计信息卡片 -->
    <div class="row" style="margin-bottom: 15px;">
        <div class="col-md-3">
            <div class="small-box bg-green">
                <div class="inner">
                    <h3>{$gmIncreaseTotal}</h3>
                    <p>{:__('GM Balance Increase')}</p>
                </div>
                <div class="icon">
                    <i class="fa fa-arrow-up"></i>
                </div>
                <div class="small-box-footer">
                    &nbsp;
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="small-box bg-red">
                <div class="inner">
                    <h3>{$gmDecreaseTotal}</h3>
                    <p>{:__('GM Balance Decrease')}</p>
                </div>
                <div class="icon">
                    <i class="fa fa-arrow-down"></i>
                </div>
                <div class="small-box-footer">
                    &nbsp;
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="small-box bg-aqua">
                <div class="inner">
                    <h3>{$totalDeposit}</h3>
                    <p>{:__('Total Deposit')}</p>
                </div>
                <div class="icon">
                    <i class="fa fa-money"></i>
                </div>
                <div class="small-box-footer">
                    &nbsp;
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="small-box bg-yellow">
                <div class="inner">
                    <h3>{$totalWithdraw}</h3>
                    <p>{:__('Total Withdraw')}</p>
                </div>
                <div class="icon">
                    <i class="fa fa-credit-card"></i>
                </div>
                <div class="small-box-footer">
                    &nbsp;
                </div>
            </div>
        </div>
    </div>

    <form id="coin-log-form" class="form-horizontal" role="form" method="get" action="detail_tabs/coin_log?player_id={$player_id}">
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-1">{:__('Player ID')}:</label>
            <div class="col-xs-12 col-sm-2">
                <input type="text" class="form-control" name="player_id" value="{$player_id}" readonly>
            </div>
            <label class="control-label col-xs-12 col-sm-1">{:__('Operation Type')}:</label>
            <div class="col-xs-12 col-sm-2">
                <select class="form-control" name="transaction_type">
                    <option value="">{:__('All')}</option>
                    {foreach name="transactionTypeList" item="name" key="id"}
                    <option value="{$id}" {if condition="$selectedTransactionType eq $id"}selected{/if}>{:__($name)}</option>
                    {/foreach}
                </select>
            </div>
            <div class="col-xs-12 col-sm-2">
                <button type="submit" class="btn btn-default">{:__('Refresh')}</button>
            </div>
            <input type="hidden" name="name" value="coin_log">
        </div>
    </form>

    <div class="table-responsive">
        <table id="coin-log-table" class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th>{:__('ID')}</th>
                    <th>{:__('Player ID')}</th>
                    <th>{:__('Operation Type')}</th>
                    <th>{:__('Amount Before Change')}</th>
                    <th>{:__('Change Amount')}</th>
                    <th>{:__('Amount After Change')}</th>
                    <th>{:__('Remark')}</th>
                    <th>{:__('Operation Time')}</th>
                </tr>
            </thead>
            <tbody>
                {notempty name="coinLogData"}
                    {foreach name="coinLogData" item="item"}
                    <tr>
                        <td>{$item.id}</td>
                        <td>{$item.player_id}</td>
                        <td>{$item.transaction_type_text}</td>
                        <td>{$item.balance_before}</td>
                        <td><span style="color: {if condition="$item.amount egt 0"}green{else/}red{/if}">{$item.amount}</span></td>
                        <td>{$item.balance_after}</td>
                        <td>{$item.remark}</td>
                        <td>{$item.created_at}</td>
                    </tr>
                    {/foreach}
                {else/}
                    <tr>
                        <td colspan="8" class="text-center">{:__('No matching records found')}</td>
                    </tr>
                {/notempty}
            </tbody>
        </table>
    </div>
</div>
