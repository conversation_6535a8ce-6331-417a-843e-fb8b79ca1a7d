<!-- 充值记录标签页 -->
<div class="panel-body">
    <div class="table-responsive">
        <table id="deposit-table" class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th>订单号</th>
                    <th>金额</th>
                    <th>状态</th>
                    <th>支付时间</th>
                    <th>支付渠道</th>
                    <th>税收</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="6" class="text-center">正在加载数据...</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
// 当标签页被激活时加载数据
function loadDepositTab() {
    console.log("开始加载充值记录数据...");

    // 等待jQuery加载完成后再加载数据
    waitForJQuery(function() {
        console.log("jQuery已加载，加载充值记录数据");

        try {
            // 显示加载中提示
            $('#deposit-table tbody').html('<tr><td colspan="6" class="text-center"><i class="fa fa-spinner fa-spin"></i> 正在加载数据...</td></tr>');

            // 加载数据
            $.ajax({
                url: 'players/players/get_deposit?player_id=' + '{$player_id}',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (!data || !data.rows || !data.rows.length) {
                        $('#deposit-table tbody').html('<tr><td colspan="6" class="text-center">没有找到匹配的记录</td></tr>');
                        return;
                    }

                    var html = '';
                    $.each(data.rows, function(i, item) {
                        html += '<tr>';
                        html += '<td>' + (item.third_order_no || '') + '</td>';
                        html += '<td>' + (item.amount || '0.00') + '</td>';
                        html += '<td>' + (item.status_text || '') + '</td>';
                        html += '<td>' + (item.paid_at || '') + '</td>';
                        html += '<td>' + (item.channel_name || '') + '</td>';
                        html += '<td>' + (item.tax || '0.00') + '</td>';
                        html += '</tr>';
                    });

                    $('#deposit-table tbody').html(html);
                },
                error: function(xhr, status, error) {
                    console.error("加载充值记录数据失败:", error);
                    $('#deposit-table tbody').html('<tr><td colspan="6" class="text-center text-danger">加载数据失败，请稍后重试</td></tr>');
                }
            });
        } catch (error) {
            console.error("加载充值记录数据出错:", error);
            $('#deposit-table tbody').html('<tr><td colspan="6" class="text-center text-danger">加载数据失败: ' + error.message + '</td></tr>');
        }
    });
}
</script>
