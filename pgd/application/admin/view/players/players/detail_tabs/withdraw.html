<!-- 提现记录标签页 -->
<div class="panel-body">
    <div class="table-responsive">
        <table id="withdraw-table" class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th>订单号</th>
                    <th>第三方订单号</th>
                    <th>金额</th>
                    <th>状态</th>
                    <th>申请时间</th>
                    <th>开户人</th>
                    <th>提现方式</th>
                    <th>提现账户</th>
                    <th>PIX 号</th> <!-- 添加 PIX 号表头 -->
                    <!-- <th>开户行</th> --> <!-- 移除开户行 -->
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="9" class="text-center">正在加载数据...</td> <!-- colspan 调整为 9 -->
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
// 当标签页被激活时加载数据
function loadWithdrawTab() {
    console.log("开始加载提现记录数据...");

    // 等待jQuery加载完成后再加载数据
    waitForJQuery(function() {
        console.log("jQuery已加载，加载提现记录数据");

        try {
            // 显示加载中提示
            $('#withdraw-table tbody').html('<tr><td colspan="9" class="text-center"><i class="fa fa-spinner fa-spin"></i> 正在加载数据...</td></tr>'); // colspan 调整为 9

            // 加载数据
            $.ajax({
                url: 'players/players/get_withdraw',
                type: 'GET',
                data: { player_id: '{$player_id}' },
                dataType: 'json',
                success: function(data) {
                    if (!data || !data.rows || !data.rows.length) {
                        $('#withdraw-table tbody').html('<tr><td colspan="9" class="text-center">没有找到匹配的记录</td></tr>'); // colspan 调整为 9
                        return;
                    }

                    var html = '';
                    $.each(data.rows, function(i, item) {
                        html += '<tr>';
                        html += '<td>' + (item.id || '') + '</td>';
                        html += '<td>' + (item.third_order_no || '') + '</td>';
                        html += '<td>' + (item.amount || '0.00') + '</td>';
                        html += '<td>' + (item.status || '') + '</td>';
                        html += '<td>' + (item.apply_time || '') + '</td>';
                        html += '<td>' + (item.account_holder_name || 'N/A') + '</td>'; // 假设后端 pwa.account_name AS account_holder_name
                        html += '<td>' + (item.account_type || 'N/A') + '</td>';
                        html += '<td>' + (item.account_number || 'N/A') + '</td>';
                        html += '<td>' + (item.pix_number || 'N/A') + '</td>'; // 显示 PIX 号
                        // html += '<td>' + (item.bank_name || 'N/A') + '</td>'; // 移除 bank_name
                        html += '</tr>';
                    });

                    $('#withdraw-table tbody').html(html);
                },
                error: function(xhr, status, error) {
                    console.error("加载提现记录数据失败:", error);
                    $('#withdraw-table tbody').html('<tr><td colspan="9" class="text-center text-danger">加载数据失败，请稍后重试</td></tr>'); // colspan 调整为 9
                }
            });
        } catch (error) {
            console.error("加载提现记录数据出错:", error);
            $('#withdraw-table tbody').html('<tr><td colspan="9" class="text-center text-danger">加载数据失败: ' + error.message + '</td></tr>'); // colspan 调整为 9
        }
    });
}
</script>
