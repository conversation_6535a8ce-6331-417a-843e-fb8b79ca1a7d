<!-- 掉绑设置标签页 -->
<div class="panel-body">

    <form id="player-unbind-settings-form" class="form-horizontal" role="form">
        <input type="hidden" name="player_id" value="{$player_id}">
        <input type="hidden" name="unbind_mode" value="2">

        <div class="panel panel-default">
            <div class="panel-heading">{:__('Register Unbind Settings')}</div>
            <div class="panel-body">
                <div class="form-group">
                    <label class="control-label col-sm-2">{:__('Unbind Type')}:</label>
                    <div class="col-sm-4">
                        <select class="form-control" name="reg_unbind_type">
                            <option value="0">{:__('Off')}</option>
                            <option value="1">{:__('Interval Unbind')}</option>
                            <option value="2">{:__('Probability Unbind')}</option>
                        </select>
                    </div>
                </div>

                <div class="form-group reg-threshold-group" style="display:none;">
                    <label class="control-label col-sm-2">{:__('Register Count Threshold')}:</label>
                    <div class="col-sm-4">
                        <input type="number" class="form-control" name="reg_unbind_threshold" placeholder="{:__('Enter register count threshold')}">
                        <span class="help-block">{:__('Set the register count threshold to trigger unbinding')}</span>
                    </div>
                </div>

                <div class="form-group reg-interval-group" style="display:none;">
                    <label class="control-label col-sm-2">{:__('Unbind Interval')}:</label>
                    <div class="col-sm-4">
                        <input type="number" class="form-control" name="reg_unbind_interval" placeholder="{:__('Enter unbind interval')}">
                        <span class="help-block">{:__('Interval value in interval unbind mode')}</span>
                    </div>
                </div>

                <div class="form-group reg-ratio-group" style="display:none;">
                    <label class="control-label col-sm-2">{:__('Unbind Probability (%)')}:</label>
                    <div class="col-sm-4">
                        <input type="number" class="form-control" name="reg_unbind_prob" placeholder="{:__('Enter unbind probability (0-100)')}">
                        <span class="help-block">{:__('Probability value in probability unbind mode (0-100)')}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">{:__('Deposit Unbind Settings')}</div>
            <div class="panel-body">
                <div class="form-group">
                    <label class="control-label col-sm-2">{:__('Unbind Type')}:</label>
                    <div class="col-sm-4">
                        <select class="form-control" name="deposit_unbind_type">
                            <option value="0">{:__('Off')}</option>
                            <option value="1">{:__('Interval Unbind')}</option>
                            <option value="2">{:__('Probability Unbind')}</option>
                        </select>
                    </div>
                </div>

                <div class="form-group deposit-threshold-group" style="display:none;">
                    <label class="control-label col-sm-2">{:__('Deposit Amount Threshold')}:</label>
                    <div class="col-sm-4">
                        <input type="number" class="form-control" name="deposit_unbind_threshold" placeholder="{:__('Enter deposit amount threshold')}">
                        <span class="help-block">{:__('Set the deposit amount threshold to trigger unbinding')}</span>
                    </div>
                </div>

                <div class="form-group deposit-interval-group" style="display:none;">
                    <label class="control-label col-sm-2">{:__('Unbind Interval')}:</label>
                    <div class="col-sm-4">
                        <input type="number" class="form-control" name="deposit_unbind_interval" placeholder="{:__('Enter unbind interval')}">
                        <span class="help-block">{:__('Interval value in interval unbind mode')}</span>
                    </div>
                </div>

                <div class="form-group deposit-ratio-group" style="display:none;">
                    <label class="control-label col-sm-2">{:__('Unbind Probability (%)')}:</label>
                    <div class="col-sm-4">
                        <input type="number" class="form-control" name="deposit_unbind_prob" placeholder="{:__('Enter unbind probability (0-100)')}">
                        <span class="help-block">{:__('Probability value in probability unbind mode (0-100)')}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="control-label col-sm-2">{:__('Sync Settings')}:</label>
            <div class="col-sm-4">
                <div class="checkbox">
                    <label>
                        <input type="checkbox" name="sync_next" value="1">
                        {:__('Sync to Next')}
                    </label>
                </div>
            </div>
        </div>

        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
                <button type="submit" class="btn btn-primary">{:__('Save Settings')}</button>
            </div>
        </div>
    </form>
</div>

<script>
// 当标签页被激活时加载数据
// 使用全局函数，确保在父页面中可以访问
window.loadUnbindSettingsTab = function() {
    console.log('===== loadUnbindSettingsTab 函数被调用 =====');
    console.log('初始化玩家掉绑设置，玩家ID:', '{$player_id}');

    // 检查 jQuery 是否可用
    if (typeof $ === 'undefined') {
        console.error('jQuery 未加载，无法初始化掉绑设置');
        return;
    }

    // 检查 buildApiUrl 函数是否可用
    if (typeof buildApiUrl !== 'function') {
        console.error('buildApiUrl 函数未定义，无法构建 API URL');
        // 尝试使用相对路径
        var getSettingsUrl = 'players/players/get_player_unbind_settings';
        console.log('使用相对路径:', getSettingsUrl);
    } else {
        var getSettingsUrl = buildApiUrl('get_player_unbind_settings');
        console.log('使用 buildApiUrl 构建的 URL:', getSettingsUrl);
    }

    // 处理掉绑类型变化
    function handleUnbindTypeChange(value, type) {
        var thresholdSelector = '.' + type + '-threshold-group';
        var intervalSelector = '.' + type + '-interval-group';
        var ratioSelector = '.' + type + '-ratio-group';

        if (value === '0') {
            // 关闭时隐藏所有输入框
            $(thresholdSelector).hide();
            $(intervalSelector).hide();
            $(ratioSelector).hide();
        } else if (value === '1') {
            // 间隔掉绑时，显示阈值和间隔框，隐藏概率框
            $(thresholdSelector).show();
            $(intervalSelector).show();
            $(ratioSelector).hide();
        } else if (value === '2') {
            // 概率掉绑时，显示阈值和概率框，隐藏间隔框
            $(thresholdSelector).show();
            $(ratioSelector).show();
            $(intervalSelector).hide();
        }
    }

    // 绑定掉绑类型变化事件
    $('select[name="reg_unbind_type"]').on('change', function() {
        handleUnbindTypeChange($(this).val(), 'reg');
    });

    $('select[name="deposit_unbind_type"]').on('change', function() {
        handleUnbindTypeChange($(this).val(), 'deposit');
    });

    // 验证概率输入
    $('input[name="reg_unbind_prob"], input[name="deposit_unbind_prob"]').on('change', function() {
        var prob = parseInt($(this).val()) || 0;
        if (prob < 0 || prob > 100) {
            alert('概率必须在0-100之间');
            $(this).val('');
            return false;
        }
    });

    // 绑定表单提交事件（先解绑再绑定，避免重复绑定）
    $('#player-unbind-settings-form').off('submit').on('submit', function(e) {
        e.preventDefault();

        // 表单验证
        var isValid = true;
        var regUnbindType = $('select[name="reg_unbind_type"]').val();

        // 根据注册掉绑类型验证相应的输入框
        if (regUnbindType !== '0') {
            // 验证阈值输入框
            if (!$('input[name="reg_unbind_threshold"]').val()) {
                alert('请填写注册人数阈值');
                isValid = false;
            }

            // 根据类型验证间隔或概率输入框
            if (regUnbindType === '1' && !$('input[name="reg_unbind_interval"]').val()) {
                alert('请填写注册掉绑间隔');
                isValid = false;
            } else if (regUnbindType === '2' && !$('input[name="reg_unbind_prob"]').val()) {
                alert('请填写注册掉绑概率');
                isValid = false;
            }
        }

        // 对充值掉绑类型也进行同样的验证
        if (isValid) {
            var depositUnbindType = $('select[name="deposit_unbind_type"]').val();
            if (depositUnbindType !== '0') {
                // 验证阈值输入框
                if (!$('input[name="deposit_unbind_threshold"]').val()) {
                    alert('请填写充值金额阈值');
                    isValid = false;
                }

                // 根据类型验证间隔或概率输入框
                if (depositUnbindType === '1' && !$('input[name="deposit_unbind_interval"]').val()) {
                    alert('请填写充值掉绑间隔');
                    isValid = false;
                } else if (depositUnbindType === '2' && !$('input[name="deposit_unbind_prob"]').val()) {
                    alert('请填写充值掉绑概率');
                    isValid = false;
                }
            }
        }

        if (!isValid) {
            return false;
        }

        // 收集表单数据
        var formData = $(this).serialize();

        // 提交表单数据
        var saveSettingsUrl = typeof buildApiUrl === 'function' ?
            buildApiUrl('save_player_unbind_setting') :
            'players/players/save_player_unbind_setting';
        console.log('保存设置 URL:', saveSettingsUrl);

        $.ajax({
            url: saveSettingsUrl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.code === 1) {
                    alert('保存成功');
                } else {
                    alert('保存失败: ' + (response.msg || '未知错误'));
                }
            },
            error: function(xhr, status, error) {
                console.error('保存掉绑设置失败:', error);
                alert('保存失败: ' + error);
            }
        });
    });

    // 获取玩家掉绑设置
    $.ajax({
        url: getSettingsUrl,
        type: 'GET',
        data: { player_id: '{$player_id}' },
        dataType: 'json',
        success: function(response) {
            console.log('获取到的掉绑设置:', response);

            if (response && response.settings) {
                var settings = response.settings;

                // 更新注册掉绑类型
                $('select[name="reg_unbind_type"]').val(settings.reg_unbind_type);
                handleUnbindTypeChange(settings.reg_unbind_type.toString(), 'reg');

                // 更新充值掉绑类型
                $('select[name="deposit_unbind_type"]').val(settings.deposit_unbind_type);
                handleUnbindTypeChange(settings.deposit_unbind_type.toString(), 'deposit');

                // 更新其他字段
                $('input[name="reg_unbind_threshold"]').val(settings.reg_unbind_threshold);
                $('input[name="reg_unbind_interval"]').val(settings.reg_unbind_interval);
                $('input[name="reg_unbind_prob"]').val(settings.reg_unbind_prob);
                $('input[name="deposit_unbind_threshold"]').val(settings.deposit_unbind_threshold);
                $('input[name="deposit_unbind_interval"]').val(settings.deposit_unbind_interval);
                $('input[name="deposit_unbind_prob"]').val(settings.deposit_unbind_prob);

                // 设置同步到下级复选框
                if (settings.sync_next == 1) {
                    $('input[name="sync_next"]').prop('checked', true);
                } else {
                    $('input[name="sync_next"]').prop('checked', false);
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('获取掉绑设置失败:', error);
        }
    });
};

// 页面加载完成后自动初始化（只执行一次）
$(document).ready(function() {
    console.log('掉绑设置页面加载完成，尝试自动初始化');

    // 初始化下拉菜单
    $('select[name="reg_unbind_type"]').val('0');
    $('select[name="deposit_unbind_type"]').val('0');

    // 使用一个标志变量确保只初始化一次
    if (!window.unbindSettingsInitialized) {
        window.unbindSettingsInitialized = true;

        setTimeout(function() {
            if (typeof window.loadUnbindSettingsTab === 'function') {
                console.log('自动调用 loadUnbindSettingsTab 函数');
                window.loadUnbindSettingsTab();
            } else {
                console.error('loadUnbindSettingsTab 函数未定义，无法自动初始化');
                window.unbindSettingsInitialized = false; // 重置标志，允许下次尝试
            }
        }, 500);
    } else {
        console.log('掉绑设置已经初始化过，跳过重复初始化');
    }
});
</script>
