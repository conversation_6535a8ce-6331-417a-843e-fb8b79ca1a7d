<!-- 代理佣金标签页 -->
<div class="panel-body">
    <div class="alert alert-warning">
        <h4><i class="fa fa-exclamation-triangle"></i> {:__('Function not implemented')}</h4>
        <p>{:__('Agent Commission')} {:__('This feature is not yet implemented')}。{:__('The controller method returns mock data')} <code>get_commission</code>。</p>
        <p>{:__('To implement this feature, please modify the controller method')} <code>Players.php</code> <code>get_commission</code>。</p>
    </div>

    <form id="commission-filter-form" class="form-horizontal" role="form">
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-1">玩家ID:</label>
            <div class="col-xs-12 col-sm-2">
                <input type="text" class="form-control" id="player-id-commission" value="{$player_id}" readonly>
            </div>
            <label class="control-label col-xs-12 col-sm-1">开始时间:</label>
            <div class="col-xs-12 col-sm-2">
                <input type="text" class="form-control datetimepicker" id="start-time" placeholder="开始时间" disabled>
            </div>
            <label class="control-label col-xs-12 col-sm-1">结束时间:</label>
            <div class="col-xs-12 col-sm-2">
                <input type="text" class="form-control datetimepicker" id="end-time" placeholder="结束时间" disabled>
            </div>
            <div class="col-xs-12 col-sm-2">
                <button type="submit" class="btn btn-primary" disabled>搜索</button>
                <button type="reset" class="btn btn-default" disabled>重置</button>
            </div>
        </div>
    </form>

    <div class="table-responsive">
        <table id="commission-table" class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>玩家ID</th>
                    <th>佣金类型</th>
                    <th>佣金金额</th>
                    <th>计算时间</th>
                    <th>发放时间</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="7" class="text-center text-muted">功能未实现，显示的是模拟数据</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
// 当标签页被激活时加载数据
function loadCommissionTab() {
    console.log("代理佣金功能未实现");

    // 显示未实现提示
    $('#commission-table tbody').html('<tr><td colspan="7" class="text-center text-muted">功能未实现，显示的是模拟数据</td></tr>');

    // 禁用表单元素
    $('#commission-filter-form input').prop('disabled', true);
    $('#commission-filter-form button').prop('disabled', true);

    // 显示模拟数据
    setTimeout(function() {
        var mockData = [
            {
                id: 1,
                player_id: '{$player_id}',
                commission_type: '直属佣金',
                amount: '100.00',
                calculate_time: '2023-01-01 00:00:00',
                issue_time: '2023-01-02 00:00:00',
                status: '已发放 (模拟数据)'
            }
        ];

        var html = '';
        $.each(mockData, function(i, item) {
            html += '<tr class="text-muted">';
            html += '<td>' + (item.id || '') + '</td>';
            html += '<td>' + (item.player_id || '') + '</td>';
            html += '<td>' + (item.commission_type || '') + '</td>';
            html += '<td>' + (item.amount || '0.00') + '</td>';
            html += '<td>' + (item.calculate_time || '') + '</td>';
            html += '<td>' + (item.issue_time || '') + '</td>';
            html += '<td>' + (item.status || '') + '</td>';
            html += '</tr>';
        });
        html += '<tr><td colspan="7" class="text-center text-danger">以上为模拟数据，非真实数据</td></tr>';
        $('#commission-table tbody').html(html);
    }, 500);
}
</script>
