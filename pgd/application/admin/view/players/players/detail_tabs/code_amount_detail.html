<!-- 打码量操作日志标签页 -->
<div class="panel-body">
    <form id="code-amount-detail-form" class="form-horizontal" role="form">
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-1">{:__('Player ID')}:</label>
            <div class="col-xs-12 col-sm-2">
                <input type="text" class="form-control" id="player-id-amount-detail" value="{$player_id}" readonly>
            </div>
            <label class="control-label col-xs-12 col-sm-1">{:__('Operator Type')}:</label>
            <div class="col-xs-12 col-sm-2">
                <select class="form-control" id="operator-type">
                    <option value="">{:__('Select')}</option>
                </select>
            </div>
            <div class="col-xs-12 col-sm-2">
                <button type="button" id="search-code-amount-detail" class="btn btn-primary">{:__('Search')}</button>
                <button type="reset" class="btn btn-default">{:__('Reset')}</button>
            </div>
        </div>
    </form>
    <div class="table-responsive">
        <table id="code-amount-detail-table" class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th>{:__('Player ID')}</th>
                    <th>{:__('Operator')}</th>
                    <th>{:__('Operator Type')}</th>
                    <th>{:__('Operation Time')}</th>
                    <th>{:__('Remark')}</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="5" class="text-center">{:__('No matching records found')}</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
// 当标签页被激活时加载数据
function loadCodeAmountDetailTab() {
    console.log("开始加载打码量操作日志数据...");
    
    // 等待jQuery加载完成后再初始化表格
    waitForJQuery(function() {
        console.log("jQuery已加载，初始化打码量操作日志表格");
        
        // 定义表格列
        var columns = [
            {field: 'id', title: '玩家id'},
            {field: 'operator', title: '操作人'},
            {field: 'operator_type', title: '操作人类型'},
            {field: 'add_time', title: '操作时间'},
            {field: 'remark', title: '备注'}
        ];
        
        try {
            // 初始化表格
            $('#code-amount-detail-table').bootstrapTable({
                url: 'players/players/get_code_amount_detail?player_id=' + '{$player_id}',
                columns: columns,
                pagination: true,
                search: true,
                showRefresh: true,
                showToggle: true,
                showColumns: true,
                pageSize: 10,
                pageList: [10, 25, 50, 100, 'All'],
                onLoadError: function(status, res) {
                    console.error("加载打码量操作日志数据失败:", status, res);
                    $('#code-amount-detail-table tbody').html('<tr><td colspan="5" class="text-center text-danger">加载数据失败，请稍后重试</td></tr>');
                }
            });
            
            // 绑定搜索按钮事件
            $('#search-code-amount-detail').off('click').on('click', function(e) {
                e.preventDefault();
                console.log("点击搜索按钮，重新加载打码量操作日志数据");
                $('#code-amount-detail-table').bootstrapTable('refresh');
            });
        } catch (error) {
            console.error("初始化打码量操作日志表格出错:", error);
            $('#code-amount-detail-table tbody').html('<tr><td colspan="5" class="text-center text-danger">初始化表格失败: ' + error.message + '</td></tr>');
        }
    });
}
</script>
