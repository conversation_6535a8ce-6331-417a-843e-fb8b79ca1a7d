<!-- 下级数据标签页 -->
<div class="panel-body">
    <!-- 开关控件 -->
    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label class="control-label">
                    <input type="checkbox" id="show-real-data" checked> {:__('Show Real Data')}
                </label>
                <span class="help-block">{:__('Real data is shown in black, virtual data is shown in blue')}</span>
            </div>
        </div>
    </div>

    <!-- 合并的下级数据表格 -->
    <div class="row">
        <div class="col-md-12">
            <div class="table-responsive">
                <table class="table table-striped table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>{:__('Hierarchy')}</th>
                            <th>{:__('Total People')}</th>
                            <th>{:__('Treasure Box Reward People')}</th>
                            <th>{:__('Total Deposit People')}</th>
                            <th>{:__('Total Deposit Amount')}</th>
                            <th>{:__('Average Deposit')}</th>
                            <th>{:__('Total Withdrawal People')}</th>
                            <th>{:__('Total Withdrawal Amount')}</th>
                            <th>{:__('Total Profit/Loss')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{:__('Hierarchy 1')}</td>
                            <td>
                                <span class="normal-data">{$childrenData.level1.total_people|default=0}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level1.total_people|default=0}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level1.treasure_box_people|default=0}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level1.treasure_box_people|default=0}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level1.deposit_people|default=0}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level1.deposit_people|default=0}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level1.deposit_amount|default=0|number_format=2}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level1.deposit_amount|default=0|number_format=2}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level1.avg_deposit|default=0|number_format=2}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level1.avg_deposit|default=0|number_format=2}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level1.withdraw_people|default=0}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level1.withdraw_people|default=0}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level1.withdraw_amount|default=0|number_format=2}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level1.withdraw_amount|default=0|number_format=2}</span>
                            </td>
                            <td>
                                <span class="normal-data {$childrenData.level1.profit >= 0 ? 'text-success' : 'text-danger'}">{$childrenData.level1.profit|default=0|number_format=2}</span>
                                <span class="data-separator">/</span> <span class="unbind-data {$fakedChildrenData.level1.profit >= 0 ? 'text-success' : 'text-danger'}">{$fakedChildrenData.level1.profit|default=0|number_format=2}</span>
                            </td>
                        </tr>
                        <tr>
                            <td>{:__('Hierarchy 2')}</td>
                            <td>
                                <span class="normal-data">{$childrenData.level2.total_people|default=0}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level2.total_people|default=0}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level2.treasure_box_people|default=0}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level2.treasure_box_people|default=0}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level2.deposit_people|default=0}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level2.deposit_people|default=0}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level2.deposit_amount|default=0|number_format=2}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level2.deposit_amount|default=0|number_format=2}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level2.avg_deposit|default=0|number_format=2}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level2.avg_deposit|default=0|number_format=2}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level2.withdraw_people|default=0}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level2.withdraw_people|default=0}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level2.withdraw_amount|default=0|number_format=2}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level2.withdraw_amount|default=0|number_format=2}</span>
                            </td>
                            <td>
                                <span class="normal-data {$childrenData.level2.profit >= 0 ? 'text-success' : 'text-danger'}">{$childrenData.level2.profit|default=0|number_format=2}</span>
                                <span class="data-separator">/</span> <span class="unbind-data {$fakedChildrenData.level2.profit >= 0 ? 'text-success' : 'text-danger'}">{$fakedChildrenData.level2.profit|default=0|number_format=2}</span>
                            </td>
                        </tr>
                        <tr>
                            <td>{:__('Hierarchy 3')}</td>
                            <td>
                                <span class="normal-data">{$childrenData.level3.total_people|default=0}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level3.total_people|default=0}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level3.treasure_box_people|default=0}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level3.treasure_box_people|default=0}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level3.deposit_people|default=0}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level3.deposit_people|default=0}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level3.deposit_amount|default=0|number_format=2}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level3.deposit_amount|default=0|number_format=2}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level3.avg_deposit|default=0|number_format=2}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level3.avg_deposit|default=0|number_format=2}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level3.withdraw_people|default=0}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level3.withdraw_people|default=0}</span>
                            </td>
                            <td>
                                <span class="normal-data">{$childrenData.level3.withdraw_amount|default=0|number_format=2}</span>
                                <span class="data-separator">/</span> <span class="unbind-data text-info">{$fakedChildrenData.level3.withdraw_amount|default=0|number_format=2}</span>
                            </td>
                            <td>
                                <span class="normal-data {$childrenData.level3.profit >= 0 ? 'text-success' : 'text-danger'}">{$childrenData.level3.profit|default=0|number_format=2}</span>
                                <span class="data-separator">/</span> <span class="unbind-data {$fakedChildrenData.level3.profit >= 0 ? 'text-success' : 'text-danger'}">{$fakedChildrenData.level3.profit|default=0|number_format=2}</span>
                            </td>
                        </tr>
                        <tr class="info">
                            <td><strong>{:__('Total')}</strong></td>
                            <td>
                                <strong class="normal-data">{$childrenData.total.total_people|default=0}</strong>
                                <span class="data-separator">/</span> <strong class="unbind-data text-info">{$fakedChildrenData.total.total_people|default=0}</strong>
                            </td>
                            <td>
                                <strong class="normal-data">{$childrenData.total.treasure_box_people|default=0}</strong>
                                <span class="data-separator">/</span> <strong class="unbind-data text-info">{$fakedChildrenData.total.treasure_box_people|default=0}</strong>
                            </td>
                            <td>
                                <strong class="normal-data">{$childrenData.total.deposit_people|default=0}</strong>
                                <span class="data-separator">/</span> <strong class="unbind-data text-info">{$fakedChildrenData.total.deposit_people|default=0}</strong>
                            </td>
                            <td>
                                <strong class="normal-data">{$childrenData.total.deposit_amount|default=0|number_format=2}</strong>
                                <span class="data-separator">/</span> <strong class="unbind-data text-info">{$fakedChildrenData.total.deposit_amount|default=0|number_format=2}</strong>
                            </td>
                            <td>
                                <strong class="normal-data">{$childrenData.total.avg_deposit|default=0|number_format=2}</strong>
                                <span class="data-separator">/</span> <strong class="unbind-data text-info">{$fakedChildrenData.total.avg_deposit|default=0|number_format=2}</strong>
                            </td>
                            <td>
                                <strong class="normal-data">{$childrenData.total.withdraw_people|default=0}</strong>
                                <span class="data-separator">/</span> <strong class="unbind-data text-info">{$fakedChildrenData.total.withdraw_people|default=0}</strong>
                            </td>
                            <td>
                                <strong class="normal-data">{$childrenData.total.withdraw_amount|default=0|number_format=2}</strong>
                                <span class="data-separator">/</span> <strong class="unbind-data text-info">{$fakedChildrenData.total.withdraw_amount|default=0|number_format=2}</strong>
                            </td>
                            <td>
                                <strong class="normal-data {$childrenData.total.profit >= 0 ? 'text-success' : 'text-danger'}">{$childrenData.total.profit|default=0|number_format=2}</strong>
                                <span class="data-separator">/</span> <strong class="unbind-data {$fakedChildrenData.total.profit >= 0 ? 'text-success' : 'text-danger'}">{$fakedChildrenData.total.profit|default=0|number_format=2}</strong>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 所有下级玩家详细信息 -->
    <div class="row" style="margin-top: 20px;">
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-6">
                    <h4>{:__('All Subordinate Players')}</h4>
                </div>
                <div class="col-md-6 text-right">
                    <div class="form-group" style="margin-bottom: 10px;">
                        <label class="control-label" style="display: inline-block; margin-right: 10px;">{:__('Hierarchy Filter')}:</label>
                        <select id="level-filter" class="form-control" style="display: inline-block; width: auto;">
                            <option value="0">{:__('All Hierarchies')}</option>
                            <option value="1">{:__('Hierarchy')} 1</option>
                            <option value="2">{:__('Hierarchy')} 2</option>
                            <option value="3">{:__('Hierarchy')} 3</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-striped table-bordered table-hover" id="subordinate-players-table">
                    <thead>
                        <tr>
                            <th>{:__('Player ID')}</th>
                            <th>{:__('Has Treasure Box')}</th>
                            <th>{:__('Phone Number')}</th>
                            <th>{:__('Hierarchy Level')}</th>
                            <th>{:__('VIP Level')}</th>
                            <th>{:__('First Deposit Amount')}</th>
                            <th>{:__('Total Deposit')}</th>
                            <th>{:__('Total Withdraw')}</th>
                            <th>{:__('Balance')}</th>
                            <th>{:__('Total Bet')}</th>
                            <th>{:__('Direct Referrals Count')}</th>
                            <th>{:__('Register Time')}</th>
                            <th>{:__('Last Login Time')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {volist name="subordinatePlayers" id="player"}
                        <tr class="level-row player-row" data-level="{$player.relation_level}">
                            <td><a href="{:url('players/players/player_detail', ['ids' => $player.id])}" target="_blank">{$player.id}</a></td>
                            <td>{$player.has_treasure_box ? __('Yes') : __('No')}</td>
                            <td>{$player.phone_number}</td>
                            <td>{:__('Hierarchy Level')} {$player.relation_level}</td>
                            <td>{$player.vip_level}</td>
                            <td>{$player.first_deposit|number_format=2}</td>
                            <td>{$player.total_deposit|number_format=2}</td>
                            <td>{$player.total_withdraw|number_format=2}</td>
                            <td>{$player.balance|number_format=2}</td>
                            <td>{$player.total_bet|number_format=2}</td>
                            <td>{$player.direct_referrals_count}</td>
                            <td>{$player.created_at}</td>
                            <td>{$player.last_login_time}</td>
                        </tr>
                        {/volist}
                    </tbody>
                </table>
                <!-- 分页控件 -->
                <div class="pagination-container" style="text-align: center; margin-top: 15px;">
                    <ul class="pagination" id="player-pagination">
                        <li><a href="javascript:void(0);" id="prev-page">&laquo;</a></li>
                        <!-- 页码将通过JavaScript动态生成 -->
                        <li><a href="javascript:void(0);" id="next-page">&raquo;</a></li>
                    </ul>
                    <div style="margin-top: 10px;">
                        <span id="pagination-info"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(function() {
    // 控制真实数据的显示和隐藏
    $('#show-real-data').change(function() {
        if ($(this).is(':checked')) {
            $('.normal-data').show();
            $('.data-separator').show(); // 显示数据分隔符（斜杠）
        } else {
            $('.normal-data').hide();
            $('.data-separator').hide(); // 隐藏数据分隔符（斜杠）
        }
    });

    // 初始化时触发一次，确保状态正确
    $('#show-real-data').trigger('change');

    // 分页相关变量
    var currentPage = 1;
    var itemsPerPage = 10; // 每页显示10条记录
    var filteredRows = []; // 当前筛选后的行
    var totalPages = 1;

    // 层级筛选功能
    $('#level-filter').change(function() {
        var selectedLevel = $(this).val();

        // 根据选择的层级筛选行
        if (selectedLevel == 0) {
            // 显示所有层级
            filteredRows = $('.player-row').toArray();
        } else {
            // 只显示选中层级的行
            filteredRows = $('.player-row[data-level="' + selectedLevel + '"]').toArray();
        }

        // 重置到第一页并更新分页
        currentPage = 1;
        updatePagination();
    });

    // 初始化分页
    function initPagination() {
        // 初始时显示所有层级的行
        filteredRows = $('.player-row').toArray();
        updatePagination();

        // 绑定分页按钮事件
        $('#prev-page').click(function() {
            if (currentPage > 1) {
                currentPage--;
                updatePagination();
            }
        });

        $('#next-page').click(function() {
            if (currentPage < totalPages) {
                currentPage++;
                updatePagination();
            }
        });

        // 绑定页码点击事件
        $(document).on('click', '.page-number', function() {
            currentPage = parseInt($(this).data('page'));
            updatePagination();
        });
    }

    // 更新分页显示
    function updatePagination() {
        // 计算总页数
        totalPages = Math.ceil(filteredRows.length / itemsPerPage);
        if (totalPages === 0) totalPages = 1;

        // 确保当前页在有效范围内
        if (currentPage > totalPages) {
            currentPage = totalPages;
        }

        // 计算当前页应显示的行
        var startIndex = (currentPage - 1) * itemsPerPage;
        var endIndex = Math.min(startIndex + itemsPerPage, filteredRows.length);

        // 隐藏所有行
        $('.player-row').hide();

        // 显示当前页的行
        for (var i = startIndex; i < endIndex; i++) {
            $(filteredRows[i]).show();
        }

        // 更新分页信息
        var showingText = '{:__("Showing")}';
        var toText = '{:__("to")}';
        var ofText = '{:__("of")}';
        var entriesText = '{:__("entries")}';

        $('#pagination-info').text(showingText + ' ' +
                                  (filteredRows.length > 0 ? startIndex + 1 : 0) +
                                  ' ' + toText + ' ' +
                                  endIndex + ' ' +
                                  ofText + ' ' +
                                  filteredRows.length + ' ' +
                                  entriesText);

        // 更新分页按钮
        updatePaginationButtons();
    }

    // 更新分页按钮
    function updatePaginationButtons() {
        // 清除现有页码按钮
        $('.page-number').parent().remove();

        // 添加新的页码按钮
        var $pagination = $('#player-pagination');
        var $nextPageLi = $('#next-page').parent();

        // 确定要显示的页码范围
        var startPage = Math.max(1, currentPage - 2);
        var endPage = Math.min(totalPages, currentPage + 2);

        // 如果页码范围不足5页，尝试扩展范围
        if (endPage - startPage < 4) {
            if (startPage === 1) {
                endPage = Math.min(totalPages, startPage + 4);
            } else if (endPage === totalPages) {
                startPage = Math.max(1, endPage - 4);
            }
        }

        // 添加页码按钮
        for (var i = startPage; i <= endPage; i++) {
            var $li = $('<li' + (i === currentPage ? ' class="active"' : '') + '><a href="javascript:void(0);" class="page-number" data-page="' + i + '">' + i + '</a></li>');
            $li.insertBefore($nextPageLi);
        }

        // 更新上一页/下一页按钮状态
        $('#prev-page').parent().toggleClass('disabled', currentPage === 1);
        $('#next-page').parent().toggleClass('disabled', currentPage === totalPages);
    }

    // 初始化时触发一次层级筛选，确保状态正确
    $('#level-filter').trigger('change');

    // 初始化分页
    initPagination();
});
</script>

<style>
/* 虚拟数据的样式 */
.unbind-data {
    color: #3c8dbc; /* 蓝色 */
}

/* 分页样式 */
.pagination-container {
    margin-top: 15px;
}

.pagination > li.disabled > a {
    color: #777;
    cursor: not-allowed;
}

.pagination > li.active > a {
    background-color: #3c8dbc;
    border-color: #3c8dbc;
    color: white;
}
</style>