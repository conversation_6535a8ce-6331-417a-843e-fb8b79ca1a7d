<!-- 同IP查询标签页 -->
<div class="panel-body">
    <div class="panel panel-default">
        <div class="panel-heading">当前玩家IP信息</div>
        <div class="panel-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>注册IP:</label>
                        <span>{$row.register_ip|default="--"}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>最后登录IP:</label>
                        <span>{$row.last_login_ip|default="--"}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading">同IP玩家列表</div>
        <div class="panel-body">
            <div class="table-responsive">
                <table id="same-ip-table" class="table table-striped table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>玩家ID</th>
                            <th>玩家账号</th>
                            <th>共同登录IP</th>
                            <th>最近登录时间</th>
                            <th>注册IP</th>
                            <th>注册时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        {empty name="sameIpPlayers"}
                            <tr>
                                <td colspan="6" class="text-center">没有找到同IP的其他玩家</td>
                            </tr>
                        {else/}
                            {foreach name="sameIpPlayers" item="item"}
                                <tr>
                                    <td>{$item.id}</td>
                                    <td>{$item.username}</td>
                                    <td>{$item.login_ip|default="--"}</td>
                                    <td>{$item.last_log_time ? date('Y-m-d H:i:s', strtotime($item.last_log_time)) : '--'}</td>
                                    <td>{$item.register_ip|default="--"}</td>
                                    <td>{$item.register_time ? date('Y-m-d H:i:s', strtotime($item.register_time)) : '--'}</td>
                                </tr>
                            {/foreach}
                        {/empty}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
