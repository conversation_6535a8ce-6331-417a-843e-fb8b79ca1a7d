<!-- 充值分成设置标签页 - 与 PlayerCommission 关联 -->
<div class="panel-body">
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">{:__('Commission Config')}</h3>
                </div>
                <div class="panel-body">
                    <form id="commission-config-form" class="form-horizontal" role="form" method="post" action="{:url('commission/player_commission/save')}">
                        <input type="hidden" name="player_id" value="{$player_id}">
                        <input type="hidden" name="referer" value="{:url('players/players/player_detail', ['ids' => $player_id])}">

                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-3">{:__('Min Deposit Amount')}:</label>
                            <div class="col-xs-12 col-sm-4">
                                <input type="number" class="form-control" name="min_deposit_amount" value="{$config.min_deposit_amount|default='0.00'}" step="0.01" min="0">
                            </div>
                            <div class="col-xs-12 col-sm-5">
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="sync_next" value="1" {$config.sync_next ? 'checked' : ''}>
                                        {:__('Sync to Next')}
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-3">{:__('Level 1 Commission Rate')}:</label>
                            <div class="col-xs-12 col-sm-2">
                                <div class="input-group">
                                    <input type="number" class="form-control" name="level1_rate" value="{$config.level1_rate|default='0.00'}" step="0.01" min="0">
                                    <span class="input-group-addon">%</span>
                                </div>
                            </div>
                            <label class="control-label col-xs-12 col-sm-3">{:__('Level 1 Max Commission')}:</label>
                            <div class="col-xs-12 col-sm-2">
                                <div class="input-group">
                                    <input type="number" class="form-control" name="level1_max_amount" value="{$config.level1_max_amount|default='0.00'}" step="0.01" min="0">
                                    <span class="input-group-addon" title="{:__('0 means no limit')}">0=∞</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-3">{:__('Level 2 Commission Rate')}:</label>
                            <div class="col-xs-12 col-sm-2">
                                <div class="input-group">
                                    <input type="number" class="form-control" name="level2_rate" value="{$config.level2_rate|default='0.00'}" step="0.01" min="0">
                                    <span class="input-group-addon">%</span>
                                </div>
                            </div>
                            <label class="control-label col-xs-12 col-sm-3">{:__('Level 2 Max Commission')}:</label>
                            <div class="col-xs-12 col-sm-2">
                                <div class="input-group">
                                    <input type="number" class="form-control" name="level2_max_amount" value="{$config.level2_max_amount|default='0.00'}" step="0.01" min="0">
                                    <span class="input-group-addon" title="{:__('0 means no limit')}">0=∞</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-3">{:__('Level 3 Commission Rate')}:</label>
                            <div class="col-xs-12 col-sm-2">
                                <div class="input-group">
                                    <input type="number" class="form-control" name="level3_rate" value="{$config.level3_rate|default='0.00'}" step="0.01" min="0">
                                    <span class="input-group-addon">%</span>
                                </div>
                            </div>
                            <label class="control-label col-xs-12 col-sm-3">{:__('Level 3 Max Commission')}:</label>
                            <div class="col-xs-12 col-sm-2">
                                <div class="input-group">
                                    <input type="number" class="form-control" name="level3_max_amount" value="{$config.level3_max_amount|default='0.00'}" step="0.01" min="0">
                                    <span class="input-group-addon" title="{:__('0 means no limit')}">0=∞</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-xs-12 col-sm-8 col-sm-offset-3">
                                <button type="submit" class="btn btn-primary">{:__('Save')}</button>
                                <button type="reset" class="btn btn-default">{:__('Reset')}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 当标签页被激活时加载数据
function loadCommissionConfigs() {
    console.log("加载充值分成设置数据");

    // 获取玩家佣金配置
    getPlayerCommissionConfig();

    // 绑定表单提交事件
    $('#commission-config-form').on('submit', function(e) {
        e.preventDefault();
        savePlayerCommissionConfig($(this));
    });

    // 获取玩家佣金配置函数
    function getPlayerCommissionConfig() {
        var playerId = '{$player_id}';

        // 使用AJAX获取玩家佣金配置
        $.ajax({
            url: '{:url("commission/player_commission/index")}',
            type: 'GET',
            data: { player_id: playerId },
            dataType: 'json',
            success: function(response) {
                if (response && response.code === 1 && response.data) {
                    // 填充表单数据
                    var config = response.data.config;
                    if (config) {
                        $('input[name="min_deposit_amount"]').val(config.min_deposit_amount);
                        $('input[name="level1_rate"]').val(config.level1_rate);
                        $('input[name="level1_max_amount"]').val(config.level1_max_amount);
                        $('input[name="level2_rate"]').val(config.level2_rate);
                        $('input[name="level2_max_amount"]').val(config.level2_max_amount);
                        $('input[name="level3_rate"]').val(config.level3_rate);
                        $('input[name="level3_max_amount"]').val(config.level3_max_amount);

                        // 设置同步到下级复选框
                        if (config.sync_next == 1) {
                            $('input[name="sync_next"]').prop('checked', true);
                        } else {
                            $('input[name="sync_next"]').prop('checked', false);
                        }
                    }
                } else {
                    // 使用默认值
                    console.log("未找到玩家佣金配置，使用默认值");
                }
            },
            error: function(xhr, status, error) {
                console.error("获取玩家佣金配置失败:", error);
                Layer.alert('获取玩家佣金配置失败，请稍后重试', {icon: 2});
            }
        });
    }

    // 保存玩家佣金配置函数
    function savePlayerCommissionConfig(form) {
        var formData = form.serialize();

        // 使用AJAX保存玩家佣金配置
        $.ajax({
            url: '{:url("commission/player_commission/save")}',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response && response.code === 1) {
                    Layer.msg(response.msg || '保存成功', {icon: 1});
                } else {
                    Layer.alert(response.msg || '保存失败', {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                console.error("保存玩家佣金配置失败:", error);
                Layer.alert('保存玩家佣金配置失败，请稍后重试', {icon: 2});
            }
        });
    }
}
</script>
