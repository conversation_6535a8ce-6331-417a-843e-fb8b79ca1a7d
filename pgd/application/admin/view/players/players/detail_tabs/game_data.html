<!-- 游戏数据标签页 -->
<div class="panel-body">
    <form id="game-data-form" class="form-horizontal" role="form">
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-1">{:__('Player ID')}:</label>
            <div class="col-xs-12 col-sm-2">
                <input type="text" class="form-control" id="player-id-game" value="{$player_id}" readonly>
            </div>
            <label class="control-label col-xs-12 col-sm-1">{:__('Game Type')}:</label>
            <div class="col-xs-12 col-sm-2">
                <select class="form-control" id="game-type">
                    <option value="">{:__('All')}</option>
                    <option value="slot">{:__('Slot')}</option>
                    <option value="fish">{:__('Fish')}</option>
                    <option value="card">{:__('Card')}</option>
                    <option value="live">{:__('Live')}</option>
                </select>
            </div>
            <label class="control-label col-xs-12 col-sm-1">{:__('Start Time')}:</label>
            <div class="col-xs-12 col-sm-2">
                <input type="text" class="form-control datetimepicker" id="start-time-game" placeholder="{:__('Start Time')}">
            </div>
            <label class="control-label col-xs-12 col-sm-1">{:__('End Time')}:</label>
            <div class="col-xs-12 col-sm-2">
                <input type="text" class="form-control datetimepicker" id="end-time-game" placeholder="{:__('End Time')}">
            </div>
            <div class="col-xs-12 col-sm-2">
                <button type="button" id="search-game-data" class="btn btn-primary">{:__('Search')}</button>
                <button type="reset" class="btn btn-default">{:__('Reset')}</button>
            </div>
        </div>
    </form>

    <div class="table-responsive">
        <table id="game-data-table" class="table table-striped table-bordered table-hover">
            <thead>
                <tr>
                    <th>{:__('Game ID')}</th>
                    <th>{:__('Game Name')}</th>
                    <th>{:__('Bet Amount')}</th>
                    <th>{:__('Win Amount')}</th>
                    <th>{:__('Profit/Loss')}</th>
                    <th>{:__('Start Time')}</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="6" class="text-center">{:__('No matching records found')}</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
// 当标签页被激活时加载数据
function loadGameDataTab() {
    console.log("加载游戏数据");

    // 初始化日期选择器
    if ($.fn.datetimepicker) {
        $('.datetimepicker').datetimepicker({
            format: 'YYYY-MM-DD HH:mm:ss',
            locale: 'zh-cn'
        });
    }

    // 绑定搜索按钮事件
    $('#search-game-data').off('click').on('click', function(e) {
        e.preventDefault();

        var gameType = $('#game-type').val();
        var startTime = $('#start-time-game').val();
        var endTime = $('#end-time-game').val();

        // 加载游戏数据
        loadGameDataList(gameType, startTime, endTime);
    });

    // 初始加载数据
    loadGameDataList();

    // 加载游戏数据函数
    function loadGameDataList(gameType, startTime, endTime) {
        var params = {
            player_id: '{$player_id}'
        };

        if (gameType) {
            params.game_type = gameType;
        }

        if (startTime) {
            params.start_time = startTime;
        }

        if (endTime) {
            params.end_time = endTime;
        }

        // 显示加载中
        $('#game-data-table tbody').html('<tr><td colspan="6" class="text-center"><i class="fa fa-spinner fa-spin"></i> 正在加载数据...</td></tr>');

        $.ajax({
            url: 'players/players/get_game_data',
            type: 'GET',
            data: params,
            dataType: 'json',
            success: function(data) {
                if (data && data.rows && data.rows.length > 0) {
                    var html = '';
                    $.each(data.rows, function(i, item) {
                        html += '<tr>';
                        html += '<td>' + (item.game_id || '') + '</td>';
                        html += '<td>' + (item.game_name || '') + '</td>';
                        html += '<td>' + (item.bet_amount || '0.00') + '</td>';
                        html += '<td>' + (item.win_amount || '0.00') + '</td>';
                        html += '<td>' + (item.profit || '0.00') + '</td>';
                        html += '<td>' + (item.start_time || '') + '</td>';
                        html += '</tr>';
                    });
                    $('#game-data-table tbody').html(html);
                } else {
                    $('#game-data-table tbody').html('<tr><td colspan="6" class="text-center">没有找到匹配的记录</td></tr>');
                }
            },
            error: function(xhr, status, error) {
                console.error("加载游戏数据失败:", error);
                $('#game-data-table tbody').html('<tr><td colspan="6" class="text-center text-danger">加载数据失败，请稍后重试</td></tr>');
            }
        });
    }
}
</script>
