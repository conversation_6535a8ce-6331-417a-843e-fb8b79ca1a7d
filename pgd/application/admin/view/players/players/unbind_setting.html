<div class="panel-body">
    <form id="global-unbind-settings-form" class="form-horizontal" role="form">
        <input type="hidden" name="player_id" value="0">

        <div class="panel panel-default">
            <div class="panel-heading">{:__('Unbind Mode Settings')}</div>
            <div class="panel-body">
                <div class="form-group">
                    <label class="control-label col-sm-2">{:__('Unbind Mode')}:</label>
                    <div class="col-sm-4">
                        <select class="form-control" name="unbind_mode" id="unbind_mode_field">
                            <option value="0">{:__('Disabled')}</option>
                            <option value="1">{:__('Channel Mode')}</option>
                            <option value="2">{:__('Personal Mode')}</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">{:__('Global Registration Unbind Settings')}</div>
            <div class="panel-body">
                <div class="form-group">
                    <label class="control-label col-sm-2">{:__('Unbind Type')}:</label>
                    <div class="col-sm-4">
                        <select class="form-control" name="reg_unbind_type">
                            <option value="0">{:__('Off')}</option>
                            <option value="1">{:__('Interval Unbind')}</option>
                            <option value="2">{:__('Probability Unbind')}</option>
                        </select>
                    </div>
                </div>

                <div class="form-group reg-threshold-group" style="display:none;">
                    <label class="control-label col-sm-2">{:__('Register Count Threshold')}:</label>
                    <div class="col-sm-4">
                        <input type="number" class="form-control" name="reg_unbind_threshold" placeholder="{:__('Enter register count threshold')}">
                        <span class="help-block">{:__('Set the register count threshold to trigger unbinding')}</span>
                    </div>
                </div>

                <div class="form-group reg-interval-group" style="display:none;">
                    <label class="control-label col-sm-2">{:__('Unbind Interval')}:</label>
                    <div class="col-sm-4">
                        <input type="number" class="form-control" name="reg_unbind_interval" placeholder="{:__('Enter unbind interval')}">
                        <span class="help-block">{:__('Interval value in interval unbind mode')}</span>
                    </div>
                </div>

                <div class="form-group reg-ratio-group" style="display:none;">
                    <label class="control-label col-sm-2">{:__('Unbind Probability (%)')}:</label>
                    <div class="col-sm-4">
                        <input type="number" class="form-control" name="reg_unbind_prob" placeholder="{:__('Enter unbind probability (0-100)')}">
                        <span class="help-block">{:__('Probability value in probability unbind mode (0-100)')}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">{:__('Global Deposit Unbind Settings')}</div>
            <div class="panel-body">
                <div class="form-group">
                    <label class="control-label col-sm-2">{:__('Unbind Type')}:</label>
                    <div class="col-sm-4">
                        <select class="form-control" name="deposit_unbind_type">
                            <option value="0">{:__('Off')}</option>
                            <option value="1">{:__('Interval Unbind')}</option>
                            <option value="2">{:__('Probability Unbind')}</option>
                        </select>
                    </div>
                </div>

                <div class="form-group deposit-threshold-group" style="display:none;">
                    <label class="control-label col-sm-2">{:__('Deposit Amount Threshold')}:</label>
                    <div class="col-sm-4">
                        <input type="number" class="form-control" name="deposit_unbind_threshold" placeholder="{:__('Enter deposit amount threshold')}">
                        <span class="help-block">{:__('Set the deposit amount threshold to trigger unbinding')}</span>
                    </div>
                </div>

                <div class="form-group deposit-interval-group" style="display:none;">
                    <label class="control-label col-sm-2">{:__('Unbind Interval')}:</label>
                    <div class="col-sm-4">
                        <input type="number" class="form-control" name="deposit_unbind_interval" placeholder="{:__('Enter unbind interval')}">
                        <span class="help-block">{:__('Interval value in interval unbind mode')}</span>
                    </div>
                </div>

                <div class="form-group deposit-ratio-group" style="display:none;">
                    <label class="control-label col-sm-2">{:__('Unbind Probability (%)')}:</label>
                    <div class="col-sm-4">
                        <input type="number" class="form-control" name="deposit_unbind_prob" placeholder="{:__('Enter unbind probability (0-100)')}">
                        <span class="help-block">{:__('Probability value in probability unbind mode (0-100)')}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
                <button type="submit" class="btn btn-primary">{:__('Save Settings')}</button>
            </div>
        </div>
    </form>
</div>

<script>
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('全局掉绑设置页面加载完成');

        // 处理掉绑类型变化
        function handleUnbindTypeChange(value, type) {
            var thresholdSelector = '.' + type + '-threshold-group';
            var intervalSelector = '.' + type + '-interval-group';
            var ratioSelector = '.' + type + '-ratio-group';

            var thresholdGroup = document.querySelector(thresholdSelector);
            var intervalGroup = document.querySelector(intervalSelector);
            var ratioGroup = document.querySelector(ratioSelector);

            if (value === '0') {
                // 关闭时隐藏所有输入框
                thresholdGroup.style.display = 'none';
                intervalGroup.style.display = 'none';
                ratioGroup.style.display = 'none';
            } else if (value === '1') {
                // 间隔掉绑时，显示阈值和间隔框，隐藏概率框
                thresholdGroup.style.display = 'block';
                intervalGroup.style.display = 'block';
                ratioGroup.style.display = 'none';
            } else if (value === '2') {
                // 概率掉绑时，显示阈值和概率框，隐藏间隔框
                thresholdGroup.style.display = 'block';
                ratioGroup.style.display = 'block';
                intervalGroup.style.display = 'none';
            }
        }

        // 处理掉绑模式变化
        function handleUnbindModeChange(value) {
            var regPanel = document.querySelector('.panel:nth-child(3)');
            var depositPanel = document.querySelector('.panel:nth-child(4)');

            if (value === '0') {
                // 关闭模式，隐藏所有设置面板
                regPanel.style.display = 'none';
                depositPanel.style.display = 'none';
            } else {
                // 渠道模式或个人模式，显示设置面板
                regPanel.style.display = 'block';
                depositPanel.style.display = 'block';
            }
        }

        // 绑定掉绑模式变化事件
        var unbindModeSelect = document.getElementById('unbind_mode_field');
        unbindModeSelect.addEventListener('change', function() {
            handleUnbindModeChange(this.value);
        });

        // 绑定掉绑类型变化事件
        var regUnbindTypeSelect = document.querySelector('select[name="reg_unbind_type"]');
        regUnbindTypeSelect.addEventListener('change', function() {
            handleUnbindTypeChange(this.value, 'reg');
        });

        var depositUnbindTypeSelect = document.querySelector('select[name="deposit_unbind_type"]');
        depositUnbindTypeSelect.addEventListener('change', function() {
            handleUnbindTypeChange(this.value, 'deposit');
        });

        // 验证概率输入
        var probInputs = document.querySelectorAll('input[name="reg_unbind_prob"], input[name="deposit_unbind_prob"]');
        probInputs.forEach(function(input) {
            input.addEventListener('change', function() {
                var prob = parseInt(this.value) || 0;
                if (prob < 0 || prob > 100) {
                    alert('{:__("Probability must be between 0 and 100")}');
                    this.value = '';
                    return false;
                }
            });
        });

        // 绑定表单提交事件
        var form = document.getElementById('global-unbind-settings-form');
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // 表单验证
            var isValid = true;
            var regUnbindType = regUnbindTypeSelect.value;

            // 根据注册掉绑类型验证相应的输入框
            if (regUnbindType !== '0') {
                // 验证阈值输入框
                var regThresholdInput = document.querySelector('input[name="reg_unbind_threshold"]');
                if (!regThresholdInput.value) {
                    alert('{:__("Please fill in the register count threshold")}');
                    isValid = false;
                }

                // 根据类型验证间隔或概率输入框
                if (regUnbindType === '1') {
                    var regIntervalInput = document.querySelector('input[name="reg_unbind_interval"]');
                    if (!regIntervalInput.value) {
                        alert('{:__("Please fill in the register unbind interval")}');
                        isValid = false;
                    }
                } else if (regUnbindType === '2') {
                    var regProbInput = document.querySelector('input[name="reg_unbind_prob"]');
                    if (!regProbInput.value) {
                        alert('{:__("Please fill in the register unbind probability")}');
                        isValid = false;
                    }
                }
            }

            // 对充值掉绑类型也进行同样的验证
            if (isValid) {
                var depositUnbindType = depositUnbindTypeSelect.value;
                if (depositUnbindType !== '0') {
                    // 验证阈值输入框
                    var depositThresholdInput = document.querySelector('input[name="deposit_unbind_threshold"]');
                    if (!depositThresholdInput.value) {
                        alert('{:__("Please fill in the deposit amount threshold")}');
                        isValid = false;
                    }

                    // 根据类型验证间隔或概率输入框
                    if (depositUnbindType === '1') {
                        var depositIntervalInput = document.querySelector('input[name="deposit_unbind_interval"]');
                        if (!depositIntervalInput.value) {
                            alert('{:__("Please fill in the deposit unbind interval")}');
                            isValid = false;
                        }
                    } else if (depositUnbindType === '2') {
                        var depositProbInput = document.querySelector('input[name="deposit_unbind_prob"]');
                        if (!depositProbInput.value) {
                            alert('{:__("Please fill in the deposit unbind probability")}');
                            isValid = false;
                        }
                    }
                }
            }

            if (!isValid) {
                return false;
            }

            // 收集表单数据
            var formData = new FormData(form);
            var params = new URLSearchParams();
            for (var pair of formData.entries()) {
                params.append(pair[0], pair[1]);
            }

            // 提交表单数据
            var xhr = new XMLHttpRequest();
            xhr.open('POST', 'save_player_unbind_setting');
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.onload = function() {
                if (xhr.status === 200) {
                    var response = JSON.parse(xhr.responseText);
                    if (response.code === 1) {
                        alert('{:__("Save successful")}');
                        // 关闭弹窗
                        if (typeof parent.Layer !== 'undefined' && parent.Layer.getFrameIndex) {
                            var index = parent.Layer.getFrameIndex(window.name);
                            parent.Layer.close(index);
                        }
                    } else {
                        alert('{:__("Save failed")}: ' + (response.msg || '{:__("Unknown error")}'));
                    }
                } else {
                    console.error('{:__("Save failed")}:', xhr.statusText);
                    alert('{:__("Save failed")}: ' + xhr.statusText);
                }
            };
            xhr.onerror = function() {
                console.error('{:__("Failed to save unbind settings")}:', xhr.statusText);
                alert('{:__("Save failed")}: ' + '{:__("Network error")}');
            };
            xhr.send(params.toString());
        });

        // 获取全局掉绑设置
        var xhr = new XMLHttpRequest();
        xhr.open('GET', 'get_player_unbind_settings?player_id=0');
        xhr.onload = function() {
            if (xhr.status === 200) {
                var response = JSON.parse(xhr.responseText);
                console.log('获取到的全局掉绑设置:', response);

                if (response && response.settings) {
                    var settings = response.settings;

                    // 更新unbind_mode字段
                    document.getElementById('unbind_mode_field').value = settings.unbind_mode;
                    // 根据掉绑模式显示或隐藏设置面板
                    handleUnbindModeChange(settings.unbind_mode.toString());

                    // 更新注册掉绑类型
                    regUnbindTypeSelect.value = settings.reg_unbind_type;
                    handleUnbindTypeChange(settings.reg_unbind_type.toString(), 'reg');

                    // 更新充值掉绑类型
                    depositUnbindTypeSelect.value = settings.deposit_unbind_type;
                    handleUnbindTypeChange(settings.deposit_unbind_type.toString(), 'deposit');

                    // 更新其他字段
                    document.querySelector('input[name="reg_unbind_threshold"]').value = settings.reg_unbind_threshold;
                    document.querySelector('input[name="reg_unbind_interval"]').value = settings.reg_unbind_interval;
                    document.querySelector('input[name="reg_unbind_prob"]').value = settings.reg_unbind_prob;
                    document.querySelector('input[name="deposit_unbind_threshold"]').value = settings.deposit_unbind_threshold;
                    document.querySelector('input[name="deposit_unbind_interval"]').value = settings.deposit_unbind_interval;
                    document.querySelector('input[name="deposit_unbind_prob"]').value = settings.deposit_unbind_prob;
                }
            } else {
                console.error('{:__("Get settings failed")}:', xhr.statusText);
            }
        };
        xhr.onerror = function() {
            console.error('{:__("Get settings failed")}:', xhr.statusText);
        };
        xhr.send();
    });
</script>