{__NOLAYOUT__}
<!DOCTYPE html>
<html>
<head>
    {include file="common/meta" /}
    <title>{$title}</title>
    <style>
        body {
            background: #f1f4f6;
            padding-top: 40px;
        }
        .panel-default {
            margin-top: 20px;
            border-radius: 5px;
            border: none;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }
        .panel-heading {
            background-color: #f8f9fa;
            border-bottom: 1px solid #eee;
            padding: 15px;
            font-size: 16px;
            font-weight: 600;
        }
        .panel-body {
            padding: 20px;
        }
        .setup-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .qr-container {
            text-align: center;
            margin: 20px 0;
        }
        .qr-container img {
            max-width: 200px;
            border: 1px solid #eee;
            padding: 10px;
            background: #fff;
        }
        .step {
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            background: #3c8dbc;
            color: #fff;
            border-radius: 50%;
            margin-right: 10px;
            font-weight: bold;
        }
        .step-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        .step-content {
            margin-left: 40px;
            color: #666;
        }
        .verification-code {
            max-width: 300px;
            margin: 0 auto;
        }
        .btn-container {
            text-align: center;
            margin-top: 20px;
        }
        .app-links {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        .app-links a {
            display: inline-block;
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
            font-size: 12px;
        }
        .app-links a:hover {
            background: #f5f5f5;
        }
        .secret-key {
            font-family: monospace;
            background: #f5f5f5;
            padding: 5px 10px;
            border-radius: 3px;
            border: 1px solid #ddd;
            font-size: 14px;
            word-break: break-all;
        }
        .alert-info {
            background-color: #d9edf7;
            border-color: #bce8f1;
            color: #31708f;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-success {
            background-color: #dff0d8;
            border-color: #d6e9c6;
            color: #3c763d;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container setup-container">
        <div class="panel panel-default">
            <div class="panel-heading">
                <i class="fa fa-shield"></i> {:__('Two-Factor Authentication Setup')}
            </div>
            <div class="panel-body">
                {if condition="!$two_factor_enabled"}
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> {:__('Two-factor authentication adds an extra layer of security to your account. Once enabled, you will need to enter a verification code from your authenticator app when logging in.')}
                </div>

                <div class="step">
                    <div class="step-title">
                        <span class="step-number">1</span> {:__('Download an authenticator app')}
                    </div>
                    <div class="step-content">
                        <p>{:__('Download and install an authenticator app on your mobile device. We recommend Google Authenticator, Microsoft Authenticator, or Authy.')}</p>
                        <div class="app-links">
                            <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" target="_blank"><i class="fa fa-android"></i> Google Authenticator (Android)</a>
                            <a href="https://apps.apple.com/app/google-authenticator/id388497605" target="_blank"><i class="fa fa-apple"></i> Google Authenticator (iOS)</a>
                            <a href="https://authy.com/download/" target="_blank"><i class="fa fa-mobile"></i> Authy</a>
                        </div>
                    </div>
                </div>

                <div class="step">
                    <div class="step-title">
                        <span class="step-number">2</span> {:__('Scan the QR code')}
                    </div>
                    <div class="step-content">
                        <p>{:__('Open your authenticator app and scan the QR code below to add your account.')}</p>
                        <div class="qr-container">
                            {if condition="$otpauth_uri"}
                            <img src="{:addon_url('qrcode/index/build', ['text' => $otpauth_uri])}" alt="QR Code">
                            {else/}
                            <p class="text-danger">{:__('Unable to generate QR code')}</p>
                            {/if}
                        </div>
                        <p>{:__('If you cannot scan the QR code, you can manually enter this secret key into your app:')}</p>
                        <p class="text-center">
                            <code class="secret-key">{:session('tmp_admin_totp_secret_'.$admin['id'])}</code>
                        </p>
                    </div>
                </div>

                <div class="step">
                    <div class="step-title">
                        <span class="step-number">3</span> {:__('Verify and enable')}
                    </div>
                    <div class="step-content">
                        <p>{:__('Enter the 6-digit verification code from your authenticator app to enable two-factor authentication.')}</p>
                        <form id="two-factor-form" class="form-horizontal" role="form" method="POST" action="{:url('general/two_factor/update')}">
                            {:token()}
                            <div class="form-group verification-code">
                                <input type="text" class="form-control" id="c-totp-verify" placeholder="{:__('Enter the 6-digit code')}" name="totp_verify" value="" data-rule="required;digits;length(6)" autocomplete="off" />
                            </div>
                            <div class="btn-container">
                                <button type="submit" class="btn btn-success btn-lg">{:__('Enable Two-Factor Authentication')}</button>
                            </div>
                        </form>
                    </div>
                </div>
                {else/}
                <div class="alert alert-success">
                    <i class="fa fa-check-circle"></i> {:__('Two-factor authentication is currently enabled for your account.')}
                </div>

                <div class="step">
                    <div class="step-title">
                        <span class="step-number"><i class="fa fa-shield"></i></span> {:__('Disable Two-Factor Authentication')}
                    </div>
                    <div class="step-content">
                        <p>{:__('If you want to disable two-factor authentication, please enter the verification code from your authenticator app.')}</p>
                        <form id="disable-two-factor-form" class="form-horizontal" role="form" method="POST" action="{:url('general/two_factor/disable')}">
                            {:token()}
                            <div class="form-group verification-code">
                                <input type="text" class="form-control" id="c-totp-verify" placeholder="{:__('Enter the 6-digit code')}" name="totp_verify" value="" data-rule="required;digits;length(6)" autocomplete="off" />
                            </div>
                            <div class="btn-container">
                                <button type="submit" class="btn btn-danger btn-lg">{:__('Disable Two-Factor Authentication')}</button>
                            </div>
                        </form>
                    </div>
                </div>
                {/if}
            </div>
        </div>
    </div>
    {include file="common/script" /}
    <script>
        $(function () {
            Form.api.bindevent($("form[role=form]"));
        });
    </script>
</body>
</html>
