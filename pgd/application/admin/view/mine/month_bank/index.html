<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead">
            <em>每月财报</em>
            {if isset($agent_name)}
            <small>- 业务员: {$agent_name}</small>
            {/if}
        </div>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div class="bootstrap-table">
                        <div class="fixed-table-toolbar">
                            <div class="bs-bars pull-left">
                                <div class="input-group">
                                    <span class="input-group-addon">时间:</span>
                                    <input type="text" class="form-control" id="month" value="{:date('Y-m')}" />
                                    <span class="input-group-btn">
                                        <button class="btn btn-success" type="button" id="search">搜索</button>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="fixed-table-container" style="padding-bottom: 0px;">
                            <div class="fixed-table-body">
                                <table class="table table-striped table-bordered table-hover table-nowrap">
                                    <tbody>
                                        <tr>
                                            <td width="200">总充值:</td>
                                            <td><span id="total_deposit">0.00</span></td>
                                        </tr>
                                        <tr>
                                            <td>总提现:</td>
                                            <td><span id="total_withdraw">0.00</span></td>
                                        </tr>
                                        <tr>
                                            <td>充值手续费:</td>
                                            <td><span id="deposit_fee">0.00</span></td>
                                        </tr>
                                        <tr>
                                            <td>提现手续费:</td>
                                            <td><span id="withdraw_fee">0.00</span></td>
                                        </tr>
                                        <tr>
                                            <td>API费用:</td>
                                            <td><span id="api_fee">0.00</span></td>
                                        </tr>
                                        <tr>
                                            <td>总利润:</td>
                                            <td>
                                                <span id="total_profit">0.00</span>
                                                /
                                                <input type="text" class="form-control" id="rate" style="width: 100px; display: inline-block;" placeholder="请输入汇率">
                                                =
                                                <span id="converted_profit">0.00</span>
                                                <button class="btn btn-success btn-xs" id="calculate">重新计算</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>备注:</td>
                                            <td>
                                                总利润=总充值-总提现-充值手续费-提现手续费-API费用<br>
                                                <div class="text-muted">
                                                    当前费率：充值手续费 {$rateInfo.deposit_fee_rate}%，
                                                    提现手续费 {$rateInfo.withdraw_fee_rate}%，
                                                    API费用 {$rateInfo.api_fee_rate}%
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>