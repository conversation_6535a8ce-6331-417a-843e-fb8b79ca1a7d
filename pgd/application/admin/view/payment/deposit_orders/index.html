<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <!-- <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('payment/deposit_orders/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a> -->
                        <!-- <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('payment/deposit_orders/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a> -->
                        <!-- <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('payment/deposit_orders/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a> -->





                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('payment/deposit_orders/edit')}"
                           data-operate-del="{:$auth->check('payment/deposit_orders/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>

<script type="text/html" id="playertagtemplate">
    <select name="tag_id" class="form-control selectpicker" data-live-search="true">
        <option value="">选择玩家标签</option>
        <option value="all">全部</option>
        {foreach name="playerTagList" item="vo"}
        <option value="{$vo.id}">{$vo.name}</option>
        {/foreach}
    </select>
</script>

<script type="text/html" id="agenttemplate">
    <select name="agent_id" class="form-control selectpicker" data-live-search="true">
        <option value="">选择业务员</option>
        <option value="all">全部</option>
        {foreach name="agentList" item="vo"}
        <option value="{$vo.id}">{$vo.id}({$vo.admin.username})</option>
        {/foreach}
    </select>
</script>


