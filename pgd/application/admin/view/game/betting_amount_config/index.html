<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh,add,edit,del')}
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover" 
                           data-operate-edit="{:$auth->check('game/betting_amount_config/edit')}" 
                           data-operate-del="{:$auth->check('game/betting_amount_config/del')}" 
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'game/betting_amount_config/index',
                    add_url: 'game/betting_amount_config/add',
                    edit_url: 'game/betting_amount_config/edit',
                    del_url: 'game/betting_amount_config/del',
                    table: 'betting_amount_config',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'min_amount', title: __('Min_amount'), operate: 'BETWEEN'},
                        {field: 'max_amount', title: __('Max_amount'), operate: 'BETWEEN'},
                        {field: 'status', title: __('Status'), searchList: {"1":__('Enabled'),"0":__('Disabled')}, formatter: Table.api.formatter.status},
                        {field: 'create_time', title: __('Create_time'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'update_time', title: __('Update_time'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
</script> 