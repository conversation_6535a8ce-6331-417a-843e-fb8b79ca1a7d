<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-type_id" data-rule="required" class="form-control selectpicker" name="row[type_id]">
                {foreach name="typeList" item="vo" key="key"}
                <option value="{$key}" {in name="key" value="$row.type_id"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Betting_multiplier')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-betting_multiplier" class="form-control" name="row[betting_multiplier]" type="number" value="{$row.betting_multiplier}" step="0.1" min="0" data-rule="required">
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>