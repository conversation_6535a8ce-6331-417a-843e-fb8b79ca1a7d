<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Level')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-level" data-rule="required" class="form-control" name="row[level]" type="number" min="0" max="255">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Turnover_requirement')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-turnover_requirement" data-rule="required" class="form-control" step="0.01" name="row[turnover_requirement]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Deposit_requirement')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-deposit_requirement" data-rule="required" class="form-control" step="0.01" name="row[deposit_requirement]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Daily_max_withdraw_amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-daily_max_withdraw_amount" data-rule="required" class="form-control" step="0.01" name="row[daily_max_withdraw_amount]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Daily_withdrawal_times')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-daily_withdrawal_times" data-rule="required" class="form-control" name="row[daily_withdrawal_times]" type="number" value="1">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Max_withdraw_amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-max_withdraw_amount" data-rule="required" class="form-control" step="0.01" name="row[max_withdraw_amount]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Single_withdraw_limit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-single_withdraw_limit" data-rule="required" class="form-control" step="0.01" name="row[single_withdraw_limit]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Daily_deposit_rebate_rate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-daily_deposit_rebate_rate" data-rule="required" class="form-control" step="0.01" name="row[daily_deposit_rebate_rate]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Level_up_bonus')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-level_up_bonus" data-rule="required" class="form-control" step="0.01" name="row[level_up_bonus]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Daily_bonus')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-daily_bonus" data-rule="required" class="form-control" step="0.01" name="row[daily_bonus]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weekly_bonus')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weekly_bonus" data-rule="required" class="form-control" step="0.01" name="row[weekly_bonus]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Monthly_bonus')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-monthly_bonus" data-rule="required" class="form-control" step="0.01" name="row[monthly_bonus]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Created_at')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-created_at" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[created_at]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Updated_at')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-updated_at" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[updated_at]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
