<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddFieldsToAgentsTable extends Migrator
{
    public function change()
    {
        $this->table('agents')
            ->addColumn('game_quota', 'decimal', ['precision' => 10, 'scale' => 2, 'default' => 0.00, 'comment' => '游戏额度'])
            ->addColumn('used_game_quota', 'decimal', ['precision' => 10, 'scale' => 2, 'default' => 0.00, 'comment' => '已使用游戏额度'])
            ->addColumn('withdraw_permission', 'boolean', ['default' => 0, 'comment' => '放款权限：0=无，1=有'])
            ->addColumn('withdraw_quota', 'decimal', ['precision' => 10, 'scale' => 2, 'default' => 0.00, 'comment' => '放款额度'])
            ->addColumn('can_change_password', 'boolean', ['default' => 0, 'comment' => '是否可改密码直接登录：0=否，1=是'])
            ->addColumn('promotion_link', 'string', ['limit' => 255, 'null' => true, 'comment' => '推广链接'])
            ->addColumn('agent_level', 'integer', ['default' => 0, 'comment' => '业务员级别'])
            ->addColumn('team_leader', 'string', ['limit' => 50, 'null' => true, 'comment' => '组长名称'])
            ->update();
    }
} 