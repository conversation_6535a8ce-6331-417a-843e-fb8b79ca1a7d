<!DOCTYPE html>
<html lang="{$config.language}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('VIP')}</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}

    <!-- 引入活动页面专用CSS -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/event/event.css?v=1.02">
    <!-- 只使用一个VIP样式文件 -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/event/event_vip.css?v=1.01">

    <style>
        body {
            overflow: hidden; /* 禁止body滚动 */
            height: 100vh; /* 设置高度为视口高度 */
            position: fixed; /* 固定定位 */
            width: 100%; /* 宽度100% */
        }
    </style>
</head>
<body>
    <div class="container">

        <div class="horizontal-tabs">
            <div class="tab" data-tab="eventos">{:__('Events')}</div>
            <div class="tab" data-tab="missao">{:__('Tasks')}</div>
            <div class="tab active" data-tab="vip">{:__('VIP')}</div>
            <div class="tab" data-tab="rebate">{:__('Rebate')}</div>
            <div class="tab" data-tab="pendente">{:__('Pending')}</div>
            <div class="tab" data-tab="history">{:__('History')}</div>
        </div>

        <div class="main-content">
            <!-- VIP状态卡片 -->
            <div class="vip-status-card">
                <div class="vip-content">
                    <div class="vip-level-container">
                        <div class="vip-level-circle-bg"></div>
                        <div class="vip-level-circle-overlay"></div>
                        <div class="vip-level-number">{$vipLevel}</div>
                    </div>
                    <div class="vip-info">
                        <div class="vip-info-row">
                            <div class="vip-info-label">{:__('Restante')}</div>
                            <div class="vip-info-value">VIP{$vipLevel}</div>
                        </div>
                        <div class="vip-info-row">
                            <div class="vip-info-label">{:__('Valor para Depósito')}</div>
                            <div class="vip-info-value">{$nextLevelDeposit|default='-'}</div>
                        </div>
                        <div class="vip-info-row">
                            <div class="vip-info-label">{:__('Codificação')}</div>
                            <div class="vip-info-value">{$playerDeposit|default='0.00'}</div>
                        </div>
                    </div>
                    <div class="vip-actions">
                        <button class="vip-btn collect-all">{:__('Coletar Tudo')}</button>
                        <button class="vip-btn history" onclick="switchTab('history')">{:__('Histórico')}</button>
                    </div>
                </div>
            </div>

            <!-- VIP等级列表标题 -->
            <h2 class="vip-section-title">{:__('Lista De Níveis VIP')}</h2>

            <!-- VIP特权分类标签 -->
            <div class="vip-category-tabs">
                <div class="vip-category-tab active" data-category="levelup">{:__('Bônus De Aumento De Nível')}</div>
                <div class="vip-category-tab" data-category="weekly">{:__('Bônus Semanal')}</div>
                <div class="vip-category-tab" data-category="monthly">{:__('Bônus Mensal')}</div>
                <div class="vip-category-tab" data-category="privilege">{:__('Privilégio VIP')}</div>
            </div>

            <!-- VIP特权内容 -->
            <div class="vip-category-content">
                <!-- 升级奖励内容 -->
                <div class="vip-category-item active" data-category="levelup">
                    <div class="vip-table-container">
                        <table class="vip-table">
                            <thead>
                                <tr>
                                    <th>{:__('Nível')}</th>
                                    <th>{:__('Depósito para promoção')}</th>
                                    <th>{:__('Aposta para promoção')}</th>
                                    <th>{:__('Bônus')}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="vipList" id="vip"}
                                {php}
                                $depositPercent = 0;
                                $turnoverPercent = 0;
                                if (($vipLevel+1) == $vip['level']) {
                                    $depositPercent = $playerDeposit >= $vip['deposit_requirement'] ? 100 : ($playerDeposit/$vip['deposit_requirement']*100);
                                    $depositPercent = $depositPercent > 100 ? 100 : $depositPercent;

                                    $turnoverPercent = $playerDeposit >= $vip['turnover_requirement'] ? 100 : ($playerDeposit/$vip['turnover_requirement']*100);
                                    $turnoverPercent = $turnoverPercent > 100 ? 100 : $turnoverPercent;
                                }
                                {/php}
                                <tr class="{$vipLevel == $vip.level ? 'current-vip' : ''}">
                                    <td>VIP {$vip.level}</td>
                                    <td>
                                        {if ($vipLevel+1) == $vip.level}
                                        <div class="progress-container">
                                            <div class="progress-bar" data-width="{$depositPercent|default=0}"></div>
                                            <div class="progress-text">{$playerDeposit}/{$vip.deposit_requirement}</div>
                                        </div>
                                        {else}
                                        {$vip.deposit_requirement}
                                        {/if}
                                    </td>
                                    <td>
                                        {if ($vipLevel+1) == $vip.level}
                                        <div class="progress-container">
                                            <div class="progress-bar" data-width="{$turnoverPercent|default=0}"></div>
                                            <div class="progress-text">{$playerDeposit}/{$vip.turnover_requirement}</div>
                                        </div>
                                        {else}
                                        {$vip.turnover_requirement}
                                        {/if}
                                    </td>
                                    <td>{$vip.level_up_bonus}</td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 特权内容 -->
                <div class="vip-category-item" data-category="privilege">
                    <div class="vip-table-container">
                        <table class="vip-table">
                            <thead>
                                <tr>
                                    <th>{:__('Nível')}</th>
                                    <th>{:__('Retirada diária')}</th>
                                    <th>{:__('Vezes de Retirada')}</th>
                                    <th>{:__('Taxa de Retorno')}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="vipList" id="vip"}
                                <tr class="{$vipLevel == $vip.level ? 'current-vip' : ''}">
                                    <td>VIP {$vip.level}</td>
                                    <td>{$vip.level * 1000}</td>
                                    <td>{$vip.level * 2}</td>
                                    <td>{$vip.level}.0%</td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 每周奖励内容 -->
                <div class="vip-category-item" data-category="weekly">
                    <div class="vip-table-container">
                        <table class="vip-table">
                            <thead>
                                <tr>
                                    <th>{:__('Nível')}</th>
                                    <th>{:__('Bônus')}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="vipList" id="vip"}
                                <tr class="{$vipLevel == $vip.level ? 'current-vip' : ''}">
                                    <td>VIP {$vip.level}</td>
                                    <td>{$vip.level * 50}</td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 每月奖励内容 -->
                <div class="vip-category-item" data-category="monthly">
                    <div class="vip-table-container">
                        <table class="vip-table">
                            <thead>
                                <tr>
                                    <th>{:__('Nível')}</th>
                                    <th>{:__('Bônus')}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {volist name="vipList" id="vip"}
                                <tr class="{$vipLevel == $vip.level ? 'current-vip' : ''}">
                                    <td>VIP {$vip.level}</td>
                                    <td>{$vip.level * 200}</td>
                                </tr>
                                {/volist}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        {include file="common/bottom_nav" /}
    </div>

    <!-- 引入活动页面专用JS -->
    <script src="__CDN__/assets/js/frontend/event.js?v=1.01"></script>

    <!-- VIP分类标签切换脚本 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const categoryTabs = document.querySelectorAll('.vip-category-tab');
        const categoryItems = document.querySelectorAll('.vip-category-item');

        // 设置进度条宽度
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const width = bar.getAttribute('data-width');
            if (width !== null) {
                bar.style.width = width + '%';
            }
        });

        categoryTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const category = this.getAttribute('data-category');

                // 切换标签样式
                categoryTabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                // 切换内容显示
                categoryItems.forEach(item => {
                    if (item.getAttribute('data-category') === category) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });
            });
        });
    });
    </script>
</body>
</html>