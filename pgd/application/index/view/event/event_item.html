{include file="common/meta" /}

<div class="container">
    <!-- 引入通用header -->
    {include file="common/common_header" /}

    <!-- 移除历史按钮 -->

    <!-- 根据current参数显示不同内容 -->
    {if $current == 1}
    <!-- 邀请奖励页面 -->
    <div class="event-content">

        <!-- 二维码部分 - 与agent/qr-section相似 -->
        <div class="qr-section">
            <div class="qr-title">{:__('reference_info')}</div>
            <div class="qr-code-container">
                <div class="qr-code">
                    <div id="qrcode"></div>
                </div>
                <div class="share-qr-btn">{:__('share_qr')}</div>
            </div>
            <div class="qr-info">
                <div class="my-link-container">
                    <div class="link-text">{$link_url}
                        <span id="copyLinkBtn"><i class="fa fa-copy"></i></span>
                    </div>
                </div>
            </div>
            <div class="info-row">
                <div class="info-label">{:__('direct_subordinates')}</div>
                <div class="info-value">{$r_count}</div>
            </div>
            <div class="info-row">
                <div class="info-label">{:__('invitation_code')}</div>
                <div class="info-value">
                    <span class="code-value">{$id_code}</span>
                    <span id="copyCodeBtn"><i class="fa fa-copy"></i></span>
                </div>
            </div>
        </div>

        <!-- 社交分享栏 -->
        <div class="social-bar">
            <div class="social-bar-label">{:__('share')}</div>
            <div class="social-icons-row">
                <div class="social-icon" data-platform="telegram">
                    <img src="/assets/img/frontend/socials/TG.png" alt="Telegram">
                </div>
                <div class="social-icon" data-platform="facebook">
                    <img src="/assets/img/frontend/socials/FaceBook.png" alt="Facebook">
                </div>
                <div class="social-icon" data-platform="whatsapp">
                    <img src="/assets/img/frontend/socials/WhatsApp.png" alt="WhatsApp">
                </div>
                <div class="social-icon" data-platform="instagram">
                    <img src="/assets/img/frontend/socials/INS.png" alt="Instagram">
                </div>
                <div class="social-icon" data-platform="youtube">
                    <img src="/assets/img/frontend/socials/18+.png" alt="Youtube">
                </div>
            </div>
        </div>

        <!-- 有效下属说明 -->
        <div class="valid-section">
            <div class="valid-title">
                <div class="valid-count">{:__('valid_subordinates')} {$valid_subordinates}</div>
                <div class="valid-question">{:__('what_is_valid')}</div>
            </div>
            <div class="valid-explanation">{:__('valid_explanation')}</div>
            <div class="valid-condition">
                <div class="condition-label">{:__('first_deposit_amount')}</div>
                <div class="condition-value">{$validSubordinatesThreshold}.00 {:__('or_more')}</div>
            </div>
        </div>

        <!-- 宝箱领取部分 -->
        {if condition="isset($showTreasureBox) && $showTreasureBox"}
        <div class="treasure-section">
            <div class="treasure-grid">
                <!-- 根据宝箱配置和状态生成宝箱 -->
                {volist name="treasureBoxConfig" id="box"}
                <div class="treasure-box {$box.status}"
                     data-id="{$box.id}"
                     data-people="{$box.invite_count}"
                     data-reward="{$box.reward_amount}"
                     data-status="{$box.status}">
                    <div class="treasure-img">
                        <img src="/assets/img/frontend/common/{eq name='box.status' value='claimed'}Box3.png{elseif condition="$box.status eq 'available'"}Box2.png{else}Box1.png{/eq}" alt="Treasure Box" onerror="this.onerror=null; this.parentElement.innerHTML='<i class=\'fa fa-gift fa-3x\'></i>';">
                    </div>
                    <div class="treasure-info">
                        <div class="people-count">{$box.invite_count} {:__('people')}</div>
                        <div class="treasure-price">{$box.reward_amount}</div>
                    </div>
                    {eq name="box.status" value="available"}
                    <div class="claim-btn">{:__('claim_treasure')}</div>
                    {/eq}
                    {eq name="box.status" value="claimed"}
                    <div class="claimed-text">{:__('Claimed')}</div>
                    {/eq}
                </div>
                {/volist}
            </div>
        </div>
        {/if}
    </div>
    {elseif $current == 2}
    <!-- VIP俱乐部页面 -->
    <div class="event-content">
        <!-- VIP俱乐部标题 -->
        <div class="vip-club-header">
            <div class="vip-club-title">{:__('vip_club')}</div>
            <div class="vip-club-subtitle">{:__('global_gaming_platform')}</div>
        </div>

        <!-- 活动详情 -->
        <div class="vip-details">
            <div class="vip-details-title">{:__('vip_details')}</div>
            <div class="vip-description">
                <p>{:__('vip_description_1')}</p>
                <p>{:__('vip_description_2')}</p>
                <p>{:__('vip_description_3')}</p>
                <p>{:__('vip_description_4')}</p>
            </div>
        </div>

        <!-- VIP等级表格 -->
        <div class="vip-table-container">
            <table class="vip-table">
                <thead>
                    <tr>
                        <th>{:__('level')}</th>
                        <th>{:__('deposit')}</th>
                        <th>{:__('bet')}</th>
                        <th>{:__('level_up_bonus')}</th>
                        <th>{:__('weekly_bonus')}</th>
                        <th>{:__('monthly_bonus')}</th>
                    </tr>
                </thead>
                <tbody>
                    {volist name="vipList" id="vip"}
                    <tr>
                        <td>{$vip.level}</td>
                        <td>{$vip.deposit_requirement}</td>
                        <td>{$vip.turnover_requirement}</td>
                        <td>{$vip.level_up_bonus}</td>
                        <td>{$vip.weekly_bonus|default='1.00'}</td>
                        <td>{$vip.monthly_bonus|default='5.00'}</td>
                    </tr>
                    {/volist}
                </tbody>
            </table>
        </div>

        <!-- 示例说明 -->
        <div class="vip-example">
            <div class="example-title">{:__('example')}</div>
            <div class="example-text">{:__('example_text')}</div>
        </div>

        <!-- 如何领取奖金 -->
        <div class="vip-claim-section">
            <div class="claim-title">{:__('how_to_claim_bonus')}</div>
            <div class="claim-text">
                <p>1. {:__('claim_bonus_step1')}</p>
                <p>2. {:__('claim_bonus_step2')}</p>
                <p>3. {:__('claim_bonus_step3')}</p>
                <p>4. {:__('claim_bonus_step4')}</p>
            </div>

            <div class="claim-title">{:__('how_to_claim_weekly')}</div>
            <div class="claim-text">
                <p>1. {:__('claim_weekly_step1')}</p>
                <p>2. {:__('claim_weekly_step2')}</p>
                <p>3. {:__('claim_weekly_step3')}</p>
            </div>

            <div class="claim-title">{:__('how_to_claim_monthly')}</div>
            <div class="claim-text">
                <p>1. {:__('claim_monthly_step1')}</p>
                <p>2. {:__('claim_monthly_step2')}</p>
                <p>3. {:__('claim_monthly_step3')}</p>
            </div>
        </div>

        <!-- 活动规则 -->
        <div class="vip-rules">
            <div class="rules-title">{:__('event_rules')}</div>
            <div class="rules-list">
                <div class="rule-item">{:__('rule_1')}</div>
                <div class="rule-item">{:__('rule_2')}</div>
                <div class="rule-item">{:__('rule_3')}</div>
                <div class="rule-item">{:__('rule_4')}</div>
                <div class="rule-item">{:__('rule_5')}</div>
            </div>
        </div>
    </div>
    {elseif $current == 3}
    <!-- 存款奖励页面 -->
    <div class="event-content">

        <!-- 首充奖励 -->
        <div class="bonus-section">
            <div class="bonus-title">{:__('initial_deposit_bonus')}</div>
            <div class="bonus-table">
                <div class="bonus-table-header">
                    <div class="header-cell">{:__('first_deposit')}</div>
                    <div class="header-cell">{:__('reward_proportion')}</div>
                    <div class="header-cell">{:__('reward_limit')}</div>
                </div>
                <div class="bonus-table-row">
                    <div class="table-cell">≥{$firstDepositThreshold}</div>
                    <div class="table-cell">20%</div>
                    <div class="table-cell">{:__('unlimited')}</div>
                </div>
            </div>
        </div>

        <!-- 重复充值奖励 -->
        <div class="bonus-section">
            <div class="bonus-title">{:__('repeated_deposit_bonus')}</div>
            <div class="bonus-table">
                <div class="bonus-table-header">
                    <div class="header-cell">{:__('deposit')}</div>
                    <div class="header-cell">{:__('reward_proportion')}</div>
                    <div class="header-cell">{:__('reward_limit')}</div>
                </div>
                <div class="bonus-table-row">
                    <div class="table-cell">≥{$firstDepositThreshold}</div>
                    <div class="table-cell">0%</div>
                    <div class="table-cell">{:__('unlimited')}</div>
                </div>
            </div>
        </div>
    </div>
    {/if}
    <!-- 移除底部导航栏 -->
</div>

<!-- 添加CSS样式 -->
<style>
/* 通用样式 */
.container {
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 隐藏滚动条 */
    height: 100vh; /* 确保容器占满整个视口高度 */
}

.event-content {
    padding: 10px 15px;
    margin-bottom: 60px;
    overflow-y: auto; /* 允许垂直滚动 */
    height: calc(100vh - 120px); /* 设置高度为视口高度减去头部和底部空间 */
    -webkit-overflow-scrolling: touch; /* 增加iOS滚动惯性 */
}

/* 自定义滚动条样式 */
.event-content::-webkit-scrollbar {
    width: 5px; /* 滚动条宽度 */
    display: block; /* 显示滚动条 */
}

.event-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1); /* 滚动条轨道背景 */
    border-radius: 10px;
}

.event-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3); /* 滚动条滑块颜色 */
    border-radius: 10px;
}

.event-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5); /* 鼠标悬停时滑块颜色 */
}

/* 邀请奖励页面样式 */
.event-title {
    background-color: var(--darker-bg-color);
    padding: 15px;
    text-align: center;
    margin: 10px 0;
    border-radius: 5px;
}

.title-text {
    font-size: 16px;
    color:var(--text-color);
}

.bonus-text {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-color);
    margin-top: 5px;
}

.history-btn {
    color: var(--text-color);
    font-size: 14px;
    cursor: pointer;
}

/* 二维码部分样式 */
.qr-section {
    padding: 15px;
    background-color: var(--darker-bg-color);
    position: relative;
    border-radius: 5px;
    margin-bottom: 15px;
}

.qr-title {
    font-size: 13px;
    color: var(--text-color);
    margin-bottom: 15px;
    text-align: left;
    margin-left: 90px;
}

.qr-code-container {
    float: left;
    width: 75px;
    margin-right: 15px;
    overflow: hidden;
    position: relative;
    margin-bottom: 15px;
}

.qr-code {
    width: 100%;
    height: 75px;
    background-color: var(--text-color);
    overflow: hidden;
}

.qr-code img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.share-qr-btn {
    width: 100%;
    background-color: var(--secondary-color);
    color: var(--text-color);
    text-align: center;
    padding: 3px 0;
    font-size: 10px;
    font-weight: bold;
    cursor: pointer;
    opacity: 1;
}

.qr-info {
    overflow: hidden;
}

.my-link-container {
    margin-bottom: 10px;
}

.link-text {
    font-size: 12px;
    color: var(--text-color);
    background-color:var(--secondary-color);
    padding: 5px 10px;
    border-radius: 3px;
    word-break: break-all;
    position: relative;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    clear: both;
}

.info-label {
    color: var(--text-color);
    font-size: 13px;
}

.info-value {
    color: var(--text-color);
    font-size: 13px;
    font-weight: bold;
}

.fa-copy {
    color: var(--text-color);
    margin-left: 5px;
    cursor: pointer;
}

/* 社交分享栏样式 */
.social-bar {
    background-color: var(--darker-bg-color);
    margin-top: 15px;
    padding: 10px 15px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.social-bar-label {
    color: var(--text-color);
    font-size: 14px;
    margin-right: 10px;
}

.social-icons-row {
    display: flex;
    flex: 1;
    justify-content: flex-start;
    gap: 10px;
}

.social-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
}

.social-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* 有效下属说明样式 */
.valid-section {
    background-color: var(--darker-bg-color);
    margin-top: 15px;
    padding: 15px;
    border-radius: 5px;
}

.valid-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.valid-count {
    font-size: 14px;
    font-weight: bold;
    color: #fff;
}

.valid-question {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.valid-explanation {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 10px;
    text-align: center;
}

.valid-condition {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 10px 15px;
    border-radius: 5px;
}

.condition-label {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
}

.condition-value {
    font-size: 13px;
    color: var(--text-color);
    font-weight: bold;
}

/* 宝箱领取部分样式 */
.treasure-section {
    margin-top: 15px;
    padding-bottom: 20px;
}

.treasure-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 保持每行4个宝箱 */
    gap: 10px;
    margin-bottom: 20px; /* 增加底部间距 */
}

.treasure-box {
    background-color: var(--darker-bg-color);
    border-radius: 5px;
    padding: 8px; /* 减小内边距 */
    text-align: center;
    position: relative;
}

.treasure-box.locked {
    opacity: 0.5;
}

.treasure-box.available {
    opacity: 1;
}

.treasure-box.claimed {
    opacity: 0.8;
}

.claimed-text {
    background-color: #888;
    color: #fff;
    font-size: 11px;
    padding: 2px 0;
    border-radius: 3px;
    margin-top: 3px;
    text-align: center;
}

.treasure-img {
    width: 100%;
    height: 50px; /* 减小高度 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.treasure-img img {
    max-width: 100%;
    max-height: 100%;
}

.treasure-img .fa-gift {
    color: var(--text-color);
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.treasure-info {
    margin-top: 3px; /* 减小上边距 */
}

.people-count {
    font-size: 11px; /* 减小字体大小 */
    color: rgba(255, 255, 255, 0.8);
}

.treasure-price {
    font-size: 13px; /* 减小字体大小 */
    color: var(--text-color);
    font-weight: bold;
}

.claim-btn {
    background-color: var(--secondary-color);
    color: var(--text-color);
    font-size: 11px; /* 减小字体大小 */
    padding: 2px 0; /* 减小内边距 */
    border-radius: 3px;
    margin-top: 3px; /* 减小上边距 */
    cursor: pointer;
}

/* VIP俱乐部页面样式 */
.vip-club-header {
    background-color: var(--darker-bg-color);
    padding: 15px;
    text-align: center;
    margin: 10px 0;
    border-radius: 5px;
}

.vip-club-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-color);
}

.vip-club-subtitle {
    font-size: 14px;
    color: #fff;
    margin-top: 5px;
}

.vip-details {
    background-color: var(--darker-bg-color);
    padding: 15px;
    margin-top: 15px;
    border-radius: 5px;
}

.vip-details-title {
    font-size: 16px;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 10px;
}

.vip-description p {
    font-size: 13px;
    color: #fff;
    margin-bottom: 8px;
    line-height: 1.4;
}

.vip-table-container {
    margin-top: 15px;
    overflow-x: auto;
}

.vip-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--darker-bg-color);
    border-radius: 5px;
}

.vip-table th, .vip-table td {
    padding: 8px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 12px;
}

.vip-table th {
    background-color: rgba(0, 0, 0, 0.2);
    color: var(--text-color);
    font-weight: bold;
}

.vip-table td {
    color: #fff;
}

.vip-example {
    background-color: var(--darker-bg-color);
    padding: 15px;
    margin-top: 15px;
    border-radius: 5px;
}

.example-title {
    font-size: 14px;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 5px;
}

.example-text {
    font-size: 13px;
    color: #fff;
    line-height: 1.4;
}

.vip-claim-section {
    background-color: var(--darker-bg-color);
    padding: 15px;
    margin-top: 15px;
    border-radius: 5px;
}

.claim-title {
    font-size: 14px;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 5px;
    margin-top: 10px;
}

.claim-title:first-child {
    margin-top: 0;
}

.claim-text {
    font-size: 13px;
    color: #fff;
    line-height: 1.4;
    margin-bottom: 10px;
}

.vip-rules {
    background-color: var(--darker-bg-color);
    padding: 15px;
    margin-top: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.rules-title {
    font-size: 14px;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 10px;
}

.rule-item {
    font-size: 13px;
    color: #fff;
    margin-bottom: 8px;
    line-height: 1.4;
}

/* 存款奖励页面样式 */
.deposit-bonus-header {
    background-color: var(--darker-bg-color);
    padding: 15px;
    text-align: center;
    margin: 10px 0;
    border-radius: 5px;
}

.deposit-bonus-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-color);
}

.bonus-section {
    background-color: var(--darker-bg-color);
    padding: 15px;
    margin-top: 15px;
    border-radius: 5px;
}

.bonus-title {
    font-size: 16px;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 15px;
    text-align: center;
}

.bonus-table {
    width: 100%;
    border-radius: 5px;
    overflow: hidden;
}

.bonus-table-header {
    display: flex;
    background-color: rgba(0, 0, 0, 0.2);
}

.bonus-table-row {
    display: flex;
}

.header-cell, .table-cell {
    flex: 1;
    padding: 10px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.header-cell {
    color: var(--text-color);
    font-weight: bold;
    font-size: 14px;
}

.table-cell {
    color: #fff;
    font-size: 14px;
}
</style>

<!-- 添加JavaScript -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 只在current=1时初始化二维码
    if (parseInt({$current}) === 1) {
        var qrcodeElement = document.getElementById("qrcode");
        if (qrcodeElement) {
            var link = "{$link_url}";
            new QRCode(qrcodeElement, {
                text: link,
                width: 128,
                height: 128
            });
        }
    }

    // 返回按钮功能
    const backBtn = document.getElementById('backBtn');
    if (backBtn) {
        backBtn.addEventListener('click', function() {
            window.location.href = '/index/event/index';
        });
    }

    // 历史按钮功能
    const historyBtn = document.querySelector('.history-btn');
    if (historyBtn) {
        historyBtn.addEventListener('click', function() {
            Toast.info('{:__("processing")}');
        });
    }

    // 只在current=1时初始化复制和分享功能
    if (parseInt({$current}) === 1) {
        // 复制链接功能
        const copyLinkBtn = document.getElementById('copyLinkBtn');
        if (copyLinkBtn) {
            copyLinkBtn.addEventListener('click', function() {
                const linkText = '{$link_url}';
                copyToClipboard(linkText, function() {
                    Toast.success('{:__("copy_success")}');
                });
            });
        }

        // 复制邀请码功能
        const copyCodeBtn = document.getElementById('copyCodeBtn');
        if (copyCodeBtn) {
            copyCodeBtn.addEventListener('click', function() {
                const codeText = '{$id_code}';
                copyToClipboard(codeText, function() {
                    Toast.success('{:__("copy_success")}');
                });
            });
        }

        // 社交分享功能
        const socialIcons = document.querySelectorAll('.social-icon');
        socialIcons.forEach(icon => {
            icon.addEventListener('click', function() {
                const platform = this.getAttribute('data-platform');
                const shareUrl = '{$link_url}';
                let shareWindow = null;

                switch(platform) {
                    case 'telegram':
                        shareWindow = window.open(`https://t.me/share/url?url=${encodeURIComponent(shareUrl)}`, '_blank');
                        break;
                    case 'facebook':
                        shareWindow = window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`, '_blank');
                        break;
                    case 'whatsapp':
                        shareWindow = window.open(`https://api.whatsapp.com/send?text=${encodeURIComponent(shareUrl)}`, '_blank');
                        break;
                    case 'instagram':
                    case 'youtube':
                    case 'tiktok':
                    default:
                        Toast.info('{:__("processing")}');
                        break;
                }

                if (shareWindow) {
                    setTimeout(() => shareWindow.focus(), 500);
                }
            });
        });
    }

    // 只在current=1时初始化宝箱领取功能
    if (parseInt({$current}) === 1) {
        const claimBtns = document.querySelectorAll('.claim-btn');
        if (claimBtns && claimBtns.length > 0) {
            claimBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const treasureBox = this.closest('.treasure-box');
                    const treasureBoxId = treasureBox.getAttribute('data-id');

                    // 如果treasureBoxId未定义，尝试从其他属性获取
                    if (!treasureBoxId) {
                        console.error('Treasure box ID is undefined');
                        Toast.error('{:__("Invalid treasure box")}');
                        return;
                    }

                    // 禁用按钮，防止重复点击
                    this.disabled = true;
                    this.style.opacity = '0.7';
                    this.textContent = '{:__("Processing")}';

                    // 使用fetch API发送请求领取奖励
                    fetch('/index/event/claimTreasureBox', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: new URLSearchParams({
                            treasure_box_id: treasureBoxId
                        })
                    })
                    .then(response => response.json())
                    .then(res => {
                        if (res.code === 1) {
                            // 领取成功
                            Toast.success(res.msg);

                            // 更新宝箱状态
                            treasureBox.classList.remove('available');
                            treasureBox.classList.add('claimed');
                            treasureBox.setAttribute('data-status', 'claimed');

                            // 更新宝箱图片
                            const imgElement = treasureBox.querySelector('.treasure-img img');
                            if (imgElement) {
                                imgElement.src = '/assets/img/frontend/common/Box3.png';
                            }

                            // 移除领取按钮，添加已领取文本
                            btn.remove();
                            const claimedText = document.createElement('div');
                            claimedText.className = 'claimed-text';
                            claimedText.textContent = '{:__("Claimed")}';
                            treasureBox.appendChild(claimedText);

                            // // 如果返回了新的奖励余额，可以更新显示
                            // if (res.data && res.data.new_reward_balance) {
                            //     // 如果页面上有显示奖励余额的元素，可以在这里更新
                            //     const rewardBalanceEl = document.querySelector('.reward-balance');
                            //     if (rewardBalanceEl) {
                            //         rewardBalanceEl.textContent = res.data.new_reward_balance;
                            //     }
                            // }
                        } else {
                            // 领取失败
                            Toast.error(res.msg);

                            // 恢复按钮状态
                            btn.disabled = false;
                            btn.style.opacity = '1';
                            btn.textContent = '{:__("claim_treasure")}';

                            // 如果是"记录不存在"错误，可能需要刷新页面
                            if (res.msg.includes('not found')) {
                                Toast.info('{:__("Refreshing page...")}');
                                setTimeout(() => {
                                    location.reload();
                                }, 1500);
                            }
                        }
                    })
                    .catch(error => {
                        // 请求错误
                        console.error('Error:', error);
                        Toast.error('{:__("Network error, please try again")}');

                        // 恢复按钮状态
                        btn.disabled = false;
                        btn.style.opacity = '1';
                        btn.textContent = '{:__("claim_treasure")}';
                    });
                });
            });
        }
    }

    // 复制到剪贴板的通用函数
    function copyToClipboard(text, callback) {
        // 创建临时输入框
        const tempInput = document.createElement('input');
        tempInput.value = text;
        document.body.appendChild(tempInput);

        // 选择并复制内容
        tempInput.select();
        document.execCommand('copy');

        // 移除临时输入框
        document.body.removeChild(tempInput);

        // 执行回调
        if (typeof callback === 'function') {
            callback();
        }
    }
});
</script>
