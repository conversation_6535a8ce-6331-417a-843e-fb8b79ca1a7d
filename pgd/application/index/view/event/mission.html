<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Tasks')}</title>
    
    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}
    
    <!-- 引入活动页面通用CSS和任务页面专用CSS -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/event/event.css?v=1.02">
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/event/event_mission.css?v=1.01">
</head>
<body>
    <div class="container">        
        <div class="horizontal-tabs">
            <div class="tab" data-tab="eventos" data-translation-key="Events">{:__('Events')}</div>
            <div class="tab active" data-tab="missao" data-translation-key="Tasks">{:__('Tasks')}</div>
            <div class="tab" data-tab="vip" data-translation-key="VIP">VIP</div>
            <div class="tab" data-tab="rebate" data-translation-key="Rebate">{:__('Rebate')}</div>
            <div class="tab" data-tab="pendente" data-translation-key="Pending">{:__('Pending')}</div>
            <div class="tab" data-tab="history" data-translation-key="History">{:__('History')}</div>
        </div>
        
        <div class="main-content">
            {notempty name="missions"}
                <div class="mission-container">
                    {foreach $missions as $mission}
                        <div class="mission-item">
                            <div class="mission-header">
                                <div class="mission-title">{$mission.title}</div>
                                <div class="mission-reward" data-translation-key="Reward">{:__('Reward')}: {$mission.reward}</div>
                            </div>
                            <div class="mission-description">{$mission.description}</div>
                            <div class="mission-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" data-progress="{$mission.progress|default=0}"></div>
                                </div>
                                <div class="progress-text">
                                    <span data-translation-key="Progress">{:__('Progress')}: {$mission.current|default=0}/{$mission.target|default=0}</span>
                                    <span>{$mission.progress|default=0}%</span>
                                </div>
                </div>
                            {if $mission.completed}
                                {if $mission.claimed}
                                    <button class="mission-button completed" disabled data-translation-key="Claimed">{:__('Claimed')}</button>
                                {else}
                                    <button class="mission-button" data-id="{$mission.id}" data-translation-key="Claim Reward">{:__('Claim Reward')}</button>
                                {/if}
                            {else}
                                <button class="mission-button disabled" disabled data-translation-key="Incomplete">{:__('Incomplete')}</button>
                            {/if}
            </div>
                    {/foreach}
                </div>
            {else}
                <div class="empty-state">
                    <img src="__CDN__/assets/img/frontend/common/empty.png" alt="{:__('No missions available')}" class="empty-image">
                    <div class="empty-text" data-translation-key="No missions available">{:__('No missions available')}</div>
                </div>
            {/notempty}
        </div>
        
        {include file="common/bottom_nav" /}
    </div>
    
    <!-- 引入活动页面专用JS -->
    <script src="__CDN__/assets/js/frontend/event.js?v=1.01"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
        // 设置进度条宽度
        const progressBars = document.querySelectorAll('.progress-fill[data-progress]');
        progressBars.forEach(function(bar) {
            const progress = bar.getAttribute('data-progress');
            if (progress) {
                bar.style.width = progress + '%';
            }
        });
        
        // 任务奖励领取按钮点击事件
        const rewardButtons = document.querySelectorAll('.mission-button:not(.disabled):not(.completed)');
        rewardButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const missionId = this.getAttribute('data-id');
                if (!missionId) return;
                
                // 这里可以添加AJAX请求领取奖励的代码
                console.log('Claiming reward for mission:', missionId);
                
                // 模拟成功领取
                this.classList.add('completed');
                this.textContent = window.__ ? window.__('Claimed') : 'Claimed';
                this.disabled = true;
                
                // 显示成功消息
                Toast.success(window.__ ? window.__('Reward claimed successfully') : 'Reward claimed successfully');
                });
            });
        });
    </script>
</body>
</html> 