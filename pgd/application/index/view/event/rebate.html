<!DOCTYPE html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Rebate')}</title>
    
    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}
    
    <!-- 引入活动页面专用CSS -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/event/event.css?v=1.02">
</head>
<body>
    <div class="container">        
        <div class="horizontal-tabs">
            <div class="tab" data-tab="eventos" data-translation-key="Events">{:__('Events')}</div>
            <div class="tab" data-tab="missao" data-translation-key="Tasks">{:__('Tasks')}</div>
            <div class="tab" data-tab="vip" data-translation-key="VIP">VIP</div>
            <div class="tab active" data-tab="rebate" data-translation-key="Rebate">{:__('Rebate')}</div>
            <div class="tab" data-tab="pendente" data-translation-key="Pending">{:__('Pending')}</div>
            <div class="tab" data-tab="history" data-translation-key="History">{:__('History')}</div>
        </div>
        
        <div class="main-content">
            {notempty name="rebates"}
                <!-- 返利信息展示 -->
                <div class="rebate-info">
                    <h2 data-translation-key="Your Rebate">{:__('Your Rebate')}</h2>
                    <div class="rebate-data">
                        <div class="rebate-amount">
                            <span class="label" data-translation-key="Available">{:__('Available')}</span>
                            <span class="value">R$ 125.50</span>
                        </div>
                        <button class="rebate-button" data-translation-key="Claim now">{:__('Claim now')}</button>
                    </div>
                </div>
                
                <!-- 返利统计信息 -->
                <div class="rebate-stats">
                    <div class="stat-item">
                        <div class="stat-value">R$ 1,280.35</div>
                        <div class="stat-label" data-translation-key="Total Rebates">{:__('Total Rebates')}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">R$ 235.75</div>
                        <div class="stat-label" data-translation-key="Last Month">{:__('Last Month')}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">1.5%</div>
                        <div class="stat-label" data-translation-key="Your Rate">{:__('Your Rate')}</div>
                    </div>
                </div>
                
                <!-- 返利说明 -->
                <div class="rebate-explanation">
                    <h3 class="section-title" data-translation-key="How Rebates Work">{:__('How Rebates Work')}</h3>
                    <div class="explanation-card">
                        <p data-translation-key="Rebate explanation text 1">{:__('Rebate explanation text 1')}</p>
                        <p data-translation-key="Rebate explanation text 2">{:__('Rebate explanation text 2')}</p>
                        <p data-translation-key="Rebate explanation text 3">{:__('Rebate explanation text 3')}</p>
                    </div>
                </div>
                
                <!-- 返利历史 -->
                <div class="rebate-history">
                    <h3 class="section-title" data-translation-key="Recent Rebates">{:__('Recent Rebates')}</h3>
                    <div class="rebate-list">
                        <div class="rebate-item">
                            <div class="rebate-date">15/06/2023</div>
                            <div class="rebate-game" data-translation-key="Game">{:__('Game')}: Aviator</div>
                            <div class="rebate-amount">R$ 25.50</div>
                        </div>
                        <div class="rebate-item">
                            <div class="rebate-date">12/06/2023</div>
                            <div class="rebate-game" data-translation-key="Game">{:__('Game')}: Fortune Tiger</div>
                            <div class="rebate-amount">R$ 42.75</div>
                        </div>
                        <div class="rebate-item">
                            <div class="rebate-date">10/06/2023</div>
                            <div class="rebate-game" data-translation-key="Game">{:__('Game')}: Mines</div>
                            <div class="rebate-amount">R$ 18.30</div>
                        </div>
                        <div class="rebate-item">
                            <div class="rebate-date">08/06/2023</div>
                            <div class="rebate-game" data-translation-key="Game">{:__('Game')}: Spaceman</div>
                            <div class="rebate-amount">R$ 39.25</div>
                        </div>
                    </div>
                    
                    <button class="view-all-button" data-translation-key="View all rebates">{:__('View all rebates')}</button>
                </div>
                
                <!-- 返利等级 -->
                <div class="rebate-levels">
                    <h3 class="section-title" data-translation-key="Rebate Rates">{:__('Rebate Rates')}</h3>
                    <div class="level-table">
                        <div class="level-row header">
                            <div class="level-cell" data-translation-key="Level">{:__('Level')}</div>
                            <div class="level-cell" data-translation-key="Wager">{:__('Wager')}</div>
                            <div class="level-cell" data-translation-key="Rate">{:__('Rate')}</div>
                        </div>
                        <div class="level-row">
                            <div class="level-cell">1</div>
                            <div class="level-cell">R$ 0 - 1,000</div>
                            <div class="level-cell">0.5%</div>
                        </div>
                        <div class="level-row active">
                            <div class="level-cell">2</div>
                            <div class="level-cell">R$ 1,000 - 5,000</div>
                            <div class="level-cell">1.0%</div>
                        </div>
                        <div class="level-row">
                            <div class="level-cell">3</div>
                            <div class="level-cell">R$ 5,000 - 10,000</div>
                            <div class="level-cell">1.5%</div>
                        </div>
                        <div class="level-row">
                            <div class="level-cell">4</div>
                            <div class="level-cell">R$ 10,000+</div>
                            <div class="level-cell">2.0%</div>
                        </div>
                    </div>
                </div>
            {else}
                <div class="empty-state">
                    <img src="__CDN__/assets/img/frontend/common/empty.png" alt="{:__('No rebates available')}" class="empty-image">
                    <div class="empty-text" data-translation-key="No rebates available">{:__('No rebates available')}</div>
                </div>
            {/notempty}
        </div>
        
        {include file="common/bottom_nav" /}
    </div>

    <!-- 引入活动页面专用JS -->
    <script src="__CDN__/assets/js/frontend/event.js?v=1.01"></script>
</body>
</html> 