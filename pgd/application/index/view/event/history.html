<!DOCTYPE html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Activity History')}</title>
    
    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}
    
    <!-- 引入活动页面通用CSS和历史页面专用CSS -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/event/event.css?v=1.02">
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/event/event_history.css?v=1.01">
</head>
<body>
    <div class="container">        
        <div class="horizontal-tabs">
            <div class="tab" data-tab="eventos" data-translation-key="Events">{:__('Events')}</div>
            <div class="tab" data-tab="missao" data-translation-key="Tasks">{:__('Tasks')}</div>
            <div class="tab" data-tab="vip" data-translation-key="VIP">VIP</div>
            <div class="tab" data-tab="rebate" data-translation-key="Rebate">{:__('Rebate')}</div>
            <div class="tab" data-tab="pendente" data-translation-key="Pending">{:__('Pending')}</div>
            <div class="tab active" data-tab="history" data-translation-key="History">{:__('History')}</div>
        </div>
        
        <!-- 筛选栏 -->
        <div class="filter-container">
            <div class="filter-section">
                <div class="filter-dropdown date-filter">
                    <div class="filter-btn" id="dateFilterBtn">
                        <span class="filter-text" data-translation-key="Today">{:__('Today')}</span>
                        <span class="arrow">▼</span>
                    </div>
                    <div class="filter-menu" id="dateFilterMenu">
                        <div class="filter-option active" data-value="today" data-translation-key="Today">{:__('Today')}</div>
                        <div class="filter-option" data-value="yesterday" data-translation-key="Yesterday">{:__('Yesterday')}</div>
                        <div class="filter-option" data-value="7days" data-translation-key="Last 7 Days">{:__('Last 7 Days')}</div>
                        <div class="filter-option" data-value="15days" data-translation-key="Last 15 Days">{:__('Last 15 Days')}</div>
                        <div class="filter-option" data-value="30days" data-translation-key="Last 30 Days">{:__('Last 30 Days')}</div>
                        <div class="filter-option" data-value="all" data-translation-key="All">{:__('All')}</div>
                    </div>
                </div>
                
                <div class="filter-dropdown type-filter">
                    <div class="filter-btn" id="typeFilterBtn">
                        <span class="filter-text" data-translation-key="All">{:__('All')}</span>
                        <span class="arrow">▼</span>
                    </div>
                    <div class="filter-menu" id="typeFilterMenu">
                        <div class="filter-option active" data-value="all" data-translation-key="All">{:__('All')}</div>
                        <div class="filter-option" data-value="claimed" data-translation-key="Claimed">{:__('Claimed')}</div>
                        <div class="filter-option" data-value="distributed" data-translation-key="Distributed">{:__('Distributed')}</div>
                    </div>
                </div>
            </div>
            
            <div class="bonus-display">
                <span class="bonus-label" data-translation-key="Bonus">{:__('Bonus')}</span>
                <span class="bonus-value">0,00</span>
            </div>
        </div>
        
        <div class="main-content">
            {notempty name="history_records"}
                <div class="history-container">
                    <h2 class="history-title" data-translation-key="Activity History">{:__('Activity History')}</h2>
                    
                    <!-- 统计数据 -->
                    <div class="history-stats">
                        <div class="history-stat-item">
                            <div class="history-stat-value">42</div>
                            <div class="history-stat-label" data-translation-key="Total Activities">{:__('Total Activities')}</div>
                        </div>
                        <div class="history-stat-item">
                            <div class="history-stat-value">R$ 3,754.50</div>
                            <div class="history-stat-label" data-translation-key="Total Rewards">{:__('Total Rewards')}</div>
                        </div>
                        <div class="history-stat-item">
                            <div class="history-stat-value">28</div>
                            <div class="history-stat-label" data-translation-key="Completed">{:__('Completed')}</div>
                        </div>
                    </div>
                    
                    <!-- 历史记录列表 -->
                    <div class="history-list">
                        <div class="history-item" data-date="2023-11-15" data-type="reward">
                            <div class="history-item-badge completed" data-translation-key="Completed">{:__('Completed')}</div>
                            <div class="history-item-header">
                                <div class="history-item-title" data-translation-key="First Deposit Bonus">{:__('First Deposit Bonus')}</div>
                                <div class="history-item-amount">R$ 100.00</div>
                            </div>
                            <div class="history-item-details">
                                <div data-translation-key="Type">{:__('Type')}: {:__('Reward')}</div>
                                <div data-translation-key="ID">{:__('ID')}: #1285764</div>
                            </div>
                            <div class="history-item-date">15/11/2023 14:30</div>
                        </div>
                        
                        <div class="history-item" data-date="2023-11-14" data-type="mission">
                            <div class="history-item-badge completed" data-translation-key="Completed">{:__('Completed')}</div>
                            <div class="history-item-header">
                                <div class="history-item-title" data-translation-key="Daily Login Reward">{:__('Daily Login Reward')}</div>
                                <div class="history-item-amount">R$ 5.00</div>
                            </div>
                            <div class="history-item-details">
                                <div data-translation-key="Type">{:__('Type')}: {:__('Mission')}</div>
                                <div data-translation-key="ID">{:__('ID')}: #1285623</div>
                            </div>
                            <div class="history-item-date">14/11/2023 09:15</div>
                        </div>
                        
                        <div class="history-item" data-date="2023-11-10" data-type="event">
                            <div class="history-item-badge rejected" data-translation-key="Expired">{:__('Expired')}</div>
                            <div class="history-item-header">
                                <div class="history-item-title" data-translation-key="Weekly Tournament">{:__('Weekly Tournament')}</div>
                                <div class="history-item-amount">--</div>
                            </div>
                            <div class="history-item-details">
                                <div data-translation-key="Type">{:__('Type')}: {:__('Event')}</div>
                                <div data-translation-key="ID">{:__('ID')}: #1284503</div>
                            </div>
                            <div class="history-item-date">10/11/2023 23:59</div>
                        </div>
                        
                        <div class="history-item" data-date="2023-11-08" data-type="reward">
                            <div class="history-item-badge completed" data-translation-key="Completed">{:__('Completed')}</div>
                            <div class="history-item-header">
                                <div class="history-item-title" data-translation-key="Referral Bonus">{:__('Referral Bonus')}</div>
                                <div class="history-item-amount">R$ 50.00</div>
                            </div>
                            <div class="history-item-details">
                                <div data-translation-key="Type">{:__('Type')}: {:__('Reward')}</div>
                                <div data-translation-key="ID">{:__('ID')}: #1283741</div>
                            </div>
                            <div class="history-item-date">08/11/2023 16:45</div>
                        </div>
                        
                        <div class="history-item" data-date="2023-11-05" data-type="reward">
                            <div class="history-item-badge completed" data-translation-key="Completed">{:__('Completed')}</div>
                            <div class="history-item-header">
                                <div class="history-item-title" data-translation-key="VIP Level Up Bonus">{:__('VIP Level Up Bonus')}</div>
                                <div class="history-item-amount">R$ 200.00</div>
                            </div>
                            <div class="history-item-details">
                                <div data-translation-key="Type">{:__('Type')}: {:__('Reward')}</div>
                                <div data-translation-key="ID">{:__('ID')}: #1283654</div>
                            </div>
                            <div class="history-item-date">05/11/2023 11:20</div>
                        </div>
                    </div>
                    
                    <!-- 加载更多按钮 -->
                    <button class="load-more-btn" data-translation-key="Load More">{:__('Load More')}</button>
                </div>
            {else}
                <div class="empty-state">
                    <img src="__CDN__/assets/img/frontend/common/empty.png" alt="{:__('No history records')}" class="empty-image">
                    <div class="empty-text" data-translation-key="No history records">{:__('No history records')}</div>
                </div>
            {/notempty}
        </div>
        
        {include file="common/bottom_nav" /}
    </div>

    <!-- 引入活动页面专用JS -->
    <script src="__CDN__/assets/js/frontend/event.js?v=1.01"></script>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 下拉菜单切换
        function setupDropdown(btnId, menuId) {
            const btn = document.getElementById(btnId);
            const menu = document.getElementById(menuId);
            
            btn.addEventListener('click', function() {
                // 切换当前菜单显示状态
                const isOpen = btn.classList.contains('active');
                
                // 关闭所有下拉菜单
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.filter-menu').forEach(m => m.classList.remove('show'));
                
                // 如果当前菜单是关闭的，则打开它
                if (!isOpen) {
                    btn.classList.add('active');
                    menu.classList.add('show');
                }
            });
            
            // 点击选项时设置按钮文本并触发筛选
            menu.querySelectorAll('.filter-option').forEach(option => {
                option.addEventListener('click', function() {
                    // 更新选中状态
                    menu.querySelectorAll('.filter-option').forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 更新按钮文本
                    btn.querySelector('.filter-text').textContent = this.textContent;
                    
                    // 关闭下拉菜单
                    btn.classList.remove('active');
                    menu.classList.remove('show');
                    
                    // 应用筛选
                    applyFilters();
                });
            });
            
            // 点击其他地方关闭下拉菜单
            document.addEventListener('click', function(e) {
                if (!btn.contains(e.target) && !menu.contains(e.target)) {
                    btn.classList.remove('active');
                    menu.classList.remove('show');
                }
            });
        }
        
        // 设置两个下拉菜单
        setupDropdown('dateFilterBtn', 'dateFilterMenu');
        setupDropdown('typeFilterBtn', 'typeFilterMenu');
        
        // 应用筛选
        function applyFilters() {
            const dateFilterValue = document.querySelector('#dateFilterMenu .filter-option.active').getAttribute('data-value');
            const typeFilterValue = document.querySelector('#typeFilterMenu .filter-option.active').getAttribute('data-value');
            
            const historyItems = document.querySelectorAll('.history-item');
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            historyItems.forEach(item => {
                // 日期筛选
                let passDateFilter = true;
                const itemDate = new Date(item.getAttribute('data-date'));
                
                switch (dateFilterValue) {
                    case 'today':
                        const todayEnd = new Date(today);
                        todayEnd.setHours(23, 59, 59);
                        passDateFilter = itemDate >= today && itemDate <= todayEnd;
                        break;
                    case 'yesterday':
                        const yesterday = new Date(today);
                        yesterday.setDate(yesterday.getDate() - 1);
                        const yesterdayEnd = new Date(yesterday);
                        yesterdayEnd.setHours(23, 59, 59);
                        passDateFilter = itemDate >= yesterday && itemDate <= yesterdayEnd;
                        break;
                    case '7days':
                        const last7Days = new Date(today);
                        last7Days.setDate(last7Days.getDate() - 7);
                        passDateFilter = itemDate >= last7Days;
                        break;
                    case '15days':
                        const last15Days = new Date(today);
                        last15Days.setDate(last15Days.getDate() - 15);
                        passDateFilter = itemDate >= last15Days;
                        break;
                    case '30days':
                        const last30Days = new Date(today);
                        last30Days.setDate(last30Days.getDate() - 30);
                        passDateFilter = itemDate >= last30Days;
                        break;
                    case 'all':
                    default:
                        passDateFilter = true;
                }
                
                // 类型筛选
                let passTypeFilter = true;
                const itemType = item.getAttribute('data-type');
                
                if (typeFilterValue !== 'all') {
                    passTypeFilter = (typeFilterValue === 'claimed' && itemType === 'reward') || 
                                     (typeFilterValue === 'distributed' && itemType === 'mission');
                }
                
                // 显示或隐藏项目
                if (passDateFilter && passTypeFilter) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
            
            // 检查是否有可见的记录
            checkVisibleRecords();
        }
        
        // 检查是否有可见的记录
        function checkVisibleRecords() {
            const visibleRecords = document.querySelectorAll('.history-item[style="display: block;"]');
            const emptyMessage = document.querySelector('.empty-message');
            const historyList = document.querySelector('.history-list');
            
            if (visibleRecords.length === 0 && historyList) {
                // 如果没有可见记录，显示空状态消息
                if (!emptyMessage) {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'empty-message';
                    messageDiv.style.textAlign = 'center';
                    messageDiv.style.padding = '20px';
                    messageDiv.style.color = '#999';
                    messageDiv.setAttribute('data-translation-key', 'No matching records found');
                    messageDiv.textContent = window.__ ? window.__('No matching records found') : '{:__("No matching records found")}';
                    
                    // 确保historyList存在再添加
                    if (historyList) {
                        historyList.appendChild(messageDiv);
                    } else {
                        // 如果找不到historyList，尝试添加到其他可用的容器
                        const mainContent = document.querySelector('.main-content');
                        if (mainContent) {
                            mainContent.appendChild(messageDiv);
                        }
                    }
                }
            } else {
                // 如果有可见记录，移除空状态消息
                if (emptyMessage) {
                    emptyMessage.remove();
                }
            }
        }
        
        // 加载更多按钮点击事件
        const loadMoreBtn = document.querySelector('.load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', function() {
                // 这里应该发送AJAX请求加载更多历史记录
                // 为了演示，我们只显示一个加载中的文本
                this.textContent = '{:__("Loading...")}';
                
                // 模拟加载延迟
                setTimeout(() => {
                    this.textContent = window.__ ? window.__('Load More') : '{:__("Load More")}';
                    Toast.info('{:__("No more records to load")}');
                }, 1500);
            });
        }
    });
    </script>
</body>
</html> 
