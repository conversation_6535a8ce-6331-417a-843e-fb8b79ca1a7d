<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Pending')}</title>
    
    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}
    
    <!-- 引入活动页面通用CSS和待领取页面专用CSS -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/event/event.css?v=1.02">
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/event/event_pendente.css?v=1.01">
</head>
<body>
    <div class="container">        
        <div class="horizontal-tabs">
            <div class="tab" data-tab="eventos" data-translation-key="Events">{:__('Events')}</div>
            <div class="tab" data-tab="missao" data-translation-key="Tasks">{:__('Tasks')}</div>
            <div class="tab" data-tab="vip" data-translation-key="VIP">VIP</div>
            <div class="tab" data-tab="rebate" data-translation-key="Rebate">{:__('Rebate')}</div>
            <div class="tab active" data-tab="pendente" data-translation-key="Pending">{:__('Pending')}</div>
            <div class="tab" data-tab="history" data-translation-key="History">{:__('History')}</div>
        </div>
        
        <div class="main-content">
            {notempty name="pending_rewards"}
                <div class="pending-container">
                    <h2 class="pending-title" data-translation-key="Pending Rewards">{:__('Pending Rewards')}</h2>
                    
                    <!-- 统计数据 -->
                    <div class="pending-stats">
                        <div class="pending-stat-item">
                            <div class="pending-stat-value">3</div>
                            <div class="pending-stat-label" data-translation-key="Available Rewards">{:__('Available Rewards')}</div>
                        </div>
                        <div class="pending-stat-item">
                            <div class="pending-stat-value">R$ 250.00</div>
                            <div class="pending-stat-label" data-translation-key="Total Value">{:__('Total Value')}</div>
                        </div>
                        <div class="pending-stat-item">
                            <div class="pending-stat-value">1</div>
                            <div class="pending-stat-label" data-translation-key="Expiring Soon">{:__('Expiring Soon')}</div>
                        </div>
                    </div>
                    
                    <!-- 待领取奖励列表 -->
                    <div class="pending-reward-list">
                        <div class="pending-reward-item">
                            <div class="pending-reward-header">
                                <div class="pending-reward-title" data-translation-key="First Deposit Bonus">{:__('First Deposit Bonus')}</div>
                                <div class="pending-reward-amount">R$ 100.00</div>
                            </div>
                            <div class="pending-reward-description" data-translation-key="First deposit bonus reward description">
                                {:__('First deposit bonus reward description')}
                            </div>
                            <div class="pending-reward-footer">
                                <div class="pending-reward-expiry" data-translation-key="Expires">{:__('Expires')}: 30/12/2023</div>
                                <button class="pending-reward-claim" data-translation-key="Claim">{:__('Claim')}</button>
                            </div>
                        </div>
                        
                        <div class="pending-reward-item">
                            <div class="pending-reward-header">
                                <div class="pending-reward-title" data-translation-key="Daily Login Reward">{:__('Daily Login Reward')}</div>
                                <div class="pending-reward-amount">R$ 50.00</div>
                            </div>
                            <div class="pending-reward-description" data-translation-key="Daily login reward description">
                                {:__('Daily login reward description')}
                            </div>
                            <div class="pending-reward-footer">
                                <div class="pending-reward-expiry" data-translation-key="Expires">{:__('Expires')}: 15/12/2023</div>
                                <button class="pending-reward-claim" data-translation-key="Claim">{:__('Claim')}</button>
                            </div>
                        </div>
                        
                        <div class="pending-reward-item">
                            <div class="pending-reward-header">
                                <div class="pending-reward-title" data-translation-key="Referral Bonus">{:__('Referral Bonus')}</div>
                                <div class="pending-reward-amount">R$ 100.00</div>
                            </div>
                            <div class="pending-reward-description" data-translation-key="Referral bonus description">
                                {:__('Referral bonus description')}
                            </div>
                            <div class="pending-reward-footer">
                                <div class="pending-reward-expiry" data-translation-key="Expires">{:__('Expires')}: 05/01/2024</div>
                                <button class="pending-reward-claim" data-translation-key="Claim">{:__('Claim')}</button>
                            </div>
                        </div>
                    </div>
                </div>
            {else}
                <div class="empty-state">
                    <img src="__CDN__/assets/img/frontend/common/empty.png" alt="{:__('No pending rewards')}" class="empty-image">
                    <div class="empty-text" data-translation-key="No pending rewards">{:__('No pending rewards')}</div>
                </div>
            {/notempty}
        </div>
        
        {include file="common/bottom_nav" /}
    </div>

    <!-- 引入活动页面专用JS -->
    <script src="__CDN__/assets/js/frontend/event.js?v=1.01"></script>
</body>
</html> 