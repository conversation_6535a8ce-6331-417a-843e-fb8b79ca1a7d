<!DOCTYPE html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Events')}</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}

    <!-- 引入活动页面专用CSS -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/event/event.css?v=1.02">
    <script>
        // 使用常量存储模板变量值
        const loginStatus = {json_encode};
        // 初始化全局APP对象
        window.APP = {
            isLoggedIn: loginStatus
        };
    </script>
</head>
<body>
    <div class="container">
        <div class="horizontal-tabs">
            <div class="tab active" data-tab="eventos" data-translation-key="Events">{:__('Events')}</div>
            <div class="tab" data-tab="missao" data-translation-key="Tasks">{:__('Tasks')}</div>
            <div class="tab" data-tab="vip" data-translation-key="VIP">VIP</div>
            <div class="tab" data-tab="rebate" data-translation-key="Rebate">{:__('Rebate')}</div>
            <div class="tab" data-tab="pendente" data-translation-key="Pending">{:__('Pending')}</div>
            <div class="tab" data-tab="history" data-translation-key="History">{:__('History')}</div>
        </div>

        <div class="main-content">
            <div class="event-container">
                <!-- 左侧黄色按钮 -->
                <div class="event-sidebar">
                    <button class="event-sidebar-btn active" data-filter="all">{:__('All')}</button>
                    <button class="event-sidebar-btn" data-filter="history" data-tab-target="history" onclick="switchTab('history')">{:__('History')}</button>
                </div>

                <!-- 右侧卡片列表 -->
                <div class="event-content">
                    {notempty name="banners"}
                        {foreach name="banners" item="banner"}
                            <div class="event-card" onclick="location.href='{$banner.link}'">
                                <div class="event-image" style="background-image: url('{$banner.image}');">
                        </div>
                    </div>
                        {/foreach}
                    {else}
                        <div class="empty-state">
                            <img src="__CDN__/assets/img/frontend/common/empty.png" alt="No Data" class="empty-icon">
                            <div class="empty-text" data-translation-key="No events available">{:__('No events available')}</div>
                        </div>
                    {/notempty}
                </div>
            </div>
        </div>

        {include file="common/bottom_nav" /}
    </div>

    <!-- 引入活动页面专用JS -->
    <script src="__CDN__/assets/js/frontend/event.js?v=1.01"></script>

    <!-- 在页面底部添加登录检查脚本 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 获取登录状态
        const isLoggedIn = window.APP.isLoggedIn;
        
        // 初始化标签点击事件
        initTabsWithLoginCheck(isLoggedIn);
        
        // 初始化底部导航登录检查
        initBottomNavLoginCheck(isLoggedIn);
    });

    /**
     * 初始化标签点击事件，包含登录检查
     */
    function initTabsWithLoginCheck(isLoggedIn) {
        const tabs = document.querySelectorAll('.horizontal-tabs .tab');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const tabId = this.getAttribute('data-tab');
                
                // 如果是当前活动标签，不做任何操作
                if (this.classList.contains('active')) {
                    return;
                }
                
                // 如果是index标签，直接跳转
                if (tabId === 'eventos') {
                    window.location.href = '/index/event/index';
                    return;
                }
                
                // 其他标签需要登录检查
                if (!isLoggedIn) {
                    // 显示登录提示
                    showLoginRequiredToast();
                    return;
                }
                
                // 已登录，跳转到相应页面
                const tabMapping = {
                    'missao': '/index/event/mission',
                    'vip': '/index/event/vip',
                    'rebate': '/index/event/rebate',
                    'pendente': '/index/event/pendente',
                    'history': '/index/event/history'
                };
                
                if (tabMapping[tabId]) {
                    window.location.href = tabMapping[tabId];
                }
            });
        });
    }

    /**
     * 初始化底部导航登录检查
     */
    function initBottomNavLoginCheck(isLoggedIn) {
        // 获取底部导航中的agent按钮
        const agentNavItem = document.querySelector('.bottom-nav .nav-item[data-page="agent"]');
        
        if (agentNavItem) {
            // 移除原有的点击事件
            const newAgentNavItem = agentNavItem.cloneNode(true);
            agentNavItem.parentNode.replaceChild(newAgentNavItem, agentNavItem);
            
            // 添加新的点击事件，包含登录检查
            newAgentNavItem.addEventListener('click', function(e) {
                // 如果已经是活动页面，不做任何操作
                if (this.classList.contains('active')) {
                    return;
                }
                
                // 检查是否已登录
                if (!isLoggedIn) {
                    // 显示登录提示
                    showLoginRequiredToast();
                    return;
                }
                
                // 已登录，跳转到代理页面
                window.location.href = '/index/agent/index';
            });
        }
    }

    /**
     * 显示需要登录的提示
     */
    function showLoginRequiredToast() {
        // 使用网站的Toast组件显示提示
        if (typeof Toast !== 'undefined') {
            Toast.warning('{:__("Please login first")}');
        } else {
            console.log('{:__("Please login first")}');
        }
        
        // 可选：延迟后显示登录弹窗
        setTimeout(function() {
            if (window.authPopup && typeof window.authPopup.show === 'function') {
                window.authPopup.show('login');
            } else {
                window.location.href = '/?login=1';
            }
        }, 1500);
    }
    </script>
</body>
</html>
