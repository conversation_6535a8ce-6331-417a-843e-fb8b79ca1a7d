<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>Jogo - <?php echo isset($gamecode) ? $gamecode : ''; ?><?php echo isset($gameid) && !empty($gameid) ? ' (ID:'.$gameid.')' : ''; ?></title>
    <!-- 加载Font Awesome图标库 -->
    <link rel="stylesheet" href="__CDN__/assets/libs/font-awesome/css/font-awesome.min.css">

    <style>
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }
        .game-container {
            width: 100%;
            height: 100%;
            position: relative;
        }
        #game-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            font-size: 16px;
            color: #333;
        }
        .error-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            font-size: 16px;
            color: var(--error-msg-color);
            background-color: var(--secondary-color);
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            max-width: 80%;
        }

        .back-btn {
            /* 基础圆形按钮 */
            top: 20px;
            left: 20px;
            width: 65px;
            height: 65px;
            background: #272a63;
            border-radius: 50%;
            border: 2px solid #fff;
            position: absolute;
            cursor: pointer;
            z-index: 20000;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 8px;
            box-sizing: border-box;
             transform: translateZ(0); /* 触发硬件加速 */
             -webkit-font-smoothing: antialiased; /* 字体抗锯齿 */
        }

        .back-btn i {
            color: white;
            font-size: 30px;
            margin-bottom: 2px;
        }
        .back-btn span {
            color: white;
            font-size: 13px;
            font-weight: bold;
            text-align: center;
            line-height: 1;
            white-space: nowrap;
            margin-top:-5px;
        }

        /* 白色左箭头（通过边框旋转实现） */
        /* .back-btn::before {
            content: "";
            position: absolute;
            left: 50%;
            top: 50%;
            width: 15px;
            height: 15px;
            border-left: 3px solid #fff;
            border-bottom: 3px solid #fff;
            transform: translate(-50%, -50%) rotate(45deg);
        } */



        /* 悬停高亮效果 */
        /* .back-btn:hover {
            background: #333;
            box-shadow: 0 0 10px rgba(255,255,255,0.3);
        } */

        /* 响应式设计 - 手机屏幕适配 */
        /* 超小屏幕手机 (宽度 < 360px) */
        @media (max-width: 359px) {
            .back-btn {
                top: 10px;
                left: 10px;
                width: 45px;
                height: 45px;
                padding: 4px;
            }
            .back-btn i {
                font-size: 18px;
                margin-bottom: 1px;
            }
            .back-btn span {
                font-size: 8px;
            }
        }

        /* 小屏幕手机 (360px - 414px) */
        @media (min-width: 360px) and (max-width: 414px) {
            .back-btn {
                top: 12px;
                left: 12px;
                width: 50px;
                height: 50px;
                padding: 5px;
            }
            .back-btn i {
                font-size: 22px;
                margin-bottom: 1px;
            }
            .back-btn span {
                font-size: 9px;
            }
        }

        /* 中等屏幕手机 (415px - 767px) */
        @media (min-width: 415px) and (max-width: 767px) {
            .back-btn {
                top: 15px;
                left: 15px;
                width: 58px;
                height: 58px;
                padding: 6px;
            }
            .back-btn i {
                font-size: 26px;
                margin-bottom: 2px;
            }
            .back-btn span {
                font-size: 11px;
            }
        }

        /* 平板和大屏幕 (768px+) - 保持原始大小 */
        @media (min-width: 768px) {
            .back-btn {
                top: 20px;
                left: 20px;
                width: 65px;
                height: 65px;
                padding: 8px;
            }
            .back-btn i {
                font-size: 30px;
                margin-bottom: 2px;
            }
            .back-btn span {
                font-size: 13px;
            }
        }

        /* 横屏模式适配 - 针对小屏幕横屏 */
        @media (orientation: landscape) and (max-height: 500px) {
            .back-btn {
                top: 8px;
                left: 8px;
                width: 40px;
                height: 40px;
                padding: 3px;
            }
            .back-btn i {
                font-size: 16px;
                margin-bottom: 1px;
            }
            .back-btn span {
                font-size: 7px;
            }
        }

        /* 超高分辨率屏幕适配 */
        @media (min-width: 1200px) {
            .back-btn {
                top: 25px;
                left: 25px;
                width: 75px;
                height: 75px;
                padding: 10px;
            }
            .back-btn i {
                font-size: 32px;
                margin-bottom: 3px;
            }
            .back-btn span {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <?php if (isset($error_msg) && !empty($error_msg)): ?>
        <div class="error-message" style="display: none;"><?php echo $error_msg; ?></div>
        <?php else: ?>
        <div id="loading" class="loading" style="display: none;">Carregando o jogo, por favor aguarde...</div>
        <iframe id="game-iframe" src="<?php echo isset($game_url) ? $game_url : ''; ?>" frameborder="0" scrolling="no"></iframe>
        <?php endif; ?>
        <!-- 添加返回按钮 -->
        <div class="back-btn" onclick="window.location.href='/'">
            <i class="fa fa-home"></i>
            <span>Lobby</span>
        </div>
    </div>

    <!-- 将PHP条件判断移出JavaScript区域 -->
    <?php if (!isset($error_msg) || empty($error_msg)): ?>
    <script>
        document.getElementById('game-iframe').onload = function() {
            document.getElementById('loading').style.display = 'none';
        };

        // 监听游戏iframe加载失败
        document.getElementById('game-iframe').onerror = function() {
            // document.getElementById('loading').innerHTML = 'Falha ao carregar o jogo, atualize a página e tente novamente';
        };
        console.log('<?php echo isset($error_msg) ? json_encode($error_msg) : "null" ?>');
    </script>
    <?php endif; ?>
</body>
</html>