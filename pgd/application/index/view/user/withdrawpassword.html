<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('withdrawpassword')}</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}
    <!-- 引入Security专用CSS -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/security/security.css?v=1.01">

</head>
<body>
    <div class="container">
        <!-- 引入header部分 -->
       <!-- header部分 -->
    <div class="header">
        <div class="back-btn" id="backBtn" data-url="{:url('')}">←</div>
        <div class="header-title">{$title|default=__('withdrawpassword')}</div>
    </div>
    <!-- 设置提款密码页面 -->
<div class="setup-withdraw-password">
    <div class="setup-description">{:__('setup_withdraw_password_description')}</div>

    <div class="pin-setup-container">
        <div class="pin-label">{:__('enter_new_withdraw_password')}</div>
        <div class="pin-grid" id="newPinGrid">
            <div class="pin-box"></div>
            <div class="pin-box"></div>
            <div class="pin-box"></div>
            <div class="pin-box"></div>
            <div class="pin-box"></div>
            <div class="pin-box"></div>
        </div>

        <div class="pin-label">{:__('confirm_withdraw_password')}</div>
        <div class="pin-grid" id="confirmPinGrid">
            <div class="pin-box"></div>
            <div class="pin-box"></div>
            <div class="pin-box"></div>
            <div class="pin-box"></div>
            <div class="pin-box"></div>
            <div class="pin-box"></div>
        </div>

        <div class="submit-btn" id="setupPasswordBtn">{:__('confirmar')}</div>
    </div>
</div>
<!-- 引入安全中心专用JavaScript -->
    <!-- <script src="__CDN__/assets/js/frontend/security/security.js?v=1.01"></script> -->

    <!-- 引入Header Tabs JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/header_tabs.js?v=1.01"></script>
<!-- 设置密码的JavaScript -->
<script>
    // 定义翻译函数和翻译字典
    var Lang = {
        'please_enter_6_digit_password': '{:__("please_enter_6_digit_password")}',
        'please_confirm_password': '{:__("please_confirm_password")}',
        'passwords_do_not_match': '{:__("passwords_do_not_match")}',
        'processing': '{:__("processing")}',
        'success': '{:__("success")}',
        'error': '{:__("error")}'
    };

    // 翻译函数
    window.__ = function(key) {
        return Lang[key] || key;
    };

    document.addEventListener('DOMContentLoaded', function() {
        // 返回按钮由header_tabs.js处理

        // 初始化PIN输入
        initSetupPinInput();

        // 初始化提交按钮
        initSetupPasswordBtn();

        // 初始化PIN输入功能
        function initSetupPinInput() {
            // 创建新密码的隐藏输入框
            const newPinContainer = document.getElementById('newPinGrid');
            if (!newPinContainer) return;

            const newPinInput = document.createElement('input');
            newPinInput.type = 'password';
            newPinInput.className = 'pin-input-hidden';
            newPinInput.id = 'newPinInput';
            newPinInput.maxLength = 6;
            newPinInput.autocomplete = 'off';
            newPinInput.setAttribute('inputmode', 'numeric');
            newPinContainer.appendChild(newPinInput);

            // 创建确认密码的隐藏输入框
            const confirmPinContainer = document.getElementById('confirmPinGrid');
            if (!confirmPinContainer) return;

            const confirmPinInput = document.createElement('input');
            confirmPinInput.type = 'password';
            confirmPinInput.className = 'pin-input-hidden';
            confirmPinInput.id = 'confirmPinInput';
            confirmPinInput.maxLength = 6;
            confirmPinInput.autocomplete = 'off';
            confirmPinInput.setAttribute('inputmode', 'numeric');
            confirmPinContainer.appendChild(confirmPinInput);

            // 获取所有PIN盒子
            const newPinBoxes = newPinContainer.querySelectorAll('.pin-box');
            const confirmPinBoxes = confirmPinContainer.querySelectorAll('.pin-box');

            // 点击新密码PIN盒子时聚焦隐藏输入框
            newPinBoxes.forEach(box => {
                box.addEventListener('click', function() {
                    newPinInput.focus();
                });
            });

            // 点击确认密码PIN盒子时聚焦隐藏输入框
            confirmPinBoxes.forEach(box => {
                box.addEventListener('click', function() {
                    confirmPinInput.focus();
                });
            });

            // 处理新密码PIN输入
            newPinInput.addEventListener('input', function() {
                // 只保留数字
                this.value = this.value.replace(/\D/g, '');
                const value = this.value;

                // 清空所有PIN盒子
                newPinBoxes.forEach(box => {
                    box.textContent = '';
                });

                // 依次填充PIN盒子
                for (let i = 0; i < value.length; i++) {
                    if (i < newPinBoxes.length) {
                        // 使用 • 代替实际输入的数字，保护隐私
                        newPinBoxes[i].textContent = '•';
                    }
                }

                // 如果输入完成，自动跳到确认密码
                if (value.length === 6) {
                    confirmPinInput.focus();
                }
            });

            // 处理确认密码PIN输入
            confirmPinInput.addEventListener('input', function() {
                // 只保留数字
                this.value = this.value.replace(/\D/g, '');
                const value = this.value;

                // 清空所有PIN盒子
                confirmPinBoxes.forEach(box => {
                    box.textContent = '';
                });

                // 依次填充PIN盒子
                for (let i = 0; i < value.length; i++) {
                    if (i < confirmPinBoxes.length) {
                        // 使用 • 代替实际输入的数字，保护隐私
                        confirmPinBoxes[i].textContent = '•';
                    }
                }
            });

            // 禁止粘贴操作
            newPinInput.addEventListener('paste', function(e) {
                e.preventDefault();
            });
            confirmPinInput.addEventListener('paste', function(e) {
                e.preventDefault();
            });

            // 初始聚焦到新密码输入框
            setTimeout(() => {
                newPinInput.focus();
            }, 500);
        }

        // 初始化提交按钮
        function initSetupPasswordBtn() {
            const setupBtn = document.getElementById('setupPasswordBtn');
            if (!setupBtn) return;

            setupBtn.addEventListener('click', function() {
                const newPin = document.getElementById('newPinInput').value;
                const confirmPin = document.getElementById('confirmPinInput').value;

                // 验证输入
                if (newPin.length < 6) {
                    Toast.error(window.__('please_enter_6_digit_password'));
                    document.getElementById('newPinInput').focus();
                    return;
                }

                if (confirmPin.length < 6) {
                    Toast.error(window.__('please_confirm_password'));
                    document.getElementById('confirmPinInput').focus();
                    return;
                }

                if (newPin !== confirmPin) {
                    Toast.error(window.__('passwords_do_not_match'));
                    document.getElementById('confirmPinInput').value = '';
                    document.getElementById('confirmPinGrid').querySelectorAll('.pin-box').forEach(box => {
                        box.textContent = '';
                    });
                    document.getElementById('confirmPinInput').focus();
                    return;
                }

                // 显示处理中
                Toast.info(window.__('processing'));

                // 禁用提交按钮，防止重复提交
                setupBtn.style.opacity = '0.7';
                setupBtn.style.pointerEvents = 'none';

                // 发送AJAX请求设置密码
                fetch('/index/security/setupWithdrawPassword', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({
                        password: newPin
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // 重新启用提交按钮
                    setupBtn.style.opacity = '1';
                    setupBtn.style.pointerEvents = 'auto';

                    if (data.code === 1) {
                        // 设置成功
                        Toast.success(data.msg || window.__('success'));

                        // 刷新页面
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        // 设置失败
                        Toast.error(data.msg || window.__('error'));
                    }
                })
                .catch(error => {
                    console.error('设置密码失败:', error);

                    // 重新启用提交按钮
                    setupBtn.style.opacity = '1';
                    setupBtn.style.pointerEvents = 'auto';

                    Toast.error(window.__('error'));
                });
            });
        }
    });
</script>

    </div>

    <!-- 引入安全中心专用JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/security.js?v=1.01"></script>
</body>
</html>