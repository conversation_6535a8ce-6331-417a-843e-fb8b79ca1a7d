<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Security')}</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}

    <style>
        /* 安全中心页面特定样式 */
        .container {
            padding-bottom: 20px;
        }

        .header {
            background-color: var(--darker-bg-color);
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            color: var(--text-color);
            position: relative;
        }

        .back-btn {
            font-size: 24px;
            cursor: pointer;
        }

        .header-title {
            font-size: 18px;
            font-weight: bold;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        /* 安全项目列表 */
        .security-list {
            margin-top: 10px;
        }

        .security-item {
            display: flex;
            align-items: center;
            padding: 16px 15px;
            border-bottom: 1px solid var(--gray-blue);
            position: relative;
            text-decoration: none;
        }

        .security-item-left {
            display: flex;
            align-items: center;
        }

        .security-icon {
            width: 24px;
            height: 24px;
            margin-right: 15px;
            color: var(--secondary-color);
        }

        .security-title {
            font-size: 16px;
            color: var(--text-color);
            text-decoration: none;
        }

        .security-value {
            font-size: 14px;
            color: var(--text-color);
            margin-right: 10px;
            margin-left: auto; /* 使其右对齐 */
        }

        .arrow-icon {
            color:  var(--text-color);
            font-size: 24px;
            margin-left: 5px;
            font-weight: bold;
        }

        /* 分组样式 */
        .security-group {
            margin-bottom: 15px;
            margin-left: 16px;
            margin-right: 16px;
            background-color: var(--secondary-color);
            border-radius: 10px;
            overflow: hidden;
        }

        .unlinked {
            color: var(--text-color);
        }

        .linked {
            color: var(--secondary-color);;
        }

        /* 状态样式 */
        .status-defined {
            color: var(--secondary-color);;
        }

        .status-undefined {
            color: var(--text-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="back-btn"><i class="fa fa-arrow-left"></i></div>
            <div class="header-title">{:__('Security')}</div>
        </div>

        <!-- 安全设置列表 -->
        <div class="security-list">
            <!-- 第一组 -->
            <div class="security-group">
                <!-- 账户信息 -->
                <div class="security-item">
                    <div class="security-item-left">
                        <svg class="security-icon" fill="#fff" viewBox="0 0 24 24">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                        <div class="security-title">{:__('Conta do Membro')}</div>
                    </div>
                    <div class="security-value">{$userInfo.username|default='gabiorsi'}</div>
                </div>

                <!-- 手机号 -->
                <div class="security-item">
                    <div class="security-item-left">
                        <svg class="security-icon" fill="#fff" viewBox="0 0 24 24">
                            <path d="M17 1.01L7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM17 19H7V5h10v14z"/>
                        </svg>
                        <div class="security-title">{:__('Telefone Celular')}</div>
                    </div>
                    <div class="security-value">
                        {if condition="!empty($userInfo.mobile)"}
                        +55-{$userInfo.mobile|substr=0,2}****{$userInfo.mobile|substr=-4}
                        {else}
                        +55-
                        {/if}
                    </div>
                </div>
            </div>

            <!-- 第二组 -->
            <div class="security-group">
                <!-- 电子邮件 -->
                <div class="security-item">
                    <div class="security-item-left">
                        <svg class="security-icon" fill="#fff" viewBox="0 0 24 24">
                            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                        </svg>
                        <div class="security-title">{:__('Email')} </div>
                    </div>
                    <div class="security-value unlinked">
                        {:__('Não vinculado')}
                    </div>
                    <div class="arrow-icon">›</div>
                </div>

                <!-- Google身份验证器 -->
                <div class="security-item">
                    <div class="security-item-left">
                        <svg class="security-icon" fill="#fff" viewBox="0 0 24 24">
                            <path d="M12 2L4 5v6.09c0 5.05 3.41 9.76 8 10.91 4.59-1.15 8-5.86 8-10.91V5l-8-3zm3.56 12.44l-3.56 3.55-3.56-3.55c-0.78-0.78-0.78-2.05 0-2.83 0.78-0.78 2.05-0.78 2.83 0L12 12.35l0.73-0.73c0.78-0.78 2.05-0.78 2.83 0 0.78 0.78 0.78 2.05 0 2.83z"/>
                        </svg>
                        <div class="security-title">{:__('Google Authenticator')}</div>
                    </div>
                    <div class="security-value unlinked">
                        {:__('Não vinculado')}
                    </div>
                    <div class="arrow-icon">›</div>
                </div>
            </div>

            <!-- 第三组 -->
            <div class="security-group">
                <!-- 登录密码 -->
                <a href="{:url('index/user/loginpassword')}" class="security-item">
                    <div class="security-item-left">
                        <svg class="security-icon" fill="#fff" viewBox="0 0 24 24">
                            <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/>
                        </svg>
                        <div class="security-title">{:__('Senha de Login')}</div>
                    </div>
                    <div class="arrow-icon">›</div>
                </a>

                <!-- 提款密码 -->
                <a href="{:url('index/user/withdrawpassword')}" class="security-item">
                    <div class="security-item-left">
                        <svg class="security-icon" fill="#fff" viewBox="0 0 24 24">
                            <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
                        </svg>
                        <div class="security-title">{:__('Senha de Retirada')}</div>
                    </div>
                    <div class="security-value status-defined">
                        {:__('Já Definida')}
                    </div>
                    <div class="arrow-icon">›</div>
                </a>

                <!-- 安全问题 -->
                <div class="security-item">
                    <div class="security-item-left">
                        <svg class="security-icon" fill="#fff" viewBox="0 0 24 24">
                            <path d="M11 18h2v-2h-2v2zm1-16C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z"/>
                        </svg>
                        <div class="security-title">{:__('Pergunta de Segurança')}</div>
                    </div>
                    <div class="security-value status-undefined">
                        {:__('Não definida')}
                    </div>
                    <div class="arrow-icon">›</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(function() {
            // 返回按钮点击事件
            // $('.back-btn').on('click', function() {
            //     history.back();
            // });

            // 点击没有链接的项目显示提示
            $('.security-item').each(function() {
                var $item = $(this);
                if (!$item.is('a') && $item.find('.unlinked, .status-undefined').length > 0) {
                    $item.on('click', function() {
                        Toast.info('{:__("This feature is not available yet")}');
                    });
                }
            });
        });
    </script>
</body>
</html>