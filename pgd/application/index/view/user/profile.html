<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Profile')}</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}

    <style>
        /* Profile页面特定样式 */
        .container {
            padding-bottom: 100px;
        }

        .header {
            background-color: var(darker-bg-color);
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            color: #fff;
            position: relative;
        }

        .back-btn {
            font-size: 24px;
            cursor: pointer;
        }

        .header-title {
            font-size: 18px;
            font-weight: bold;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .user-profile-container {
            padding: 20px 15px;
            margin-bottom: 15px;
        }

        .user-profile-header {
            text-align: center;
            position: relative;
            margin-bottom: 20px;
            background-color: var(--secondary-color);
        }

        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .vip-badge {
            position: absolute;
            bottom: 0;
            right: 0;
            background: linear-gradient(135deg, var(--gray-blue), var(--gray-blue));
            color: var(--secondary-color);
            font-weight: bold;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 10px;
            border: 2px solid var(--gray-blue);
        }

        .user-id {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
            color: var(--text-color);
            font-size: 14px;
        }

        .user-id .copy-icon {
            font-size: 16px;
            margin-left: 5px;
            cursor: pointer;
            color: var(--text-color);
        }

        .username {
            font-size: 18px;
            font-weight: bold;
            margin-top: 15px;
            color: #fff;
        }

        .social-links-container {
            margin-top: 30px;
        }

        .social-link-item {
            display: flex;
            align-items: center;
            background-color: var(--secondary-color);
            padding: 15px;
            margin-bottom: 15px;
            border-radius: var(--border-radius);
        }

        .social-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .instagram-icon {
            background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
        }

        .whatsapp-icon {
            background-color: #25D366;
        }

        .facebook-icon {
            background-color: #3b5998;
        }

        .telegram-icon {
            background-color: #0088cc;
        }

        .social-input {
            flex: 1;
        }

        .social-input input {
            width: 100%;
            background: transparent;
            border: none;
            color: #fff;
            font-size: 14px;
            padding: 8px 0;
            outline: none;
        }

        .social-input input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .social-input input:focus {
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        }

        .bottom-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            max-width: 516px;
            margin: 0 auto;
            padding: 15px;
            z-index: 100;
            width: 100%;
            box-sizing: border-box;
        }

        .button {
            flex: 1;
            padding: 10px 15px;
            margin: 0 10px;
            text-align: center;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            border: none;
            font-size: 14px;
        }

        .back-button {
            background-color: var(--darker-bg-color);
            color: var(--text-color);
            border: 1px solid  var(--gray-blue);
        }

        .save-button {
            background-color: var(--secondary-color);
            color: var(--text-color);
        }

        @media (max-width: 516px) {
            .bottom-buttons {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="back-btn"><i class="fa fa-arrow-left"></i></div>
            <div class="header-title">{:__('Profile')}</div>
        </div>

        <!-- 用户信息区域 -->
        <div class="user-profile-container">
            <div class="user-profile-header">
                <div class="user-avatar">
                    <img src="/assets/img/frontend/common/109x112_png.png" alt="User Avatar">
                    {if condition="isset($userInfo.vip_level) && $userInfo.vip_level > 0"}
                    <div class="vip-badge">VIP!</div>
                    {/if}
                </div>
                <div class="username">{$userInfo.username}</div>
                <div class="user-id">
                    ID:{$userInfo.id}
                    <i class="fa fa-copy copy-icon"></i>
                </div>
            </div>

            <div class="social-links-container">
                <!-- Instagram -->
                <div class="social-link-item">
                    <div class="social-icon instagram-icon">
                        <svg fill="#FFFFFF" height="18" width="18" viewBox="0 0 448 512">
                            <path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"/>
                        </svg>
                    </div>
                    <div class="social-input">
                        <input type="text" placeholder="{:__('Por favor, insira a conta do Instagram')}" id="instagram">
                    </div>
                </div>

                <!-- WhatsApp -->
                <div class="social-link-item">
                    <div class="social-icon whatsapp-icon">
                        <svg fill="#FFFFFF" height="18" width="18" viewBox="0 0 448 512">
                            <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/>
                        </svg>
                    </div>
                    <div class="social-input">
                        <input type="text" placeholder="{:__('Por favor, insira o WhatsApp')}" id="whatsapp">
                    </div>
                </div>

                <!-- Facebook -->
                <div class="social-link-item">
                    <div class="social-icon facebook-icon">
                        <svg fill="#FFFFFF" height="18" width="18" viewBox="0 0 320 512">
                            <path d="M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z"/>
                        </svg>
                    </div>
                    <div class="social-input">
                        <input type="text" placeholder="{:__('Por favor, insira a conta do Facebook')}" id="facebook">
                    </div>
                </div>

                <!-- Telegram -->
                <div class="social-link-item">
                    <div class="social-icon telegram-icon">
                        <svg fill="#FFFFFF" height="18" width="18" viewBox="0 0 496 512">
                            <path d="M248,8C111.033,8,0,119.033,0,256S111.033,504,248,504,496,392.967,496,256,384.967,8,248,8ZM362.952,176.66c-3.732,39.215-19.881,134.378-28.1,178.3-3.476,18.584-10.322,24.816-16.948,25.425-14.4,1.326-25.338-9.517-39.287-18.661-21.827-14.308-34.158-23.215-55.346-37.177-24.485-16.135-8.612-25,5.342-39.5,3.652-3.793,67.107-61.51,68.335-66.746.153-.655.3-3.1-1.154-4.384s-3.59-.849-5.135-.5q-3.283.746-104.608,69.142-14.845,10.194-26.894,9.934c-8.855-.191-25.888-5.006-38.551-9.123-15.531-5.048-27.875-7.717-26.8-16.291q.84-6.7,18.45-13.7,108.446-47.248,144.628-62.3c68.872-28.647,83.183-33.623,92.511-33.789,2.052-.034,6.639.474,9.61,2.885a10.452,10.452,0,0,1,3.53,6.716A43.765,43.765,0,0,1,362.952,176.66Z"/>
                        </svg>
                    </div>
                    <div class="social-input">
                        <input type="text" placeholder="{:__('Por favor, insira o Telegram')}" id="telegram">
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <div class="bottom-buttons">
            <div class="button back-button" id="backBtn">{:__('Voltar')}</div>
            <div class="button save-button" id="saveBtn">{:__('Salvar')}</div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化返回按钮
            const backBtn = document.querySelector('.back-btn');
            const backBtnBottom = document.getElementById('backBtn');

            if (backBtn) {
                backBtn.addEventListener('click', function() {
                    window.history.back();
                });
            }

            if (backBtnBottom) {
                backBtnBottom.addEventListener('click', function() {
                    window.history.back();
                });
            }

            // 初始化保存按钮
            const saveBtn = document.getElementById('saveBtn');
            if (saveBtn) {
                saveBtn.addEventListener('click', function() {
                    // 获取所有社交媒体链接
                    const instagram = document.getElementById('instagram').value;
                    const whatsapp = document.getElementById('whatsapp').value;
                    const facebook = document.getElementById('facebook').value;
                    const telegram = document.getElementById('telegram').value;

                    // 发送保存请求
                    const data = {
                        instagram: instagram,
                        whatsapp: whatsapp,
                        facebook: facebook,
                        telegram: telegram
                    };

                    // 显示保存中提示
                    // Toast.loading('{:__("Salvando...")}');

                    // 使用Ajax发送数据
                    $.ajax({
                        url: '{:url("user/saveProfile")}',
                        type: 'POST',
                        data: data,
                        dataType: 'json',
                        success: function(response) {
                            // Toast.hide();
                            if (response.code == 1) {
                                // Toast.success('{:__("Salvo com sucesso")}');
                                setTimeout(function() {
                                    window.history.back();
                                }, 1500);
                            } else {
                                // Toast.error(response.msg || '{:__("Falha ao salvar")}');
                            }
                        },
                        error: function() {
                            // Toast.hide();
                            // Toast.error('{:__("Falha na conexão")}');
                        }
                    });
                });
            }

            // 初始化复制ID按钮
            const copyIdBtn = document.querySelector('.copy-icon');
            if (copyIdBtn) {
                copyIdBtn.addEventListener('click', function() {
                    const userId = document.querySelector('.user-id').textContent.trim().replace('ID:', '');

                    // 创建临时textarea元素来复制文本
                    const textarea = document.createElement('textarea');
                    textarea.value = userId;
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);

                    Toast.success('{:__("ID copiado")}');
                });
            }

            // 加载已保存的社交媒体链接
            function loadSavedProfile() {
                $.ajax({
                    url: '{:url("user/getProfile")}',
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.code == 1 && response.data) {
                            const data = response.data;
                            if (data.instagram) document.getElementById('instagram').value = data.instagram;
                            if (data.whatsapp) document.getElementById('whatsapp').value = data.whatsapp;
                            if (data.facebook) document.getElementById('facebook').value = data.facebook;
                            if (data.telegram) document.getElementById('telegram').value = data.telegram;
                        }
                    }
                });
            }

            // 页面加载时获取已保存的社交媒体链接
            loadSavedProfile();
        });
    </script>
</body>
</html>