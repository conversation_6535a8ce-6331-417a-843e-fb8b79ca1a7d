<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Recover balance')}</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}

    <style>
        /* Balance页面特定样式 */
        .container {
            padding-bottom: 20px;
        }

        .header {
            background-color: var(--secondary-color);
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            color: var(--text-color);
            position: relative;
        }

        .back-btn {
            font-size: 24px;
            cursor: pointer;
        }

        .header-title {
            font-size: 18px;
            font-weight: bold;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        /* 余额信息卡片 */
        .balance-card {
            background-color: var(--secondary-color);
            border-radius: 10px;
            margin: 20px 15px;
            padding: 20px;
            color: var(--text-color);
            position: relative;
        }

        .balance-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .balance-label {
            font-size: 24px;
            color: var(--text-color);
            display: flex;
            align-items: center;
        }

        .balance-value {
            font-size: 28px;
            font-weight: bold;
            color: var(--text-color);
            display: flex;
            align-items: center;
        }

        .balance-value .refresh-icon {
            margin-left: 10px;
            width: 24px;
            height: 24px;
            cursor: pointer;
            color: var(--text-color);
        }

        .balance-note {
            font-size: 14px;
            text-align: right;
            margin-top: 15px;
            color: var(--text-color);
            line-height: 1.4;
        }

        .one-click-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: var(--button-color);
            color: var(--text-color);
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
            text-align: center;
        }

        /* 搜索框区域 */
        .search-container {
            display: flex;
            margin: 20px 15px;
            position: relative;
        }

        .platform-all-btn {
            background-color: var(--text-color);
            border-radius: 8px;
            padding: 12px 15px;
            display: flex;
            align-items: center;
            margin-right: 10px;
            cursor: pointer;
            flex-shrink: 0;
        }

        .platform-all-icon {
            display: flex;
            font-size: 20px;
            margin-right: 8px;
            flex-shrink: 0;
        }

        .platform-all-text {
            font-weight: bold;
            color: var(--text-color);
            font-size: 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .search-input-container {
            flex-grow: 1;
            position: relative;
        }

        .search-input {
            width: 100%;
            height: 45px;
            background-color: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 22.5px;
            padding: 0 45px 0 20px;
            color: var(--tetx-color);
            font-size: 16px;
            outline: none;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            color: var(--text-color);
            opacity: 0.7;
        }

        /* 平台按钮 */
        .platform-btn {
            display: flex;
            align-items: center;
            background-color: var(--button-normal);
            padding: 12px 15px;
            margin: 15px;
            border-radius: 8px;
            cursor: pointer;
        }

        .platform-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            margin-right: 15px;
            font-size: 24px;
        }

        .platform-text {
            font-weight: bold;
            color: var(--text-color);
        }

        /* 空状态 */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-top: 100px;
            opacity: 0.7;
        }

        .empty-state-icon {
            width: 120px;
            height: 120px;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        /* 回收按钮 */
        .recover-button {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: calc(100% - 30px);
            max-width: 486px;
            padding: 14px 0;
            background-color: var(--button-color);
            color: var(--text-color);
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            cursor: pointer;
            display: none; /* 初始隐藏，选择平台后显示 */
        }

        /* 弹窗样式 */
        .popup {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            display: none;
        }

        .popup-content {
            background-color: var(--secondary-color);
            width: 80%;
            max-width: 320px;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .popup-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--text-color);
        }

        .popup-message {
            font-size: 14px;
            margin-bottom: 20px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .popup-buttons {
            display: flex;
            justify-content: center;
        }

        .popup-button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            margin: 0 10px;
            font-weight: bold;
            cursor: pointer;
        }

        .popup-cancel {
            background-color: var(--button-normal);
            color: var(--text-color);
        }

        .popup-confirm {
            background-color: var(--button-normal);
            color: var(--text-color);
        }

        /* 一键点击下载按钮 */
        .download-btn {
            background-color: var(--secondary-color);
            color: var(--text-color);
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            display: inline-block;
            margin-top: 10px;
            text-decoration: none;
        }

        /* 动画效果 */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .one-click-btn {
            animation: pulse 2s infinite;
            transition: all 0.3s ease;
        }

        .one-click-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .platform-all-btn, .platform-btn {
            transition: all 0.3s ease;
        }

        .platform-all-btn:hover, .platform-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .platform-all-btn.selected, .platform-btn.selected {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
            border: 2px solid var(--text-color);
        }

        @keyframes rotating {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .rotating {
            animation: rotating 1s linear infinite;
        }

        #recoverButton {
            transition: all 0.3s ease;
        }

        #recoverButton:hover {
            transform: translateY(-2px) translateX(-50%);
            box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
        }

        /* 响应式布局 */
        @media screen and (max-width: 480px) {
            .search-container {
                flex-direction: column;
            }

            .platform-all-btn {
                width: 100%;
                margin-right: 0;
                margin-bottom: 10px;
                justify-content: center;
            }

            .search-input-container {
                width: 100%;
            }

            .platform-btn {
                margin: 10px 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="back-btn">←</div>
            <div class="header-title">{:__('Recover balance')}</div>
        </div>

        <!-- 余额信息卡片 -->
        <div class="balance-card">
            <div class="balance-info">
                <div class="balance-label">{:__('Current balance')}</div>
                <div class="balance-value">
                    {$userInfo.balance|default='10,05'}
                    <svg class="refresh-icon" id="refreshBalance" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M23 4v6h-6M1 20v-6h6M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15"></path>
                    </svg>
                </div>
            </div>
            <div class="one-click-btn">{:__('Um clique para ...')}</div>
            <div class="balance-note">
                {:__('You can only recover the whole number of balance (no decimal)')}
                <br>
            </div>
        </div>

        <!-- 搜索框 -->
        <div class="search-container">
            <div class="platform-all-btn" data-platform="todos">
                <div class="platform-all-icon">♠♣♥♦</div>
                <div class="platform-all-text">{:__('Tudos')}</div>
            </div>
            <div class="search-input-container">
                <input type="text" class="search-input" placeholder="{:__('Pesquisa de plataforma')}" id="searchInput">
                <div class="search-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                    </svg>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <img src="/assets/img/frontend/common/empty.png" alt="Empty" class="empty-state-icon">
            <div class="empty-text">{:__('No games with balance found')}</div>
        </div>

        <!-- 回收按钮 -->
        <div class="recover-button" id="recoverButton">{:__('Recover balance')}</div>

        <!-- 确认弹窗 -->
        <div class="popup" id="confirmPopup">
            <div class="popup-content">
                <div class="popup-title">{:__('Confirm balance recovery')}</div>
                <div class="popup-message">{:__('Are you sure you want to recover all balance from selected platform?')}</div>
                <div class="popup-buttons">
                    <button class="popup-button popup-cancel" id="cancelRecover">{:__('Cancel')}</button>
                    <button class="popup-button popup-confirm" id="confirmRecover">{:__('Confirm')}</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(function () {
            var selectedPlatform = null;

            // 点击平台选择（包括"Tudos"按钮）
            $(document).on('click', '.platform-btn, .platform-all-btn', function() {
                selectedPlatform = $(this).data('platform');

                // 高亮选中的平台
                $('.platform-btn, .platform-all-btn').removeClass('selected');
                $(this).addClass('selected');

                // 显示恢复按钮
                $('#recoverButton').show();
            });

            // 搜索平台
            $('#searchInput').on('input', function() {
                var searchTerm = $(this).val().toLowerCase();

                $('.platform-btn').each(function() {
                    var platformText = $(this).find('.platform-text').text().toLowerCase();
                    if (platformText.includes(searchTerm)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });

                // 检查是否有可见的平台按钮
                var visiblePlatforms = $('.platform-btn:visible').length;
                if (visiblePlatforms === 0) {
                    // 如果没有可见的平台，显示空态
                    $('#emptyState').show();
                } else {
                    // 如果有可见的平台，隐藏空态
                    $('#emptyState').hide();
                }
            });

            // 点击"Um clique para ..."按钮
            $('.one-click-btn').on('click', function() {
                // 这里可以添加一键操作的功能
                Toast.info('{:__("One-click feature coming soon")}');
            });

            // 点击恢复余额
            $('#recoverButton').on('click', function() {
                if (!selectedPlatform) {
                    Toast.info('{:__("Please select a platform first")}');
                    return;
                }

                // 显示确认弹窗
                $('#confirmPopup').show();
            });

            // 点击刷新按钮
            $('#refreshBalance').on('click', function() {
                var $icon = $(this);
                $icon.addClass('rotating');

                // 请求刷新余额
                $.ajax({
                    url: '{:url("index/user/refreshBalance")}',
                    type: 'post',
                    dataType: 'json',
                    success: function(response) {
                        if (response.code === 1) {
                            $('.balance-value').html(response.data.balance + '<svg class="refresh-icon" id="refreshBalance" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M23 4v6h-6M1 20v-6h6M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15"></path></svg>');
                            Toast.success('{:__("Balance refreshed")}');
                        } else {
                            Toast.error(response.msg || '{:__("Refresh failed")}');
                        }
                    },
                    error: function() {
                        Toast.error('{:__("Connection failed")}');
                    },
                    complete: function() {
                        setTimeout(function() {
                            $icon.removeClass('rotating');
                        }, 500);
                    }
                });
            });

            // 确认恢复余额
            $('#confirmRecover').on('click', function() {
                // 隐藏确认弹窗
                $('#confirmPopup').hide();

                Toast.loading('{:__("Processing...")}');

                // 发送恢复请求
                $.ajax({
                    url: '{:url("index/user/recoverBalance")}',
                    type: 'post',
                    dataType: 'json',
                    data: {
                        platform: selectedPlatform
                    },
                    success: function(response) {
                        Toast.hide();
                        if (response.code === 1) {
                            Toast.success('{:__("Balance recovered successfully")}');
                            // 刷新余额显示
                            $('.balance-value').html(response.data.balance + '<svg class="refresh-icon" id="refreshBalance" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M23 4v6h-6M1 20v-6h6M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15"></path></svg>');
                        } else {
                            Toast.error(response.msg || '{:__("Recovery failed")}');
                        }
                    },
                    error: function() {
                        Toast.hide();
                        Toast.error('{:__("Connection failed")}');
                    }
                });
            });

            // 取消恢复
            $('#cancelRecover').on('click', function() {
                $('#confirmPopup').hide();
            });

            // 点击返回按钮
            // $('.back-btn').on('click', function() {
            //     history.back();
            // });
        });
    </script>
</body>
</html>