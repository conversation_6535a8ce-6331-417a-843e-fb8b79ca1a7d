<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('User center')}</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}

    <!-- 引入用户中心页面样式 -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/user/user.css?v=1.0.3">
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/index/deposit_popup.css?v=1.01">

    <!-- 引入用户中心页面JS -->
    <script src="__CDN__/assets/js/frontend/user/user.js?v=1.01"></script>

    <!-- 引入充值弹窗JS -->
    <script src="__CDN__/assets/js/frontend/index/deposit_popup.js?v=1.01"></script>
    <script src="__CDN__/assets/js/frontend/index/deposit_history_popup.js?v=1.01"></script>
</head>
<body>
    <div class="container">
        <!-- 主要头部区域 -->
        <div class="main-header">
            <!-- 添加波浪曲线元素 -->
            <div class="wave wave1"></div>
            <div class="wave wave2"></div>
            <div class="curve curve1"></div>
            <div class="curve curve2"></div>

            <!-- 顶部按钮 -->
            <div class="top-buttons">
                <div data-page="customerservice/index" class="top-button">
                    <div class="top-button-icon"><i class="fa fa-headphones"></i></div>
                    <div class="top-button-text">{:__('Support')}</div>
                </div>
                <div data-page="customerservice/index" class="top-button">
                    <div class="top-button-icon"><i class="fa fa-comment"></i></div>
                    <div class="top-button-text">{:__('Message')}</div>
                </div>
                <div data-page="user/profile" class="top-button">
                    <div class="top-button-icon"><i class="fa fa-user"></i></div>
                    <div class="top-button-text">{:__('Profile')}</div>
                </div>
            </div>

            <!-- 用户信息区域 -->
            <div class="user-profile">
                <div class="user-profile-content">
                    <div class="user-avatar">
                        <img src="/assets/img/frontend/common/109x112_png.png" alt="">
                    </div>
                    <div class="user-info-container">
                        <div class="user-info-row">
                            <span class="account-label">{:__('Account label')}</span>
                            <span class="account-value">{$userInfo.username}</span>
                        </div>
                        <div class="user-info-row">
                            <span class="id-label">{:__('ID label')}</span>
                            <span class="id-value">{$userInfo.id}</span>
                            <span class="copy-id" id="copyIdBtn"><i class="fa fa-copy"></i></span>
                            <img src="/assets/img/frontend/common/area_yuan.6eeafff3.png" alt="Brazil" class="country-flag">
                            <span class="balance-value">{$userInfo.balance}</span>
                            <span class="refresh-balance" id="refreshBalanceBtn"><i class="fa fa-refresh"></i></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要操作按钮 -->
            <div class="main-actions">
                <div class="action-button withdraw-btn" id="withdrawBtn" data-page="security/index">
                    <span class="icon-withdraw"><i class="fa fa-money"></i></span>
                    <span>{:__('Withdraw')}</span>
                </div>
                <div class="action-button deposit-btn" id="rechargeBtn">
                    <span class="icon-deposit"><i class="fa fa-credit-card"></i></span>
                    <span>{:__('Deposit')}</span>
                </div>
            </div>
        </div>

        <!-- VIP状态区域 -->
        <div class="vip-status" data-page="event/vip">
            <div class="vip-header">
                <div class="vip-header-content">
                    <div class="vip-level">
                        <div class="level-box">VIP{$userInfo.vip_level}</div>
                        <span class="vip-title">VIP{$userInfo.vip_level}{:__('VIP next')}</span>
                    </div>
                    <span class="vip-next">{:__('Remaining deposit')} {$userInfo.remaining_deposit}</span>
                </div>
                <span class="arrow-right"></span>
            </div>

            <div class="vip-progress">
                <div class="progress-item">
                    <div class="progress-label">
                        <span>{:__('Deposit promotion')}</span>
                        <span class="progress-value">{$userInfo.total_deposit}/{$userInfo.next_vip_deposit}</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar">
                            <div class="progress-fill" style="--progress-width: {$userInfo.deposit_progress}%"></div>
                        </div>
                    </div>
                </div>

                <div class="progress-item">
                    <div class="progress-label">
                        <span>{:__('Bet promotion')}</span>
                        <span class="progress-value">{$userInfo.require_amount ?? '0.00'}/{$userInfo.completed_amount}</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar">
                            <div class="progress-fill" style="--progress-width: {$userInfo.amount_progress}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 菜单列表 -->
        <div class="user-menu-list">
            <!-- 第一组：前4个菜单项 -->
            <div class="menu-group">
                <div data-page="user/balance" class="user-menu-item">
                    <div class="user-menu-icon">
                        <i class="fa fa-refresh"></i>
                    </div>
                    <span>{:__('Recover balance')}</span>
                </div>
                <div data-page="user/profile" class="user-menu-item">
                    <div class="user-menu-icon">
                        <i class="fa fa-user"></i>
                    </div>
                    <span>{:__('Account')}</span>
                </div>
                <div data-page="user/bets" class="user-menu-item">
                    <div class="user-menu-icon">
                        <i class="fa fa-calendar"></i>
                    </div>
                    <span>{:__('Bets')}</span>
                </div>
                <div data-page="security/index" class="user-menu-item">
                    <div class="user-menu-icon">
                        <i class="fa fa-money"></i>
                    </div>
                    <span>{:__('Withdraw management')}</span>
                </div>
            </div>

            <!-- 第二组：后7个菜单项 -->
            <div class="menu-group">
                <div data-page="agent/index" class="user-menu-item">
                    <div class="user-menu-icon">
                        <i class="fa fa-user-plus"></i>
                    </div>
                    <span>{:__('Invite')}</span>
                </div>
                <div data-page="user/profile" class="user-menu-item">
                    <div class="user-menu-icon">
                        <i class="fa fa-database"></i>
                    </div>
                    <span>{:__('Data')}</span>
                </div>
                <div data-page="user/securitycenter" class="user-menu-item">
                    <div class="user-menu-icon">
                        <i class="fa fa-shield"></i>
                    </div>
                    <span>{:__('Security')}</span>
                </div>
                <div data-page="user/music" class="user-menu-item">
                    <div class="user-menu-icon">
                        <i class="fa fa-music"></i>
                    </div>
                    <span>{:__('Music')}</span>
                </div>
                <div data-page="customerservice/index" class="user-menu-item">
                    <div class="user-menu-icon">
                        <i class="fa fa-question-circle"></i>
                    </div>
                    <span>{:__('FAQ')}</span>
                </div>
                <div data-page="customerservice/index" class="user-menu-item">
                    <div class="user-menu-icon">
                        <i class="fa fa-comment"></i>
                    </div>
                    <span>{:__('Suggestion')}</span>
                </div>
                <div data-page="index/playerLogout" class="user-menu-item">
                    <div class="user-menu-icon">
                        <i class="fa fa-sign-out"></i>
                    </div>
                    <span>{:__('Logout')}</span>
                </div>
            </div>
        </div>
        <!-- 添加底部空白区域，确保最后一个菜单项不被底部导航栏遮挡 -->
        <div class="bottom-spacer" style="height: 100px;"></div>
    </div>

    {include file="common/bottom_nav" /}

    <!-- 引入充值弹窗 -->
    {include file="pop/deposit_popup" /}

    <!-- 引入充值历史弹窗 -->
    {include file="pop/deposit_history_popup" /}

    <!-- 确保弹窗默认不显示并修复样式 -->
    <style>
    /* 强制隐藏所有弹窗覆盖层 - 确保页面加载时不会显示 */
    .deposit-popup-overlay,
    #depositPopupOverlay,
    .deposit-history-popup-overlay,
    #depositHistoryPopupOverlay,
    div[style*="background-color: rgba(0, 0, 0"] {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        z-index: -1 !important;
        pointer-events: none !important;
    }

    /* 添加手型指针样式到可点击元素 */
    .top-button,
    .user-menu-item,
    .action-button {
        cursor: pointer;
    }
    </style>

    <script>
    // 立即执行，不等待DOM加载
    (function() {
        // 强制隐藏所有弹窗覆盖层
        var overlays = [
            'depositPopupOverlay',
            'depositHistoryPopupOverlay',
            'deposit-popup-overlay',
            'deposit-history-popup-overlay'
        ];

        // 遍历所有可能的弹窗ID
        overlays.forEach(function(id) {
            var element = document.getElementById(id) || document.querySelector('.' + id);
            if (element) {
                element.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; z-index: -1 !important; pointer-events: none !important;';
            }
        });

        // 检查页面是否有黑色背景遮罩
        var blackOverlays = document.querySelectorAll('div[style*="background-color: rgba(0, 0, 0"]');
        blackOverlays.forEach(function(overlay) {
            overlay.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; z-index: -1 !important; pointer-events: none !important;';
        });
    })();

    // DOM加载完成后再次检查
    document.addEventListener('DOMContentLoaded', function() {
        // 强制隐藏所有弹窗覆盖层
        var overlays = [
            'depositPopupOverlay',
            'depositHistoryPopupOverlay',
            'deposit-popup-overlay',
            'deposit-history-popup-overlay'
        ];

        // 遍历所有可能的弹窗ID
        overlays.forEach(function(id) {
            var element = document.getElementById(id) || document.querySelector('.' + id);
            if (element) {
                element.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; z-index: -1 !important; pointer-events: none !important;';
            }
        });

        // 检查页面是否有黑色背景遮罩
        var blackOverlays = document.querySelectorAll('div[style*="background-color: rgba(0, 0, 0"]');
        blackOverlays.forEach(function(overlay) {
            overlay.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; z-index: -1 !important; pointer-events: none !important;';
        });

        // 1秒后再次检查，确保所有动态加载的弹窗也被隐藏
        setTimeout(function() {
            var allOverlays = document.querySelectorAll('.deposit-popup-overlay, #depositPopupOverlay, .deposit-history-popup-overlay, #depositHistoryPopupOverlay, div[style*="background-color: rgba(0, 0, 0"]');
            allOverlays.forEach(function(overlay) {
                overlay.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; z-index: -1 !important; pointer-events: none !important;';
            });
        }, 1000);
    });
    </script>


    <!-- 添加语言包配置 -->
    <script>
    // 定义全局配置对象
    window.Config = window.Config || {};

    // 添加语言包配置
    Config.lang = {
        user: {
            'ID copiado': '{:__("ID copiado")}',
            'Refresh failed': '{:__("Refresh failed")}',
            'Network error': '{:__("Network error")}',
            'Logout successful': '{:__("Logout successful")}'
        },
        deposit: {
            'deposit_online': '{:__("deposit_online")}',
            'deposit_history': '{:__("deposit_history")}',
            'select_payment_method': '{:__("select_payment_method")}',
            'select_amount': '{:__("select_amount")}',
            'deposit_amount': '{:__("deposit_amount")}',
            'need_bonus': '{:__("need_bonus")}',
            'deposit_now': '{:__("deposit_now")}'
        }
    };
    </script>

    <!-- 用户中心页面脚本 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM加载完成，开始初始化用户中心页面');

        // 确保user-menu-list可以滚动但不显示滚动条
        const menuList = document.querySelector('.user-menu-list');
        if (menuList) {
            // 计算合适的最大高度
            const calculateMaxHeight = () => {
                const viewportHeight = window.innerHeight;
                const menuListTop = menuList.getBoundingClientRect().top;
                const bottomNavHeight = 60; // 底部导航栏高度
                const maxHeight = viewportHeight - menuListTop - bottomNavHeight - 20; // 额外留出20px空间

                menuList.style.maxHeight = `${Math.max(200, maxHeight)}px`; // 确保最小高度为200px
                console.log('设置菜单列表最大高度:', menuList.style.maxHeight);
            };

            // 初始计算
            calculateMaxHeight();

            // 窗口大小改变时重新计算
            window.addEventListener('resize', calculateMaxHeight);
        }

        // 为充值按钮添加点击事件
        const rechargeBtn = document.getElementById('rechargeBtn');
        if (rechargeBtn) {
            console.log('为充值按钮添加点击事件');

            // 移除所有现有的点击事件处理函数
            let newRechargeBtn = rechargeBtn.cloneNode(true);
            rechargeBtn.parentNode.replaceChild(newRechargeBtn, rechargeBtn);

            // 添加新的点击事件处理函数
            newRechargeBtn.addEventListener('click', function(e) {
                console.log('充值按钮被点击');
                e.preventDefault();
                e.stopPropagation();

                // 检查是否有修复函数可用
                if (typeof window.fixDepositPopup === 'function') {
                    console.log('使用修复函数显示充值弹窗');

                    // 先检查是否有其他弹窗正在显示，如果有则先隐藏
                    const otherPopups = document.querySelectorAll('.payment-iframe-popup, .payment-iframe-overlay, .auth-popup.active, .notice-popup[style*="display: flex"], .agreement-popup.active');
                    if (otherPopups.length > 0) {
                        console.log('发现其他弹窗正在显示，先隐藏它们');
                        otherPopups.forEach(popup => {
                            popup.style.display = 'none';
                        });
                    }

                    // 确保充值弹窗存在
                    const depositPopupOverlay = document.getElementById('depositPopupOverlay') || document.querySelector('.deposit-popup-overlay');
                    if (!depositPopupOverlay) {
                        console.error('找不到充值弹窗元素，尝试重新加载');
                        // 如果弹窗不存在，可能需要重新加载页面或者动态创建弹窗
                        location.reload();
                        return;
                    }

                    // 显示充值弹窗
                    depositPopupOverlay.style.display = 'flex';

                    // 使用修复函数确保弹窗正确显示
                    window.fixDepositPopup();

                    // 延迟再次检查弹窗是否真的显示了
                    setTimeout(function() {
                        if (typeof window.ensureDepositPopupVisible === 'function') {
                            window.ensureDepositPopupVisible();
                        }
                    }, 500);
                }
                // 如果没有修复函数，则使用标准方法
                else if (typeof window.depositPopup !== 'undefined' && typeof window.depositPopup.show === 'function') {
                    console.log('使用标准方法显示充值弹窗');
                    window.depositPopup.show();
                } else {
                    console.error('充值功能暂时不可用，请稍后再试');
                }
            });
        } else {
            console.error('找不到充值按钮元素');
        }

        // 为提现按钮添加点击事件
        const withdrawBtn = document.getElementById('withdrawBtn');
        if (withdrawBtn) {
            console.log('为提现按钮添加点击事件');

            // 移除所有现有的点击事件处理函数
            let newWithdrawBtn = withdrawBtn.cloneNode(true);
            withdrawBtn.parentNode.replaceChild(newWithdrawBtn, withdrawBtn);

            // 添加新的点击事件处理函数
            newWithdrawBtn.addEventListener('click', function(e) {
                console.log('提现按钮被点击');
                e.preventDefault();
                e.stopPropagation();

                // 使用通用导航函数处理
                const targetPage = this.getAttribute('data-page');
                if (targetPage) {
                    handleNavigation(targetPage);
                }
            });
        } else {
            console.error('找不到提现按钮元素');
        }

        // 通用页面导航处理函数
        function handleNavigation(targetPage) {
            if (!targetPage) return;

            console.log('跳转到页面:', targetPage);

            // 处理带有查询参数的路径
            let basePath = targetPage;
            let queryParams = '';

            // 检查是否包含查询参数
            if (targetPage.includes('?')) {
                const parts = targetPage.split('?');
                basePath = parts[0];
                queryParams = '?' + parts[1];
            }

            console.log('基础路径:', basePath, '查询参数:', queryParams);

            // 特殊处理security/index页面 - 检查是否设置了提款密码
            if (basePath === 'security/index' || basePath === 'security') {
                // 检查用户是否设置了提现密码
                fetch('/index/user/checkWithdrawPassword', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('检查提现密码结果:', data);
                    if (data.code === 1 && data.data && data.data.has_password) {
                        // 已设置提现密码，跳转到安全中心主页
                        console.log('用户已设置提现密码，跳转到安全中心主页');
                        window.location.href = '/index/security/index' + queryParams;
                    } else {
                        // 未设置提现密码，跳转到密码设置页面
                        console.log('用户未设置提现密码，跳转到密码设置页面');
                        window.location.href = '/index/security/password_setup' + queryParams;
                    }
                })
                .catch(error => {
                    console.error('检查提现密码状态出错:', error);
                    // 出错时默认跳转到安全中心主页
                    window.location.href = '/index/security/index' + queryParams;
                });
                return;
            }

            if (basePath === 'user/profile') {
                window.location.href = '/index/user/profile' + queryParams;
            } else if (basePath === 'index/user/profile') {
                // 处理特殊情况：index/user/profile
                window.location.href = '/' + basePath + queryParams;
            } else if (basePath === 'player/logout' || basePath === 'index/playerLogout') {
                // 处理登出 - 使用专门的登出函数
                console.log('使用专门的登出函数');

                // 如果全局登出函数存在，则使用它
                if (typeof window.handleUserLogout === 'function') {
                    window.handleUserLogout();
                } else {
                    console.error('全局登出函数不存在，使用备用方法');

                    // 清除客户端缓存
                    localStorage.clear();
                    sessionStorage.clear();

                    // 清除所有cookie
                    const cookies = document.cookie.split(";");
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i];
                        const eqPos = cookie.indexOf("=");
                        const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
                        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/";
                    }

                    // 直接跳转到登出页面，添加时间戳防止缓存
                    window.location.href = '/index/index/playerLogout?t=' + new Date().getTime();
                }
            } else if (basePath.startsWith('user/')) {
                // 其他以user/开头的页面
                window.location.href = '/index/' + basePath + queryParams;
            } else if (basePath.startsWith('index/')) {
                // 以index/开头的页面
                window.location.href = '/' + basePath + queryParams;
            } else if (basePath.startsWith('security/')) {
                // 处理security开头的页面
                window.location.href = '/index/' + basePath + queryParams;
            } else {
                // 其他页面
                window.location.href = '/index/' + basePath + queryParams;
            }
        }

        // 为顶部按钮添加点击事件
        const topButtons = document.querySelectorAll('.top-button');
        if (topButtons.length > 0) {
            topButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetPage = this.getAttribute('data-page');
                    if (targetPage) {
                        handleNavigation(targetPage);
                    }
                });
            });
        }

        // 为菜单项添加点击事件
        const menuItems = document.querySelectorAll('.user-menu-item');
        if (menuItems.length > 0) {
            menuItems.forEach(item => {
                item.addEventListener('click', function() {
                    const targetPage = this.getAttribute('data-page');
                    if (targetPage) {
                        handleNavigation(targetPage);
                    }
                });
            });
        }
    });
    </script>

</body>
</html>
