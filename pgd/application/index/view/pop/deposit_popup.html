<!-- 充值弹窗 -->
<link rel="stylesheet" href="/assets/css/frontend/index/deposit_popup_override.css?v=1.01">
<style>
    /* 确保所有元素可见 */
    .deposit-popup-title,
    .history-button,
    .payment-section h4,
    .deposit-section h4,
    .summary-section span,
    .offer-bonus-checkbox span,
    .btn-confirm {
        visibility: visible !important;
    }

    /* 黄色角标样式 */
    .amount-item {
        position: relative;
        overflow: visible; /* 确保角标可见 */
    }

    .bonus-badge {
        position: absolute;
        top: 0;
        right: 0;
        background-color: var(--darker-bg-color);
        color: var(--secondary-color);
        font-size: 9px; /* 减小字体大小 */
        font-weight: bold;
        padding: 1px 3px; /* 减小内边距 */
        border-radius: 0 0 0 3px; /* 减小圆角 */
        box-shadow: 0 1px 2px rgba(0,0,0,0.2);
        z-index: 1;
        max-width: 70%; /* 增加最大宽度比例 */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1;
        transform: scale(0.95); /* 稍微缩小一点 */
    }
</style>

<div class="deposit-popup-overlay" id="depositPopupOverlay" style="z-index: 1000000;">
    <div class="deposit-popup" style="z-index: 1000001;">
        <div class="deposit-popup-container" style="z-index: 1000002;">
            <div class="deposit-popup-header">
                <button class="back-button" id="backFromDepositPopup"><i class="fa fa-arrow-left"></i></button>
                <h3 class="deposit-popup-title">{:__('deposit_online')}</h3>
                <span class="history-button" id="depositHistoryBtn">{:__('deposit_history')}</span>
            </div>
            <div class="deposit-popup-body">
                <div class="payment-section">
                    <h4>{:__('select_payment_method')}</h4>
                    <div class="payment-grid" id="paymentGrid">
                        {foreach name="paymentChannels" item="channel"}
                        <div class="payment-item" data-channel-id="{$channel.code}" data-channel-code="{$channel.code}">
                            <div class="payment-icon">
                                <img src="/assets/img/frontend/payment/{$channel.code}.png" alt="{$channel.name}" />
                            </div>
                            <span>pix</span>
                        </div>
                        {/foreach}
                    </div>
                </div>
                <div class="deposit-section" id="depositSection">
                    <h4>{:__('select_amount')}</h4>
                    <div class="amount-grid" id="amountGrid">

                        <!-- 动态生成金额列表 -->
                        {foreach name="amountList" item="amount"}
                            <div class="amount-item" data-amount="{$amount}">
                                <span class="amount">{:intval($amount)}</span>
                                {if condition="isset($isFirstDeposit) && $isFirstDeposit && isset($firstDepositThreshold) && $amount >= $firstDepositThreshold"}
                                <span class="bonus-badge">+{:number_format($amount * 0.2, 0)}</span>
                                {/if}
                            </div>
                        {/foreach}
                    </div>
                </div>
                <div class="deposit-popup-footer">
                    <div class="summary-section" id="summarySection">
                        <div class="summary-row">
                            <span>{:__('deposit_amount')}:</span>
                            <input type="number" id="summaryAmount" value="0" min="10" max="20000" class="summary-input">
                        </div>
                    </div>
                    <div class="offer-bonus-section">
                        <label class="offer-bonus-checkbox">
                            <input type="checkbox" id="offerBonusCheckbox" checked>
                            <span>{:__('need_bonus')}</span>
                        </label>
                    </div>
                    <button id="confirmPayment" class="btn-confirm">{:__('deposit_now')}</button>
                </div>
            </div>

        </div>
    </div>
</div>


