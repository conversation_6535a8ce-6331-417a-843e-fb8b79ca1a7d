<!-- 用户协议弹窗 -->
<!-- 引入用户协议弹窗CSS -->
<link rel="stylesheet" href="/assets/css/frontend/pop/agreement_popup.css?v=1.01">

<div class="agreement-popup" id="agreementPopup">
    <div class="agreement-popup-content">
        <h3 class="agreement-title">{:__('user_agreement')}</h3>
        
        <div class="agreement-content">
            <p><strong>1.</strong> {:__('agreement_clause_1')}</p>
            
            <p><strong>2.</strong> {:__('agreement_clause_2')}</p>
            
            <p><strong>3.</strong> {:__('agreement_clause_3')}</p>
            
            <p><strong>4.</strong> {:__('agreement_clause_4')}</p>
            
            <p><strong>5.</strong> {:__('agreement_clause_5')}</p>
        </div>
        
        <button class="agreement-accept-btn" id="agreementAcceptBtn">{:__('read_and_understood')}</button>
        
        <div class="agreement-close" id="agreementClose">✕</div>
    </div>
</div>

<!-- 引入协议弹窗JavaScript -->
<script src="/assets/js/frontend/pop/agreement_popup.js" defer></script> 