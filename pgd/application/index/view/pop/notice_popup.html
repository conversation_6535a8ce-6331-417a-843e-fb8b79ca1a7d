<!-- 公告弹窗 -->
<!-- 引入公告弹窗CSS -->
<link rel="stylesheet" href="/assets/css/frontend/pop/notice_popup.css?v=1.01">

<!-- 公告弹窗HTML结构 -->
<div class="notice-popup" id="noticePopup" style="display:none">
    <div class="notice-popup-content">
        <!-- 主要内容区 -->
        <div class="notice-content-wrapper">
            <!-- 左侧选项菜单 -->
            <div class="notice-popup-sidebar">
                <div class="tab-option active" data-tab="notice">
                    <i>📢</i> {:__('announcement')}
                </div>
                <div class="tab-option" data-tab="bonus">
                    <i>🎁</i> {:__('reward')}
                </div>
                <div class="tab-option" data-tab="about">
                    <i>ℹ️</i> {:__('about')}
                </div>
            </div>
            
            <!-- 右侧内容区域 - 仅显示图片 -->
            <div class="notice-popup-main">
                <!-- 公告图片标签页 -->
                <div class="content-tab active" id="notice-tab">
                    <div class="notice-popup-image">
                        <img src="/assets/img/frontend/common/notice_banner.png" alt="{:__('announcement')}" onerror="this.src='/assets/img/frontend/common/pop1.0bb5f763.png';">
                    </div>
                </div>
                
                <!-- 奖励图片标签页 -->
                <div class="content-tab" id="bonus-tab">
                    <div class="notice-popup-image">
                        <img src="/assets/img/frontend/common/bonus_banner.png" alt="{:__('reward')}" onerror="this.src='/assets/img/frontend/common/pop1.0bb5f763.png';">
                    </div>
                </div>
                
                <!-- 关于图片标签页 -->
                <div class="content-tab" id="about-tab">
                    <div class="notice-popup-image">
                        <img src="/assets/img/frontend/common/about_banner.png" alt="{:__('about')}" onerror="this.src='/assets/img/frontend/common/pop1.0bb5f763.png';">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部关闭按钮 -->
        <div class="notice-popup-footer">
            <button class="notice-popup-close" id="closeNotice" type="button">{:__('close')}</button>
        </div>
    </div>
</div>

<!-- 直接使用内联JavaScript，避免使用require -->
<script>
// 等待jQuery加载完成
(function() {
    // 检查jQuery是否已加载
    function checkJQuery() {
        if (window.jQuery) {
            // jQuery已加载，初始化弹窗
            initNoticePopup();
        } else {
            // 100ms后再次检查
            setTimeout(checkJQuery, 100);
        }
    }
    
    // 初始检查
    checkJQuery();
    
    // 初始化公告弹窗
    function initNoticePopup() {
        console.log('公告弹窗初始化中...');
        var $ = window.jQuery;
        
        // 公告弹窗API
        window.noticePopup = {
            // 显示弹窗
            show: function() {
                $("#noticePopup").show();
                console.log('公告弹窗已显示');
            },
            
            // 隐藏弹窗
            hide: function() {
                $("#noticePopup").hide();
                console.log('公告弹窗已隐藏');
                
                // 发送Ajax请求记录关闭状态
                $.ajax({
                    url: '/index/index/markNoticePopupClosed',
                    type: 'GET',
                    cache: false,
                    dataType: 'json',
                    success: function(response) {
                        console.log('关闭弹窗请求成功:', response);
                    },
                    error: function(xhr, status, error) {
                        console.error('关闭弹窗请求失败:', status, error);
                    }
                });
            }
        };
        
        // 防止点击弹窗内容区域时关闭弹窗
        $('.notice-popup-content').on('click', function(e) {
            e.stopPropagation();
        });
        
        // 点击弹窗背景关闭弹窗
        $('#noticePopup').on('click', function() {
            window.noticePopup.hide();
        });
        
        // 点击关闭按钮关闭弹窗
        $('#closeNotice').on('click', function(e) {
            e.preventDefault();
            console.log('关闭按钮被点击');
            window.noticePopup.hide();
        });
        
        // 标签页切换
        $('.tab-option').on('click', function() {
            // 移除所有标签的active类
            $('.tab-option').removeClass('active');
            $(this).addClass('active');
            
            // 切换内容显示
            var tabId = $(this).data('tab') + '-tab';
            $('.content-tab').removeClass('active');
            $('#' + tabId).addClass('active');
        });
        
        // 自动显示弹窗
        setTimeout(function() {
            window.noticePopup.show();
        }, 1000);
        
        console.log('公告弹窗初始化完成');
    }
})();
</script>