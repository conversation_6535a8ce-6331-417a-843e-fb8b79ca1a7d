<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Search')}</title>
    <link rel="stylesheet" href="/assets/css/frontend/other/search.css?v=1.01">
</head>
<body>
    <div class="header">
        <div class="back-btn">←</div>
        <div class="search-box">
            <input type="text" class="search-input" placeholder="搜索游戏、活动...">
            <div class="search-icon"><i fa class="fa-search"></i></div>
        </div>
    </div>

    <div class="content">
        <div class="section-title">最近搜索</div>
        <div class="history-tags">
            <div class="history-tag">Fortune Tiger <span class="tag-close">×</span></div>
            <div class="history-tag">Bonus <span class="tag-close">×</span></div>
            <div class="history-tag">Slots <span class="tag-close">×</span></div>
            <div class="history-tag">Jackpot <span class="tag-close">×</span></div>
        </div>

        <div class="section-title">热门搜索</div>
        <div class="popular-searches">
            {empty name="popular_games"}
                <div class="empty-state">
                    <div class="empty-text">暂无热门游戏</div>
                </div>
            {else/}
                {volist name="popular_games" id="game" offset="0" length="5"}
                    <div class="popular-item" data-code="{$game.code}" data-id="{$game.id}">
                        <div class="popular-rank">{$i}</div>
                        <div class="popular-name">{$game.name}</div>
                    </div>
                {/volist}
            {/empty}
        </div>

        <div class="section-title">推荐游戏</div>
        <div class="suggested-games">
            {empty name="popular_games"}
                <div class="empty-state">
                    <div class="empty-text">暂无推荐游戏</div>
                </div>
            {else/}
                {volist name="popular_games" id="game" offset="0" length="6"}
                    <div class="game-item" data-code="{$game.code}" data-id="{$game.id}">
                        <div class="game-img" data-name="{$game.name}" style="background-image: url('{$game.image}'); background-size: cover; background-position: center;"></div>
                        <div class="game-name">{$game.name}</div>
                    </div>
                {/volist}
            {/empty}
        </div>
    </div>

    <script src="/assets/js/frontend/other/search.js"></script>
</body>
</html>