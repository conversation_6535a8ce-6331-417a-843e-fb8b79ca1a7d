<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>游戏详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            background-color: var(--main-bg-color);
            color: var(--text-color);
            font-family: Arial, sans-serif;
        }
        .header {
            height: 50px;
            background-color: var(--darker-bg-color);
            display: flex;
            align-items: center;
            padding: 0 15px;
            position: relative;
        }
        .back-btn {
            font-size: 24px;
            margin-right: 15px;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
        }
        .game-banner {
            width: 100%;
            height: 200px;
            background-color: var(--secondary-color);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }
        .game-info {
            padding: 15px;
        }
        .game-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .game-provider {
            font-size: 14px;
            color: rgba(255,255,255,0.7);
            margin-bottom: 15px;
        }
        .game-stats {
            display: flex;
            justify-content: space-between;
            background-color: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 12px;
            color: rgba(255,255,255,0.7);
        }
        .game-buttons {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .game-btn {
            flex: 1;
            height: 46px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            border-radius: 5px;
        }
        .play-btn {
            background-color: var(--button-normal);
            color: var(--text-color);
            margin-right: 10px;
        }
        .demo-btn {
            background-color: rgba(255,255,255,0.1);
        }
        .game-description {
            background-color: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .description-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .description-text {
            font-size: 14px;
            line-height: 1.5;
            color: rgba(255,255,255,0.9);
        }
        .similar-games {
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .game-list {
            display: flex;
            overflow-x: auto;
            padding-bottom: 15px;
        }
        .game-item {
            min-width: 120px;
            margin-right: 10px;
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
            overflow: hidden;
        }
        .game-img {
            width: 120px;
            height: 120px;
            background-color: #444;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
        }
        .game-name {
            padding: 8px;
            font-size: 12px;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="back-btn">←</div>
        <div class="title">游戏详情</div>
    </div>
    
    <div class="game-banner">Fortune Tiger</div>
    
    <div class="game-info">
        <div class="game-title">Fortune Tiger</div>
        <div class="game-provider">提供商: PG SOFT</div>
        
        <div class="game-stats">
            <div class="stat-item">
                <div class="stat-value">96.8%</div>
                <div class="stat-label">返奖率</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">中</div>
                <div class="stat-label">波动性</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">5x3</div>
                <div class="stat-label">游戏格式</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">10</div>
                <div class="stat-label">赔付线</div>
            </div>
        </div>
        
        <div class="game-buttons">
            <div class="game-btn play-btn">立即游戏</div>
            <div class="game-btn demo-btn">试玩模式</div>
        </div>
        
        <div class="game-description">
            <div class="description-title">游戏介绍</div>
            <div class="description-text">
                Fortune Tiger是一款以亚洲传统为主题的老虎机游戏，拥有精美的图形和刺激的游戏玩法。游戏中的老虎象征着力量和财富，据说可以为玩家带来好运。游戏设有多种特殊功能，包括免费旋转、乘数和野生符号替换。
            </div>
        </div>
        
        <div class="similar-games">
            <div class="section-title">类似游戏</div>
            <div class="game-list">
                <div class="game-item">
                    <div class="game-img">Fortune OX</div>
                    <div class="game-name">Fortune OX</div>
                </div>
                <div class="game-item">
                    <div class="game-img">Fortune Mouse</div>
                    <div class="game-name">Fortune Mouse</div>
                </div>
                <div class="game-item">
                    <div class="game-img">Lucky Neko</div>
                    <div class="game-name">Lucky Neko</div>
                </div>
                <div class="game-item">
                    <div class="game-img">Mahjong Ways</div>
                    <div class="game-name">Mahjong Ways</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 