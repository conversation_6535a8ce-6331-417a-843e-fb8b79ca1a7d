<!-- 日期选择器组件 -->
<div class="date-picker-modal" id="datePickerModal" style="display: none;">
    <div class="date-picker-content">
        <div class="date-picker-body">
            <!-- 开始日期部分 -->
            <div class="date-section">
                <div class="date-section-title">{:__('date_start')}</div>
                <div class="date-columns">
                    <!-- 年份选择 -->
                    <div class="date-column">
                        <div class="date-scroll" id="startYearScroll">
                            <!-- 年份将通过JS动态生成 -->
                        </div>
                    </div>

                    <!-- 开始日期月份 -->
                    <div class="date-column">
                        <div class="date-scroll" id="startMonthScroll">
                            <!-- 月份将通过JS动态生成 -->
                        </div>
                    </div>

                    <!-- 开始日期日 -->
                    <div class="date-column">
                        <div class="date-scroll" id="startDayScroll">
                            <!-- 日将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分隔线 -->
            <div class="vertical-divider"></div>

            <!-- 结束日期部分 -->
            <div class="date-section">
                <div class="date-section-title">{:__('date_end')}</div>
                <div class="date-columns">
                    <!-- 年份选择 -->
                    <div class="date-column">
                        <div class="date-scroll" id="endYearScroll">
                            <!-- 年份将通过JS动态生成 -->
                        </div>
                    </div>

                    <!-- 结束日期月份 -->
                    <div class="date-column">
                        <div class="date-scroll" id="endMonthScroll">
                            <!-- 月份将通过JS动态生成 -->
                        </div>
                    </div>

                    <!-- 结束日期日 -->
                    <div class="date-column">
                        <div class="date-scroll" id="endDayScroll">
                            <!-- 日将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <div class="date-picker-footer">
            <div class="button-container">
                <button class="date-picker-btn cancel-btn" id="cancelDateBtn">{:__('cancel')}</button>
                <button class="date-picker-btn confirm-btn" id="confirmDateBtn">{:__('confirm')}</button>
            </div>
        </div>
    </div>
</div>

<!-- 日期选择器样式 -->
<style>
    /* 日期选择器弹窗样式 */
    .date-picker-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 1000;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .date-picker-content {
        width: 90%;
        max-width: 400px;
        background-color: var(--darker-bg-color, #a32a37); /* 使用CSS变量 */
        border-radius: 10px;
        padding: 20px;
        color: var(--text-color);
    }

    .date-picker-body {
        display: flex;
        justify-content: space-between;
        align-items: flex-start; /* 确保所有列从顶部开始对齐 */
    }

    /* 日期部分（左右两侧） */
    .date-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    /* 日期部分标题 */
    .date-section-title {
        font-size: 18px;
        margin-bottom: 15px;
        font-weight: bold;
        height: 30px; /* 固定高度 */
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 隐藏溢出的文本 */
        text-overflow: ellipsis; /* 如果文本溢出，显示省略号 */
        width: 100%;
        text-align: center;
    }

    /* 日期列容器 */
    .date-columns {
        display: flex;
        justify-content: space-between;
        width: 100%;
    }

    .date-column {
        flex: 1;
        text-align: center;
        display: flex;
        flex-direction: column;
        height: 200px; /* 固定高度，确保所有列高度一致 */
    }

    .date-scroll {
        height: 200px;
        overflow-y: scroll; /* 使用scroll而不是auto，确保滚动行为一致 */
        margin: 0 10px;
        position: relative;
        /* 隐藏滚动条但保留滚动功能 - 多种方法确保在所有浏览器中都生效 */
        scrollbar-width: none !important; /* Firefox */
        -ms-overflow-style: none !important; /* IE and Edge */
        -webkit-overflow-scrolling: touch; /* 平滑滚动在iOS上 */
    }

    /* 确保在所有WebKit浏览器中隐藏滚动条 */
    .date-scroll::-webkit-scrollbar,
    .date-scroll::-webkit-scrollbar-thumb,
    .date-scroll::-webkit-scrollbar-track,
    .date-scroll::-webkit-scrollbar-track-piece,
    .date-scroll::-webkit-scrollbar-corner,
    .date-scroll::-webkit-resizer {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
        background: transparent !important;
        -webkit-appearance: none !important;
    }

    .date-item {
        padding: 10px 0;
        font-size: 16px;
        cursor: pointer;
    }

    .date-item.selected {
        color: var(--white-add);
        font-weight: bold;
    }

    .date-picker-footer {
        display: flex;
        justify-content: center; /* 居中对齐 */
        margin-top: 20px;
    }

    .button-container {
        display: flex;
        justify-content: center;
        gap: 20px; /* 按钮之间的间距 */
        width: 60%; /* 控制按钮容器的宽度，使按钮靠近中间 */
    }

    .date-picker-btn {
        padding: 10px 20px;
        border-radius: 20px;
        border: none;
        font-size: 16px;
        cursor: pointer;
    }

    .cancel-btn {
        background-color: transparent;
        border: 1px solid var(--gray-blue);
        color: var(--text-color);
    }

    .confirm-btn {
        background-color: var(--button-normal);
        color: var(--text-color);
        font-weight: bold;
    }

    .vertical-divider {
        width: 1px;
        background-color: rgba(255, 255, 255, 0.5);
        margin: 0 15px;
        height: 230px; /* 与日期选择器高度匹配 */
        align-self: center; /* 在父容器中垂直居中 */
    }
</style>
