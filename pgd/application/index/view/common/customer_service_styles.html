<style>
    /* 只保留特定的客服页面样式，通用样式已移至common-components.css */
    
    /* 客服页面特定样式 */
    .message-list {
        padding: 15px;
        overflow-y: auto;
        max-height: calc(100vh - 120px);
    }
    
    .message {
        margin-bottom: 20px;
        display: flex;
        flex-direction: column;
    }
    
    .message-info {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }
    
    .message-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 10px;
        flex-shrink: 0;
    }
    
    .message-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .message-sender {
        font-weight: bold;
        font-size: 14px;
        color: var(--secondary-color);
    }
    
    .message-time {
        margin-left: auto;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
    }
    
    .message-content {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 10px 15px;
        border-radius: 8px;
        font-size: 14px;
        color: #fff;
        max-width: 80%;
        margin-left: 50px;
    }
    
    .message.received .message-content {
        align-self: flex-start;
        border-top-left-radius: 0;
    }
    
    .message.sent {
        align-items: flex-end;
    }
    
    .message.sent .message-content {
        background-color: rgba(255, 255, 255, 0.2);
        border-top-right-radius: 0;
    }
    
    .message.sent .message-info {
        flex-direction: row-reverse;
    }
    
    .message.sent .message-avatar {
        margin-right: 0;
        margin-left: 10px;
    }
    
    .message.sent .message-time {
        margin-left: 0;
        margin-right: auto;
    }
    
    .chat-input {
        display: flex;
        padding: 10px;
        background-color: var(--darker-bg-color);
        position: fixed;
        bottom: 60px;
        left: 0;
        width: 100%;
        z-index: 100;
    }
    
    .chat-input input {
        flex: 1;
        padding: 10px 15px;
        border-radius: 20px;
        border: none;
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
        margin-right: 10px;
    }
    
    .chat-input button {
        background-color: var(--secondary-color);
        color: #000;
        border: none;
        border-radius: 20px;
        padding: 10px 15px;
        font-weight: bold;
        cursor: pointer;
    }
</style> 