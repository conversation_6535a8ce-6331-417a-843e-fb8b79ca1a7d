{__NOLAYOUT__}
<link rel="stylesheet" href="__CDN__/assets/css/frontend/index/deposit_popup.css?v=1.01">

<div class="deposit-history-overlay">
    <div class="deposit-history-popup">
        <div class="deposit-history-header">
            <button class="back-button" onclick="closeDepositHistory()">
                <i class="fa fa-chevron-left"></i>
            </button>
            <h2>{:__('deposit_history')}</h2>
            <button class="close-button" onclick="closeDepositHistory()">
                <i class="fa fa-times"></i>
            </button>
        </div>
        
        <div class="deposit-history-filter">
            <div class="filter-input">
                <input type="text" id="search-input" placeholder="Order ID">
                <button id="search-btn">
                    <img src="__CDN__/assets/img/frontend/btns/btn_search.png" alt="Search">
                </button>
            </div>
            <div class="filter-dates">
                <select id="date-filter">
                    <option value="today">{:__('Hoje')}</option>
                    <option value="yesterday">{:__('Ontem')}</option>
                    <option value="7days">{:__('Últimos 7 Dias')}</option>
                    <option value="15days">{:__('Últimos 15 Dias')}</option>
                    <option value="30days">{:__('Últimos 30 Dias')}</option>
                    <option value="all">{:__('Tudo')}</option>
                </select>
            </div>
        </div>
        
        <div class="deposit-history-total">
            <span>{:__('Depósito Total')}: $1,250.00</span>
        </div>
        
        <div class="deposit-history-list">
            <!-- Sample history items, to be replaced with dynamic data -->
            <div class="history-item">
                <div class="history-info">
                    <div class="history-date">2023-06-15 14:30</div>
                    <div class="history-id">Order ID: CH123456789</div>
                </div>
                <div class="history-amount">$100.00</div>
                <div class="history-status success">Success</div>
            </div>
            
            <div class="history-item">
                <div class="history-info">
                    <div class="history-date">2023-06-14 10:15</div>
                    <div class="history-id">Order ID: CH123456788</div>
                </div>
                <div class="history-amount">$75.00</div>
                <div class="history-status pending">Pending</div>
            </div>
            
            <div class="history-item">
                <div class="history-info">
                    <div class="history-date">2023-06-12 18:45</div>
                    <div class="history-id">Order ID: CH123456787</div>
                </div>
                <div class="history-amount">$50.00</div>
                <div class="history-status failed">Failed</div>
            </div>
            
            <!-- Empty state for no records -->
            <div class="empty-records" style="display: none;">
                <img src="__CDN__/assets/img/frontend/common/empty.png" alt="No Records">
                <p>{:__('Nenhum registro encontrado')}</p>
            </div>
        </div>
        
        <div class="deposit-history-pagination">
            <button class="prev-page" disabled><i class="fa fa-chevron-left"></i></button>
            <span class="page-info">Page 1 of 3</span>
            <button class="next-page"><i class="fa fa-chevron-right"></i></button>
        </div>
    </div>
</div>

<script>
function openDepositHistory() {
    document.querySelector('.deposit-history-overlay').style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeDepositHistory() {
    document.querySelector('.deposit-history-overlay').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Sample code for search functionality
document.getElementById('search-btn').addEventListener('click', function() {
    const searchValue = document.getElementById('search-input').value;
    // Implement search logic here
    console.log('Searching for:', searchValue);
});

// Sample code for date filter
document.getElementById('date-filter').addEventListener('change', function() {
    const selectedDate = this.value;
    // Implement date filtering logic here
    console.log('Filtering by date:', selectedDate);
});
</script>
