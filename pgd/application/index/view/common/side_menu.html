<!-- 左侧弹出菜单 -->
<!-- 引用侧边菜单CSS文件 -->
<link rel="stylesheet" href="__CDN__/assets/css/frontend/common/side-menu.css?v=1.01">

<!-- 防止模板标签未渲染就显示的样式 -->
<style>
    /* 隐藏未渲染的模板标签 */
    .side-menu-card-text,
    .song-list-title,
    .side-menu-group-title {
        visibility: hidden;
    }

    /* 当页面加载完成后显示 */
    .js-rendered .side-menu-card-text,
    .js-rendered .song-list-title,
    .js-rendered .side-menu-group-title {
        visibility: visible;
    }
</style>

<script>
    // 在DOMContentLoaded事件后添加js-rendered类
    document.addEventListener('DOMContentLoaded', function() {
        document.body.classList.add('js-rendered');
    });
</script>

<!-- 左侧菜单 -->
<div class="side-menu">
    <div class="side-menu-content">
        <!-- 常用功能卡片组 - 已移除，使用新的布局替代 -->

        <!-- 游戏分类卡片组 - 第一排和第二排 -->
        <div class="side-menu-group">
            <div class="side-menu-group-title">{:__('Game categories')}</div>
            <!-- 第一排：Popular, Slots -->
            <div class="side-menu-cards">
                <div class="side-menu-card" data-category="popular">
                    <div class="side-menu-card-icon">
                        <i class="fa fa-fire"></i>
                    </div>
                    <div class="side-menu-card-text">{:__('Popular')}</div>
                </div>
                <div class="side-menu-card" data-category="slots">
                    <div class="side-menu-card-icon">
                        <i class="fa fa-gamepad"></i>
                    </div>
                    <div class="side-menu-card-text">{:__('Slots')}</div>
                </div>
            </div>

            <!-- 第二排：Recent, Favorite -->
            <div class="side-menu-cards" style="margin-top: 10px;">
                <div class="side-menu-card" data-category="recent">
                    <div class="side-menu-card-icon">
                        <i class="fa fa-clock-o"></i>
                    </div>
                    <div class="side-menu-card-text">{:__('Recent')}</div>
                </div>
                <div class="side-menu-card" data-category="favorite">
                    <div class="side-menu-card-icon">
                        <i class="fa fa-star"></i>
                    </div>
                    <div class="side-menu-card-text">{:__('Favorite')}</div>
                </div>
            </div>
        </div>

        <!-- 迷你音乐播放器 (放在代理按钮上面) -->
        <div class="side-menu-group music-player-group" style="margin-top: 10px; padding-top: 0; border-top: none;">
            <div class="side-menu-group-title">{:__('Music')}</div>
            <div class="mini-player">
                <div class="mini-player-controls">
                    <div class="control-btn mini-player-prev">
                        <div class="prev-icon"></div>
                    </div>
                    <div class="control-btn mini-player-play"></div>
                    <div class="control-btn mini-player-next">
                        <div class="next-icon"></div>
                    </div>
                </div>
                <div class="mini-player-info">
                    <div class="song-title">{:__('Loading...')}</div>
                    <div class="song-count"></div>
                </div>
            </div>
        </div>

        <!-- 第三排：推广文字，左对齐，样式同游戏分类 -->
        <div class="side-menu-group">
            <div class="side-menu-group-title">{:__('Promotion')}</div>
        </div>

        <!-- 第四排：Eventos, Rebate (只显示图标，图片大小与按钮一致) -->
        <div class="side-menu-group">
            <div class="side-menu-cards">
                <div class="side-menu-card icon-only-card" data-event="events">
                    <div class="side-menu-card-icon">
                        <img src="__CDN__/assets/img/frontend/common/side_event.png" alt="Eventos">
                    </div>
                    <div class="side-menu-card-text">{:__('Events')}</div>
                </div>
                <div class="side-menu-card icon-only-card" data-event="rebate">
                    <div class="side-menu-card-icon">
                        <img src="__CDN__/assets/img/frontend/common/side_rebate.png" alt="Rebate">
                    </div>
                    <div class="side-menu-card-text">{:__('Rebate')}</div>
                </div>
            </div>
        </div>

        <!-- 第五排：Pendente, History (只显示图标，图片大小与按钮一致) -->
        <div class="side-menu-group">
            <div class="side-menu-cards">
                <div class="side-menu-card icon-only-card" data-event="pendente">
                    <div class="side-menu-card-icon">
                        <img src="__CDN__/assets/img/frontend/common/side_pendente.png" alt="Pendente">
                    </div>
                    <div class="side-menu-card-text">{:__('Pending')}</div>
                </div>
                <div class="side-menu-card icon-only-card" data-event="history">
                    <div class="side-menu-card-icon">
                        <img src="__CDN__/assets/img/frontend/common/side_history.png" alt="History">
                    </div>
                    <div class="side-menu-card-text">{:__('History')}</div>
                </div>
            </div>
        </div>

        <!-- 第六排：VIP (使用图片做背景，文字左侧居中，白色) -->
        <div class="side-menu-group">
            <div class="side-menu-cards">
                <div class="side-menu-card vip-card" data-event="vip" style="grid-column: span 2; background-image: url('__CDN__/assets/img/frontend/common/side_vip.png');">
                    <div class="side-menu-card-text">VIP</div>
                </div>
            </div>
        </div>

        <!-- 社交媒体：Instagram 和 Telegram 在同一排 -->
        <div class="side-menu-group">
            <div class="side-menu-group-title">{:__('Social Media')}</div>
            <div class="side-menu-cards">
                <div class="side-menu-card social-card" data-social="instagram">
                    <div class="side-menu-card-icon">
                        <img src="__CDN__/assets/img/frontend/socials/INS.png" alt="Instagram" style="width: 28px; height: 28px;">
                    </div>
                </div>
                <div class="side-menu-card social-card" data-social="telegram">
                    <div class="side-menu-card-icon">
                        <img src="__CDN__/assets/img/frontend/socials/TG.png" alt="Telegram" style="width: 28px; height: 28px;">
                    </div>
                </div>
            </div>
        </div>

        <!-- 音乐播放器已移至代理按钮上方 -->

        <!-- 底部其他链接 -->
        <!-- <div class="side-menu-bottom">
            <a href="{:url('index/index')}" class="side-menu-link">
                <div class="side-menu-link-icon">
                    <i class="fa fa-home"></i>
                </div>
                <span>{:__('Home')}</span>
            </a>
            <a href="{:url('user/index')}" class="side-menu-link">
                <div class="side-menu-link-icon">
                    <i class="fa fa-user"></i>
                </div>
                <span>{:__('User center')}</span>
            </a>
            <a href="{:url('agent/index')}" class="side-menu-link">
                <div class="side-menu-link-icon">
                    <i class="fa fa-users"></i>
                </div>
                <span>{:__('Agent center')}</span>
            </a>
            <a href="{:url('customerservice/index')}" class="side-menu-link">
                <div class="side-menu-link-icon">
                    <i class="fa fa-headphones"></i>
                </div>
                <span>{:__('Customer service')}</span>
            </a>
            <a href="{:url('user/settings')}" class="side-menu-link">
                <div class="side-menu-link-icon">
                    <i class="fa fa-cog"></i>
                </div>
                <span>{:__('Settings')}</span>
            </a>
        </div> -->


    </div>
</div>

<!-- 遮罩层 -->
<div class="side-menu-overlay"></div>

<!-- 全屏音乐播放器模态框 -->
<div class="music-player-modal">
    <div class="music-player-container">
        <div class="music-player-header">
            <div class="close-music-modal">
                <div class="close-icon"></div>
            </div>
        </div>

        <div class="current-song-info">
            <h2 class="current-song-title">See You Again</h2>
            <p class="current-song-artist">Wiz Khalifa ft. Charlie Puth</p>
        </div>

        <div class="player-progress">
            <div class="progress-time current-time">0:00</div>
            <div class="progress-bar-container">
                <div class="progress-bar">
                    <div class="progress-bar-fill"></div>
                    <div class="progress-handle"></div>
                </div>
            </div>
            <div class="progress-time duration">3:45</div>
        </div>

        <div class="player-controls">
            <div class="control-btn player-shuffle">
                <div class="shuffle-icon"></div>
            </div>
            <div class="control-btn player-prev">
                <div class="prev-icon"></div>
            </div>
            <div class="control-btn player-play-pause">
                <div class="play-icon"></div>
                <div class="pause-icon"></div>
            </div>
            <div class="control-btn player-next">
                <div class="next-icon"></div>
            </div>
            <div class="control-btn player-repeat">
                <div class="repeat-icon"></div>
            </div>
        </div>

        <div class="volume-control">
            <div class="volume-icon">
                <i class="fa fa-volume-up"></i>
            </div>
            <div class="volume-bar-container">
                <div class="volume-bar">
                    <div class="volume-bar-fill"></div>
                    <div class="volume-handle"></div>
                </div>
            </div>
        </div>

        <div class="song-list-container">
            <h3 class="song-list-title">{:__('Playlist')}</h3>
            <ul class="song-list">
                <li class="song-item active">
                    <div class="song-number">1</div>
                    <div class="song-info">
                        <div class="song-name">See You Again</div>
                        <div class="song-artist">Wiz Khalifa ft. Charlie Puth</div>
                    </div>
                    <div class="song-duration">3:45</div>
                </li>
                <li class="song-item">
                    <div class="song-number">2</div>
                    <div class="song-info">
                        <div class="song-name">Without You</div>
                        <div class="song-artist">Avicii</div>
                    </div>
                    <div class="song-duration">4:02</div>
                </li>
                <li class="song-item">
                    <div class="song-number">3</div>
                    <div class="song-info">
                        <div class="song-name">Faded</div>
                        <div class="song-artist">Alan Walker</div>
                    </div>
                    <div class="song-duration">3:32</div>
                </li>
                <li class="song-item">
                    <div class="song-number">4</div>
                    <div class="song-info">
                        <div class="song-name">The Nights</div>
                        <div class="song-artist">Avicii</div>
                    </div>
                    <div class="song-duration">2:56</div>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- 在引入side-menu-navigation.js之前添加 -->
<script>
    // 创建全局变量存储社交媒体链接
    window.socialMediaLinks = {
        instagram: '{$socialMedia.instagram|default="https://www.instagram.com/lotoland_group/"}',
        telegram: '{$socialMedia.telegram|default="https://t.me/+_E2zemghlelkYjQ1"}'
    };
</script>

<!-- 引用侧边菜单JS文件 -->
<script src="__CDN__/assets/js/frontend/common/songs.js?v=1.01"></script>
<script src="__CDN__/assets/js/frontend/common/side-menu.js"></script>
<script src="__CDN__/assets/js/frontend/common/music-player.js?v=1.01"></script>
<script src="__CDN__/assets/js/frontend/common/side-menu-player.js?v=1.01"></script>
<script src="__CDN__/assets/js/frontend/common/side-menu-navigation.js?v=1.01"></script>

<!-- 音乐播放器样式 -->
<style>
/* 迷你播放器样式 */
.mini-player {
    position: relative;
    width: calc(100% - 30px); /* 减去左右边距 */
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin: 8px 15px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mini-player:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* 音乐播放时的样式 */
.mini-player.playing {
    box-shadow: 0 4px 15px #67d1f9;
}

/* 为音乐播放器组添加特殊样式 */
.music-player-group {
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.mini-player-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.song-title {
    color: #fff;
    font-weight: 600;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.song-count {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
}

.mini-player-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.control-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.mini-player-play {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.2);
    position: relative;
    cursor: pointer;
}

/* 播放图标 (三角形) */
.mini-player-play::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 54%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 0 8px 14px;
    border-color: transparent transparent transparent #fff;
    transition: all 0.2s ease;
}

/* 暂停图标 (两个矩形) */
.mini-player-play::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 14px;
    opacity: 0;
    transition: all 0.2s ease;
}

/* 播放状态下显示暂停图标，隐藏播放图标 */
.mini-player.playing .mini-player-play::before {
    opacity: 0;
}

.mini-player.playing .mini-player-play::after {
    opacity: 1;
    background-image: linear-gradient(to right, #fff 4px, transparent 4px, transparent 8px, #fff 8px);
    background-repeat: no-repeat;
}

/* CSS绘制上一首/下一首图标 */
.prev-icon, .next-icon {
    position: relative;
    width: 8px;
    height: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.prev-icon::before, .prev-icon::after,
.next-icon::before, .next-icon::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    top: 50%;
    transform: translateY(-50%);
}

.prev-icon::before {
    border-width: 6px 9px 6px 0;
    border-color: transparent #fff transparent transparent;
    left: 2px;
}

.prev-icon::after {
    border-width: 6px 9px 6px 0;
    border-color: transparent #fff transparent transparent;
    left: -6px;
}

.next-icon::before {
    border-width: 6px 0 6px 9px;
    border-color: transparent transparent transparent #fff;
    right: 2px;
}

.next-icon::after {
    border-width: 6px 0 6px 9px;
    border-color: transparent transparent transparent #fff;
    right: -6px;
}

/* CSS绘制全屏图标 */
.fullscreen-icon {
    width: 10px;
    height: 10px;
    border: 2px solid #fff;
    position: relative;
}

.fullscreen-icon::before {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    border-top: 2px solid #fff;
    border-right: 2px solid #fff;
    top: -3px;
    right: -3px;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.mini-player-play:hover {
    background: rgba(255, 255, 255, 0.4);
}

.control-btn:active {
    transform: scale(0.95);
}

/* 添加动画效果 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.mini-player.playing .mini-player-play {
    animation: pulse 1.5s infinite;
}

/* 全屏音乐播放器样式 */
.music-player-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(95, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    z-index: 9999;
    display: none;
    overflow-y: auto;
}

.music-player-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 40px 20px;
    color: #fff;
}

.music-player-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 30px;
}

.close-music-modal {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.close-music-modal:hover {
    background: rgba(255, 255, 255, 0.2);
}

.close-icon {
    position: relative;
    width: 20px;
    height: 20px;
}

.close-icon::before,
.close-icon::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background-color: #fff;
    top: 9px;
    left: 0;
}

.close-icon::before {
    transform: rotate(45deg);
}

.close-icon::after {
    transform: rotate(-45deg);
}

.close-music-modal:hover .close-icon::before,
.close-music-modal:hover .close-icon::after {
    background-color: var(--button-active);
}

.current-song-info {
    text-align: center;
    margin-bottom: 40px;
}

.current-song-title {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 10px;
}

.current-song-artist {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
}

.player-progress {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.progress-time {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    width: 50px;
}

.progress-bar-container {
    flex: 1;
    padding: 0 10px;
}

.progress-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    position: relative;
    cursor: pointer;
}

.progress-bar-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 35%;
    background: #731919;
    border-radius: 2px;
}

.progress-handle {
    position: absolute;
    top: 50%;
    left: 35%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.player-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 40px;
}

.player-controls .control-btn {
    margin: 0 10px;
}

.player-play-pause {
    width: 50px;
    height: 50px;
    background: #731919;
}

.player-play-pause i {
    font-size: 18px;
}

.player-play-pause:hover {
    background: var(--secondary-color);
}

.volume-control {
    display: flex;
    align-items: center;
    margin-bottom: 40px;
}

.volume-icon {
    width: 30px;
    color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
}

.volume-bar-container {
    flex: 1;
    padding-left: 10px;
}

.volume-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    position: relative;
    cursor: pointer;
}

.volume-bar-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 70%;
    background: #731919;
    border-radius: 2px;
}

.volume-handle {
    position: absolute;
    top: 50%;
    left: 70%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.song-list-container {
    margin-top: 20px;
}

.song-list-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: rgba(255, 255, 255, 0.9);
}

.song-list {
    list-style: none;
    padding: 0;
}

.song-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.song-item:hover {
    background: rgba(255, 255, 255, 0.08);
}

.song-item.active {
    background: rgba(115, 25, 25, 0.2);
}

.song-number {
    width: 30px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
}

.song-info {
    flex: 1;
}

.song-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
}

.song-artist {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

.song-duration {
    width: 50px;
    text-align: right;
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
}

/* Modal open body style */
body.modal-open {
    overflow: hidden;
}

/* CSS绘制全屏播放器按钮图标 */
.player-controls .play-icon {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 10px 0 10px 16px;
    border-color: transparent transparent transparent #fff;
    margin-left: 4px;
}

.player-controls .pause-icon {
    display: none;
    width: 16px;
    height: 20px;
    border-left: 5px solid #fff;
    border-right: 5px solid #fff;
}

.player-controls.playing .play-icon {
    display: none;
}

.player-controls.playing .pause-icon {
    display: block;
}

/* CSS绘制上一首/下一首图标 */
.player-controls .prev-icon, .player-controls .next-icon {
    position: relative;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.player-controls .prev-icon::before, .player-controls .prev-icon::after,
.player-controls .next-icon::before, .player-controls .next-icon::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    top: 50%;
    transform: translateY(-50%);
}

.player-controls .prev-icon::before {
    border-width: 6px 9px 6px 0;
    border-color: transparent #fff transparent transparent;
    left: 2px;
}

.player-controls .prev-icon::after {
    border-width: 6px 9px 6px 0;
    border-color: transparent #fff transparent transparent;
    left: -6px;
}

.player-controls .next-icon::before {
    border-width: 6px 0 6px 9px;
    border-color: transparent transparent transparent #fff;
    right: 2px;
}

.player-controls .next-icon::after {
    border-width: 6px 0 6px 9px;
    border-color: transparent transparent transparent #fff;
    right: -6px;
}

/* CSS绘制随机播放图标 */
.player-controls .shuffle-icon {
    position: relative;
    width: 16px;
    height: 16px;
}

.player-controls .shuffle-icon::before,
.player-controls .shuffle-icon::after {
    content: '';
    position: absolute;
    background: #fff;
    height: 2px;
}

.player-controls .shuffle-icon::before {
    width: 16px;
    transform: rotate(45deg);
    top: 8px;
    left: 0;
}

.player-controls .shuffle-icon::after {
    width: 10px;
    transform: rotate(-45deg);
    top: 12px;
    left: 6px;
}

/* CSS绘制循环播放图标 */
.player-controls .repeat-icon {
    position: relative;
    width: 16px;
    height: 16px;
    border: 2px solid #fff;
    border-radius: 50%;
}

.player-controls .repeat-icon::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 4px 4px 0;
    border-color: transparent #fff transparent transparent;
    transform: rotate(-45deg);
    right: 2px;
    top: 2px;
}

/* 活跃状态的按钮 */
.player-shuffle.active .shuffle-icon::before,
.player-shuffle.active .shuffle-icon::after,
.player-repeat.active .repeat-icon,
.player-repeat.active .repeat-icon::before {
    border-color: var(--gray-blue);
    background: var(--button-active);
}

.player-repeat.active .repeat-icon {
    border-color: var(--button-active);
}
</style>
