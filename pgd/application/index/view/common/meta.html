<meta charset="utf-8">
<title>{$title|default=''|htmlentities} – {$site.name|htmlentities}</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
<meta name="renderer" content="webkit">
<meta name="referrer" content="never">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">

{if isset($keywords)}
<meta name="keywords" content="{$keywords|htmlentities}">
{/if}
{if isset($description)}
<meta name="description" content="{$description|htmlentities}">
{/if}

<link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico" />

<!-- 加载主题和通用样式 -->
<link rel="stylesheet" href="__CDN__/assets/css/frontend/variables.css?v=1.01">
<link rel="stylesheet" href="__CDN__/assets/css/frontend/theme.css?v=1.01">
<link rel="stylesheet" href="__CDN__/assets/css/frontend/common-components.css?v=1.01">
<!-- 加载通用Toast提示组件样式 -->
<link rel="stylesheet" href="__CDN__/assets/css/frontend/common/toast.css?v=1.0.2">
<!-- 加载通用空状态组件样式 -->
<link rel="stylesheet" href="__CDN__/assets/css/frontend/common/empty-state.css?v=1.02">
<!-- 加载Font Awesome图标库 -->
<link rel="stylesheet" href="__CDN__/assets/libs/font-awesome/css/font-awesome.min.css">

<!-- 全局强制样式 - 修复header宽度问题和弹窗显示问题 -->
<style>
    /* 直接内联样式，确保最高优先级 */
    html body .header,
    body .header,
    .fixed-header-section .header,
    .header {
        width: 100% !important;
        max-width: 516px !important;
        margin-left: auto !important;
        margin-right: auto !important;
        box-sizing: border-box !important;
    }

    html body .fixed-header-section,
    body .fixed-header-section,
    .fixed-header-section {
        width: 100% !important;
        max-width: 516px !important;
        margin-left: auto !important;
        margin-right: auto !important;
        box-sizing: border-box !important;
    }

    html body .horizontal-tabs,
    body .horizontal-tabs,
    .horizontal-tabs {
        width: 100% !important;
        max-width: 516px !important;
        margin-left: auto !important;
        margin-right: auto !important;
        box-sizing: border-box !important;
    }

    /* 强制隐藏所有弹窗覆盖层 - 确保页面加载时不会显示 */
    .deposit-popup-overlay,
    #depositPopupOverlay,
    .deposit-history-popup-overlay,
    #depositHistoryPopupOverlay,
    div[style*="background-color: rgba(0, 0, 0"] {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        z-index: -1 !important;
        pointer-events: none !important;
    }
</style>

<!-- 根据当前控制器加载对应的CSS -->
{if $Request.controller == 'Index' && $Request.action == 'index'}
<link rel="stylesheet" href="__CDN__/assets/css/frontend/index/index.css?v=1.0.2">
{/if}

{if $Request.controller == 'Security'}
<link rel="stylesheet" href="__CDN__/assets/css/frontend/security/security.css?v=1.01">
<link rel="stylesheet" href="__CDN__/assets/css/frontend/security/custom-balance.css?v=1.01">
{/if}

<!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
<!--[if lt IE 9]>
  <script src="__CDN__/assets/js/html5shiv.js"></script>
  <script src="__CDN__/assets/js/respond.min.js"></script>
<![endif]-->

<!-- 加载基础JS库 -->
<script src="__CDN__/assets/libs/jquery/dist/jquery.min.js"></script>

<!-- 加载通用工具脚本 -->
<script src="__CDN__/assets/js/js.cookie.min.js"></script>
<script src="__CDN__/assets/js/page-utils.js"></script>
<script src="__CDN__/assets/js/image-loader.js"></script>
<!-- 加载通用Toast提示组件 -->
<script src="__CDN__/assets/js/frontend/common/toast.js?v=1.01"></script>

<!-- 加载FastAdmin标准语言系统 -->
<script>
// FastAdmin标准语言加载
var require = {
    config: {
        'fast/lang': {
            // 语言URL，将从此URL获取所有语言数据
            langUrl: "{:url('ajax/lang', ['controllername'=>'index'])}",
        }
    }
};
</script>
<!-- 添加define函数的polyfill，替代require.js -->
<script src="__CDN__/assets/js/define-polyfill.js"></script>
<script src="__CDN__/assets/js/fast.js"></script>
