<script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.1/dist/js.cookie.min.js"></script>

<div class="tab-nav">
    <div class="tab-item {$Request.action == 'suporte' ? 'active' : ''}" data-tab="suporte">{:__('Suporte')}</div>
    <div class="tab-item {$Request.action == 'noticie' ? 'active' : ''}" data-tab="noticie">{:__('Noticie')}</div>
    <div class="tab-item {$Request.action == 'notificacao' ? 'active' : ''}" data-tab="notificacao">{:__('Notificação')}</div>
    <div class="tab-item {$Request.action == 'painel' ? 'active' : ''}" data-tab="painel">{:__('Painel Rolante')}</div>
    <div class="tab-item {$Request.action == 'bon' ? 'active' : ''}" data-tab="bon">{:__('Bônus')}</div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 使用通用工具初始化返回按钮
    PageUtils.initBackButton('.back-btn', '/index/customerservice/index');
    
    // 使用通用工具初始化标签导航
    PageUtils.initTabNavigation('.tab-nav', function(selectedTab) {
        const targetTab = selectedTab.getAttribute('data-tab');
            window.location.href = '/index/customerservice/' + targetTab;
    });
});
</script> 