<!-- 底部导航 -->
<div class="bottom-nav">
    <div data-page="index" class="nav-item {$Request.controller == 'Index' ? 'active' : ''}">
        <div class="nav-icon">
            <img src="/assets/img/frontend/btn-icons/index.png" alt="Home" class="icon-normal">
            <img src="/assets/img/frontend/btn-icons/index_selected.png" alt="Home" class="icon-selected">
        </div>
        <span>{:__('nav_home')}</span>
    </div>
    <div data-page="event" class="nav-item {$Request.controller == 'Event' ? 'active' : ''}">
        <div class="nav-icon">
            <img src="/assets/img/frontend/btn-icons/event.png" alt="Promotion" class="icon-normal">
            <img src="/assets/img/frontend/btn-icons/event_selected.png" alt="Promotion" class="icon-selected">
        </div>
        <span>{:__('nav_promotion')}</span>
    </div>
    <div data-page="agent" class="nav-item {$Request.controller == 'Agent' ? 'active' : ''}">
        <div class="nav-icon">
            <img src="/assets/img/frontend/btn-icons/agent.png" alt="Agent" class="icon-normal">
            <img src="/assets/img/frontend/btn-icons/agent_selected.png" alt="Agent" class="icon-selected">
        </div>
        <span>{:__('nav_agent')}</span>
    </div>
    <div data-page="customerservice" class="nav-item {$Request.controller == 'Customerservice' ? 'active' : ''}">
        <div class="nav-icon">
            <img src="/assets/img/frontend/btn-icons/customerservice.png" alt="Support" class="icon-normal">
            <img src="/assets/img/frontend/btn-icons/customerservice_selected.png" alt="Support" class="icon-selected">
        </div>
        <span>{:__('nav_support')}</span>
    </div>
    <div data-page="user" class="nav-item {$Request.controller == 'User' ? 'active' : ''}">
        <div class="nav-icon">
            <img src="/assets/img/frontend/btn-icons/user.png" alt="Profile" class="icon-normal">
            <img src="/assets/img/frontend/btn-icons/user_selected.png" alt="Profile" class="icon-selected">
        </div>
        <span>{:__('nav_profile')}</span>
    </div>
</div>

<style>
/* 底部导航样式 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    max-width: 516px;
    margin: 0 auto;
    height: 60px;
    background-color: var(--darker-bg-color);
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 5px 0;
    z-index: 1000;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    box-sizing: border-box;
}

/* 在大屏幕上居中显示 */
@media (min-width: 481px) {
    .bottom-nav {
        max-width: 516px !important;
        left: 0 !important;
        right: 0 !important;
        transform: none !important;
        margin: 0 auto !important;
    }
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    font-size: 12px;
    transition: color 0.3s;
    position: relative;
}

.nav-item.active {
    color: var(--secondary-color);
    font-weight: bold;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 3px;
    background-color: var(--secondary-color);
    border-radius: 2px;
}

.nav-item.active .nav-icon {
    color: var(--secondary-color);
}

.nav-icon {
    width: 24px;
    height: 24px;
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.nav-icon .icon-normal,
.nav-icon .icon-selected {
    width: 24px;
    height: 24px;
    object-fit: contain;
    position: absolute;
    top: 0;
    left: 0;
    transition: opacity 0.3s;
}

.nav-icon .icon-normal {
    opacity: 1;
}

.nav-icon .icon-selected {
    opacity: 0;
}

.nav-item.active .nav-icon .icon-normal {
    opacity: 0;
}

.nav-item.active .nav-icon .icon-selected {
    opacity: 1;
}

.nav-icon i {
    font-size: 28px;
    color: rgba(255, 255, 255, 0.6);
    transition: color 0.3s;
}

.nav-item.active .nav-icon i {
    color: var(--secondary-color);
}

.nav-item span {
    margin-top: 2px;
    font-size: 12px;
    white-space: nowrap;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 底部导航点击事件
    document.querySelectorAll('.bottom-nav .nav-item').forEach((item, index) => {
        item.addEventListener('click', function(e) {
            // 当点击的是当前页面链接时，不执行任何操作
            if(this.classList.contains('active')) {
                return;
            }
            console.log('点击底部导航:', this.getAttribute('data-page'));

            // 获取页面标识
            const page = this.getAttribute('data-page');
            if (page) {
                // 检查用户是否已登录 - 用户中心、客服页面和代理页面需要登录
                // if ((page === 'user'  || page === 'agent') && !isUserLoggedIn()) {
                //     // 用户未登录，显示Toast提示
                //     showToast('{:__("Please login first")}');
                //     return;
                // }

                // 根据页面标识跳转到相应页面
                window.location.href = '/index/' + page + '/index';
            }
        });
    });

    /**
     * 检查用户是否已登录
     * @returns {boolean} 用户登录状态
     */
    function isUserLoggedIn() {
        // 方法1: 检查全局变量
        if (typeof window.isLoggedIn !== 'undefined') {
            return window.isLoggedIn;
        }

        // 方法2: 检查DOM中的登录状态指示器
        const loggedInHeader = document.querySelector('.header-right.logged-in');
        if (loggedInHeader) {
            return true;
        }

        // 方法3: 检查余额显示元素
        const balanceDisplay = document.querySelector('.balance-display');
        if (balanceDisplay && window.getComputedStyle(balanceDisplay).display !== 'none') {
            return true;
        }

        // 方法4: 检查是否有用户session标记
        return !!localStorage.getItem('userId') ||
               !!document.cookie.split(';').some(c => c.trim().startsWith('userId=')) ||
               document.body.classList.contains('logged-in');
    }

    /**
     * 显示Toast提示
     * @param {string} message 提示消息
     */
    function showToast(message, type = 'warning') {
        // 如果存在全局Toast对象，优先使用
        if (window.Toast && typeof window.Toast.show === 'function') {
            switch(type) {
                case 'success':
                    window.Toast.success(message);
                    break;
                case 'error':
                    window.Toast.error(message);
                    break;
                case 'warning':
                    window.Toast.warning(message);
                    break;
                default:
                    window.Toast.info(message);
            }
            return;
        }

        // 否则创建简单的Toast提示
        let toast = document.querySelector('.toast-container');
        if (!toast) {
            // 创建Toast容器
            toast = document.createElement('div');
            toast.className = 'toast-container';
            toast.style.cssText = `
                position: fixed;
                bottom: 80px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 9999;
                width: auto;
                max-width: 80%;
            `;
            document.body.appendChild(toast);
        }

        // 创建新的Toast消息
        const toastMessage = document.createElement('div');
        toastMessage.className = 'toast-message';
        toastMessage.style.cssText = `
            background-color: rgba(0, 0, 0, 0.8);
            color: var(--text-color);
            padding: 10px 20px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 14px;
            text-align: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        toastMessage.textContent = message;
        toast.appendChild(toastMessage);

        // 显示Toast
        setTimeout(() => {
            toastMessage.style.opacity = '1';
        }, 10);

        // 自动关闭Toast
        setTimeout(() => {
            toastMessage.style.opacity = '0';
            setTimeout(() => {
                if (toast.contains(toastMessage)) {
                    toast.removeChild(toastMessage);
                }
            }, 300);
        }, 3000);
    }
});
</script>
