<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Centro de Mensagens')} - {:__('Painel Rolante')}</title>
    {include file="common/customer_service_styles" /}
    <style>
        /* 滚动面板特定样式 */
        /* Marquee specific styles */
        .marquee-section {
            background-color: var(--card-bg-color);
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 8px;
            color: var(--secondary-color);
        }
        
        .marquee-container {
            overflow: hidden;
            position: relative;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 5px;
            padding: 10px;
        }
        
        .marquee-items {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .marquee-item {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
            padding: 10px;
        }
        
        .marquee-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: var(--secondary-color);
        }
        
        .marquee-content {
            font-size: 14px;
            line-height: 1.5;
            color: rgba(255, 255, 255, 0.9);
        }
        
        /* Winners table */
        .winners-section {
            background-color: var(--card-bg-color);
            border-radius: var(--border-radius);
            padding: 15px;
        }
        
        .winners-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            margin-top: 10px;
        }
        
        .winners-table th {
            text-align: left;
            padding: 8px 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.7);
        }
        
        .winners-table td {
            padding: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .winners-table tr:last-child td {
            border-bottom: none;
        }
        
        .amount {
            font-weight: bold;
            color: var(--secondary-color);
        }
        
        /* 定义滚动面板中的高亮色变量 */
        :root {
            --highlight-color: #FF5252;
        }
    </style>
</head>
<body>
    <div class="container">
        {assign name="title" value="Centro de Mensagens"}
        {include file="common/common_header" /}
        {include file="common/customer_service_header" /}
        
        <div class="content">
            <!-- Announcements Section -->
            <div class="marquee-section">
                <div class="section-title">
                    <i>📢</i> {:__('Anúncios Importantes')}
                </div>
                <div class="marquee-container">
                    <div class="marquee-items">
                        <div class="marquee-item">
                            <div class="marquee-title">{:__('Manutenção Programada')}</div>
                            <div class="marquee-content">
                                {:__('O sistema estará em manutenção no dia 15/06/2023 das 03:00 às 05:00 (GMT-3). Pedimos desculpas por qualquer inconveniente.')}
                            </div>
                        </div>
                        <div class="marquee-item">
                            <div class="marquee-title">{:__('Novo Método de Pagamento')}</div>
                            <div class="marquee-content">
                                {:__('Adicionamos PIX como novo método de pagamento. Agora você pode fazer depósitos e saques instantaneamente.')}
                            </div>
                        </div>
                        <div class="marquee-item">
                            <div class="marquee-title">{:__('Promoção de Aniversário')}</div>
                            <div class="marquee-content">
                                {:__('Celebrando 3 anos! Ofertas especiais para todos os usuários de 10/06 a 20/06. Bônus de até 200% nos depósitos.')}
                            </div>
                        </div>
                        <div class="marquee-item">
                            <div class="marquee-title">{:__('Nova Atualização')}</div>
                            <div class="marquee-content">
                                {:__('Lançamos uma atualização com novos jogos e melhorias no sistema. Confira as novidades na seção de notícias.')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Winners Section -->
            <div class="winners-section">
                <div class="section-title">
                    <i>🏆</i> {:__('Ganhadores Recentes')}
                </div>
                <table class="winners-table">
                    <thead>
                        <tr>
                            <th>{:__('Usuário')}</th>
                            <th>{:__('Jogo')}</th>
                            <th>{:__('Valor')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>João S***</td>
                            <td>Fortune Tiger</td>
                            <td class="amount">R$ 12.450,00</td>
                        </tr>
                        <tr>
                            <td>Maria L***</td>
                            <td>Sweet Bonanza</td>
                            <td class="amount">R$ 8.970,00</td>
                        </tr>
                        <tr>
                            <td>Pedro R***</td>
                            <td>Gates of Olympus</td>
                            <td class="amount">R$ 5.320,00</td>
                        </tr>
                        <tr>
                            <td>Ana C***</td>
                            <td>Aviator</td>
                            <td class="amount">R$ 3.750,00</td>
                        </tr>
                        <tr>
                            <td>Carlos M***</td>
                            <td>Mines</td>
                            <td class="amount">R$ 2.890,00</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Empty state (hidden by default) -->
            <div class="empty-state" style="display: none;">
                <div class="empty-icon">📋</div>
                <div class="empty-text">{:__('Nenhuma informação disponível no momento')}</div>
            </div>
        </div>
    </div>
</body>
</html> 