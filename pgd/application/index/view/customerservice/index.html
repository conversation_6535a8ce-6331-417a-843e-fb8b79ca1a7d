<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Centro de Mensagens')}</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}
</head>
<body>
    <div class="container">
        {assign name="title" value="Centro de Mensagens"}
        {include file="common/common_header" /}

        <div class="tab-nav">
            <div class="tab-item active" data-tab="suporte">{:__('Suporte')}</div>
            <div class="tab-item" data-tab="noticie">{:__('Noticie')}</div>
            <div class="tab-item" data-tab="notificacao">{:__('Notificação')}</div>
            <div class="tab-item" data-tab="painel">{:__('Painel Rolante')}</div>
            <div class="tab-item" data-tab="bon">{:__('Bôn')}</div>
        </div>

        <!-- 添加可滚动内容区域，隐藏滚动条 -->
        <div class="scrollable-content">
            <div class="content">
                <!-- Suporte Tab Content -->
                <div class="tab-content active" id="suporte-content">
                    <!-- Online Support Card -->
                    <div class="support-card" data-url="{$supportConfig ? $supportConfig.link_url : ''}">
                        <div class="support-icon">
                            <img src="/assets/img/frontend/common/support.png" alt="Support">
                        </div>
                        <div class="support-info">
                            <div class="support-title">{:__('Apoio online 24/7')}</div>
                            <div class="support-desc">{:__('Converse com o serviço profissional de apoio ao cliente online para resolver os seus problemas.')}</div>
                            <div class="support-button">{:__('Serviço on-line')}</div>
                        </div>
                    </div>

                    <!-- Telegram Support Section -->
                    <div class="telegram-card" data-url="{$telegramConfig ? $telegramConfig.link_url : ''}">
                        <div class="telegram-header">{:__('Telegram Suporte')}</div>
                        <div class="telegram-contact">
                            <div class="telegram-icon">T</div>
                            <div class="telegram-info">
                                <div class="telegram-name">{:__('Telegram Suporte')}</div>
                                <div class="telegram-id">{$telegramConfig ? substr(strrchr($telegramConfig.link_url, '/'), 1) : 'Não disponível'}</div>
                            </div>
                            <div class="telegram-button">{:__('Contactar agora')}</div>
                        </div>
                    </div>
                </div>

                <!-- Noticie Tab Content -->
                <div class="tab-content" id="noticie-content">
                    <div class="empty-state">
                        <img src="/assets/img/frontend/common/empty.png" alt="No News" class="empty-icon">
                        <div class="empty-text">{:__('Não há notícias disponíveis no momento')}</div>
                    </div>
                </div>

                <!-- Notificação Tab Content -->
                <div class="tab-content" id="notificacao-content">
                    <div class="empty-state">
                        <img src="/assets/img/frontend/common/empty.png" alt="No Notifications" class="empty-icon">
                        <div class="empty-text">{:__('Não há notificações disponíveis no momento')}</div>
                    </div>
                </div>

                <!-- Painel Rolante Tab Content -->
                <div class="tab-content" id="painel-content">
                    <!-- 添加公告条 -->
                    <div class="announcement-banner">
                        <div class="announcement-content">
                            <div class="announcement-icon">
                                <img src="/assets/img/frontend/common/notice.png" alt="Announcement">
                            </div>
                            <div class="announcement-text">
                                <div class="announcement-scroll">
                                    <div class="scroll-content">
                                        <span class="announcement-message">De acordo com as novas regulamentações do ban</span>
                                    </div>
                                </div>
                            </div>
                            <div class="announcement-arrow">›</div>
                        </div>
                    </div>

                    <!-- 空状态区域，如果没有更多公告则显示 -->
                    <div class="empty-state" style="display: none;">
                        <img src="/assets/img/frontend/common/empty.png" alt="No Info" class="empty-icon">
                        <div class="empty-text">{:__('Não há mais informações disponíveis no momento')}</div>
                    </div>
                </div>

                <!-- Bôn Tab Content -->
                <div class="tab-content" id="bon-content">
                    <div class="empty-state">
                        <img src="/assets/img/frontend/common/empty.png" alt="No Bonus" class="empty-icon">
                        <div class="empty-text">{:__('Não há bônus disponíveis no momento')}</div>
                    </div>
                </div>
            </div>
        </div><!-- 结束 scrollable-content -->
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 使用通用工具初始化返回按钮
            PageUtils.initBackButton('.back-btn');

            // Tab navigation
            PageUtils.initTabNavigation('.tab-nav', function(selectedTab) {
                // Show corresponding content
                const targetTab = selectedTab.getAttribute('data-tab');
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(targetTab + '-content').classList.add('active');
            });

            // Support card click
            const supportCard = document.querySelector('.support-card');
            if (supportCard) {
                const supportUrl = supportCard.getAttribute('data-url');
                if (supportUrl) {
                    supportCard.addEventListener('click', function() {
                        Toast.info('{:__("Iniciando chat com suporte online...")}');
                        setTimeout(function() {
                            window.open(supportUrl, '_blank');
                        }, 500);
                    });
                } else {
                    supportCard.addEventListener('click', function() {
                        Toast.error('{:__("Serviço temporariamente indisponível")}');
                    });
                }
            }

            // Telegram card click
            const telegramCard = document.querySelector('.telegram-card');
            if (telegramCard) {
                const telegramUrl = telegramCard.getAttribute('data-url');
                if (telegramUrl) {
                    telegramCard.addEventListener('click', function() {
                        window.open(telegramUrl, '_blank');
                    });
                } else {
                    telegramCard.addEventListener('click', function() {
                        Toast.error('{:__("Serviço temporariamente indisponível")}');
                    });
                }
            }

            // 公告条点击处理
            const announcementBanner = document.querySelector('.announcement-banner');
            if (announcementBanner) {
                announcementBanner.addEventListener('click', function() {
                    // 显示公告详情
                    showAnnouncementDetail({
                        title: 'Aviso Importante',
                        content: 'De acordo com as novas regulamentações do banco, ao fazer um depósito, você deve fornecer o nome real do pagador. Isso ajudará a verificar sua identidade e proteger a segurança da sua conta.',
                    });
                });
            }

            // 显示公告详情的函数
            function showAnnouncementDetail(announcement) {
                // 创建遮罩层
                const overlay = document.createElement('div');
                overlay.className = 'announcement-overlay';

                // 创建弹窗内容
                const modal = document.createElement('div');
                modal.className = 'announcement-modal';

                // 添加关闭按钮
                const closeBtn = document.createElement('div');
                closeBtn.className = 'announcement-close';
                closeBtn.innerHTML = '×';
                closeBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    document.body.removeChild(overlay);
                });

                // 添加标题和内容
                modal.innerHTML += `
                    <div class="announcement-modal-title">${announcement.title}</div>
                    <div class="announcement-modal-content">${announcement.content}</div>
                `;

                // 将关闭按钮和模态框添加到遮罩
                modal.appendChild(closeBtn);
                overlay.appendChild(modal);

                // 点击遮罩层关闭弹窗
                overlay.addEventListener('click', function(e) {
                    if (e.target === overlay) {
                        document.body.removeChild(overlay);
                    }
                });

                // 添加到页面
                document.body.appendChild(overlay);
            }
        });
    </script>

    <style>
        /* 客服页面特定样式 */
        .content {
            padding: 20px 15px;
        }

        .support-card {
            background-color: var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .support-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            overflow: hidden;
            background-color: #FFF;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .support-icon img {
            width: 70%;
            height: 70%;
            object-fit: contain;
        }

        .support-info {
            flex: 1;
        }

        .support-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .support-desc {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.4;
            margin-bottom: 15px;
        }

        .support-button {
            display: inline-block;
            background-color: var(--button-active);
            color: var(--text-color);
            font-size: 14px;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            cursor: pointer;
        }

        .telegram-card {
            background-color: var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
        }

        .telegram-header {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .telegram-contact {
            display: flex;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius);
            padding: 10px 15px;
        }

        .telegram-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 15px;
            background-color: #0088cc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: var(--text-color);
        }

        .telegram-info {
            flex: 1;
        }

        .telegram-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .telegram-id {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .telegram-button {
            background-color: var(--button-active);
            color: var(--text-color);
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: bold;
            cursor: pointer;
            white-space: nowrap;
        }

        /* 公告条样式 */
        .announcement-banner {
            background-color: var(--secondary-color);
            border-radius: 10px;
            margin: 0 0 20px 0;
            overflow: hidden;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .announcement-content {
            display: flex;
            align-items: center;
            padding: 12px 15px;
        }

        .announcement-icon {
            display: flex;
            justify-content: center;
            margin-right: 15px;
            width: 30px;
            height: 30px;   
        }

        .announcement-sound-icon {
            font-size: 20px;
            color: var(--secondary-color);
        }

        .announcement-text {
            flex: 1;
            overflow: hidden;
        }

        .announcement-scroll {
            width: 100%;
            overflow: hidden;
        }

        .scroll-content {
            display: flex;
            flex-direction: column;
        }

        .announcement-message {
            font-size: 14px;
            color: #FFF;
            white-space: nowrap;
            font-weight: bold;
        }

        .announcement-message-cn {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.8);
            white-space: nowrap;
            margin-top: 2px;
        }

        .announcement-arrow {
            font-size: 24px;
            font-weight: bold;
            margin-left: 10px;
            color: var(--secondary-color);
        }

        /* Tab Content Areas */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 滚动内容区域 */
        .scrollable-content {
            max-height: calc(100vh - 110px);
            overflow-y: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE 和 Edge */
        }

        .scrollable-content::-webkit-scrollbar {
            display: none; /* Chrome, Safari 和 Opera */
        }

        /* 空状态样式 */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 80px 20px;
            text-align: center;
        }

        .empty-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            opacity: 0.7;
        }

        .empty-text {
            color: var(--secondary-color);
            font-size: 14px;
        }

        /* 公告详情弹窗样式 */
        .announcement-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .announcement-modal {
            background-color: var(--darker-bg-color);
            width: 85%;
            max-width: 380px;
            border-radius: 12px;
            padding: 25px 20px;
            position: relative;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        }

        .announcement-close {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 24px;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
        }

        .announcement-modal-title {
            font-size: 18px;
            font-weight: bold;
            color: var(--secondary-color);
            margin-bottom: 5px;
            text-align: center;
        }

        .announcement-modal-title-cn {
            font-size: 16px;
            color: rgba(255, 215, 0, 0.8);
            margin-bottom: 20px;
            text-align: center;
        }

        .announcement-modal-content {
            font-size: 14px;
            line-height: 1.5;
            color: #FFFFFF;
            margin-bottom: 10px;
        }

        .announcement-modal-content-cn {
            font-size: 13px;
            line-height: 1.5;
            color: rgba(255, 255, 255, 0.8);
        }
    </style>

<script>
    $(function() {
        // 返回按钮点击事件
        $('.back-btn').on('click', function() {
            history.back();
        });
    });
</script>
</body>
</html>