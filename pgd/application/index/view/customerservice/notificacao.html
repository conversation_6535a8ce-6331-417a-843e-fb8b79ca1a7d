<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Centro de Mensagens')} - {:__('Notificação')}</title>
    {include file="common/customer_service_styles" /}
    <style>
        /* 通知页面特定样式 */
        .notification-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .notification-item {
            background-color: var(--card-bg-color);
            border-radius: var(--border-radius);
            padding: 15px;
            position: relative;
            transition: background-color 0.3s;
        }
        
        .notification-item.unread {
            border-left: 3px solid var(--unread-color);
        }
        
        .notification-item.unread::before {
            content: '';
            position: absolute;
            top: 15px;
            right: 15px;
            width: 8px;
            height: 8px;
            background-color: var(--unread-color);
            border-radius: 50%;
        }
        
        .notification-title {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 16px;
        }
        
        .notification-message {
            font-size: 14px;
            line-height: 1.5;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 10px;
        }
        
        .notification-time {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            display: flex;
            align-items: center;
        }
        
        .notification-time i {
            margin-right: 5px;
            font-size: 14px;
        }
        
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 50px 0;
            color: rgba(255, 255, 255, 0.6);
            text-align: center;
        }
        
        .empty-icon {
            font-size: 40px;
            margin-bottom: 20px;
        }
        
        .empty-text {
            font-size: 16px;
        }
        
        .unread-color {
            color: var(--unread-color);
        }
    </style>
</head>
<body>
    <div class="container">
        {assign name="title" value="Centro de Mensagens"}
        {include file="common/common_header" /}
        {include file="common/customer_service_header" /}
        
        <div class="content">
            <div class="notification-list">
                <div class="notification-item unread">
                    <div class="notification-title">{:__('Depósito confirmado')}</div>
                    <div class="notification-message">
                        {:__('Seu depósito de R$ 100,00 foi confirmado e creditado em sua conta. Seu saldo atual é de R$ 250,00.')}
                    </div>
                    <div class="notification-time">
                        <i class="fa fa-clock-o">🕒</i> {:__('Hoje')} 10:45
                    </div>
                </div>

                <div class="notification-item unread">
                    <div class="notification-title">{:__('Bônus creditado')}</div>
                    <div class="notification-message">
                        {:__('Você recebeu um bônus de boas-vindas de R$ 50,00. Este bônus pode ser utilizado em qualquer jogo da plataforma.')}
                    </div>
                    <div class="notification-time">
                        <i class="fa fa-clock-o"></i> {:__('Hoje')} 09:30
                    </div>
                </div>

                <div class="notification-item">
                    <div class="notification-title">{:__('Saque processado')}</div>
                    <div class="notification-message">
                        {:__('Seu pedido de saque de R$ 200,00 foi processado com sucesso e está a caminho da sua conta bancária. Pode levar até 24 horas para aparecer.')}
                    </div>
                    <div class="notification-time">
                        <i class="fa fa-clock-o"></i> {:__('Ontem')} 16:15
                    </div>
                </div>

                <div class="notification-item">
                    <div class="notification-title">{:__('Novo método de pagamento')}</div>
                    <div class="notification-message">
                        {:__('Adicionamos PIX como novo método de pagamento. Agora você pode fazer depósitos e saques instantaneamente.')}
                    </div>
                    <div class="notification-time">
                        <i class="fa fa-clock-o"></i> 15/05/2023 11:00
                    </div>
                </div>
            </div>

            <!-- Empty state (hidden by default) -->
            <div class="empty-state" style="display: none;">
                <div class=“fa fa-bell-o”></div>
                <div class="empty-text">{:__('Nenhuma notificação no momento')}</div>
            </div>
        </div>
    </div>
</body>
</html> 