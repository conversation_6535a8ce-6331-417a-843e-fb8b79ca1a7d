<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Centro de Mensagens')} - {:__('Noticie')}</title>
    {include file="common/customer_service_styles" /}
    <style>
        /* 新闻页面特定样式 */
        .news-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .news-item {
            background-color: var(--card-bg-color);
            border-radius: var(--border-radius);
            overflow: hidden;
        }
        
        .news-image {
            width: 100%;
            height: 160px;
            object-fit: cover;
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
        }
        
        .news-content {
            padding: 15px;
        }
        
        .news-category {
            display: inline-block;
            padding: 3px 8px;
            background-color: var(--primary-color);
            color: #FFF;
            font-size: 12px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .news-category.promotion {
            background-color: #6200EA;
        }
        
        .news-category.update {
            background-color: #00C853;
        }
        
        .news-category.event {
            background-color: #FF6D00;
        }
        
        .news-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            line-height: 1.3;
        }
        
        .news-summary {
            font-size: 14px;
            line-height: 1.5;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
        }
        
        .news-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 12px;
            font-size: 13px;
        }
        
        .news-date {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .news-read-more {
            background-color: transparent;
            color: var(--secondary-color);
            border: 1px solid var(--secondary-color);
            border-radius: 15px;
            padding: 5px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .news-read-more:active {
            background-color: var(--secondary-color);
            color: #000;
        }
        
        /* 定义新闻页面中使用的主要颜色变量 */
        :root {
            --primary-color: #E63946;
        }
    </style>
</head>
<body>
    <div class="container">
        {assign name="title" value="Centro de Mensagens"}
        {include file="common/common_header" /}
        {include file="common/customer_service_header" /}
        
        <div class="content">
            <div class="news-list">
                <div class="news-item">
                    <img src="/assets/img/frontend/news/promo.jpg" alt="Promotion" class="news-image">
                    <div class="news-content">
                        <span class="news-category promotion">{:__('Promoção')}</span>
                        <h3 class="news-title">{:__('Bônus Especial de Boas-Vindas para Novos Jogadores')}</h3>
                        <p class="news-summary">
                            {:__('Estamos oferecendo um bônus exclusivo de 200% até R$ 1.000 para todos os novos jogadores que se registrarem este mês. Aproveite esta oportunidade incrível!')}
                        </p>
                        <div class="news-footer">
                            <div class="news-date">10/06/2023</div>
                            <button class="news-read-more">{:__('Ler mais')}</button>
                        </div>
                    </div>
                </div>
                
                <div class="news-item">
                    <img src="/assets/img/frontend/news/update.jpg" alt="Update" class="news-image">
                    <div class="news-content">
                        <span class="news-category update">{:__('Atualização')}</span>
                        <h3 class="news-title">{:__('Novos Jogos Adicionados à Nossa Plataforma')}</h3>
                        <p class="news-summary">
                            {:__('Temos o prazer de anunciar que adicionamos mais de 50 novos jogos à nossa plataforma, incluindo títulos populares como Fortune Tiger, Sweet Bonanza e Gates of Olympus.')}
                        </p>
                        <div class="news-footer">
                            <div class="news-date">05/06/2023</div>
                            <button class="news-read-more">{:__('Ler mais')}</button>
                        </div>
                    </div>
                </div>
                
                <div class="news-item">
                    <img src="/assets/img/frontend/news/event.jpg" alt="Event" class="news-image">
                    <div class="news-content">
                        <span class="news-category event">{:__('Evento')}</span>
                        <h3 class="news-title">{:__('Torneio Semanal com Prêmio de R$ 50.000')}</h3>
                        <p class="news-summary">
                            {:__('Participe do nosso torneio semanal e concorra a um prêmio total de R$ 50.000. O torneio começa toda segunda-feira e termina domingo à meia-noite. Não perca esta chance!')}
                        </p>
                        <div class="news-footer">
                            <div class="news-date">01/06/2023</div>
                            <button class="news-read-more">{:__('Ler mais')}</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Empty state (hidden by default) -->
            <div class="empty-state" style="display: none;">
                <div class="empty-icon">📰</div>
                <div class="empty-text">{:__('Nenhuma notícia disponível no momento')}</div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Read more buttons
            document.querySelectorAll('.news-read-more').forEach(button => {
                button.addEventListener('click', function() {
                    const newsTitle = this.closest('.news-item').querySelector('.news-title').textContent.trim();
                    Toast.info(`{:__('Lendo mais sobre')}: ${newsTitle}`);
                    // Here you would navigate to the full article page
                });
            });
        });
    </script>
</body>
</html> 