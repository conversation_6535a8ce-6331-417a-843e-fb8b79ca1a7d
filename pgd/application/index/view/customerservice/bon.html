<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('Centro de Mensagens')} - {:__('Bônus')}</title>
    {include file="common/customer_service_styles" /}
    <style>
        /* 奖金页面特定样式 */
        .bon-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .bon-item {
            background-color: var(--card-bg-color);
            border-radius: var(--border-radius);
            padding: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .bon-item.available {
            border-left: 3px solid var(--highlight-color);
        }
        
        .bon-item.expired {
            opacity: 0.7;
        }
        
        .bon-badge {
            position: absolute;
            top: 0;
            right: 0;
            background-color: var(--secondary-color);
            color: #000;
            padding: 3px 10px;
            font-size: 12px;
            font-weight: bold;
            transform: rotate(45deg) translate(15px, -10px);
            width: 80px;
            text-align: center;
        }
        
        .bon-badge.expired {
            background-color: #888;
        }
        
        .bon-title {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        
        .bon-title i {
            margin-right: 10px;
            font-size: 22px;
        }
        
        .bon-amount {
            font-size: 26px;
            font-weight: bold;
            color: var(--secondary-color);
            margin: 10px 0;
        }
        
        .bon-conditions {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .bon-conditions-title {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 15px;
        }
        
        .bon-condition-item {
            display: flex;
            margin-bottom: 5px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .bon-condition-item i {
            margin-right: 8px;
            color: var(--secondary-color);
        }
        
        .bon-dates {
            display: flex;
            justify-content: space-between;
            font-size: 13px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 15px;
        }
        
        .bon-action {
            margin-top: 15px;
            text-align: center;
        }
        
        .bon-button {
            background-color: var(--secondary-color);
            color: #000;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            cursor: pointer;
            font-size: 14px;
        }
        
        .bon-button:disabled {
            background-color: #555;
            color: #888;
            cursor: not-allowed;
        }
        
        /* 定义高亮颜色变量 */
        :root {
            --highlight-color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        {assign name="title" value="Centro de Mensagens"}
        {include file="common/common_header" /}
        {include file="common/customer_service_header" /}
        
        <div class="content">
            <div class="bon-list">
                <!-- Available Bonus -->
                <div class="bon-item available">
                    <div class="bon-badge">{:__('Disponível')}</div>
                    <div class="bon-title">
                        <i>🎁</i> {:__('Bônus de Boas-Vindas')}
                    </div>
                    <div class="bon-amount">R$ 100,00</div>
                    <div class="bon-conditions">
                        <div class="bon-conditions-title">{:__('Condições')}:</div>
                        <div class="bon-condition-item">
                            <i>✓</i> {:__('Apostar 3x o valor do bônus')}
                        </div>
                        <div class="bon-condition-item">
                            <i>✓</i> {:__('Válido para todos os jogos')}
                        </div>
                        <div class="bon-condition-item">
                            <i>✓</i> {:__('Saque disponível após cumprir requisitos')}
                        </div>
                    </div>
                    <div class="bon-dates">
                        <div>{:__('Válido até')}: 30/06/2023</div>
                    </div>
                    <div class="bon-action">
                        <button class="bon-button">{:__('Resgatar Agora')}</button>
                    </div>
                </div>
                
                <!-- Available Bonus -->
                <div class="bon-item available">
                    <div class="bon-badge">{:__('Disponível')}</div>
                    <div class="bon-title">
                        <i>🏆</i> {:__('Bônus da Semana')}
                    </div>
                    <div class="bon-amount">R$ 50,00</div>
                    <div class="bon-conditions">
                        <div class="bon-conditions-title">{:__('Condições')}:</div>
                        <div class="bon-condition-item">
                            <i>✓</i> {:__('Apostar 2x o valor do bônus')}
                        </div>
                        <div class="bon-condition-item">
                            <i>✓</i> {:__('Válido apenas para jogos de slot')}
                        </div>
                    </div>
                    <div class="bon-dates">
                        <div>{:__('Válido até')}: 15/06/2023</div>
                    </div>
                    <div class="bon-action">
                        <button class="bon-button">{:__('Resgatar Agora')}</button>
                    </div>
                </div>
                
                <!-- Expired Bonus -->
                <div class="bon-item expired">
                    <div class="bon-badge expired">{:__('Expirado')}</div>
                    <div class="bon-title">
                        <i>🎯</i> {:__('Bônus de Depósito')}
                    </div>
                    <div class="bon-amount">R$ 75,00</div>
                    <div class="bon-conditions">
                        <div class="bon-conditions-title">{:__('Condições')}:</div>
                        <div class="bon-condition-item">
                            <i>✓</i> {:__('Apostar 3x o valor do bônus')}
                        </div>
                        <div class="bon-condition-item">
                            <i>✓</i> {:__('Válido para todos os jogos')}
                        </div>
                    </div>
                    <div class="bon-dates">
                        <div>{:__('Expirou em')}: 01/05/2023</div>
                    </div>
                    <div class="bon-action">
                        <button class="bon-button" disabled>{:__('Expirado')}</button>
                    </div>
                </div>
            </div>
            
            <!-- Empty state (hidden by default) -->
            <div class="empty-state" style="display: none;">
                <div class="empty-icon">🎁</div>
                <div class="empty-text">{:__('Nenhum bônus disponível no momento')}</div>
            </div>
        </div>
    </div>
</body>
</html> 