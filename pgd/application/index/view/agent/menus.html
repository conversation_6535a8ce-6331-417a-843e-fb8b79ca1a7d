<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    
    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }
        
        body {
            background-color: var(--outer-bg-color) !important;
            color: var(--text-color);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            background-color: var(--main-bg-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 设置标题变量，然后引入通用header部分 -->
        {assign name="title" value="Convidar"}
        {include file="common/common_header" /}
        <!-- 引入共用header和tabs -->
        {include file="agent/common/agent_header_tabs" title=__('my_data_title') active_tab="menus" /}
        
        <!-- 引入共用数据部分 -->
        {include file="agent/common/agent_data_section" /}
    </div>
</body>
</html> 