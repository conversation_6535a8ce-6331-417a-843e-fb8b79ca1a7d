<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('agent_title')}</title>
    
    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}
    
    <!-- 引入Agent专用CSS -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/agent/index.css?v=1.0.2">
</head>
<body>
    <div class="container">
        <!-- 设置标题变量，然后引入通用header部分 -->
        {assign name="title" value="Convidar"}
        {include file="common/common_header" /}
        
        <!-- 引入标签导航 -->
        <div class="tab-nav">
            <div class="tab-item active" data-tab="link">{:__('tab_link')}</div>
            <div class="tab-item" data-tab="menus">{:__('tab_my_data')}</div>
            <div class="tab-item" data-tab="all_data">{:__('tab_all_data')}</div>
            <div class="tab-item" data-tab="subordinates">{:__('tab_subordinates')}</div>
            <div class="tab-item" data-tab="bonus">{:__('tab_bonus')}</div>
        </div>
        
        <!-- 添加可滚动内容区域，隐藏滚动条 -->
        <div class="scrollable-content">
        <!-- 代理信息部分 -->
        <div class="agent-info-box">
            <div class="agent-level-badge">
                <div class="badge-bg">
                    <img src="/assets/img/frontend/common/170x150_png.png" alt="VIP Badge">
                </div>
                <div class="badge-circle">?</div>
            </div>
            <div class="agent-info-content">
                <div class="bonus-row">
                        <div class="bonus-label">{:__('bonus')}</div>
                        <div class="bonus-value">{$balance}</div>
                </div>
                <div class="agent-mode-row">
                        <div class="agent-mode-text">{:__('agent_mode')}</div>
                        <div class="agent-mode-text level-difference">{:__('level_difference')}</div>
                </div>
            </div>
            <div class="action-buttons">
                    <div class="action-btn receive-btn">{:__('receive')}</div>
                    <div class="action-btn history-btn">{:__('history')}</div>
            </div>
        </div>
        
        <!-- 代理信息下方的二维码和邀请码部分 -->
        <div class="qr-section">
                <div class="qr-title">{:__('my_link')}</div>
            <div class="qr-code-container">
                <div class="qr-code">
                    <div id="qrcode"></div>
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
                    <script>
                        var link = "{$link_url}";
                        new QRCode(document.getElementById("qrcode"), {
                            text: link,
                            width: 128,
                            height: 128
                        });
                    </script>
                </div>
                    <div class="share-qr-btn">{:__('share_qr')}</div>
            </div>
            <div class="qr-info">
                <div class="my-link-container">
                    <div class="link-text" style="display:inline-block;"> {$link_url}
                        <span id="copyLinkBtn"><i class="fa fa-copy"></i></span>
                    </div>
                    <div id="qrcode" style="display:inline-block; vertical-align:middle; margin-left:10px;"></div>
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
                    <script>
                        var link = "{$link_url}";
                        new QRCode(document.getElementById("qrcode"), {
                            text: link,
                            width: 128,
                            height: 128
                        });
                    </script>
                </div>
            </div>
                <div class="info-row">
                        <div class="info-label">{:__('subordinates_direct')}</div>
                        <div class="info-value">{$r_count}</div>
                </div>
                <div class="info-row">
                        <div class="info-label">{:__('invitation_code')}</div>
                        <div class="info-value"><span class="code-value">{$id_code}</span>
                             <span id="copyCodeBtn"><i class="fa fa-copy"></i></span>
                            </div>
                </div>
                
                <!-- 社交分享栏 -->
                <div class="social-bar">
                        <div class="social-bar-label">{:__('share')}</div>
                    <div class="social-icons-row">
                        <div class="social-icon" data-platform="telegram">
                            <img src="/assets/img/frontend/socials/TG.png" alt="Telegram">
                        </div>
                        <div class="social-icon" data-platform="facebook">
                            <img src="/assets/img/frontend/socials/FaceBook.png" alt="Facebook">
                        </div>
                        <div class="social-icon" data-platform="whatsapp">
                            <img src="/assets/img/frontend/socials/WhatsApp.png" alt="WhatsApp">
                        </div>
                        <div class="social-icon" data-platform="instagram">
                            <img src="/assets/img/frontend/socials/INS.png" alt="Instagram">
                        </div>
                        <div class="social-icon" data-platform="youtube">
                            <img src="/assets/img/frontend/socials/18+.png" alt="Youtube">
                        </div>
                        <div class="social-icon" data-platform="tiktok">
                            <img src="/assets/img/frontend/socials/Tiktok.png" alt="TikTok">
                        </div>
                    </div>
                </div>
            </div>
            <!-- 代理网络部分 -->
            <img src="/assets/img/frontend/agent/agent.png" alt="Agent Network" class="responsive-img">
        </div>
    </div>
</div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
        // 初始化返回按钮
        const backBtn = document.getElementById('backBtn');
        if (backBtn) {
            backBtn.addEventListener('click', function(e) {
                e.preventDefault();
                window.location.href = backBtn.getAttribute('data-url') || '/';
            });
        }
        
        // 初始化标签导航
        const tabItems = document.querySelectorAll('.tab-nav .tab-item');
        tabItems.forEach(tab => {
            tab.addEventListener('click', function() {
                const tabName = this.getAttribute('data-tab');
                const tabMapping = {
                    'link': '/index/agent/index',
                    'menus': '/index/agent/menus',
                    'all_data': '/index/agent/all_data',
                    'subordinates': '/index/agent/subordinates',
                    'bonus': '/index/agent/bonus'
                };
                
                if (tabMapping[tabName]) {
                    window.location.href = tabMapping[tabName];
                }
            });
        });
        
        // 复制链接功能
        const copyLinkBtn = document.getElementById('copyLinkBtn');
        if (copyLinkBtn) {
            copyLinkBtn.addEventListener('click', function() {
                const linkText = '{$link_url}';
                copyToClipboard(linkText, function() {
                    Toast.success('{:__("copy_success")}');
                });
            });
        }
        
        // 复制邀请码功能
        const copyCodeBtn = document.getElementById('copyCodeBtn');
        if (copyCodeBtn) {
            copyCodeBtn.addEventListener('click', function() {
                const codeText = '{$id_code}';
                copyToClipboard(codeText, function() {
                    Toast.success('{:__("copy_success")}');
                });
            });
        }
        
        // 社交分享功能
        const socialIcons = document.querySelectorAll('.social-icon');
        socialIcons.forEach(icon => {
                icon.addEventListener('click', function() {
                    const platform = this.getAttribute('data-platform');
                    const shareUrl = '{$link_url}';
                    let shareWindow = null;
                    
                    switch(platform) {
                        case 'telegram':
                            shareWindow = window.open(`https://t.me/share/url?url=${encodeURIComponent(shareUrl)}`, '_blank');
                            break;
                        case 'facebook':
                            shareWindow = window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`, '_blank');
                            break;
                        case 'whatsapp':
                            shareWindow = window.open(`https://api.whatsapp.com/send?text=${encodeURIComponent(shareUrl)}`, '_blank');
                            break;
                        case 'instagram':
                        case 'youtube':
                        case 'tiktok':
                    default:
                        Toast.info('{:__("processing")}');
                            break;
                    }
                    
                    if (shareWindow) {
                        setTimeout(() => shareWindow.focus(), 500);
                    }
                });
            });
        
        // 领取按钮功能
        const receiveBtn = document.querySelector('.receive-btn');
        if (receiveBtn) {
            receiveBtn.addEventListener('click', function() {
                Toast.info('{:__("processing")}');
            });
        }
        
        // 历史记录按钮功能
        const historyBtn = document.querySelector('.history-btn');
        if (historyBtn) {
            historyBtn.addEventListener('click', function() {
                window.location.href = '/index/agent/bonus';
            });
        }
        
        // 分享二维码功能
        const shareQrBtn = document.querySelector('.share-qr-btn');
        if (shareQrBtn) {
            shareQrBtn.addEventListener('click', function() {
                Toast.info('{:__("processing")}');
            });
        }
        
        // 复制到剪贴板的通用函数
        function copyToClipboard(text, successCallback) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            
            textArea.style.position = 'fixed';
            textArea.style.top = '0';
            textArea.style.left = '0';
            textArea.style.width = '2em';
            textArea.style.height = '2em';
            textArea.style.padding = '0';
            textArea.style.border = 'none';
            textArea.style.outline = 'none';
            textArea.style.boxShadow = 'none';
            textArea.style.background = 'transparent';
            
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                const successful = document.execCommand('copy');
                if (successful && typeof successCallback === 'function') {
                    successCallback();
                }
            } catch (err) {
                console.error('复制失败:', err);
            }
            
            document.body.removeChild(textArea);
        }
        });
    </script>
</body>
</html> 
