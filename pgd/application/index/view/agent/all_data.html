<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Todos os Dados</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }

        body {
            background-color: var(--outer-bg-color) !important;
            color: var(--text-color);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        .container {
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            background-color: var(--main-bg-color);
            position: relative; /* 添加相对定位，作为统计面板的定位参考 */
            padding-bottom: 200px; /* 为底部统计面板留出空间 */
            min-height: 100vh; /* 确保容器至少有视口高度 */
        }

        /* 搜索栏 */
        .search-bar {
            display: flex;
            justify-content: space-between;
            padding: 10px 15px;
            background-color: var(--main-bg-color);
        }

        .date-picker {
            flex: 1;
            padding: 8px 12px;
            background-color: var(--darker-bg-color); /* 使用CSS变量 */
            border: none;
            border-radius: 20px;
            color: var(--text-color);
            font-size: 12px;
            text-align: center;
            margin-right: 10px;
            cursor: pointer;
        }

        .search-input {
            flex: 1;
            display: flex;
            align-items: center;
            background-color: var(--darker-bg-color); /* 使用CSS变量 */
            border-radius: 20px;
            padding: 0 12px;
        }

        .search-input input {
            width: 100%;
            background: transparent;
            border: none;
            color: var(--text-color);
            padding: 8px 0;
            font-size: 12px;
        }

        .search-input input::placeholder {
            color: var(--stat-label-color);
        }

        .search-input input:focus {
            outline: none;
        }

        .search-icon {
            color: var(--text-color);
            font-size: 16px;
            margin-left: 5px;
            cursor: pointer;
        }



        /* 下属列表样式 */
        .subordinates-list {
            padding: 10px 15px;
            max-height: calc(8* 85px); /* 修改为8个卡片的高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            position: relative; /* 相对定位 */
            z-index: 1; /* 设置层级低于统计面板 */
        }

        /* 隐藏滚动条 - Chrome, Safari, Opera */
        .subordinates-list::-webkit-scrollbar {
            display: none;
        }

        .subordinate-card {
            position: relative;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 0; /* 移除间距 */
        }

        /* 奇数项使用深色背景，偶数项无背景 */
        .subordinate-card:nth-child(odd) {
            background-color: var(--darker-bg-color, var(--secondary-color)); /* 使用CSS变量 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* 只有奇数项有阴影 */
        }

        .subordinate-card:nth-child(even) {
            background-color: var(--secondary-color, var(--card-bg)); /* 无背景色 */
            box-shadow: none; /* 移除阴影 */
        }

        .vip-badge {
            position: absolute;
            top: 5px;
            left: 5px; /* 改为左边 */
            background-color: var(--card-bg); /* */
            color: var(--text-color);
            font-weight: bold;
            padding: 3px 6px; /* 缩小内边距 */
            border-radius: 5px; /* 简化圆角 */
            font-size: 10px; /* 缩小字体 */
            z-index: 1; /* 确保在最上层 */
        }

        .subordinate-info {
            display: flex;
            justify-content: space-between;
        }

        .left-info, .right-info {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .player-id {
            color: var(--text-color);
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-left: 25px; /* 向右移动，避开VIP角标 */
        }

        .register-date {
            color: var(--text-color);
            font-size: 14px;
            opacity: 0.8;
            padding-left: 25px; /* 向右移动，保持与ID对齐 */
        }

        .bet-amount, .deposit-amount {
            font-size: 14px;
            text-align: right;
            margin-bottom: 5px;
        }

        .amount-label {
            color: var(--stat-label-color); /* 标签颜色 */
            display: inline;
        }

        .amount-value {
            color: var(--text-color); /* 值颜色 */
            display: inline;
            font-weight: bold;
        }

        /* 复制按钮样式 */
        .copy-id-btn {
            margin-left: 5px;
            background: none;
            border: none;
            color: var(--text-color); 
            cursor: pointer;
            font-size: 14px;
        }

        /* 复制按钮的提示框 */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 120px;
            background-color: rgba(0, 0, 0, 0.7);
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
        }

        .tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.7) transparent transparent transparent;
        }

        .tooltip.show .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 50px 0;
        }

        .empty-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 15px;
        }

        .empty-text {
            color:var(--empty-text-color);
            font-size: 14px;
        }

        /* 统计面板样式 */
        .stats-panel {
            background-color: var(--darker-bg-color); /* 使用CSS变量 */
            border-radius: 0; /* 移除圆角 */
            padding: 15px 20px; /* 减小内边距，使布局更紧凑 */
            margin: 0; /* 移除外边距 */
            width: 100%; /* 宽度与container相同 */
            box-shadow: none; /* 移除阴影 */
            display: flex; /* 使用flex布局 */
            justify-content: space-between; /* 左右两列分开对齐 */
            position: absolute; /* 使面板固定在底部 */
            bottom: 0; /* 固定在底部 */
            left: 0; /* 左对齐 */
            z-index: 2; /* 设置层级高于列表 */
        }

        .stats-column {
            width: 48%; /* 稍微减小宽度，增加两列之间的间距 */
        }

        /* 左侧列样式 */
        .stats-column:first-child {
            text-align: left; /* 左对齐 */
        }

        /* 右侧列样式 */
        .stats-column:last-child {
            text-align: right; /* 右对齐 */
        }

        .stats-row {
            display: flex;
            margin-bottom: 10px;
            align-items: center; /* 垂直居中对齐 */
        }

        /* 左侧列的行内容左对齐 */
        .stats-column:first-child .stats-row {
            justify-content: flex-start;
        }

        /* 右侧列的行内容右对齐 */
        .stats-column:last-child .stats-row {
            justify-content: flex-end;
        }

        .stats-row:last-child {
            margin-bottom: 0;
        }

        .stats-label {
            color: var(--stat-label-color); /* 使用指定的颜色 */
            font-size: 14px;
            margin-right: 5px; /* 为左侧列添加右边距 */
        }

        .stats-value {
            color: var(--text-color); /* 改为白色 */
            font-size: 14px;
            font-weight: bold;
        }

        /* 右侧列标签样式调整 */
        .stats-column:last-child .stats-label {
            margin-right: 5px; /* 保持标签和值之间的间距 */
        }
    </style>
</head>
<body>
    <div class="container">
        {assign name="title" value="Convidar"}
        {include file="common/common_header" /}
        <!-- 引入共用header和tabs -->
        {include file="agent/common/agent_header_tabs" title=__('tab_all_data') active_tab="all_data" /}

        <!-- 搜索栏 -->
        <div class="search-bar">
            <div class="date-picker" id="datePicker">{$date_range|default='01/01/2023 - 01/01/2023'}</div>
            <div class="search-input">
                <input type="text" placeholder="{:__('member_id_placeholder')}">
                <div class="search-icon"><i class="fa fa-search"></i></div>
            </div>
        </div>

        <!-- 下属列表 -->
        <div class="subordinates-list">
            {empty name="subordinates"}
                <!-- 空状态（使用通用样式） -->
                <div class="empty-state">
                    <img src="__CDN__/assets/img/frontend/common/empty.png" alt="No Data" class="empty-icon">
                    <div class="empty-text">{:__('no_data')}</div>
                </div>
            {else/}
                <!-- 初始显示的数据 -->
                <div id="initial-items">
                    {volist name="subordinates" id="sub" offset="0" length="8"}
                        <div class="subordinate-card">
                            <div class="vip-badge">VIP{$sub.vip_level}</div>
                            <div class="subordinate-info">
                                <div class="left-info">
                                    <div class="player-id">
                                        ID: {$sub.id}
                                        <button class="copy-id-btn tooltip" data-id="{$sub.id}">
                                            <i class="fa fa-copy"></i>
                                            <span class="tooltiptext">{:__('copy_id')}</span>
                                        </button>
                                    </div>
                                    <div class="register-date">{$sub.register_date}</div>
                                </div>
                                <div class="right-info">
                                    <div class="bet-amount">
                                        <span class="amount-label">{:__('total_bets')}: </span>
                                        <span class="amount-value">{$sub.total_bet_amount}</span>
                                    </div>
                                    <div class="deposit-amount">
                                        <span class="amount-label">{:__('deposit_total')}: </span>
                                        <span class="amount-value">{$sub.total_deposit_amount}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {/volist}
                </div>

                <!-- 剩余数据，初始隐藏 -->
                <div id="remaining-items" style="display: none;">
                    {volist name="subordinates" id="sub" offset="8"}
                        <div class="subordinate-card">
                            <div class="vip-badge">VIP{$sub.vip_level}</div>
                            <div class="subordinate-info">
                                <div class="left-info">
                                    <div class="player-id">
                                        ID: {$sub.id}
                                        <button class="copy-id-btn tooltip" data-id="{$sub.id}">
                                            <i class="fa fa-copy"></i>
                                            <span class="tooltiptext">{:__('copy_id')}</span>
                                        </button>
                                    </div>
                                    <div class="register-date">{$sub.register_date}</div>
                                </div>
                                <div class="right-info">
                                    <div class="bet-amount">
                                        <span class="amount-label">{:__('total_bets')}: </span>
                                        <span class="amount-value">{$sub.total_bet_amount}</span>
                                    </div>
                                    <div class="deposit-amount">
                                        <span class="amount-label">{:__('deposit_total')}: </span>
                                        <span class="amount-value">{$sub.total_deposit_amount}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {/volist}
                </div>

                <!-- 加载指示器 -->
                <div id="load-more-indicator" style="display: none; text-align: center; padding: 10px;">
                    <span style="color: var(--text-color); opacity: 0.8;">{:__('loading_more')}...</span>
                </div>
            {/empty}
        </div>

        <!-- 统计面板 - 只在有数据时显示 -->
        {notempty name="stats"}
        <div class="stats-panel">
            <!-- 左侧列 -->
            <div class="stats-column">
                <div class="stats-row">
                    <div class="stats-label">{:__('direct_deposit_total')}</div>
                    <div class="stats-value">{$stats.direct_deposit_total|default='0.00'}</div>
                </div>
                <div class="stats-row">
                    <div class="stats-label">{:__('other_deposit_total')}</div>
                    <div class="stats-value">{$stats.other_deposit_total|default='0.00'}</div>
                </div>
                <div class="stats-row">
                    <div class="stats-label">{:__('total_deposit')}</div>
                    <div class="stats-value">{$stats.total_deposit|default='0.00'}</div>
                </div>
                <div class="stats-row">
                    <div class="stats-label">{:__('average_deposit')}</div>
                    <div class="stats-value">{$stats.average_deposit|default='0.00'}</div>
                </div>
            </div>

            <!-- 右侧列 -->
            <div class="stats-column">
                <div class="stats-row">
                    <div class="stats-label">{:__('direct_deposit_users')}</div>
                    <div class="stats-value">{$stats.direct_deposit_users|default='0'}</div>
                </div>
                <div class="stats-row">
                    <div class="stats-label">{:__('other_deposit_users')}</div>
                    <div class="stats-value">{$stats.other_deposit_users|default='0'}</div>
                </div>
                <div class="stats-row">
                    <div class="stats-label">{:__('total_deposit_users')}</div>
                    <div class="stats-value">{$stats.total_deposit_users|default='0'}</div>
                </div>
            </div>
        </div>
        {/notempty}
    </div>

    <!-- 引入日期选择器组件 -->
    {include file="common/date_picker" /}

    <!-- 引入日期选择器JS -->
    <script src="/assets/js/date-picker.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化复制按钮
        document.querySelectorAll('.copy-id-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const id = this.getAttribute('data-id');
                copyToClipboard(id);

                // 显示提示
                this.classList.add('show');
                setTimeout(() => {
                    this.classList.remove('show');
                }, 2000);
            });
        });
        // 辅助函数：更新下属列表
        function updateSubordinatesList(subordinates) {
            // 获取或创建容器元素
            let subordinatesList = document.querySelector('.subordinates-list');
            if (!subordinatesList) {
                console.error('下属列表容器不存在，尝试创建');
                subordinatesList = document.createElement('div');
                subordinatesList.className = 'subordinates-list';
                document.querySelector('.container').appendChild(subordinatesList);
            }

            // 清空列表容器
            subordinatesList.innerHTML = '';

            // 创建初始项目和剩余项目容器
            const initialItems = document.createElement('div');
            initialItems.id = 'initial-items';

            const remainingItems = document.createElement('div');
            remainingItems.id = 'remaining-items';
            remainingItems.style.display = 'none';

            const loadMoreIndicator = document.createElement('div');
            loadMoreIndicator.id = 'load-more-indicator';
            loadMoreIndicator.style.display = 'none';
            loadMoreIndicator.style.textAlign = 'center';
            loadMoreIndicator.style.padding = '10px';
            loadMoreIndicator.innerHTML = `<span style="color: var(--text-color); opacity: 0.8;">{:__('loading_more')}...</span>`;

            // 将容器添加到列表中
            subordinatesList.appendChild(initialItems);
            subordinatesList.appendChild(remainingItems);
            subordinatesList.appendChild(loadMoreIndicator);

            // 获取或创建统计面板
            let statsPanel = document.querySelector('.stats-panel');

            // 如果没有数据，隐藏统计面板
            if (!subordinates || subordinates.length === 0) {
                if (statsPanel) statsPanel.style.display = 'none';

                // 显示空状态
                const emptyState = document.createElement('div');
                emptyState.className = 'empty-state';
                emptyState.innerHTML = `
                    <img src="__CDN__/assets/img/frontend/common/empty.png" alt="No Data" class="empty-icon">
                    <div class="empty-text">{:__('no_data')}</div>
                `;
                initialItems.appendChild(emptyState);
                return;
            }

            // 显示统计面板
            if (statsPanel) statsPanel.style.display = 'flex';

            // 确定初始显示的数量
            // 如果总数据少于等于8条，全部显示在初始区域
            // 如果总数据多于8条，显示前8条，其余放入剩余区域
            const totalItems = subordinates.length;
            const initialCount = Math.min(8, totalItems);
            console.log(" subordinates.length ==" + subordinates.length)
            // 添加初始显示的项目
            for (let i = 0; i < initialCount; i++) {
                const sub = subordinates[i];
                const card = createSubordinateCard(sub);
                initialItems.appendChild(card);
            }

            // 添加剩余项目到隐藏区域
            for (let i = initialCount; i < totalItems; i++) {
                const sub = subordinates[i];
                const card = createSubordinateCard(sub);
                remainingItems.appendChild(card);
            }

            // 重置懒加载
            resetLazyLoad();
        }

        // 辅助函数：创建下属卡片
        function createSubordinateCard(sub) {
            const card = document.createElement('div');
            card.className = 'subordinate-card';
            card.innerHTML = `
                <div class="vip-badge">VIP${sub.vip_level}</div>
                <div class="subordinate-info">
                    <div class="left-info">
                        <div class="player-id">
                            ID: ${sub.id}
                            <button class="copy-id-btn tooltip" data-id="${sub.id}">
                                <i class="fa fa-copy"></i>
                                <span class="tooltiptext">{:__('copy_id')}</span>
                            </button>
                        </div>
                        <div class="register-date">${sub.register_date}</div>
                    </div>
                    <div class="right-info">
                        <div class="bet-amount">
                            <span class="amount-label">{:__('total_bets')}: </span>
                            <span class="amount-value">${sub.total_bet_amount}</span>
                        </div>
                        <div class="deposit-amount">
                            <span class="amount-label">{:__('total_deposit')}: </span>
                            <span class="amount-value">${sub.total_deposit_amount}</span>
                        </div>
                    </div>
                </div>
            `;

            // 添加复制ID按钮的点击事件
            setTimeout(() => {
                const copyBtn = card.querySelector('.copy-id-btn');
                if (copyBtn) {
                    copyBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const id = this.getAttribute('data-id');
                        copyToClipboard(id);

                        // 显示提示
                        this.classList.add('show');
                        setTimeout(() => {
                            this.classList.remove('show');
                        }, 2000);
                    });
                }
            }, 0);

            return card;
        }

        // 辅助函数：复制文本到剪贴板
        function copyToClipboard(text) {
            // 创建临时输入框
            const input = document.createElement('input');
            input.style.position = 'fixed';
            input.style.opacity = 0;
            input.value = text;
            document.body.appendChild(input);

            // 选择并复制
            input.select();
            document.execCommand('copy');

            // 移除临时输入框
            document.body.removeChild(input);

            // 显示提示
            Toast.success('{:__("copied")}');
        }

        // 辅助函数：更新统计面板
        function updateStatsPanel(stats) {
            if (!stats) return;

            try {
                // 检查统计面板是否存在，如果不存在则创建
                let statsPanel = document.querySelector('.stats-panel');
                if (!statsPanel) {
                    console.log('创建新的统计面板');
                    statsPanel = document.createElement('div');
                    statsPanel.className = 'stats-panel';
                    
                    // 创建左侧列
                    const leftColumn = document.createElement('div');
                    leftColumn.className = 'stats-column';

                    // 创建右侧列
                    const rightColumn = document.createElement('div');
                    rightColumn.className = 'stats-column';

                    // 添加统计行到左侧列
                    leftColumn.innerHTML = `
                        <div class="stats-row">
                            <div class="stats-label">{:__('direct_deposit_total')}</div>
                            <div class="stats-value">0.00</div>
                        </div>
                        <div class="stats-row">
                            <div class="stats-label">{:__('other_deposit_total')}</div>
                            <div class="stats-value">0.00</div>
                        </div>
                        <div class="stats-row">
                            <div class="stats-label">{:__('total_deposit')}</div>
                            <div class="stats-value">0.00</div>
                        </div>
                        <div class="stats-row">
                            <div class="stats-label">{:__('average_deposit')}</div>
                            <div class="stats-value">0.00</div>
                        </div>
                    `;

                    // 添加统计行到右侧列
                    rightColumn.innerHTML = `
                        <div class="stats-row">
                            <div class="stats-label">{:__('direct_deposit_users')}</div>
                            <div class="stats-value">0</div>
                        </div>
                        <div class="stats-row">
                            <div class="stats-label">{:__('other_deposit_users')}</div>
                            <div class="stats-value">0</div>
                        </div>
                        <div class="stats-row">
                            <div class="stats-label">{:__('total_deposit_users')}</div>
                            <div class="stats-value">0</div>
                        </div>
                    `;

                    // 将列添加到面板
                    statsPanel.appendChild(leftColumn);
                    statsPanel.appendChild(rightColumn);

                    // 将面板添加到容器
                    document.querySelector('.container').appendChild(statsPanel);
                }

                // 更新左侧列统计数据
                const leftColumn = statsPanel.querySelector('.stats-column:nth-child(1)');
                if (leftColumn) {
                    const directDepositTotal = leftColumn.querySelector('.stats-row:nth-child(1) .stats-value');
                    const otherDepositTotal = leftColumn.querySelector('.stats-row:nth-child(2) .stats-value');
                    const totalDeposit = leftColumn.querySelector('.stats-row:nth-child(3) .stats-value');
                    const averageDeposit = leftColumn.querySelector('.stats-row:nth-child(4) .stats-value');

                    if (directDepositTotal) directDepositTotal.textContent = stats.direct_deposit_total;
                    if (otherDepositTotal) otherDepositTotal.textContent = stats.other_deposit_total;
                    if (totalDeposit) totalDeposit.textContent = stats.total_deposit;
                    if (averageDeposit) averageDeposit.textContent = stats.average_deposit;
                }

                // 更新右侧列统计数据
                const rightColumn = statsPanel.querySelector('.stats-column:nth-child(2)');
                if (rightColumn) {
                    const directDepositUsers = rightColumn.querySelector('.stats-row:nth-child(1) .stats-value');
                    const otherDepositUsers = rightColumn.querySelector('.stats-row:nth-child(2) .stats-value');
                    const totalDepositUsers = rightColumn.querySelector('.stats-row:nth-child(3) .stats-value');

                    if (directDepositUsers) directDepositUsers.textContent = stats.direct_deposit_users;
                    if (otherDepositUsers) otherDepositUsers.textContent = stats.other_deposit_users;
                    if (totalDepositUsers) totalDepositUsers.textContent = stats.total_deposit_users;
                }

                // 显示统计面板
                statsPanel.style.display = 'flex';
            } catch (error) {
                console.error('更新统计面板时出错:', error);
            }
        }

        // 辅助函数：重置懒加载
        function resetLazyLoad() {
            // 获取DOM元素
            const subordinatesList = document.querySelector('.subordinates-list');
            const remainingItems = document.getElementById('remaining-items');
            const loadMoreIndicator = document.getElementById('load-more-indicator');

            // 如果没有列表或者没有更多项目，则不需要设置滚动监听
            if (!subordinatesList || !remainingItems) {
                return;
            }

            // 获取所有剩余的卡片
            const remainingCards = remainingItems.querySelectorAll('.subordinate-card');
            if (remainingCards.length === 0) {
                return; // 没有更多数据
            }

            // 显示剩余项目容器，但内容仍然隐藏
            remainingItems.style.display = 'block';

            // 标记是否正在加载
            window.isLoading = false;
            // 当前已加载的索引
            window.currentIndex = 0;
        }
        // 初始化日期选择器
        var datePicker = new DatePicker({
            triggerElement: '#datePicker',
            initialStartDate: new Date(), // 默认开始日期为今天
            initialEndDate: new Date(),   // 默认结束日期为今天
            onDateRangeChange: function(startDate, endDate) {
                // 格式化日期为YYYY-MM-DD格式
                const formatDate = function(date) {
                    const year = date.getFullYear();
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const day = date.getDate().toString().padStart(2, '0');
                    return `${year}-${month}-${day}`;
                };

                const startDateStr = formatDate(startDate);
                const endDateStr = formatDate(endDate);

                // 显示处理中提示
                Toast.success('{:__("processing")}');

                // 使用AJAX获取数据，而不是重定向
                fetch(`/index/agent/fetchData?start_date=${startDateStr}&end_date=${endDateStr}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 1) {
                            // 更新日期范围显示
                            const formattedStartDate = new Date(startDate).toLocaleDateString('pt-BR');
                            const formattedEndDate = new Date(endDate).toLocaleDateString('pt-BR');
                            document.getElementById('datePicker').textContent = `${formattedStartDate} - ${formattedEndDate}`;

                            // 更新列表数据
                            updateSubordinatesList(data.data.subordinates);

                            // 更新统计数据
                            updateStatsPanel(data.data.stats);
                        } else {
                            Toast.error(data.msg || '{:__("Error occurred")}');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        Toast.error('{:__("Error occurred")}');
                    });
            }
        });

        // 实现搜索功能
        const searchInput = document.querySelector('.search-input input');
        const searchIcon = document.querySelector('.search-icon');

        if (searchInput && searchIcon) {
            searchIcon.addEventListener('click', function() {
                performSearch(searchInput.value);
            });

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch(searchInput.value);
                }
            });
        }

        function performSearch(keyword) {
            if (keyword.trim() === '') {
                Toast.info('{:__("please_select")}');
                return;
            }

            // 显示处理中提示
            // Toast.info('{:__("processing")}: ' + keyword);

            // 使用AJAX获取数据
            fetch(`/index/agent/search?id=${keyword}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        // 更新列表数据
                        updateSubordinatesList(data.data.subordinates);

                        // 更新统计数据
                        updateStatsPanel(data.data.stats);
                    } else {
                        Toast.error(data.msg || '{:__("Error occurred")}');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Toast.error('{:__("Error occurred")}');
                });
        }

        // 懒加载功能实现
        const subordinatesList = document.querySelector('.subordinates-list');

        // 初始化全局变量
        window.isLoading = false;
        window.currentIndex = 0;

        // 加载更多项目
        function loadMoreItems() {
            const initialItems = document.getElementById('initial-items');
            const remainingItems = document.getElementById('remaining-items');
            const loadMoreIndicator = document.getElementById('load-more-indicator');

            if (!initialItems || !remainingItems || !loadMoreIndicator) return;

            const remainingCards = remainingItems.querySelectorAll('.subordinate-card');
            if (remainingCards.length === 0) return;

            window.isLoading = true;
            loadMoreIndicator.style.display = 'block';

            // 模拟网络延迟
            setTimeout(function() {
                // 计算本次要加载的数量（最多8个）
                const itemsToLoad = Math.min(8, remainingCards.length - window.currentIndex);

                // 如果没有更多项目，则隐藏加载指示器并返回
                if (itemsToLoad <= 0) {
                    loadMoreIndicator.style.display = 'none';
                    window.isLoading = false;
                    return;
                }

                // 将要加载的项目移动到初始项目容器中
                for (let i = 0; i < itemsToLoad; i++) {
                    if (window.currentIndex < remainingCards.length) {
                        initialItems.appendChild(remainingCards[window.currentIndex]);
                        window.currentIndex++;
                    }
                }

                // 隐藏加载指示器
                loadMoreIndicator.style.display = 'none';
                window.isLoading = false;

                // 如果已经加载完所有项目，则移除滚动监听
                if (window.currentIndex >= remainingCards.length) {
                    console.log('所有项目已加载完成');
                }
            }, 500); // 500ms延迟，模拟网络请求
        }

        // 监听滚动事件
        if (subordinatesList) {
            subordinatesList.addEventListener('scroll', function() {
                const remainingItems = document.getElementById('remaining-items');
                if (!remainingItems) return;

                const remainingCards = remainingItems.querySelectorAll('.subordinate-card');

                // 如果正在加载或者已经没有更多数据，则不处理
                if (window.isLoading || window.currentIndex >= remainingCards.length) {
                    return;
                }

                // 检查是否滚动到底部附近
                const scrollPosition = subordinatesList.scrollTop + subordinatesList.clientHeight;
                const scrollHeight = subordinatesList.scrollHeight;

                // 当滚动到距离底部100px时，加载更多
                if (scrollPosition >= scrollHeight - 100) {
                    loadMoreItems();
                }
            });
        }
    });
    </script>
</body>
</html>
