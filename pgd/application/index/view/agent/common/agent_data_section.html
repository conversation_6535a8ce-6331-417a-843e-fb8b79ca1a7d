<!-- 数据部分共用组件 -->
<div class="scrollable-container">
    {volist name="dataSections" id="section"}
    <div class="data-section">
        <div class="section-header">
            <div class="section-title">{$section.title}</div>
        </div>
        
        <div class="data-list">
            {volist name="section.items" id="item"}
            <div class="data-item">
                <div class="data-label">{$item.label}</div>
                <div class="data-value {$item.highlight?'highlight':''}">{$item.value}</div>
            </div>
            {/volist}
        </div>
    </div>
    {/volist}
</div>

<style>
/* 数据统计部分 */
.scrollable-container {
    max-height: calc(100vh - 120px);
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 和 Edge */
}

.scrollable-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari 和 Opera */
}

.data-section {
    padding: 0;
    margin-bottom: 16px;
}

.section-header {
    background-color: var(--darker-bg-color);
    color: #fff;
    padding: 10px 15px;
    font-size: 14px;
    font-weight: bold;
}

.section-title {
    font-size: 14px;
    font-weight: bold;
}

.data-list {
    background-color: var(--darker-bg-color);
}

.data-item {
    display: flex;
    justify-content: space-between;
    padding: 12px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    border-bottom-color: var(--white-add);
}

.data-label {
    color: var(--stat-label-color);
    font-size: 13px;
    display: flex;
    align-items: center;
}

.data-value {
    color: var(--text-color);
    font-size: 13px;
    font-weight: bold;
}

.data-value.highlight {
    color: var(--stat-label-color);
}
</style> 