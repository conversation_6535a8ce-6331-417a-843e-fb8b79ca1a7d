<!-- 从agent/index.html提取的共用header和tab-nav部分 -->

<div class="tab-nav">
    <div class="tab-item {if $active_tab=='link'}active{/if}" data-tab="link">{:__('tab_link')}</div>
    <div class="tab-item {if $active_tab=='menus'}active{/if}" data-tab="menus">{:__('tab_my_data')}</div>
    <div class="tab-item {if $active_tab=='all_data' || $active_tab=='todos'}active{/if}" data-tab="all_data">{:__('tab_all_data')}</div>
    <div class="tab-item {if $active_tab=='subordinates' || $active_tab=='dados'}active{/if}" data-tab="subordinates">{:__('tab_subordinates')}</div>
    <div class="tab-item {if $active_tab=='bonus'}active{/if}" data-tab="bonus">{:__('tab_bonus')}</div>
</div>

<style>
.tab-nav {
    display: flex;
    position: relative;
    margin-bottom: 1px;
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.tab-nav::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.tab-nav::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--secondary-color);
}

.tab-item {
    flex: 0 1 auto; /* 改为flex: 0 1 auto，允许根据内容自适应宽度 */
    min-width: 0; /* 移除最小宽度限制 */
    text-align: center;
    padding: 10px 10px; /* 修改padding，左右各增加内边距 */
    font-size: 14px;
    cursor: pointer;
    position: relative;
    white-space: nowrap;
}

.tab-item.active {
    border-bottom: 3px solid var(--text-color);
    color: var(--text-color);
    z-index: 1;
}

@media (max-width: 480px) {
    .tab-item {
        min-width: 0; /* 移除最小宽度限制 */
        font-size: 12px;
        padding: 10px 8px; /* 小屏幕上减小内边距 */
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 跳转到相应页面的方法
    function navigateToTab(tab) {
        const tabMapping = {
            'link': '/index/agent/index',
            'menus': '/index/agent/menus',
            'all_data': '/index/agent/all_data',
            'subordinates': '/index/agent/subordinates',
            'bonus': '/index/agent/bonus',
            // 保留旧映射以兼容旧链接
            'todos': '/index/agent/all_data',
            'dados': '/index/agent/subordinates'
        };

        const tabName = tab.getAttribute('data-tab');
        if (tabMapping[tabName]) {
            window.location.href = tabMapping[tabName];
        }
    }

    // 初始化标签导航
    const tabItems = document.querySelectorAll('.tab-nav .tab-item');
    tabItems.forEach(tab => {
        tab.addEventListener('click', function() {
            navigateToTab(this);
        });
    });

    // 初始化返回按钮
    const backBtn = document.getElementById('backBtn');
    if (backBtn) {
        backBtn.addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = '/';
        });
    }
});
</script>
