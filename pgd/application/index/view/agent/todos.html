<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Todos os Dados</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }

        body {
            background-color: var(--outer-bg-color) !important;
            color: var(--text-color);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        .container {
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            background-color: var(--main-bg-color);
        }

        /* 搜索栏 */
        .search-bar {
            display: flex;
            justify-content: space-between;
            padding: 10px 15px;
            background-color: var(--main-bg-color);
        }

        .date-picker {
            flex: 1;
            padding: 8px 12px;
            background-color: #5a0912;
            border: none;
            border-radius: 20px;
            color: var(--text-color);
            font-size: 12px;
            text-align: center;
            margin-right: 10px;
            cursor: pointer;
        }

        .search-input {
            flex: 1;
            display: flex;
            align-items: center;
            background-color: #5a0912;
            border-radius: 20px;
            padding: 0 12px;
        }

        .search-input input {
            width: 100%;
            background: transparent;
            border: none;
            color: var(--text-color);
            padding: 8px 0;
            font-size: 12px;
        }

        .search-input input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .search-input input:focus {
            outline: none;
        }

        .search-icon {
            color: var(--text-color);
            font-size: 16px;
            margin-left: 5px;
            cursor: pointer;
        }

        /* 日期选择器弹窗 */
        .date-picker-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .date-picker-content {
            width: 90%;
            max-width: 400px;
            background-color: var(--secondary-color);
            border-radius: 10px;
            padding: 20px;
            color: var(--text-color);
        }

        .date-picker-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .date-picker-body {
            display: flex;
            justify-content: space-between;
            align-items: flex-start; /* 确保所有列从顶部开始对齐 */
        }

        /* 日期部分（左右两侧） */
        .date-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        /* 日期部分标题 */
        .date-section-title {
            font-size: 18px;
            margin-bottom: 15px;
            font-weight: bold;
            height: 30px; /* 固定高度 */
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap; /* 防止文本换行 */
            overflow: hidden; /* 隐藏溢出的文本 */
            text-overflow: ellipsis; /* 如果文本溢出，显示省略号 */
            width: 100%;
            text-align: center;
        }

        /* 日期列容器 */
        .date-columns {
            display: flex;
            justify-content: space-between;
            width: 100%;
        }

        .date-column {
            flex: 1;
            text-align: center;
            display: flex;
            flex-direction: column;
            height: 200px; /* 固定高度，确保所有列高度一致 */
        }

        .date-scroll {
            height: 200px;
            overflow-y: scroll; /* 使用scroll而不是auto，确保滚动行为一致 */
            margin: 0 10px;
            position: relative;
            /* 隐藏滚动条但保留滚动功能 - 多种方法确保在所有浏览器中都生效 */
            scrollbar-width: none !important; /* Firefox */
            -ms-overflow-style: none !important; /* IE and Edge */
            -webkit-overflow-scrolling: touch; /* 平滑滚动在iOS上 */
        }

        /* 确保在所有WebKit浏览器中隐藏滚动条 */
        .date-scroll::-webkit-scrollbar,
        .date-scroll::-webkit-scrollbar-thumb,
        .date-scroll::-webkit-scrollbar-track,
        .date-scroll::-webkit-scrollbar-track-piece,
        .date-scroll::-webkit-scrollbar-corner,
        .date-scroll::-webkit-resizer {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
            background: transparent !important;
            -webkit-appearance: none !important;
        }

        .date-item {
            padding: 10px 0;
            font-size: 16px;
            cursor: pointer;
        }

        .date-item.selected {
            color: var(--text-color);
            font-weight: bold;
        }

        .date-picker-footer {
            display: flex;
            justify-content: center; /* 居中对齐 */
            margin-top: 20px;
        }

        .button-container {
            display: flex;
            justify-content: center;
            gap: 20px; /* 按钮之间的间距 */
            width: 60%; /* 控制按钮容器的宽度，使按钮靠近中间 */
        }

        .date-picker-btn {
            padding: 10px 20px;
            border-radius: 20px;
            border: none;
            font-size: 16px;
            cursor: pointer;
        }

        .cancel-btn {
            background-color: transparent;
            border: 1px solid var(--gray-blue);
            color: var(--text-color);
        }

        .confirm-btn {
            background-color: var(--button-normal);
            color: var(--text-color);
            font-weight: bold;
        }

        .vertical-divider {
            width: 1px;
            background-color: rgba(255, 255, 255, 0.5);
            margin: 0 15px;
            height: 230px; /* 与日期选择器高度匹配 */
            align-self: center; /* 在父容器中垂直居中 */
        }
    </style>
</head>
<body>
    <div class="container">
        {assign name="title" value="Convidar"}
        {include file="common/common_header" /}
        <!-- 引入共用header和tabs -->
        {include file="agent/common/agent_header_tabs" title=__('tab_all_data') active_tab="todos" /}

        <!-- 搜索栏 -->
        <div class="search-bar">
            <div class="date-picker" id="datePicker">02/04/2025 - 02/04/2025</div>
            <div class="search-input">
                <input type="text" placeholder="{:__('member_id_placeholder')}">
                <div class="search-icon"><i class="fa fa-search"></i></div>
            </div>
        </div>

        <!-- 空状态（使用通用样式） -->
        <div class="empty-state">
            <img src="__CDN__/assets/img/frontend/common/empty.png" alt="No Data" class="empty-icon">
            <div class="empty-text">{:__('no_data')}</div>
        </div>
    </div>

    <!-- 引入日期选择器组件 -->
    {include file="common/date_picker" /}

    <!-- 引入日期选择器JS -->
    <script src="/assets/js/date-picker.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化日期选择器
        var datePicker = new DatePicker({
            triggerElement: '#datePicker',
            onDateRangeChange: function(startDate, endDate) {
                console.log('日期范围变化:', startDate, endDate);
                // 这里可以添加获取数据的逻辑
                Toast.success('{:__("processing")}');
            }
        });

        // 实现搜索功能
        const searchInput = document.querySelector('.search-input input');
        const searchIcon = document.querySelector('.search-icon');

        if (searchInput && searchIcon) {
            searchIcon.addEventListener('click', function() {
                performSearch(searchInput.value);
            });

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch(searchInput.value);
                }
            });
        }

        function performSearch(keyword) {
            console.log('Searching for:', keyword);
            if (keyword.trim() === '') {
                Toast.info('{:__("please_select")}');
                return;
            }
            Toast.info('{:__("processing")}: ' + keyword);
        }
    });
    </script>
</body>
</html>