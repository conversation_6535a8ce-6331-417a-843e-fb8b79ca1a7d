<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{:__('tab_bonus')}</title>
    
    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }
        
        body {
            background-color: var(--outer-bg-color) !important;
            color: var(--text-color);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 480px;
            width: 100%;
            margin: 0 auto;
            background-color: var(--main-bg-color);
        }
        
        /* 下拉选择器 */
        .dropdown-selector {
            margin: 15px;
            position: relative;
        }
        
        .dropdown-btn {
            background-color: var(--secondary-color);
            color: var(--text-color);
            border: none;
            border-radius: 5px;
            padding: 12px 15px;
            width: 100%;
            text-align: left;
            font-size: 14px;
            position: relative;
            cursor: pointer;
        }
        
        .dropdown-btn::after {
            content: '▼';
            position: absolute;
            right: 15px;        
            top: 12px;
            font-size: 12px;
            color: var(--text-color);
        }
    </style>
</head>
<body>
    <div class="container">
        {assign name="title" value="Convidar"}
        {include file="common/common_header" /}
        <!-- 引入共用header和tabs -->
        {include file="agent/common/agent_header_tabs" title=__('tab_bonus') active_tab="bonus" /}
        
        <!-- 下拉选择器 -->
        <div class="dropdown-selector">
            <button class="dropdown-btn" id="dropdownBtn">{:__('conquest_invite')}</button>
        </div>
        
        <!-- 空状态（使用通用样式） -->
        <div class="empty-state">
            <img src="__CDN__/assets/img/frontend/common/empty.png" alt="No Data" class="empty-icon">
            <div class="empty-text">{:__('no_bonus_data')}</div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 下拉菜单功能
        const dropdownBtn = document.getElementById('dropdownBtn');
        if (dropdownBtn) {
            dropdownBtn.addEventListener('click', function() {
                Toast.info('{:__("processing")}');
            });
        }
    });
    </script>
</body>
</html> 