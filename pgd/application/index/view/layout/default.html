<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>{$site.name|htmlentities}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
        <meta name="renderer" content="webkit">
        <meta name="referrer" content="never">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">

        {include file="common/meta" /}
        
        <!-- JS Cookie库 -->
        <script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.1/dist/js.cookie.min.js"></script>
        
        <!-- 加载自定义CSS和JS -->
        {if $Think.config.app_debug}
        {__STYLE__}
        {/if}
    </head>
    <body class="{$Think.config.fastadmin.adminskin|default='skin-black-blue'} {:defined('IS_DIALOG') && IS_DIALOG ? 'is-dialog' : ''}">
        <nav class="navbar navbar-white navbar-fixed-top" role="navigation">
            <div class="container">
                <div class="navbar-header">
                    <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#header-navbar">
                        <span class="sr-only">Toggle navigation</span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </button>
                    <a class="navbar-brand" href="{:url('/')}">{$site.name|htmlentities}</a>
                </div>
                <div class="collapse navbar-collapse" id="header-navbar">
                    <ul class="nav navbar-nav navbar-right">
                        <li><a href="{:url('/')}">{:__('Home')}</a></li>
                        <li class="dropdown">
                            {if $user}
                            <a href="{:url('user/index')}" class="dropdown-toggle" data-toggle="dropdown">
                                <span class="avatar-img"><img src="{$user.avatar|htmlentities|cdnurl}" alt=""></span>
                                <span class="visible-xs-inline-block" style="padding:5px;">{$user.nickname} <b class="caret"></b></span>
                            </a>
                            {else /}
                            <a href="{:url('user/index')}" class="dropdown-toggle" data-toggle="dropdown">{:__('Member center')} <b class="caret"></b></a>
                            {/if}
                            <ul class="dropdown-menu">
                                {if $user}
                                <li><a href="{:url('user/index')}"><i class="fa fa-user-circle fa-fw"></i>{:__('User center')}</a></li>
                                <li><a href="{:url('user/profile')}"><i class="fa fa-user-o fa-fw"></i>{:__('Profile')}</a></li>
                                <li><a href="{:url('user/changepwd')}"><i class="fa fa-key fa-fw"></i>{:__('Change password')}</a></li>
                                <li><a href="{:url('user/logout')}"><i class="fa fa-sign-out fa-fw"></i>{:__('Sign out')}</a></li>
                                {else /}
                                <li><a href="{:url('user/login')}"><i class="fa fa-sign-in fa-fw"></i> {:__('Sign in')}</a></li>
                                <li><a href="{:url('user/register')}"><i class="fa fa-user-o fa-fw"></i> {:__('Sign up')}</a></li>
                                {/if}

                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <main class="content">
            {__CONTENT__}
        </main>

        <footer class="footer" style="clear:both">
            <p class="copyright">Copyright&nbsp;©&nbsp;{:date("Y")} {$site.name|htmlentities} All Rights Reserved <a href="https://beian.miit.gov.cn" target="_blank">{$site.beian|htmlentities}</a></p>
        </footer>

        {if $Think.config.app_debug}
        {__SCRIPT__}
        {/if}
    </body>
</html>
