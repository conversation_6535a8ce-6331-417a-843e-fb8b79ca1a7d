<style>
    body {
        margin: 0;
        padding: 0;
        font-family: 'Arial', sans-serif;
        background-color: var(--outer-bg-color);
    }
    
    /* 容器样式使用全局样式，移除冗余定义 */
    
    /* 任务列表样式 */
    .todo-list {
        padding: 0 15px;
    }
    
    .todo-item {
        background-color: var(--card-bg-color);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        position: relative;
    }
    
    .todo-item.completed {
        background-color: var(--completed-bg-color);
    }
    
    .todo-item.pending {
        background-color: var(--pending-bg-color);
    }
    
    .todo-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .todo-title {
        font-size: 16px;
        font-weight: bold;
        color: #fff;
    }
    
    .todo-status {
        font-size: 12px;
        padding: 3px 8px;
        border-radius: 10px;
    }
    
    .status-pending {
        background-color: #FFA500;
        color: #fff;
    }
    
    .status-completed {
        background-color: #4CAF50;
        color: #fff;
    }
    
    .todo-content {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 10px;
    }
    
    .todo-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
    }
    
    .todo-button {
        background-color: var(--button-bg);
        color: var(--button-text);
        border: none;
        border-radius: 15px;
        padding: 5px 15px;
        font-size: 14px;
        font-weight: bold;
        cursor: pointer;
        margin-top: 10px;
    }
</style> 