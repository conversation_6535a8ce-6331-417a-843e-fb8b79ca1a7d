<!-- 添加PIX账户弹窗 -->
<div class="add-pix-account-popup" id="addPixAccountPopup" style="display: none;">
    <div class="popup-overlay"></div>
    <div class="popup-container">
        <div class="popup-header">
            <h2 class="popup-title">{:__('Adicionar PIX')}</h2>
        </div>
        
        <div class="popup-content">
            <!-- 姓名输入 -->
            <div class="form-group">
                <input type="text" class="form-input" id="pixAccountName" placeholder="{:__('Por favor, insira o nome')}" maxlength="16">
            </div>
            
            <!-- 账户类型选择 -->
            <div class="form-group">
                <div class="select-container">
                    <div class="select-box" id="pixAccountTypeSelect">
                        <div class="select-value">CPF</div>
                        <div class="select-arrow">&#9662;</div>
                    </div>
                    <div class="select-dropdown" id="pixAccountTypeDropdown" style="display: none;">
                        <div class="select-option selected" data-value="CPF">CPF</div>
                        <div class="select-option" data-value="PHONE">PHONE</div>
                        <div class="select-option" data-value="EMAIL">EMAIL</div>
                        <div class="select-option" data-value="EVP">EVP</div>
                        <div class="select-option" data-value="CNPJ">CNPJ</div>
                    </div>
                </div>
            </div>
            
            <!-- PIX账户输入 -->
            <div class="form-group" id="pixAccountInputContainer">
                <input type="text" class="form-input" id="pixAccountValue" placeholder="{:__('Por favor, insira a conta PIX')}">
            </div>
            
            <!-- CPF输入 -->
            <div class="form-group">
                <input type="text" class="form-input" id="pixAccountCpf" placeholder="{:__('Por favor, insira o número de CPF de 11 dígitos')}">
            </div>
            
            <!-- 提示信息 -->
            <div class="pix-account-hint">
                <p>{:__('Apenas 1 conta PIX do tipo PIX-CPF pode ser adicionada')}</p>
                <p>{:__('Por favor, verifique cuidadosamente, caso contrário, não será creditado.')}</p>
            </div>
        </div>
        
        <div class="popup-footer">
            <button class="popup-button" id="confirmAddPixAccountBtn">{:__('Confirmar')}</button>
        </div>
        
        <div class="popup-close" id="addPixAccountCloseBtn">
            <i class="fa fa-times"></i>
        </div>
    </div>
</div>
