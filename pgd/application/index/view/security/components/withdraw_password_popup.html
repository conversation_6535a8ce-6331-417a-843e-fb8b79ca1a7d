<!-- 取款密码输入弹窗 -->
<div class="withdraw-password-popup" id="withdrawPasswordPopup" style="display: none;">
    <div class="popup-overlay"></div>
    <div class="popup-container">
        <div class="popup-header">
            <h2 class="popup-title">{:__('Inserir Senha')}</h2>
        </div>

        <div class="popup-content">
            <div class="password-label">{:__('Sen<PERSON> de Saque')}</div>

            <div class="pin-grid pin-6" id="withdrawPinGrid">
                <div class="pin-box" data-index="0"></div>
                <div class="pin-box" data-index="1"></div>
                <div class="pin-box" data-index="2"></div>
                <div class="pin-box" data-index="3"></div>
                <div class="pin-box" data-index="4"></div>
                <div class="pin-box" data-index="5"></div>
                <input type="password" class="pin-input-hidden" maxlength="6" inputmode="numeric" autocomplete="off" pattern="[0-9]*">
            </div>

            <div class="password-hint">
                <p>{:__('Para a segurança da sua conta, introduza a palavra-passe de levantamento')}</p>
            </div>

            <div class="forgot-password">
                <a href="javascript:void(0);" id="forgotPasswordLink">{:__('Esqueceu a senha?')}</a>
            </div>
        </div>

        <div class="popup-footer">
            <button class="popup-button" id="withdrawPasswordNextBtn">{:__('Próximo')}</button>
        </div>



        <div class="popup-close" id="withdrawPasswordCloseBtn">
            <i class="fa fa-times"></i>
        </div>
    </div>
</div>


