<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="currency-symbol" content="{$Think.config.site.currency_symbol|default='R$'}">
    <title>{:__('password_setup')}</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}

    <!-- 引入Security专用CSS -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/security/security.css?v=1.01">
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/security/password_setup.css?v=1.01">
    <!-- 引入通用PIN输入框组件CSS（放在最后以确保其样式优先） -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/common/pin-input.css?v=1.01">

    <!-- 设置货币符号 -->
    <script>
        // 设置货币符号
        window.currency = '{$Think.config.site.currency_symbol|default="R$"}';
    </script>
</head>
<body>
    <div class="container">
        <!-- 引入通用header部分 -->
        <!-- 设置标题变量，然后引入通用header部分 -->
        {assign name="title" value="Centro de Segurança"}
        {include file="common/common_header" /}

        <!-- 设置提款密码页面 -->
        <div class="setup-withdraw-password">
            <div class="setup-description">{:__('setup_withdraw_password_description')}</div>

            <div class="pin-setup-container">
                <div class="pin-label">{:__('enter_new_withdraw_password')}</div>
                <div class="pin-grid pin-6" id="newPinGrid">
                    <div class="pin-box" data-index="0"></div>
                    <div class="pin-box" data-index="1"></div>
                    <div class="pin-box" data-index="2"></div>
                    <div class="pin-box" data-index="3"></div>
                    <div class="pin-box" data-index="4"></div>
                    <div class="pin-box" data-index="5"></div>
                </div>

                <div class="pin-label">{:__('confirm_withdraw_password')}</div>
                <div class="pin-grid pin-6" id="confirmPinGrid">
                    <div class="pin-box" data-index="0"></div>
                    <div class="pin-box" data-index="1"></div>
                    <div class="pin-box" data-index="2"></div>
                    <div class="pin-box" data-index="3"></div>
                    <div class="pin-box" data-index="4"></div>
                    <div class="pin-box" data-index="5"></div>
                </div>

                <div class="submit-btn" id="setupPasswordBtn">{:__('Confirm')}</div>
            </div>
        </div>
    </div>

    <!-- 引入安全中心语言包 -->
    <script src="__CDN__/assets/js/frontend/security/security-lang.js?v=1.01"></script>

    <!-- 引入安全中心专用JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/security.js?v=1.01"></script>

    <!-- 引入Header Tabs JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/header_tabs.js?v=1.01"></script>

    <!-- 引入密码设置页面专用JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/password_setup.js?v=1.01"></script>

    <!-- 通用PIN输入组件已在password_setup.js中动态加载 -->

</body>
</html>
