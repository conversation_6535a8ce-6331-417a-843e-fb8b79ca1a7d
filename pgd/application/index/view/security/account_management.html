<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="currency-symbol" content="{$Think.config.site.currency_symbol|default='R$'}">
    <title>{:__('account_management')}</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}

    <!-- 引入Security专用CSS -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/security/security.css?v=1.01">
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/security/account_management.css?v=1.01">
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/common/refresh-btn.css?v=1.01">
    <!-- 引入PIN输入框样式 -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/common/pin-input.css?v=1.01">
    <!-- 引入添加PIX账户弹窗样式 -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/security/add_pix_account_popup.css?v=1.01">

    <!-- 设置货币符号 -->
    <script>
        // 设置货币符号
        window.currency = '{$Think.config.site.currency_symbol|default="R$"}';
    </script>
</head>
<body>
    <div class="container">
        <!-- 引入通用header部分 -->
        <!-- 设置标题变量，然后引入通用header部分 -->
        {assign name="title" value="Centro de Segurança"}
        {include file="common/common_header" /}

            <!-- 引入标签导航 -->
            {include file="security/security_tabs/security_tabs" active_tab='account_management' /}

            <!-- 添加可滚动内容区域，隐藏滚动条 -->
            <div class="scrollable-content">
                <!-- 引入取款密码弹窗组件 -->
                {include file="security/components/withdraw_password_popup" /}

                <!-- 引入添加PIX账户弹窗组件 -->
                {include file="security/components/add_pix_account_popup" /}

                <!-- 管理账户标签页内容 -->
                <div id="account_management-content" class="tab-content">

                <div class="account-management">
                    <!-- 账户列表 -->
                    <div class="account-list" id="accounts-container">
                        <!-- 账户列表将通过JavaScript加载 -->
                        <!-- 加载状态 -->
                        <div class="loading-container" style="text-align: center; padding: 30px;">
                            <div class="loading-spinner"></div>
                            <div class="loading-text">{:__('loading')}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入安全中心语言包 -->
    <script src="__CDN__/assets/js/frontend/security/security-lang.js?v=1.01"></script>

    <!-- 引入安全中心专用JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/security.js?v=1.01"></script>

    <!-- 引入Header Tabs JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/header_tabs.js?v=1.01"></script>

    <!-- 引入账户管理页面专用JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/account_management.js?v=1.01"></script>

    <!-- 引入添加PIX账户的JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/add_pix_account.js?v=1.01"></script>
</body>
</html>
