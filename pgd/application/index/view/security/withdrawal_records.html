<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="currency-symbol" content="{$Think.config.site.currency_symbol|default='R$'}">
    <title>{:__('withdrawal_records')}</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}

    <!-- 引入Security专用CSS -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/security/security.css?v=1.01">
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/security/withdrawal_records.css?v=1.0.2">
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/security/scrollable-content.css?v=1.01">
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/common/refresh-btn.css?v=1.01">

    <!-- 设置货币符号 -->
    <script>
        // 设置货币符号
        window.currency = '{$Think.config.site.currency_symbol|default="R$"}';
    </script>
</head>
<body>
    <div class="container">
        <!-- 引入通用header部分 -->
        <!-- 设置标题变量，然后引入通用header部分 -->
        {assign name="title" value="Centro de Segurança"}
        {include file="common/common_header" /}

        <!-- 引入标签导航 -->
        {include file="security/security_tabs/security_tabs" active_tab='withdrawal_records' /}

        <!-- 添加可滚动内容区域，显示滚动条 -->
        <div class="scrollable-content" id="withdrawal-scrollable-content">
            <!-- 提款记录标签页内容 -->
            <div id="withdrawal_records-content" class="tab-content">
                <!-- 记录头部 -->
                <div class="records-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <!-- 日期选择器 - 左侧 - 修改为filter-dropdown样式 -->
                    <div class="filter-container" style="margin: 0; text-align: left;">
                        <div class="filter-dropdown date-filter">
                            <div class="filter-btn" id="dateFilterBtn">
                                <span class="filter-text">
                                    {switch name="dateRange"}
                                        {case value="today"}{:__('hoje')}{/case}
                                        {case value="yesterday"}{:__('yesterday')}{/case}
                                        {case value="week"}{:__('semana')}{/case}
                                        {case value="month"}{:__('mes')}{/case}
                                        {case value="half_year"}{:__('half_year')}{/case}
                                        {case value="year"}{:__('year')}{/case}
                                        {default}{:__('all')}{/default}
                                    {/switch}
                                </span>
                                <span class="arrow">▼</span>
                            </div>
                            <div class="filter-menu" id="dateFilterMenu">
                                <div class="filter-option {$dateRange == 'today' ? 'active' : ''}" data-value="today">{:__('hoje')}</div>
                                <div class="filter-option {$dateRange == 'yesterday' ? 'active' : ''}" data-value="yesterday">{:__('yesterday')}</div>
                                <div class="filter-option {$dateRange == 'week' ? 'active' : ''}" data-value="week">{:__('semana')}</div>
                                <div class="filter-option {$dateRange == 'month' ? 'active' : ''}" data-value="month">{:__('mes')}</div>
                                <div class="filter-option {$dateRange == 'half_year' ? 'active' : ''}" data-value="half_year">{:__('half_year')}</div>
                                <div class="filter-option {$dateRange == 'year' ? 'active' : ''}" data-value="year">{:__('year')}</div>
                                <div class="filter-option {$dateRange == 'all' ? 'active' : ''}" data-value="all">{:__('all')}</div>
                            </div>
                        </div>
                    </div>

                    <!-- 总金额 - 右侧 -->
                    <div class="total-section" style="text-align: right; margin: 0;">
                        <div class="total-label">{:__('total_de_saques')}</div>
                        <div class="total-value"><span class="total-withdrawal-amount" style="color: var(--secondary-color);">{$Think.config.site.currency_symbol|default='R$'} {$totalAmount|default='0.00'}</span></div>
                    </div>
                </div>

                <!-- 记录列表 -->
                <div class="withdrawal-records" style="display: block;">
                    {empty name="records"}
                        <!-- 无记录状态 -->
                        <div class="no-records-container">
                            <div class="empty-state">
                                <img src="__CDN__/assets/img/frontend/common/empty.png" alt="No Records" class="empty-icon">
                                <div class="no-records-message">{:__('no_withdrawal_records')}</div>
                                <!-- 添加一个隐藏的翻译元素，用于JavaScript获取 -->
                                <div class="js-translation" data-key="no_withdrawal_records" style="display: none;">{:__('no_withdrawal_records')}</div>
                            </div>
                        </div>
                    {else/}
                        {volist name="records" id="record"}
                            <div class="history-item">
                                <div class="history-item-row">
                                    <div class="history-item-left">
                                         <img src="__CDN__/assets/img/frontend/common/ipx.png" class="channel-icon">
                                        <span class="channel-name">{$record.channel_code|default='PIX'}</span>
                                    </div>
                                    <div class="history-item-right">
                                        <span class="deposit-amount">{$Think.config.site.currency_symbol|default='R$'} {$record.amount}</span>
                                    </div>
                                </div>
                                <div class="history-item-row">
                                    <div class="history-item-left">
                                        <span class="deposit-time">{$record.create_time}</span>
                                    </div>
                                    <div class="history-item-middle">
                                        <div class="order-container">
                                            <span class="order-no">{$record.third_order_no|default=$record.id}</span>
                                            <button id ="copy-btn" data-clipboard-text="{$record.third_order_no|default=$record.id}" title="copy No."><i class="fa fa-copy"></i></button>
                                        </div>
                                    </div>
                                    <div class="history-item-right">
                                        <span class="deposit-status status-{$record.status_class}">{$record.status_text}</span>
                                    </div>
                                </div>
                            </div>
                        {/volist}
                    {/empty}
                </div>
            </div>
        </div>
    </div>

    <!-- 引入安全中心语言包 -->
    <script src="__CDN__/assets/js/frontend/security/security-lang.js?v=1.01"></script>

    <!-- 引入安全中心专用JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/security.js?v=1.01"></script>

    <!-- 引入Header Tabs JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/header_tabs.js?v=1.01"></script>

    <!-- 引入提款记录页面专用JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/withdrawal_records.js?v=1.01"></script>
</body>
</html>
