<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="currency-symbol" content="{$Think.config.site.currency_symbol|default='R$'}">
    <title>{:__('withdraw')}</title>

    <!-- 使用通用meta文件引入CSS和JS -->
    {include file="common/meta" /}

    <!-- 引入Security专用CSS -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/security/security.css?v=1.02">
    <!-- <link rel="stylesheet" href="__CDN__/assets/css/frontend/security/withdraw.css?v=1.01"> -->
    <!-- <link rel="stylesheet" href="__CDN__/assets/css/frontend/common/refresh-btn.css?v=1.01"> -->
    <!-- 引入通用PIN输入框组件CSS（放在最后以确保其样式优先） -->
    <link rel="stylesheet" href="__CDN__/assets/css/frontend/common/pin-input.css?v=1.01">

    <!-- 设置系统变量 -->
    <script type="text/javascript">
        // 设置货币符号
        window.currency = '{$Think.config.site.currency_symbol|default="R$"}';
        // 设置打码任务完成状态
        window.hasCompletedBetting = '{$has_completed_betting}' === '1';
    </script>
</head>
<body>
    <div class="container">
        <!-- 引入通用header部分 -->
        <!-- 设置标题变量，然后引入通用header部分 -->
        {assign name="title" value="Centro de Segurança"}
        {include file="common/common_header" /}

        <!-- 引入标签导航 -->
        {include file="security/security_tabs/security_tabs" active_tab='withdraw' /}

        <!-- 添加可滚动内容区域，隐藏滚动条 -->
        <div class="scrollable-content">
            <!-- 引入余额信息区域 -->
            {include file="security/security_tabs/balance_section" /}

            <div class="tab-content active" id="withdraw-content">
                <div class="withdrawal-form">
                    <div class="payment-method" id="payment-method-selector">
                        {empty name="withdrawAccounts"}
                        <!-- 无账户状态 -->
                        <div class="payment-logo">
                            <img src="/assets/img/frontend/payment/ipx.png" alt="Add Account" width="30" height="30">
                        </div>
                        <div class="payment-info">
                            <div class="payment-name">Adicionar Conta</div>
                            <div class="payment-details">{:__('add_account_description')}</div>
                        </div>
                        <div id="payment-add-account-link"> <i class="fa fa-plus"></i></div>
                        <!-- <div class="payment-dropdown">+</div> -->
                        {else/}
                        <!-- 有账户状态 - 默认显示第一个账户 -->
                        <div class="payment-logo">
                            <img src="/assets/img/frontend/common/ipx.png" alt="{$withdrawAccounts[0]['account_type']|default='PIX'|strtoupper}" width="30" height="30">
                        </div>
                        <div class="payment-info">
                            <div class="payment-name">PIX({$withdrawAccounts[0]['account_number']}){$withdrawAccounts[0]['account_type']}</div>                <!-- <div class="payment-details">{$withdrawAccounts[0]['account_details']|default=''}</div> -->
                        </div>
                        <!-- <div class="payment-dropdown">▼</div> -->
                        <!-- 隐藏的账户ID，用于提交表单 -->
                        <input type="hidden" id="selected-account-id" value="{$withdrawAccounts[0]['id']|default='0'}">
                        {/empty}
                    </div>

                    <div class="amount-input">
                        <input type="text" class="amount-input-field" placeholder="0.00" inputmode="decimal" autocomplete="off" data-independent="true" min="{$minWithdrawal|default=100}" max="{$maxWithdrawal|default=50000}">
                    </div>

                    <div class="withdrawal-notice">
                        <p class="withdrawal-notice-label">{$withdrawalNotice}</p>
                    </div>

                    <div class="pin-verification">
                        <div class="pin-label">
                            {:__('verificar_senha_de_saque')}
                        </div>

                        <div class="pin-grid pin-6" id="withdrawPinGrid">
                            <div class="pin-box" data-index="0"></div>
                            <div class="pin-box" data-index="1"></div>
                            <div class="pin-box" data-index="2"></div>
                            <div class="pin-box" data-index="3"></div>
                            <div class="pin-box" data-index="4"></div>
                            <div class="pin-box" data-index="5"></div>
                        </div>

                        <div class="submit-btn" id="withdrawSubmitBtn">{:__('Confirm')}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入安全中心语言包 -->
    <!-- <script src="__CDN__/assets/js/frontend/security/security-lang.js?v=1.01"></script> -->

    <!-- 引入安全中心专用JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/security.js?v=1.01"></script>

    <!-- 引入Header Tabs JavaScript -->
    <script src="__CDN__/assets/js/frontend/security/header_tabs.js?v=1.01"></script>
</body>
</html>
