<?php

namespace app\index\model;

use think\Model;

class PaymentChannel extends Model
{
    // 表名
    protected $table = 'payment_channels';

    // 设置主键
    protected $pk = 'code';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    /**
     * 获取可用的支付通道列表
     *
     * @param string $channelType 通道类型：deposit, withdraw, both
     * @return array 支付通道列表
     */
    public static function getActiveChannels($channelType = 'deposit')
    {
        try {
            $query = self::where('is_active', 1);

            // 根据通道类型筛选
            if ($channelType) {
                $query->where(function ($query) use ($channelType) {
                    $query->where('channel_type', $channelType)
                          ->whereOr('channel_type', 'both');
                });
            }

            // 获取通道列表
            $channels = $query->order('code asc')->select();

            return $channels;
        } catch (\Exception $e) {
            // 出错时记录日志并返回空数组
            \app\common\library\FrontendLog::error('获取支付通道失败: ' . $e->getMessage());
            return [];
        }
    }
}
