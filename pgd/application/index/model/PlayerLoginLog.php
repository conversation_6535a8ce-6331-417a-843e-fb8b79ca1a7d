<?php

namespace app\index\model;

use app\common\library\FrontendLog;
use think\Model;

/**
 * 玩家登录日志模型
 * Class PlayerLoginLog
 * @package app\index\model
 */
class PlayerLoginLog extends Model
{
    // 表名
    protected $table = 'player_login_logs';

    // 禁用自动写入时间戳
    protected $autoWriteTimestamp = false;

    /**
     * 记录玩家登录日志
     * @param int $player_id 玩家ID
     * @param string $ip 登录IP
     * @return bool 是否成功
     * @throws \Exception
     */
    public function recordLogin($player_id, $ip) 
    { 
        // 参数验证
        if(!is_numeric($player_id) || $player_id <= 0) {
            throw new \Exception('无效的玩家ID');
        }
        if(!filter_var($ip, FILTER_VALIDATE_IP)) {
            throw new \Exception('无效的IP地址');
        }

        try {
            $data = [ 
                'player_id' => $player_id, 
                'login_ip' => $ip, 
                'created_at' => date('Y-m-d H:i:s')
            ]; 
            $result = $this->create($data);
            return $result !== false;
        } catch(\Exception $e) {
            // 记录错误日志
            \think\Log::error("记录玩家登录日志记录失败: {$e->getMessage()}");
            return false;
        }
    }

}
