<?php

namespace app\index\model;

use think\Model;
use think\Session;
use app\common\library\FrontendLog;

/**
 * 虚拟玩家模型
 * Class VirtualPlayer
 * @package app\index\model
 */
class VirtualPlayer extends Model
{
    // 表名
    protected $table = 'virtual_players';

    // 禁用自动写入时间戳
    protected $autoWriteTimestamp = false;

    // 时间戳字段名（不再自动填充）
    // protected $createTime = 'created_at';
    // protected $updateTime = 'updated_at';

    // 类型转换
    protected $type = [
        'id' => 'integer',
        'channel_id' => 'integer',
        'balance' => 'float',
        'is_active' => 'integer'
    ];

    /**
     * 验证虚拟玩家登录
     * @param string $username 用户名
     * @param string $password 密码
     * @return array|bool 成功返回用户信息数组，失败返回false
     */
    public static function checkLogin($username, $password)
    {
        if (empty($username) || empty($password)) {
            return false;
        }

        // 根据用户名查找
        $player = self::where(function($query) use ($username) {
            $query->where('username', $username);
        })
        ->where('is_active', 1)
        ->find();

        if (!$player) {
            return false;
        }

        // 密码验证
        if ($player['password'] != $password) {
            return false;
        }

        // 登录成功，设置session，标记为虚拟玩家
        Session::set('player_id', $player['id']);
        Session::set('player_username', $player['username']);
        Session::set('player_lastactive', time());
        Session::set('player_type', 'virtual'); // 标记为虚拟玩家

        // 更新最后登录时间和IP
        try {
            $loginIp = request()->ip();
            $player->last_login_time = date('Y-m-d H:i:s');
            $player->login_ip = $loginIp;
            $player->updated_at = date('Y-m-d H:i:s'); // 手动更新updated_at

            // 记录调试信息
            FrontendLog::info('更新虚拟玩家登录信息: ' . json_encode([
                'player_id' => $player['id'],
                'last_login_time' => $player->last_login_time,
                'login_ip' => $player->login_ip,
                'updated_at' => $player->updated_at
            ]));

            if (!$player->save()) {
                FrontendLog::error('更新虚拟玩家登录信息失败: player_id=' . $player['id']);
            }

            // 记录玩家登录日志
            $logResult = \app\index\model\PlayerLoginLog::recordLogin($player['id'], $loginIp);
            if ($logResult) {
                FrontendLog::info('记录虚拟玩家登录日志成功: player_id=' . $player['id'] . ', ip=' . $loginIp);
            } else {
                FrontendLog::error('记录虚拟玩家登录日志失败: player_id=' . $player['id'] . ', ip=' . $loginIp);
            }
        } catch (\Exception $e) {
            FrontendLog::error('更新虚拟玩家登录信息异常: ' . $e->getMessage());
        }

        // 转换为数组并添加余额
        $playerData = $player->toArray();
        $playerData['balance'] = $player['balance'];
        $playerData['reward_balance'] = 0.00; // 虚拟玩家没有奖励余额

        return $playerData;
    }
}