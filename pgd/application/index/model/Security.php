<?php

namespace app\index\model;

use think\Model;

class Security extends Model
{
    // 表名
    protected $name = 'player';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    /**
     * 获取用户的提款信息
     * @param int $user_id 用户ID
     * @return array
     */
    public function getWithdrawalInfo($user_id)
    {
        $user = $this->where('id', $user_id)->find();
        if (!$user) {
            return [
                'code' => 0,
                'msg' => __('User not found'),
                'data' => null
            ];
        }
        
        return [
            'code' => 1,
            'msg' => '',
            'data' => [
                'balance' => $user['money'],
                'min_withdrawal' => config('site.min_withdrawal') ?: 10,
                'need_bet' => $this->getNeedBet($user_id),
                'pix_info' => $this->getPixInfo($user_id)
            ]
        ];
    }
    
    /**
     * 获取用户需要投注的金额
     * @param int $user_id 用户ID
     * @return float
     */
    protected function getNeedBet($user_id)
    {
        // 实际应用中，这里应该从流水表或其他表计算用户需要投注的金额
        // 此处为示例返回
        return 0.00;
    }
    
    /**
     * 获取用户的PIX信息
     * @param int $user_id 用户ID
     * @return array
     */
    protected function getPixInfo($user_id)
    {
        // 实际应用中，这里应该从用户账户表或支付方式表获取
        // 此处为示例返回
        return [
            'pix_type' => 'CPF',
            'pix_key' => '******8996'
        ];
    }
    
    /**
     * 获取提款记录
     * @param int $user_id 用户ID
     * @param string $date 日期筛选，如 'today', 'week', 'month'
     * @return array
     */
    public function getWithdrawalRecords($user_id, $date = 'today')
    {
        // 实际应用中，这里应该从提款记录表获取
        // 此处为示例返回
        return [
            'code' => 1,
            'msg' => '',
            'data' => [
                'total' => 0.00,
                'records' => []
            ]
        ];
    }
    
    /**
     * 获取账户管理信息
     * @param int $user_id 用户ID
     * @return array
     */
    public function getAccountManageInfo($user_id)
    {
        $user = $this->where('id', $user_id)->find();
        if (!$user) {
            return [
                'code' => 0,
                'msg' => __('User not found'),
                'data' => null
            ];
        }
        
        return [
            'code' => 1,
            'msg' => '',
            'data' => [
                'payment_accounts' => $this->getPaymentAccounts($user_id)
            ]
        ];
    }
    
    /**
     * 获取用户的支付账户信息
     * @param int $user_id 用户ID
     * @return array
     */
    protected function getPaymentAccounts($user_id)
    {
        // 实际应用中，这里应该从支付账户表获取
        // 此处为示例返回
        return [
            [
                'id' => 1,
                'type' => 'PIX',
                'type_text' => 'PIX (CPF)',
                'account' => '******8996'
            ]
        ];
    }
} 