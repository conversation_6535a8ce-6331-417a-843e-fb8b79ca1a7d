<?php

namespace app\index\model;

use think\Model;

class Event extends Model
{
    // 设置数据表（不含前缀）
    protected $table = 'vip_config';
    
    /**
     * 获取VIP配置数据
     * @return array
     */
    public static function getVipList()
    {
        // 从数据库获取VIP等级配置，包含所有需要的字段
        $vipList = \think\Db::table('vip_config')
            ->field('level, deposit_requirement, turnover_requirement, level_up_bonus')
            ->order('level asc')
            ->select();
        // 为每条记录添加默认值，以防模板中引用了不存在的字段
        foreach ($vipList as &$vip) {
            // 为没有的字段设置默认值
            $vip['weekly_bonus'] = $vip['weekly_bonus'] ?? '0.00';
            $vip['monthly_bonus'] = $vip['monthly_bonus'] ?? '0.00';
            $vip['daily_max_withdraw_amount'] = $vip['daily_max_withdraw_amount'] ?? '0.00';
            $vip['daily_withdrawal_times'] = $vip['daily_withdrawal_times'] ?? 0;
            $vip['daily_deposit_rebate_rate'] = $vip['daily_deposit_rebate_rate'] ?? 0;
        }
            
        return $vipList;
    }
    
    /**
     * 获取活动数据
     * @return array
     */
    public static function getEventList()
    {
        // 从数据库获取活动列表
        $events = \think\Db::table('event')
            ->where('status', 'active')
            ->order('createtime desc')
            ->select();
            
        foreach ($events as &$event) {
            // 处理日期格式
            $event['startDate'] = date('Y-m-d', $event['starttime']);
            $event['endDate'] = date('Y-m-d', $event['endtime']);
        }
            
        return $events;
    }
    
    /**
     * 获取任务数据
     * @return array
     */
    public static function getMissionList()
    {
        // 从数据库获取任务列表
        $missions = \think\Db::table('mission')
            ->where('status', 'active')
            ->order('createtime desc')
            ->select();
            
        return $missions;
    }
    
    /**
     * 获取返利数据
     * @return array
     */
    public static function getRebateList()
    {
        // 从数据库获取返利列表
        $rebates = \think\Db::table('rebate')
            ->where('status', 'active')
            ->order('createtime desc')
            ->select();
            
        return $rebates;
    }
    
    /**
     * 获取待领取奖励数据
     * @return array
     */
    public static function getPendingRewardList()
    {
        // 从数据库获取待领取奖励列表
        $pendingRewards = \think\Db::table('reward')
            ->where('status', 'pending')
            ->order('createtime desc')
            ->select();
            
        return $pendingRewards;
    }
    
    /**
     * 获取历史记录数据
     * @return array
     */
    public static function getHistoryList()
    {
        // 从数据库获取历史记录列表
        $historyRecords = \think\Db::table('event_history')
            ->order('createtime desc')
            ->select();
            
        foreach ($historyRecords as &$record) {
            // 处理日期格式
            $record['date'] = date('Y-m-d', $record['createtime']);
        }
            
        return $historyRecords;
    }
} 