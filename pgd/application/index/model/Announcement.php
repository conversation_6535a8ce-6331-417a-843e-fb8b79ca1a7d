<?php

namespace app\index\model;

use think\Model;
use app\common\library\FrontendLog;

/**
 * 公告模型
 * Class Announcement
 * @package app\index\model
 */
class Announcement extends Model
{
    // 表名
    protected $table = 'announcements';
        
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;
    
    // 定义时间戳字段名
    protected $createTime = 'created_at'; 
    protected $updateTime = 'updated_at';
    
    /**
     * 获取活跃的公告
     * @param int $limit 限制返回条数
     * @return array 公告列表
     */
    public static function getActiveAnnouncements($limit = 5)
    {
        try {
            // 记录当前时间
            $now = date('Y-m-d H:i:s');
            
            // 使用正确的字段名查询
            $query = self::where('is_active', 1)
                ->where('start_time', '<=', $now)
                ->where('end_time', '>', $now)
                ->order('sort_order DESC, id DESC')
                ->limit($limit);
            
            $result = $query->select();
            
            // 如果没有符合条件的公告，获取最新的几条公告
            if (count($result) == 0) {
                $result = self::where('is_active', 1)
                    ->order('id DESC')
                    ->limit($limit)
                    ->select();
            }
            
            // 转换为数组
            return collection($result)->toArray();
        } catch (\Exception $e) {
            // 记录详细错误
            FrontendLog::error('获取公告数据异常: ' . $e->getMessage());
            FrontendLog::error('错误堆栈: ' . $e->getTraceAsString());
            // 出错时返回空数组
            return [];
        }
    }
    
    /**
     * 获取最新的公告
     * @param int $limit 限制返回条数
     * @return array 公告列表
     */
    public static function getLatestAnnouncements($limit = 5)
    {
        try {
            $result = self::where('is_active', 1)
                ->order('id DESC')
                ->limit($limit)
                ->select();
            return collection($result)->toArray();
        } catch (\Exception $e) {
            // 记录错误
            FrontendLog::error('获取最新公告数据异常: ' . $e->getMessage());
            // 出错时返回空数组
            return [];
        }
    }

    /**
     * 获取所有公告，不考虑状态或时间限制（用于调试）
     * @param int $limit 限制返回条数，0表示不限制
     * @return array 公告列表
     */
    public static function getAllAnnouncements($limit = 0)
    {
        try {
            $query = self::order('id DESC');
            if ($limit > 0) {
                $query->limit($limit);
            }
            
            $result = $query->select();
            $resultArray = collection($result)->toArray();
            return $resultArray;
        } catch (\Exception $e) {
            // 记录错误
            FrontendLog::error('获取所有公告异常: ' . $e->getMessage());
            FrontendLog::error('错误堆栈: ' . $e->getTraceAsString());
            // 出错时返回空数组
            return [];
        }
    }
} 