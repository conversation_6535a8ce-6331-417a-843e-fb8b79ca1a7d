<?php

namespace app\index\model;

use think\Model;

class Payment extends Model
{
    // 表名
    protected $table = 'payment_plans';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    /**
     * 获取可用充值金额列表
     * 
     * @return array 充值金额列表
     */
    public static function getAmountList()
    {
        // 从payment_plans表获取可用的金额列表
        $amounts = self::where('is_active', 1)
            ->order('amount asc')
            ->column('amount');
            
        return $amounts;
    }
} 