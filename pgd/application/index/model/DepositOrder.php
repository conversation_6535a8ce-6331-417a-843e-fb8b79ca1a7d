<?php

namespace app\index\model;

use think\Model;

/**
 * 充值订单模型
 * Class DepositOrder
 * @package app\index\model
 */
class DepositOrder extends Model
{
    // 表名
    protected $table = 'deposit_orders';

    // 禁用自动写入时间戳
    protected $autoWriteTimestamp = false;

    // 类型转换
    protected $type = [
        'id' => 'integer',
        'player_id' => 'integer',
        'referrer_id' => 'integer',
        'agent_id' => 'integer',
        'amount' => 'float',
        'gift_amount' => 'float',
        'service_fee' => 'float',
        'is_manual_fill' => 'integer',
        'payment_status' => 'integer'
    ];
    
    /**
     * 获取充值记录
     * @param int $playerId 玩家ID
     * @param int $page 页码
     * @param int $pageSize 每页条数
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array
     */
    public static function getDepositHistory($playerId, $page = 1, $pageSize = 10, $startDate = '', $endDate = '')
    {
        // 构建基础查询
        $query = self::where('player_id', $playerId);
        
        // 添加日期筛选
        if (!empty($startDate)) {
            $query->where('paid_at', '>=', $startDate . ' 00:00:00');
        }
        
        if (!empty($endDate)) {
            $query->where('paid_at', '<=', $endDate . ' 23:59:59');
        }
        
        // 获取记录总数
        $total = $query->count();
        
        // 获取总充值金额
        $totalAmount = $query->where('payment_status', 1)->sum('amount');
        
        // 分页获取记录
        $records = $query->order('paid_at desc')
            ->page($page, $pageSize)
            ->select();
        
        // 处理记录，添加状态文本
        foreach ($records as &$record) {
            switch ($record['payment_status']) {
                case 0:
                    $record['status_text'] = 'Pendente';
                    break;
                case 1:
                    $record['status_text'] = 'Concluído';
                    break;
                case 2:
                    $record['status_text'] = 'Falhou';
                    break;
                default:
                    $record['status_text'] = 'Desconhecido';
            }
            
            // 格式化金额（保留两位小数）
            $record['formatted_amount'] = number_format($record['amount'], 2);
        }
        
        return [
            'records' => $records,
            'total' => $total,
            'totalAmount' => $totalAmount,
            'totalPage' => ceil($total / $pageSize)
        ];
    }
} 
