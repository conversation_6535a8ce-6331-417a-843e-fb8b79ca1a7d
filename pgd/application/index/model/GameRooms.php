<?php

namespace app\index\model;

use app\common\library\FrontendLog;
use think\Model;

/**
 * 游戏房间模型
 * Class GameRooms
 * @package app\index\model
 */
class GameRooms extends Model
{
    // 表名
    protected $table = 'game_rooms';

    // 主键
    protected $pk = 'game_room_id';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    /**
     * 定义与游戏提供商的关联关系
     */
    public function provider()
    {
        return $this->belongsTo('app\index\model\GameProvider', 'provider_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 获取热门游戏列表
     * @param int $offset 偏移量
     * @param int $limit 限制返回数量
     * @param string $type 游戏类型，默认为popular，可选slots
     * @return array 热门游戏列表
     */
    public static function getPopularGames($offset = 0, $limit = 0, $type = 'popular')
    {
        try {
            // 初始化查询
            $query = self::alias('r')
                ->join(['game_providers' => 'p'], 'r.provider_id = p.id')
                ->where('p.is_active', 1)
                ->order('r.clicks', 'desc')
                ->field('r.game_room_id, r.code, r.name, r.type, r.clicks, p.name as provider_name');

            // 根据类型添加不同的条件
            if ($type === 'popular') {
                // 获取名称中包含PG的游戏商的游戏
                $query->where('p.name', 'like', '%PG%');
            } elseif ($type === 'slots') {
                // 获取type为slots的游戏
                $query->where('r.type', 'slots');
            }

            // 如果设置了限制数量和偏移量，则应用分页
            if ($limit > 0) {
                $query->limit($offset, $limit);
            }
            $games = $query->select();

            if ($games) {
                // 收集所有游戏的提供商ID
                $providerIds = [];
                foreach ($games as $game) {
                    if (isset($game['provider_id']) && !in_array($game['provider_id'], $providerIds)) {
                        $providerIds[] = $game['provider_id'];
                    }
                }

                // 一次性查询所有相关的提供商信息
                $providers = [];
                if (!empty($providerIds)) {
                    $providerList = \app\index\model\GameProvider::where('id', 'in', $providerIds)->select();
                    foreach ($providerList as $provider) {
                        $providers[$provider['id']] = $provider;
                    }
                }

                // 格式化游戏数据，并添加完整的提供商信息
                $formattedGames = [];
                foreach ($games as $game) {
                    // 添加提供商完整信息
                    if (isset($game['provider_id']) && isset($providers[$game['provider_id']])) {
                        $game['provider_info'] = $providers[$game['provider_id']];
                    }

                    $formattedGames[] = self::formatGameData($game);
                }

                return $formattedGames;
            }
        } catch (\Exception $e) {
            // 记录错误日志
            FrontendLog::error("获取游戏失败: " . $e->getMessage());
        }

        return [];
    }

    /**
     * 获取子游戏页面数据
     * 处理分类、标签和分页，返回格式化的游戏数据
     * @param string $category 主分类（slots或游戏提供商名称）
     * @param string $hcategory 横向分类标签（popular、recente、favoritos等）
     * @param int $page 当前页码
     * @param int $limit 每页显示的游戏数量
     * @param string $search 搜索关键词
     * @return array 游戏数据，包含游戏列表、分页信息等
     */
    public static function getSubgameData($category = 'slots', $hcategory = '', $page = 1, $limit = 18, $search = '')
    {
        try {
            // 计算偏移量
            $offset = ($page - 1) * $limit;

            // 初始化查询对象
            // 使用数据库类直接构建查询，避免模型方法可能导致的问题


            // 处理主分类
            if ($category === 'slots') {
                // 获取所有类型为slots的游戏
                // 使用完整的表名而不是别名，避免查询重置
                $query = \think\Db::table('game_rooms')
                ->alias('r')
                ->join(['game_providers' => 'p'], 'r.provider_id = p.id')
                ->where('p.is_active', 1)
                ->order('r.clicks', 'desc')
                ->field('r.game_room_id, r.code, r.name, r.type, r.clicks, r.provider_id, p.name as provider_name');
                $totalgames = $query->select();
                $categoryName = 'Slots';
            } else {
                // 使用GameProvider模型的findProviderByName方法查找提供商
                $provider = GameProvider::findProviderByName($category, true, true);

                if (!$provider) {
                    return [
                        'category' => $category,
                        'name' => $category,
                        'games' => [],
                        'has_more' => false,
                        'current_page' => $page,
                        'total_pages' => 0,
                        'total_games' => 0
                    ];
                }
                // 删除提供商名称中的"仿"字和空格
                $categoryName = trim(str_replace('仿', '', $category));
                $query = \think\Db::table('game_rooms')
                ->alias('r')
                ->join(['game_providers' => 'p'], 'r.provider_id = p.id')
                ->where('p.is_active', 1)
                ->where('r.provider_id', $provider['id'])
                ->order('r.clicks', 'desc')
                ->field('r.game_room_id, r.code, r.name, r.type, r.clicks, r.provider_id, p.name as provider_name');
                $totalgames = $query->select();
            }

            // 使用已获取的 $totalgames 进行处理

            // 处理横向分类标签
            if (!empty($hcategory)) {
                // 根据横向分类标签对 $totalgames 进行排序
                switch ($hcategory) {
                    case 'popular':
                        // 热门游戏，按点击量排序（降序）
                        usort($totalgames, function($a, $b) {
                            return $b['clicks'] - $a['clicks'];
                        });
                        break;
                    case 'recente':
                        // 最近游戏，按ID倒序排序（假设ID越大表示越新）
                        usort($totalgames, function($a, $b) {
                            return $b['game_room_id'] - $a['game_room_id'];
                        });
                        break;
                    case 'favoritos':
                        // 收藏游戏，这里需要根据实际情况实现
                        // 暂时保持默认排序
                        break;
                    default:
                        // 默认按点击量排序
                        usort($totalgames, function($a, $b) {
                            return $b['clicks'] - $a['clicks'];
                        });
                }
            }

            // 处理搜索
            if (!empty($search)) {
                // 在内存中过滤符合搜索条件的游戏
                $totalgames = array_filter($totalgames, function($game) use ($search) {
                    return stripos($game['name'], $search) !== false;
                });

                // 重新索引数组
                $totalgames = array_values($totalgames);
            }

            // 获取总数
            $totalCount = count($totalgames);

            // 分页处理
            $games = array_slice($totalgames, $offset, $limit);

            // 格式化游戏数据，并添加完整的提供商信息
            $formattedGames = [];
            foreach ($games as $game) {
                $formattedGames[] = self::formatGameData($game);
            }

            // 计算总页数（向上取整）
            $totalPages = ceil($totalCount / $limit);

            // 返回结果
            return [
                'category' => $category,
                'name' => $categoryName,
                'games' => $formattedGames,
                'has_more' => ($page < $totalPages),
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_games' => $totalCount
            ];

        } catch (\Exception $e) {
            // 记录错误日志
            FrontendLog::error("获取子游戏页面数据失败: " . $e->getMessage());

            // 返回空结果
            return [
                'category' => $category,
                'name' => $category,
                'games' => [],
                'has_more' => false,
                'current_page' => $page,
                'total_pages' => 0,
                'total_games' => 0
            ];
        }
    }

    /**
     * 根据游戏ID获取游戏信息
     * @param int $gameId 游戏ID
     * @return array|null 游戏信息
     */
    public static function getGameById($gameId)
    {
        try {
            $game = self::alias('r')
                ->join(['game_providers' => 'p'], 'r.provider_id = p.id')
                ->where('r.game_room_id', $gameId)
                ->field('r.game_room_id, r.code, r.name, r.type, r.clicks, p.name as provider_name')
                ->find();

            if ($game) {
                return self::formatGameData($game);
            }
        } catch (\Exception $e) {
            FrontendLog::error("获取游戏信息失败: " . $e->getMessage());
        }

        return null;
    }

    /**
     * 根据游戏代码获取游戏信息
     * @param string $gameCode 游戏代码
     * @return array|null 游戏信息
     */
    public static function getGameByCode($gameCode)
    {
        try {
            $game = self::alias('r')
                ->join(['game_providers' => 'p'], 'r.provider_id = p.id')
                ->where('r.code', $gameCode)
                ->field('r.game_room_id, r.code, r.name, r.type, r.clicks, p.name as provider_name')
                ->find();

            if ($game) {
                return self::formatGameData($game);
            }
        } catch (\Exception $e) {
            FrontendLog::error("获取游戏信息失败: " . $e->getMessage());
        }

        return null;
    }

    /**
     * 更新游戏点击量
     * @param int $gameId 游戏ID
     * @return bool 是否更新成功
     */
    public static function incrementClicks($gameId)
    {
        try {
            return self::where('game_room_id', $gameId)->setInc('clicks');
        } catch (\Exception $e) {
            FrontendLog::error("更新游戏点击量失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 格式化游戏数据
     * 将数据库中的游戏数据格式化为前端需要的格式
     * @param mixed $game 游戏数据（可以是数组或对象）
     * @return array 格式化后的游戏数据
     */
    public static function formatGameData($game)
    {
        // 将对象转换为数组
        if (is_object($game)) {
            $game = $game->toArray();
        } elseif (!is_array($game)) {
            // 如果既不是对象也不是数组，记录错误并返回空数据
            if (config('app_debug')) {
                FrontendLog::error("格式化游戏数据失败: 无效的游戏数据类型 " . gettype($game));
            }
            return [
                'id' => 0,
                'game_id' => 0,
                'code' => '',
                'name' => '',
                'type' => '',
                'provider' => 'unknown',
                'image' => '',
                'clicks' => 0
            ];
        }

        // 首先尝试从provider_name获取（优先使用）
        if (isset($game['provider_name']) && !empty($game['provider_name'])) {
            // 保存原始提供商名称
            $originalProviderName = $game['provider_name'];
            // 清理提供商名称，移除"仿"字和多余空格
            $providerName = trim(str_replace('仿', '', $game['provider_name']));
        }
        // 如果provider_name不存在，则尝试从provider_info获取
        else if (isset($game['provider_info']) && is_array($game['provider_info'])) {
            // 从提供商完整信息中获取名称
            $originalProviderName = isset($game['provider_info']['name']) ?
                $game['provider_info']['name'] : 'unknown';
            $providerName = trim(str_replace('仿', '', $originalProviderName));
        }
        // 如果都不存在，则使用默认值
        else {
            $originalProviderName = 'unknown';
            $providerName = 'unknown';
        }

        // 确定图片文件扩展名
        $extension = 'png';
        // 确定图片文件名后缀
        $suffix = '_en';

        // 对于PP游戏，使用不同的后缀
        if (isset($providerName) && strpos($providerName, 'PP') !== false) {
            $suffix = '';
        }

        // 检查游戏代码是否存在
        if (!isset($game['code']) || empty($game['code'])) {
            $imgurl = '';  // 返回空字符串，使用透明背景
        } else {
            // 使用原始提供商名称构建图片URL
        $imgurl = "/assets/img/frontend/games/{$originalProviderName}/{$game['code']}{$suffix}.{$extension}";

            // 检查图片文件是否存在（仅在调试模式下）
            if (config('app_debug')) {
                try {
                    // 使用 ROOT_PATH 常量获取根目录，然后拼接 public 目录和图片路径
                    $publicPath = '';

                    // 尝试多种方式获取 public 目录路径
                    if (defined('ROOT_PATH')) {
                        $publicPath = ROOT_PATH . 'public/';
                    } elseif (defined('PUBLIC_PATH')) {
                        $publicPath = PUBLIC_PATH;
                    } elseif (defined('THINK_PATH')) {
                        $publicPath = dirname(THINK_PATH) . '/public/';
                    } elseif (isset($_SERVER['DOCUMENT_ROOT'])) {
                        $publicPath = $_SERVER['DOCUMENT_ROOT'] . '/';
                    }

                    if ($publicPath) {
                        $physicalPath = $publicPath . ltrim($imgurl, '/');
                        if (!file_exists($physicalPath)) {
                            FrontendLog::warning("游戏图片不存在: {$physicalPath}");

                            // 尝试不带后缀的路径
                            $altImgUrl = "/assets/img/frontend/games/{$providerName}/{$game['code']}.{$extension}";
                            $altPhysicalPath = $publicPath . ltrim($altImgUrl, '/');

                            if (file_exists($altPhysicalPath)) {
                                $imgurl = $altImgUrl;
                                FrontendLog::info("使用替代图片路径: {$imgurl}");
                            }
                        }
                    } else {
                        FrontendLog::warning("无法确定 public 目录路径，跳过图片文件检查");
                    }
                } catch (\Exception $e) {
                    // 捕获任何异常，确保不会中断程序执行
                    FrontendLog::error("检查图片文件时出错: " . $e->getMessage());
                }
            }
        }

        // 记录日志，调试用
        if (config('app_debug')) {
            $gameName = isset($game['name']) ? $game['name'] : '未知';
            $gameCode = isset($game['code']) ? $game['code'] : '未知';
        }

        // 构建返回数据，确保所有字段都有默认值
        $result = [
            'id' => isset($game['game_room_id']) ? $game['game_room_id'] : 0,
            'game_id' => isset($game['game_room_id']) ? $game['game_room_id'] : 0, // 兼容旧代码
            'code' => isset($game['code']) ? $game['code'] : '',
            'name' => isset($game['name']) ? trim(str_replace('仿', '', $game['name'])) : '', // 过滤游戏名称中的"仿"字
            'type' => isset($game['type']) ? $game['type'] : '',
            'provider' => $providerName, // 使用清理后的提供商名称
            'provider_name' => $originalProviderName, // 保留原始提供商名称
            'image' => $imgurl,
            'clicks' => isset($game['clicks']) ? $game['clicks'] : 0
        ];

        // 如果有提供商完整信息，添加更多详细信息到结果中
        if (isset($game['provider_info']) && is_array($game['provider_info'])) {
            // 添加完整的提供商信息
            $result['provider_info'] = $game['provider_info'];

            // 添加提供商的额外字段
            if (isset($game['provider_info']['id'])) {
                $result['provider_id'] = $game['provider_info']['id'];
            }

            if (isset($game['provider_info']['code'])) {
                $result['provider_code'] = $game['provider_info']['code'];
            }

            if (isset($game['provider_info']['is_active'])) {
                $result['provider_is_active'] = (bool)$game['provider_info']['is_active'];
            }

            // 添加提供商图标路径
            if (isset($game['provider_info']['name'])) {
                $cleanProviderName = trim(str_replace('仿', '', $game['provider_info']['name']));
                $result['provider_icon'] = "/assets/img/frontend/game_providers/{$cleanProviderName}.png";
            }
        }

        return $result;
    }

    /**
     * 搜索游戏
     * @param string $searchQuery 搜索关键词
     * @return \think\db\Query 查询对象
     */
    public static function searchGames($searchQuery)
    {
        return self::alias('r')
            ->join(['game_providers' => 'p'], 'r.provider_id = p.id')
            ->where('p.is_active', 1)
            ->where(function($q) use ($searchQuery) {
                $q->where('r.name', 'like', "%{$searchQuery}%")
                ->whereOr('r.code', 'like', "%{$searchQuery}%");
            })
            ->order('r.clicks', 'desc')
            ->field('r.game_room_id, r.code, r.name, r.type, r.clicks, p.name as provider_name')
            ->select();
    }
}
