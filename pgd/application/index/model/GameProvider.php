<?php

namespace app\index\model;

use app\common\library\FrontendLog;
use think\Model;

class GameProvider extends Model
{
    const SLOTMAX = 30;
    const GAME_IMAGE_BASE_PATH = '/assets/img/frontend/games/';
    protected $noNeedLogin = ['getMoreGames'];
    protected $noNeedRight = '*';

    // 设置完整的数据表名称
    protected $table = 'game_providers';

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'datetime';

    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 静态缓存属性，用于存储游戏提供商数据
    protected static $cachedProviders = null;

    // 静态缓存属性，用于存储游戏房间数据
    protected static $cachedRooms = null;

    /**
     * 初始化缓存数据
     * 从数据库中获取游戏提供商和游戏房间数据，并存储在内存中
     * @return void
     */
    public static function initCacheData()
    {
        try {
            // 如果缓存已经初始化，则直接返回
            if (self::$cachedProviders !== null && self::$cachedRooms !== null) {
                FrontendLog::info("游戏数据缓存已存在，跳过初始化");
                return;
            }

            // 获取所有激活的游戏提供商
            self::$cachedProviders = self::where('is_active', 1)
                ->order('sort', 'asc')
                ->select();

            // 获取所有游戏房间数据
            self::$cachedRooms = \think\Db::table('game_rooms')
                ->alias('r')
                ->join(['game_providers'=>'p'], 'r.provider_id = p.id')
                ->where('p.is_active', 1)
                ->field('r.game_room_id, r.code, r.name, r.type, r.clicks, r.provider_id, p.name as provider_name')
                ->select();

        } catch (\Exception $e) {
            FrontendLog::error("初始化游戏数据缓存失败: " . $e->getMessage());
            // 确保缓存变量不为null，即使出错也设置为空数组
            self::$cachedProviders = [];
            self::$cachedRooms = [];
        }
    }

    /**
     * 获取缓存的游戏提供商数据
     * @return array 游戏提供商数据
     */
    public static function getCachedProviders()
    {
        // 如果缓存未初始化，则初始化
        if (self::$cachedProviders === null) {
            self::initCacheData();
        }

        return self::$cachedProviders;
    }

    /**
     * 获取缓存的游戏房间数据
     * @return array 游戏房间数据
     */
    public static function getCachedRooms()
    {
        // 如果缓存未初始化，则初始化
        if (self::$cachedRooms === null) {
            self::initCacheData();
        }

        return self::$cachedRooms;
    }

    /**
     * 根据条件从缓存中筛选游戏房间数据
     * @param array $conditions 筛选条件，键为字段名，值为匹配值
     * @param string $orderBy 排序字段
     * @param string $orderDir 排序方向，asc或desc
     * @return array 筛选后的游戏房间数据
     */
    public static function filterCachedRooms($conditions = [], $orderBy = 'clicks', $orderDir = 'desc')
    {
        // 获取缓存的游戏房间数据
        $rooms = self::getCachedRooms();

        // 如果没有条件，则返回所有数据
        if (empty($conditions)) {
            return $rooms;
        }

        // 筛选数据
        $filtered = [];
        foreach ($rooms as $room) {
            $match = true;

            // 检查每个条件
            foreach ($conditions as $field => $value) {
                // 特殊处理like条件
                if (is_array($value) && isset($value['op']) && $value['op'] === 'like') {
                    // 将通配符%转换为正则表达式
                    $pattern = str_replace('%', '.*', $value['value']);
                    if (!preg_match('/' . $pattern . '/i', $room[$field])) {
                        $match = false;
                        break;
                    }
                }
                // 普通相等条件
                else if (isset($room[$field]) && $room[$field] != $value) {
                    $match = false;
                    break;
                }
            }

            if ($match) {
                $filtered[] = $room;
            }
        }

        // 排序
        usort($filtered, function($a, $b) use ($orderBy, $orderDir) {
            if ($orderDir === 'asc') {
                return $a[$orderBy] <=> $b[$orderBy];
            } else {
                return $b[$orderBy] <=> $a[$orderBy];
            }
        });

        return $filtered;
    }

    /**
     * 从缓存中分页获取游戏房间数据
     * @param array $conditions 筛选条件
     * @param string $orderBy 排序字段
     * @param string $orderDir 排序方向
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array 分页数据，包含games、total_count、total_pages等
     */
    public static function paginateCachedRooms($conditions = [], $orderBy = 'clicks', $orderDir = 'desc', $page = 1, $limit = 18)
    {
        // 获取筛选后的数据
        $filtered = self::filterCachedRooms($conditions, $orderBy, $orderDir);

        // 计算总数和总页数
        $totalCount = count($filtered);
        $totalPages = ceil($totalCount / $limit);

        // 计算偏移量
        $offset = ($page - 1) * $limit;

        // 分页
        $games = array_slice($filtered, $offset, $limit);

        // 格式化游戏数据
        $formattedGames = [];
        foreach ($games as $game) {
            $formattedGames[] = self::formatGameData($game);
        }

        return [
            'games' => $formattedGames,
            'total_count' => $totalCount,
            'total_pages' => $totalPages,
            'current_page' => $page,
            'has_more' => ($page < $totalPages)
        ];
    }

    /**
     * 获取激活的游戏提供商列表
     * @param bool $namesOnly 是否只返回名称列表
     * @param bool $includeGames 是否包含游戏数据
     * @return array 游戏提供商列表
     */
    public static function getActiveProviders($namesOnly = false, $includeGames = false)
    {
        // 使用缓存的游戏提供商数据
        $providers = self::getCachedProviders();

        if ($namesOnly) {
            // 只返回名称列表
            $names = [];
            foreach ($providers as $provider) {
                $names[] = $provider['name'];
            }
            return $names;
        } else {
            // 是否包含游戏数据
            if ($includeGames && $providers) {
                // 使用缓存的游戏房间数据
                $rooms = self::getCachedRooms();

                // 按提供商ID分组游戏
                $gamesByProvider = [];
                foreach ($rooms as $room) {
                    if (!isset($gamesByProvider[$room['provider_id']])) {
                        $gamesByProvider[$room['provider_id']] = [];
                    }
                    $gamesByProvider[$room['provider_id']][] = $room;
                }

                // 为每个提供商添加游戏数据
                foreach ($providers as &$provider) {
                    $provider['games'] = isset($gamesByProvider[$provider['id']])
                        ? $gamesByProvider[$provider['id']]
                        : [];
                }
            }

            return $providers;
        }
    }

    /**
     * 获取游戏分类列表，并加载其游戏数据
     * 默认增加Popular分类在最前面
     * @param int $defaultGameCount 默认显示游戏数量
     * @return array 游戏分类列表
     */
    public static function getActiveGameList($defaultGameCount = 9)
    {
        // 创建游戏列表数组
        $activeGameList = [];

        try {
            // 获取所有激活的游戏提供商
            $activeProviders = self::where('is_active', 1)
                ->order('sort', 'asc')
                ->select();


            if (!$activeProviders) {
                FrontendLog::warning("没有找到激活的游戏提供商！");
                return [];
            }

            // 添加Popular分类 - 获取名称中包含PG的游戏商的游戏，按点击量排序取前20个
            $pgQuery = \think\Db::table('game_rooms')
                ->alias('r')
                ->join(['game_providers'=>'p'], 'r.provider_id = p.id')
                ->where('p.is_active', 1)
                ->where('p.name', 'like', '%PG%')
                ->order('r.clicks', 'desc')
                ->limit(self::SLOTMAX)
                ->field('r.game_room_id, r.code, r.name, r.type, r.clicks, p.name as provider_name');

            $popularGames = $pgQuery->select();

            if ($popularGames) {
                // 格式化 Popular 游戏数据
                $formattedPopularGames = [];
                foreach ($popularGames as $game) {
                    $formattedPopularGames[] = [
                        'id' => $game['game_room_id'],
                        'code' => $game['code'],
                        'name' => $game['name'],
                        'type' => $game['type'],
                        'provider' => $game['provider_name'],
                        'image' => self::GAME_IMAGE_BASE_PATH . "{$game['provider_name']}/{$game['code']}_en.png"
                    ];
                }

                // 添加 Popular 分类到列表
                $activeGameList[] = [
                    'code' => 'popular',
                    'name' => 'Popular',
                    'games' => array_slice($formattedPopularGames, 0, $defaultGameCount),
                    'total_games' => count($formattedPopularGames),
                    'has_more' => count($formattedPopularGames) > $defaultGameCount
                ];
            } else {
                FrontendLog::warning("未找到Popular游戏！");
            }

            // 添加Slots分类 - 获取type为slots的游戏，按点击量排序
            $slotsQuery = \think\Db::table('game_rooms')
                ->alias('r')
                ->join(['game_providers'=>'p'], 'r.provider_id = p.id')
                ->where('p.is_active', 1)
                ->where('r.type', 'slots')
                ->order('r.clicks', 'desc')
                ->limit(self::SLOTMAX)
                ->field('r.game_room_id, r.code, r.name, r.type, r.clicks, p.name as provider_name');

            $slotsGames = $slotsQuery->select();
            if ($slotsGames) {
                // 格式化 Slots 游戏数据
                $formattedSlotsGames = [];
                foreach ($slotsGames as $game) {
                    // 确定图片文件名后缀
                    $suffix = '_en';
                    // 对于PP游戏，使用不同的后缀
                    if (isset($game['provider_name']) && strpos($game['provider_name'], 'PP') !== false) {
                        $suffix = '';
                    }
                    $formattedSlotsGames[] = [
                        'id' => $game['game_room_id'],
                        'code' => $game['code'],
                        'name' => $game['name'],
                        'type' => $game['type'],
                        'provider' => $game['provider_name'],
                        'image' => self::GAME_IMAGE_BASE_PATH . "{$game['provider_name']}/{$game['code']}{$suffix}.png"
                    ];
                }

                // 添加 Slots 分类到列表
                $activeGameList[] = [
                    'code' => 'slots',
                    'name' => 'Slots',
                    'games' => array_slice($formattedSlotsGames, 0, $defaultGameCount),
                    'total_games' => count($formattedSlotsGames),
                    'has_more' => count($formattedSlotsGames) > $defaultGameCount
                ];
            } else {
                FrontendLog::warning("未找到Slots游戏！");
            }

            // 为每个激活的提供商获取游戏数据
            foreach ($activeProviders as $provider) {
                // 获取该提供商的游戏
                $gamesQuery = \think\Db::table('game_rooms')
                    ->where('provider_id', $provider['id'])
                    ->order('clicks', 'desc')
                    ->limit(50); // 取点击量前50的游戏

                $games = $gamesQuery->select();


                if ($games) {
                     // 确定图片文件名后缀
                    $suffix = '_en';

                    // 对于PP游戏，使用不同的后缀
                    if (isset($provider['name']) && strpos($provider['name'], 'PP') !== false) {
                        $suffix = '';
                    }
                    // 格式化游戏数据
                    $formattedGames = [];
                    foreach ($games as $game) {

                        $formattedGames[] = [
                            'id' => $game['game_room_id'],
                            'code' => $game['code'],
                            'name' => $game['name'],
                            'type' => $game['type'],
                            'provider' => $provider['name'],
                            'image' => self::GAME_IMAGE_BASE_PATH . "{$provider['name']}/{$game['code']}{$suffix}.png"
                        ];
                    }

                    // 使用原始提供商名称
                    $filteredProviderName = $provider['name'];

                    // 添加该提供商到列表
                    $totalGames = count($formattedGames);
                    $activeGameList[] = [
                        'code' => strtolower(str_replace(' ', '_', $filteredProviderName)),
                        'name' => $filteredProviderName,
                        'games' => array_slice($formattedGames, 0, $defaultGameCount),
                        'total_games' => $totalGames,
                        'has_more' => $totalGames > $defaultGameCount
                    ];
                } else {
                    FrontendLog::warning("提供商 {$provider['name']} 没有游戏数据！");
                }
            }
        } catch (\Exception $e) {
            // 记录错误日志
            FrontendLog::error("获取游戏数据失败: " . $e->getMessage());
            FrontendLog::error("错误堆栈: " . $e->getTraceAsString());
            return [];
        }

        return $activeGameList;
    }

    /**
     * 获取特定分类的更多游戏
     * @param string $providerCode 提供商代码或编码后的名称
     * @param int $offset 偏移量
     * @param int $limit 获取数量
     * @param string $search 搜索关键词
     * @return array 游戏列表
     */
    public static function getMoreGames($providerCode, $offset = 9, $limit = 6, $search = '')
    {
        try {
            // 如果是popular分类或slots分类，特殊处理
            if ($providerCode === 'popular' || $providerCode === 'slots') {
                // 对于popular和slots分类，限制最多30个游戏
                $maxGames = self::SLOTMAX;

                // 如果偏移量已经达到或超过最大限制，则不再返回游戏
                if ($offset >= $maxGames) {
                    return [
                        'games' => [],
                        'has_more' => false
                    ];
                }

                // 调整limit，确保不会超过30个游戏的上限
                if ($offset + $limit > $maxGames) {
                    $limit = $maxGames - $offset;
                }

                // 初始化查询对象
                $query = \think\Db::table('game_rooms')
                    ->alias('r')
                    ->join(['game_providers'=>'p'], 'r.provider_id = p.id')
                    ->where('p.is_active', 1)
                    ->order('r.clicks', 'desc')
                    ->field('r.game_room_id, r.code, r.name, r.type, r.clicks, p.name as provider_name');

                // 如果有搜索关键词，添加搜索条件
                if (!empty($search)) {
                    $query->where('r.name', 'like', "%{$search}%");
                }

                // 根据分类类型添加不同的条件
                if ($providerCode === 'popular') {
                    // 获取名称中包含PG的游戏商的游戏
                    $query->where('p.name', 'like', '%PG%');
                } else { // slots分类

                    // 获取type为slots的游戏
                    $query->where('r.type', 'slots');
                }

                // 先获取所有符合条件的游戏，然后手动分页，避免重复
                $allGames = $query->select();
                // 手动分页，确保不会有重复
                $games = array_slice($allGames, $offset, $limit);

                // 获取总数，用于判断是否还有更多
                $totalCount = count($allGames);

                // 限制总数最大为30
                $totalCount = min($totalCount, $maxGames);
            } else {
                // 对于其他分类，通过提供商编码找到对应的提供商ID

                // 将 provider_code 转换回可能的提供商名称
                $possibleName = str_replace('_', ' ', $providerCode);

                // 查找提供商
                $provider = self::where('is_active', 1)
                    ->where(function($query) use ($providerCode, $possibleName) {
                        $query->where('name', 'like', "%{$possibleName}%")
                            ->whereOr('id', $providerCode); // 如果传入的是ID
                    })
                    ->find();

                if (!$provider) {
                    return [
                        'games' => [],
                        'has_more' => false
                    ];
                }

                // 获取该提供商的所有游戏
                $allGames = \think\Db::table('game_rooms')
                    ->where('provider_id', $provider['id'])
                    ->order('clicks', 'desc')
                    ->select();

                // 手动分页，确保不会有重复
                $games = array_slice($allGames, $offset, $limit);

                // 为每个游戏添加提供商名称
                foreach ($games as &$game) {
                    $game['provider_name'] = $provider['name'];
                }
                // 获取该提供商的游戏总数
                $totalCount = count($allGames);
            }

            if ($games) {
                // 格式化游戏数据
                $formattedGames = [];
                foreach ($games as $game) {
                    $formattedGames[] = self::formatGameData($game);
                }

                // 对于popular和slots分类，特殊处理has_more逻辑
                if ($providerCode === 'popular' || $providerCode === 'slots') {
                    return [
                        'games' => $formattedGames,
                        'has_more' => ($offset + $limit) < $totalCount && ($offset + $limit) < self::SLOTMAX
                    ];
                } else {
                    return [
                        'games' => $formattedGames,
                        'has_more' => ($offset + $limit) < $totalCount
                    ];
                }
            }
        } catch (\Exception $e) {
            // 记录错误日志
            FrontendLog::error("获取更多游戏数据失败: " . $e->getMessage());
        }

        return [
            'games' => [],
            'has_more' => false
        ];
    }

    /**
     * 获取热门游戏列表
     * @param int $offset 偏移量
     * @param int $limit 限制返回数量
     * @param string $type 游戏类型，默认为popular，可选slots
     * @return array 热门游戏列表
     */
    public static function getPopularGames($offset = 0, $limit = 0, $type = 'popular')
    {
        try {
            // 初始化查询
            $query = \think\Db::table('game_rooms')
                ->alias('r')
                ->join(['game_providers'=>'p'], 'r.provider_id = p.id')
                ->where('p.is_active', 1)
                ->order('r.clicks', 'desc')
                ->field('r.game_room_id, r.code, r.name, r.type, r.clicks, p.name as provider_name');

            // 根据类型添加不同的条件
            if ($type === 'popular') {
                // 获取名称中包含PG的游戏商的游戏
                $query->where('p.name', 'like', '%PG%');
            } elseif ($type === 'slots') {
                // 获取type为slots的游戏
                $query->where('r.type', 'slots');
            }

            // 如果设置了限制数量和偏移量，则应用分页
            if ($limit > 0) {
                $query->limit($offset, $limit);
            }

            $games = $query->select();

            if ($games) {
                // 格式化游戏数据
                $formattedGames = [];
                foreach ($games as $game) {
                    $formattedGames[] = self::formatGameData($game);
                }

                return $formattedGames;
            }
        } catch (\Exception $e) {
            // 记录错误日志
            FrontendLog::error("获取游戏失败: " . $e->getMessage());
        }

        return [];
    }

    /**
     * 获取所有游戏提供商列表（包括非激活的）
     * @param bool $namesOnly 是否只返回名称列表
     * @return array 游戏提供商列表
     */
    public static function getAllProviders($namesOnly = false)
    {
        // 查询所有游戏提供商
        $query = self::order('sort', 'asc');

        if ($namesOnly) {
            // 只返回名称列表
            return $query->column('name');
        } else {
            // 返回完整数据
            return $query->select();
        }
    }

    /**
     * 获取可用的游戏提供商列表（激活且非维护状态）
     * @param bool $namesOnly 是否只返回名称列表
     * @param bool $includeGames 是否包含游戏数据
     * @return array 游戏提供商列表
     */
    public static function getAvailableProviders($namesOnly = false, $includeGames = false)
    {
        // 查询激活且非维护状态的游戏提供商
        $query = self::where('is_active', 1)
            ->where('is_maintenance', 0);

        // 排序
        $query->order('sort', 'asc');

        if ($namesOnly) {
            // 只返回名称列表
            return $query->column('name');
        } else {
            // 返回完整数据
            $providers = $query->select();

            // 是否包含游戏数据
            if ($includeGames && $providers) {
                $providers = self::loadGamesData($providers);
            }

            return $providers;
        }
    }



    /**
     * 根据名称查找游戏提供商
     * 支持精确匹配和模糊匹配
     * @param string $name 提供商名称
     * @param bool $activeOnly 是否只查找激活的提供商
     * @param bool $fuzzyMatch 是否启用模糊匹配
     * @return array|null 提供商信息，如果未找到则返回null
     */
    public static function findProviderByName($name, $activeOnly = true, $fuzzyMatch = true)
    {
        try {
            $provider = \think\Db::table('game_providers')
                ->where('name', $fuzzyMatch ? 'like' : '=', $name)
                ->find();
            return $provider;
        } catch (\Exception $e) {
            // 记录错误日志
            FrontendLog::error("查找游戏提供商失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取游戏提供商的维护状态
     * @param int $providerId 提供商ID
     * @return bool 是否处于维护状态
     */
    public static function isMaintenance($providerId)
    {
        $provider = self::where('id', $providerId)->find();
        return $provider ? (bool)$provider['is_maintenance'] : true;
    }

    /**
     * 切换游戏提供商的激活状态
     * @param int $providerId 提供商ID
     * @return bool 操作是否成功
     */
    public static function toggleActive($providerId)
    {
        $provider = self::where('id', $providerId)->find();
        if ($provider) {
            $provider['is_active'] = $provider['is_active'] ? 0 : 1;
            return $provider->save();
        }
        return false;
    }

    /**
     * 切换游戏提供商的维护状态
     * @param int $providerId 提供商ID
     * @return bool 操作是否成功
     */
    public static function toggleMaintenance($providerId)
    {
        $provider = self::where('id', $providerId)->find();
        if ($provider) {
            $provider['is_maintenance'] = $provider['is_maintenance'] ? 0 : 1;
            return $provider->save();
        }
        return false;
    }

    /**
     * 获取子游戏页面数据
     * 处理分类、标签和分页，返回格式化的游戏数据
     * @param string $category 主分类（slots或游戏提供商名称）
     * @param string $hcategory 横向分类标签（popular、recente、favoritos等）
     * @param int $page 当前页码
     * @param int $limit 每页显示的游戏数量
     * @param string $search 搜索关键词
     * @return array 游戏数据，包含游戏列表、分页信息等
     */
    public static function getSubgameData($category = 'slots', $hcategory = '', $page = 1, $limit = 18, $search = '')
    {
        try {
            // 初始化条件数组
            $conditions = [];
            $orderBy = 'clicks';
            $orderDir = 'desc';
            $categoryName = $category;

            // 处理主分类
            if ($category === 'slots') {
                // 获取所有类型为slots的游戏
                $conditions['type'] = 'slots';
                $categoryName = 'Slots';
            } else {
                // 获取指定提供商的游戏
                $provider = null;
                $providers = self::getCachedProviders();

                foreach ($providers as $p) {
                    if ($p['name'] === $category) {
                        $provider = $p;
                        break;
                    }
                }

                if (!$provider) {
                    return [
                        'category' => $category,
                        'name' => $category,
                        'games' => [],
                        'has_more' => false,
                        'current_page' => $page,
                        'total_pages' => 0,
                        'total_games' => 0
                    ];
                }

                $conditions['provider_id'] = $provider['id'];
                // 删除提供商名称中的"仿"字和空格
                $categoryName = trim(str_replace('仿', '', $provider['name']));
            }

            // 处理横向分类标签
            if (!empty($hcategory)) {
                switch ($hcategory) {
                    case 'popular':
                        // 热门游戏，按点击量排序（默认已经是按点击量排序）
                        $orderBy = 'clicks';
                        $orderDir = 'desc';
                        break;
                    case 'recente':
                        // 最新游戏，按添加时间排序
                        $orderBy = 'created_at';
                        $orderDir = 'desc';
                        break;
                    case 'favoritos':
                        // 收藏游戏，这里需要根据实际情况实现
                        // 如果有收藏表，可以加入收藏表的条件
                        // 这里暂时保持默认排序
                        $orderBy = 'clicks';
                        $orderDir = 'desc';
                        break;
                    default:
                        // 默认排序
                        $orderBy = 'clicks';
                        $orderDir = 'desc';
                        break;
                }
            }

            // 处理搜索
            if (!empty($search)) {
                $conditions['name'] = ['op' => 'like', 'value' => "%{$search}%"];
            }

            // 使用缓存数据分页获取游戏
            $result = self::paginateCachedRooms($conditions, $orderBy, $orderDir, $page, $limit);

            // 返回结果
            return [
                'category' => $category,
                'name' => $categoryName,
                'games' => $result['games'],
                'has_more' => $result['has_more'],
                'current_page' => $result['current_page'],
                'total_pages' => $result['total_pages'],
                'total_games' => $result['total_count']
            ];

        } catch (\Exception $e) {
            // 记录错误日志
            FrontendLog::error("获取子游戏页面数据失败: " . $e->getMessage());
            FrontendLog::error("错误堆栈: " . $e->getTraceAsString());

            // 返回空结果
            return [
                'category' => $category,
                'name' => $category,
                'games' => [],
                'has_more' => false,
                'current_page' => $page,
                'total_pages' => 0,
                'total_games' => 0
            ];
        }
    }

    /**
     * 格式化游戏数据
     * 将数据库中的游戏数据格式化为前端需要的格式
     * @param array $game 游戏数据
     * @return array 格式化后的游戏数据
     */
    protected static function formatGameData($game)
    {
        // 检查是否存在提供商名称
        if (!isset($game['provider_name']) || empty($game['provider_name'])) {
            $providerName = 'unknown';
        } else {
            $providerName = $game['provider_name'];
        }

        // 确定图片文件扩展名
        $extension = 'png';
        // 确定图片文件名后缀
        $suffix = '_en';

        // 对于PP游戏，使用不同的后缀
        if (isset($providerName) && strpos($providerName, 'PP') !== false) {
            $suffix = '';
        }

        // 构建图片URL
        $imgurl = self::GAME_IMAGE_BASE_PATH . "{$providerName}/{$game['code']}{$suffix}.{$extension}";

        return [
            'id' => $game['game_room_id'],
            'game_id' => $game['game_room_id'], // 兼容旧代码
            'code' => $game['code'],
            'name' => $game['name'], // 过滤游戏名称中的"仿"字
            'type' => isset($game['type']) ? $game['type'] : '',
            'provider' => $providerName,
            'image' => $imgurl,
            'clicks' => isset($game['clicks']) ? $game['clicks'] : 0
        ];
    }
}