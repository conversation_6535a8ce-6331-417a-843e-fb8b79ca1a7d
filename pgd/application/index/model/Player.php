<?php

namespace app\index\model;

use think\Model;
use think\Session;
use app\common\library\FrontendLog;

/**
 * 玩家模型
 * Class Player
 * @package app\index\model
 */
class Player extends Model
{
    // 表名
    protected $table = 'players';

    // 禁用自动写入时间戳
    protected $autoWriteTimestamp = false;

    // 时间戳字段名（不再自动填充）
    // protected $createTime = 'created_at';
    // protected $updateTime = 'updated_at';

    // 类型转换
    protected $type = [
        'id' => 'integer',
        'balance' => 'float',
        'reward_balance' => 'float',
        'is_game_banned' => 'integer',
        'vip_level' => 'integer'
    ];

    /**
     * 验证玩家登录
     * @param string $phone_number 手机号
     * @param string $password 密码
     * @return array 成功返回用户信息数组，失败返回错误信息数组
     */
    public static function checkLogin($phone_number, $password)
    {
        if (empty($phone_number) || empty($password)) {
            FrontendLog::info('登录失败: 手机号或密码为空');
            return ['code' => 100, 'msg' => 'Número de telefone ou senha não podem estar vazios'];
        }

        // 记录查询开始
        FrontendLog::info('尝试登录: phone_number=' . $phone_number);

        $player = self::where('phone_number', $phone_number)
            ->find();
        
        if (!$player) {
            $player = self::where('username', $phone_number)
            ->find();
        }
        $playerType = 'regular'; // 默认为普通玩家

        if(!$player)
        {
            $player = \app\index\model\VirtualPlayer::where('phone_number', $phone_number)
                ->where('is_active', 1)
                ->find();
            if (!$player) {
                FrontendLog::info('登录失败: 账户不存在或用户被禁用: ' . $phone_number);
                return ['code' => 101, 'msg' => 'Este número/nome de usuário não está cadastrado'];
            }
            $playerType = 'virtual'; // 标记为虚拟玩家
        }

        // 记录找到用户
        FrontendLog::info('找到用户: id=' . $player['id'] . ', phone_number=' . $player['phone_number'].', player_type=' . $playerType);

        // 密码验证
        if ($player['password'] != $password) {
            FrontendLog::info('登录失败: 密码不正确: phone_number=' . $phone_number);
            return ['code' => 102, 'msg' => 'Senha incorreta'];
        }

        // 记录密码验证通过
        FrontendLog::info('密码验证通过: phone_number=' . $phone_number);

        $isVirtual = $playerType == 'virtual';
        // 尝试获取用户的详细数据，直接从数据库查询
        try {
            if($isVirtual){
                $db = \think\Db::table('virtual_players');
            }else{
                $db = \think\Db::table('players');
            }
            $userDetail = $db->where('id', $player['id'])->find();
            if ($userDetail) {
                FrontendLog::info('直接从数据库获取到用户详情: ' . json_encode($userDetail));
            } else {
                FrontendLog::warning('无法直接从数据库获取用户详情, id=' . $player['id']);
            }
        } catch (\Exception $e) {
            FrontendLog::error('查询用户详情异常: ' . $e->getMessage());
        }

        // 登录成功，设置session
        Session::set('player_id', $player['id']);
        Session::set('player_phone_number', $player['phone_number']);
        Session::set('player_lastactive', time());
        Session::set('player_type', $playerType); // 设置玩家类型

        // 更新最后登录时间和IP
        try {
            $loginIp = request()->ip();
            $player->last_login_time = date('Y-m-d H:i:s');
            $player->last_login_ip = $loginIp;
            $player->updated_at = date('Y-m-d H:i:s'); // 手动更新updated_at字段

            // 记录调试信息
            FrontendLog::info('更新登录信息: ' . json_encode([
                'player_id' => $player['id'],
                'last_login_time' => $player->last_login_time,
                'last_login_ip' => $player->last_login_ip,
                'updated_at' => $player->updated_at
            ]));

            if (!$player->save()) {
                FrontendLog::error('更新登录信息失败: player_id=' . $player['id']);
            }
        } catch (\Exception $e) {
            FrontendLog::error('更新登录信息异常: ' . $e->getMessage());
        }

        if(!$isVirtual)
        {
            // 记录玩家登录日志
            $playerLoginLog = new \app\index\model\PlayerLoginLog();
            $logResult = $playerLoginLog->recordLogin($player['id'], $loginIp);
            if ($logResult) {
                FrontendLog::info('记录玩家登录日志成功: player_id=' . $player['id'] . ', ip=' . $loginIp);
            } else {
                FrontendLog::error('记录玩家登录日志失败: player_id=' . $player['id'] . ', ip=' . $loginIp);
            }
        }
        $playerData = $player->toArray();
        return ['code' => 1, 'msg' => 'Login bem-sucedido', 'data' => $playerData];
    }

    /**
     * 验证当前登录状态
     * @return array 已登录返回用户信息，未登录返回错误信息
     */
    public static function checkLoginStatus()
    {
        $phone_number = Session::get('player_phone_number');
        $userId = Session::get('player_id');
        $playerType = Session::get('player_type');

        if (!$phone_number || !$userId) {
            return ['code' => 0, 'msg' => 'Não logado', 'isLoggedIn' => false];
        }

        // 根据玩家类型选择不同的模型
        if ($playerType == 'virtual') {
            // 使用虚拟玩家模型
            $player = \app\index\model\VirtualPlayer::where('id', $userId)
                ->where('phone_number', $phone_number)
                ->where('is_active', 1)
                ->find();

            if (!$player) {
                // 用户验证失败，清除session
                self::logout();
                return ['code' => 0, 'msg' => 'Falha na verificação do usuário', 'isLoggedIn' => false];
            }

            // 更新session最后活动时间
            Session::set('player_lastactive', time());

            // 转换为数组并添加余额
            $playerData = $player->toArray();
            $playerData['balance'] = $player['balance'];
            $playerData['reward_balance'] = 0.00; // 虚拟玩家没有奖励余额
            $playerData['player_type'] = 'virtual';
            $playerData['isLoggedIn'] = true;

            return ['code' => 1, 'msg' => 'Logado', 'data' => $playerData, 'isLoggedIn' => true];
        } else {
            // 使用普通玩家模型
            $player = self::where('id', $userId)
                ->where('phone_number', $phone_number)
                ->find();

            if (!$player) {
                // 用户验证失败，清除session
                self::logout();
                return ['code' => 0, 'msg' => 'Falha na verificação do usuário', 'isLoggedIn' => false];
            }

            // 更新session最后活动时间
            Session::set('player_lastactive', time());

            $playerData = $player->toArray();
            $playerData['player_type'] = 'regular';
            $playerData['isLoggedIn'] = true;

            return ['code' => 1, 'msg' => 'Logado', 'data' => $playerData, 'isLoggedIn' => true];
        }
    }

    /**
     * 玩家退出登录
     * @return array 返回退出状态
     */
    public static function logout()
    {
        try {
            // 记录日志
            FrontendLog::info('执行玩家退出登录: player_id=' . Session::get('player_id') . ', phone_number=' . Session::get('player_phone_number') . ', IP=' . request()->ip());

            // 获取当前会话中的所有数据，用于日志记录
            $sessionData = Session::get();
            FrontendLog::info('退出前的会话数据: ' . json_encode($sessionData, JSON_UNESCAPED_UNICODE));

            // 清除所有玩家相关的会话数据
            Session::delete('player_phone_number');
            Session::delete('player_id');
            Session::delete('player_lastactive');
            Session::delete('player_type');

            // 清除其他可能存在的会话数据
            Session::delete('auth_player');
            Session::delete('player_data');
            Session::delete('player_balance');
            Session::delete('player_token');

            // 清除可能存在的其他会话数据
            Session::delete('user');
            Session::delete('admin');
            Session::delete('agent');

            // 清除所有会话数据
            Session::clear();

            // 销毁会话
            if (PHP_SAPI !== 'cli') {
                session_destroy();

                // 清除所有cookie
                $cookies = request()->cookie();
                foreach ($cookies as $name => $value) {
                    cookie($name, null);
                }

                // 特别处理think_var语言cookie
                cookie('think_var', null);
            }

            // 记录成功日志
            FrontendLog::info('玩家会话数据已清除');

            return ['code' => 1, 'msg' => 'Logout bem-sucedido'];
        } catch (\Exception $e) {
            // 记录错误日志
            FrontendLog::error('玩家退出登录出错: ' . $e->getMessage());

            // 即使出错，也尝试清除关键会话数据
            Session::delete('player_id');
            Session::delete('player_phone_number');

            // 尝试清除所有会话数据
            try {
                Session::clear();
                if (PHP_SAPI !== 'cli') {
                    session_destroy();
                }
            } catch (\Exception $e2) {
                FrontendLog::error('清除会话数据出错: ' . $e2->getMessage());
            }

            return ['code' => 0, 'msg' => 'Erro durante o logout, mas a sessão foi limpa'];
        }
    }

    /**
     * 刷新玩家余额
     * @return array 返回余额信息或错误信息
     */
    public static function refreshBalance()
    {
        $userId = Session::get('player_id');
        $playerType = Session::get('player_type');

        if (!$userId) {
            return ['code' => 0, 'msg' => 'Não logado'];
        }
        // 根据玩家类型选择不同的模型
        if ($playerType == 'virtual') {
            // 使用虚拟玩家模型
            $player = \app\index\model\VirtualPlayer::where('id', $userId)
                ->where('is_active', 1)
                ->find();

            if (!$player) {
                return ['code' => 0, 'msg' => 'Usuário não existe ou está desativado'];
            }

            $balanceData = [
                'balance' => $player['balance'] ?: '0.00',
                'reward_balance' => '0.00', // 虚拟玩家没有奖励余额
                'player_type' => 'virtual'
            ];

            return ['code' => 1, 'msg' => 'Atualização bem-sucedida', 'data' => $balanceData];
        } else {
            // 使用普通玩家模型
            $player = self::where('id', $userId)
                ->find();

            if (!$player) {
                return ['code' => 0, 'msg' => 'Usuário não existe ou está desativado'];
            }

            $balanceData = [
                'balance' => $player['balance'] ?: '0.00',
                'reward_balance' => $player['reward_balance'] ?: '0.00',
                'player_type' => 'regular'
            ];

            return ['code' => 1, 'msg' => 'Atualização bem-sucedida', 'data' => $balanceData];
        }
    }

    /**
     * 创建新玩家
     * @param string $username 用户名
     * @param string $password 密码
     * @param string $phone_number 手机号
     * @param string $invite_code 邀请码（邀请人ID）
     * @return array 成功返回用户信息，失败返回错误信息
     */
    public static function register($username, $password, $phone_number = '', $inviteCodeFromPlayer='')
    {
        // 检查用户名是否已存在
        if (self::where('username', $username)->find()) {
            return ['code' => 101, 'msg' => 'Nome de usuário já existe'];
        }

        // 检查手机号是否已存在
        if (!empty($phone_number) && self::where('phone_number', $phone_number)->find()) {
            return ['code' => 102, 'msg' => 'Número de telefone já está registrado'];
        }

        // 从session中获取推广参数
        $sessionIcId = Session::get('referral_ic');
        $sessionInviteCode = Session::get('referral_id');

        FrontendLog::info('玩家注册：从session中获取推广参数: ic=' . $sessionIcId . ', id=' . $sessionInviteCode );
        // 使用session中的参数
        if (!empty($sessionIcId)) {
            $ic_id = $sessionIcId;
            // 记录从session中获取的推广参数
            FrontendLog::info('玩家注册：注册使用的推广参数: ic_id=' . $ic_id );
        }

        if (!empty($sessionInviteCode)) {
            $invite_code = $sessionInviteCode;
            FrontendLog::info('玩家注册：从session中获取推广参数:  id=' . $sessionInviteCode );
        }
        if(empty($sessionIcId) && empty($sessionInviteCode) && !empty($inviteCodeFromPlayer)){
            $invite_code = $inviteCodeFromPlayer;
            FrontendLog::info('玩家注册：注册使用的邀请码是前端传的: invite_code=' . $invite_code);
        }

        // 获取当前完整的访问链接
        $request = request();
        $currentUrl = $request->url(true);
        FrontendLog::info('玩家注册：访问url::'.$currentUrl);

        if(!empty($ic_id))
        {
            $agentResult = \think\Db::table('agents')->where('id', $ic_id)->find();
            if(empty($agentResult)){
                FrontendLog::info('玩家注册：注册失败: 邀请人不存在: ' . $ic_id);
                return ['code' => 103, 'msg' => 'O referenciador não está registrado'];
            }
        }

        if(!empty($invite_code))
        {
            $playerResult = \think\Db::table('players')->where('id', $invite_code)->find();
            if(empty($playerResult)){
                FrontendLog::info('玩家注册：注册失败: 邀请码不存在: ' . $invite_code);
                return ['code' => 104, 'msg' => 'Código de convite inválido'];
            }
        }

        try {
            // 创建新玩家记录
            $player = new self;
            $player->username = $username;
            $player->password = $password; // 密码会在模型中自动加密
            $player->withdraw_password = ''; // 默认为空，用户后续可以设置
            $player->phone_number = $phone_number ? $phone_number : 'user_'.time().rand(1000,9999); // 如果手机号为空，生成一个唯一值
            $player->register_ip = request()->ip();
            $player->last_login_time = date('Y-m-d H:i:s');
            $player->last_login_ip = request()->ip();
            $player->balance = 0.00;
            $player->reward_balance = 0.00;
            $player->created_at = date('Y-m-d H:i:s'); // 手动设置created_at
            $player->updated_at = date('Y-m-d H:i:s'); // 手动设置updated_at
            $player->total_deposit = 0.00;
            $player->total_withdraw = 0.00;
            $player->total_bet = 0.00;
            $player->total_win = 0.00;
            $player->rtp = 0.0;
            $player->team_rtp = 0.0;

            // 保存邀请人ID并获取上级RTP值
            if(!empty($ic_id)){
                $player->agent_id = $ic_id;
            }
            else if (!empty($invite_code)) {
                $player->referrer_id = $invite_code;

                // 直接使用邀请码(上级玩家ID)查询上级玩家的RTP值
                $ancestorRtpData = self::getAncestorTeamRtpByReferrerId($invite_code);
                if ($ancestorRtpData) {
                    $player->rtp = $ancestorRtpData['team_rtp'];
                    FrontendLog::info('玩家注册：从上级玩家继承团队RTP值: ' . $ancestorRtpData['team_rtp']);
                }
            }

            // 记录调试日志
            FrontendLog::info('玩家注册：Player注册字段: ' . json_encode($player->toArray()));

            if ($player->save()) {
                // 登录成功，设置session
                Session::set('player_id', $player->id);
                Session::set('player_phone_number', $player->phone_number);
                Session::set('player_lastactive', time());
                Session::set('player_type', 'regular');

                // 如果有推荐人，复制推荐人的掉绑设置和佣金配置
                if (!empty($player->referrer_id)) {
                    try {
                        // 获取推荐人的掉绑设置
                        $referrerSettings = self::getAncestorUnbindSettings($player->id);

                        if ($referrerSettings) {
                            // 创建新玩家的掉绑设置记录
                            $unbindSettings = [
                                'player_id' => $player->id,
                                'sync_next' => 0,
                                'unbind_mode' => $referrerSettings['unbind_mode'],
                                'reg_unbind_type' => $referrerSettings['reg_unbind_type'],
                                'reg_unbind_threshold' => $referrerSettings['reg_unbind_threshold'],
                                'reg_unbind_interval' => $referrerSettings['reg_unbind_interval'],
                                'reg_unbind_prob' => $referrerSettings['reg_unbind_prob'],
                                'deposit_unbind_type' => $referrerSettings['deposit_unbind_type'],
                                'deposit_unbind_threshold' => $referrerSettings['deposit_unbind_threshold'],
                                'deposit_unbind_interval' => $referrerSettings['deposit_unbind_interval'],
                                'deposit_unbind_prob' => $referrerSettings['deposit_unbind_prob'],
                                'created_at' => date('Y-m-d H:i:s')
                            ];

                            \think\Db::table('player_unbind_settings')->insert($unbindSettings);
                            FrontendLog::info('玩家注册：已从第'.$referrerSettings['level'].'级推荐人复制掉绑设置: player_id=' . $player->id);
                        }

                        // 获取推荐人的佣金配置
                        $referrerCommissionConfig = self::getAncestorCommissionConfig($player->id);

                        if ($referrerCommissionConfig) {
                            // 创建新玩家的佣金配置记录
                            $commissionConfig = [
                                'player_id' => $player->id,
                                'sync_next' => 0,
                                'min_deposit_amount' => $referrerCommissionConfig['min_deposit_amount'],
                                'level1_rate' => $referrerCommissionConfig['level1_rate'],
                                'level1_max_amount' => $referrerCommissionConfig['level1_max_amount'],
                                'level2_rate' => $referrerCommissionConfig['level2_rate'],
                                'level2_max_amount' => $referrerCommissionConfig['level2_max_amount'],
                                'level3_rate' => $referrerCommissionConfig['level3_rate'],
                                'level3_max_amount' => $referrerCommissionConfig['level3_max_amount'],
                                'created_at' => date('Y-m-d H:i:s')
                            ];

                            \think\Db::table('player_commission_configs')->insert($commissionConfig);
                            FrontendLog::info('玩家注册：已从第'.$referrerCommissionConfig['level'].'级推荐人复制佣金配置: player_id=' . $player->id);
                        }
                    } catch (\Exception $e) {
                        FrontendLog::error('玩家注册：复制推荐人设置失败: ' . $e->getMessage());
                    }
                }

                // 记录玩家登录日志
                try {
                    $loginIp = request()->ip();
                    $playerLoginLog = new \app\index\model\PlayerLoginLog();
                    $logResult = $playerLoginLog->recordLogin($player->id, $loginIp);
                    if ($logResult) {
                        FrontendLog::info('玩家注册：注册后记录玩家登录日志成功: player_id=' . $player->id . ', ip=' . $loginIp);
                    } else {
                        FrontendLog::error('玩家注册：注册后记录玩家登录日志失败: player_id=' . $player->id . ', ip=' . $loginIp);
                    }
                } catch (\Exception $e) {
                    FrontendLog::error('玩家注册：注册后记录玩家登录日志异常: ' . $e->getMessage());
                }
                return ['code' => 1, 'msg' => 'Registro bem-sucedido', 'data' => $player->toArray()];
            } else {
                // 记录保存失败的详细信息
                FrontendLog::error('玩家注册：保存玩家记录失败，错误信息: ' . json_encode($player->getError()));
                return ['code' => 105, 'msg' => 'Falha ao salvar registro do jogador'];
            }
        } catch (\PDOException $pe) {
            // 记录数据库错误的详细信息
            FrontendLog::error('玩家注册：创建玩家账号时数据库错误: ' . $pe->getMessage() . ' SQL:' . $pe->getCode() . ' 堆栈跟踪: ' . $pe->getTraceAsString());
            return ['code' => 106, 'msg' => 'Erro de banco de dados: ' . $pe->getMessage()];
        } catch (\Exception $e) {
            // 记录一般异常的详细信息
            FrontendLog::error('玩家注册：创建玩家账号失败: ' . $e->getMessage() . ' 堆栈跟踪: ' . $e->getTraceAsString());
            return ['code' => 107, 'msg' => 'Falha ao criar conta: ' . $e->getMessage()];
        }
    }

     /**
     * 获取直推人数（一级下线数量）
     * @param array $data 玩家数据
     * @param bool $dropBound 是否排除掉绑下属
     * @return int 直推人数
     */
    public static function getDirectReferralsCountAttr($data, $dropBound = false)
    {
        if (isset($data['id'])) {
            // 基础查询 - 获取一级下线
            $query = \think\Db::table('player_relations')
                ->alias('pr')
                ->where('pr.ancestor_id', $data['id'])
                ->where('pr.relation_level', 1);

            // 如果需要排除掉绑下属，添加关联查询
            if ($dropBound) {
                $query->join(['players' => 'p'], 'pr.player_id = p.id')
                      ->where('p.self_unbind_status', 0);
            }

            // 执行查询获取下属数量
            $directReferralsCount = $query->count();

            return $directReferralsCount ?: 0;
        }
        return 0;
    }

    /**
     * 获取二级下线人数
     * @param array $data 玩家数据
     * @param bool $dropBound 是否排除掉绑下属
     * @return int 二级下线人数
     */
    public static function getSecondLevelReferralsCountAttr($data, $dropBound = false)
    {
        if (isset($data['id'])) {
            // 基础查询 - 获取二级下线
            $query = \think\Db::table('player_relations')
                ->alias('pr')
                ->where('pr.ancestor_id', $data['id'])
                ->where('pr.relation_level', 2);

            // 如果需要排除掉绑下属，添加关联查询
            if ($dropBound) {
                $query->join(['players' => 'p'], 'pr.player_id = p.id')
                      ->where('p.self_unbind_status', 0);
            }

            // 执行查询获取下属数量
            $secondLevelReferralsCount = $query->count();

            return $secondLevelReferralsCount ?: 0;
        }
        return 0;
    }


    /**
     * 获取三级下线人数
     * @param array $data 玩家数据
     * @param bool $dropBound 是否排除掉绑下属
     * @return int 三级下线人数
     */
    public static function getThirdLevelReferralsCountAttr($data, $dropBound = false)
    {
        if (isset($data['id'])) {
            // 基础查询 - 获取三级下线
            $query = \think\Db::table('player_relations')
                ->alias('pr')
                ->where('pr.ancestor_id', $data['id'])
                ->where('pr.relation_level', 3);

            // 如果需要排除掉绑下属，添加关联查询
            if ($dropBound) {
                $query->join(['players' => 'p'], 'pr.player_id = p.id')
                      ->where('p.self_unbind_status', 0);
            }

            // 执行查询获取下属数量
            $thirdLevelReferralsCount = $query->count();

            return $thirdLevelReferralsCount ?: 0;
        }
        return 0;
    }

    /**
     * 获取直推充值人数（一级下线中有充值记录的人数）
     * @param array $data 玩家数据
     * @param bool $dropBound 是否排除掉绑下属
     * @return int 直推充值人数
     */
    public static function getDirectDepositReferralsCountAttr($data, $dropBound = false)
    {
        if (isset($data['id'])) {
            try {
                // 构建SQL查询，考虑掉绑设置
                $sql = "SELECT COUNT(DISTINCT pr.player_id) as count
                       FROM player_relations pr
                       INNER JOIN deposit_orders d ON pr.player_id = d.player_id";

                // 如果需要排除掉绑下属，添加关联查询
                if ($dropBound) {
                    $sql .= " INNER JOIN players p ON pr.player_id = p.id AND p.self_unbind_status = 0";
                }

                $sql .= " WHERE pr.ancestor_id = ?
                       AND pr.relation_level = 1
                       AND d.payment_status = 1";

                $result = \think\Db::query($sql, [$data['id']]);
                $directDepositReferralsCount = isset($result[0]['count']) ? $result[0]['count'] : 0;

                return $directDepositReferralsCount ?: 0;
            } catch (\Exception $e) {
                // 记录错误日志
                \app\common\library\FrontendLog::error('获取直推充值人数失败: ' . $e->getMessage());
                return 0;
            }
        }
        return 0;
    }

    /**
     * 获取二级下线充值人数（二级下线中有充值记录的人数）
     * @param array $data 玩家数据
     * @param bool $dropBound 是否排除掉绑下属
     * @return int 二级下线充值人数
     */
    public static function getSecondLevelDepositReferralsCountAttr($data, $dropBound = false)
    {
        if (isset($data['id'])) {
            try {
                // 构建SQL查询，考虑掉绑设置
                $sql = "SELECT COUNT(DISTINCT pr.player_id) as count
                       FROM player_relations pr
                       JOIN deposit_orders d ON pr.player_id = d.player_id";

                // 如果需要排除掉绑下属，添加关联查询
                if ($dropBound) {
                    $sql .= " INNER JOIN players p ON pr.player_id = p.id AND p.self_unbind_status = 0";
                }

                $sql .= " WHERE pr.ancestor_id = ?
                       AND pr.relation_level = 2
                       AND d.payment_status = 1";

                $result = \think\Db::query($sql, [$data['id']]);
                $secondLevelDepositReferralsCount = isset($result[0]['count']) ? $result[0]['count'] : 0;

                return $secondLevelDepositReferralsCount ?: 0;
            } catch (\Exception $e) {
                // 记录错误日志
                \app\common\library\FrontendLog::error('获取二级下线充值人数失败: ' . $e->getMessage());
                return 0;
            }
        }
        return 0;
    }


    /**
     * 获取三级下线充值人数（三级下线中有充值记录的人数）
     * @param array $data 玩家数据
     * @param bool $dropBound 是否排除掉绑下属
     * @return int 三级下线充值人数
     */
    public static function getThirdLevelDepositReferralsCountAttr($data, $dropBound = false)
    {
        if (isset($data['id'])) {
            try {
                // 构建SQL查询，考虑掉绑设置
                $sql = "SELECT COUNT(DISTINCT pr.player_id) as count
                       FROM player_relations pr
                       JOIN deposit_orders d ON pr.player_id = d.player_id";

                // 如果需要排除掉绑下属，添加关联查询
                if ($dropBound) {
                    $sql .= " INNER JOIN players p ON pr.player_id = p.id AND p.self_unbind_status = 0";
                }

                $sql .= " WHERE pr.ancestor_id = ?
                       AND pr.relation_level = 3
                       AND d.payment_status = 1";

                $result = \think\Db::query($sql, [$data['id']]);
                $thirdLevelDepositReferralsCount = isset($result[0]['count']) ? $result[0]['count'] : 0;

                return $thirdLevelDepositReferralsCount ?: 0;
            } catch (\Exception $e) {
                // 记录错误日志
                \app\common\library\FrontendLog::error('获取三级下线充值人数失败: ' . $e->getMessage());
                return 0;
            }
        }
        return 0;
    }

    /**
     * 获取直推有效充值人数（首笔充值大于等于门槛金额的一级下线人数）
     * @param array $data 玩家数据
     * @param bool $dropBound 是否排除掉绑下属
     * @return int 直推有效充值人数
     */
    public static function getDerectDepositReferralsValidCount($data, $dropBound = false)
    {
        if (isset($data['id'])) {
            try {
                // 从环境变量中读取有效下级门槛金额，默认为25
                $validSubordinatesThreshold = \think\Env::get('app.valid_subordinates_threshold', 25);

                // 构建SQL查询，考虑掉绑设置
                $sql = "SELECT COUNT(DISTINCT pr.player_id) as count
                        FROM player_relations pr
                        JOIN (
                            SELECT player_id, amount
                            FROM deposit_orders
                            WHERE payment_status = 1
                            AND (player_id, id) IN (
                                SELECT player_id, MIN(id)
                                FROM deposit_orders
                                WHERE payment_status = 1
                                GROUP BY player_id
                            )
                            AND amount >= {$validSubordinatesThreshold}
                        ) d ON pr.player_id = d.player_id";

                // 如果需要排除掉绑下属，添加关联查询
                if ($dropBound) {
                    $sql .= " INNER JOIN players p ON pr.player_id = p.id AND p.self_unbind_status = 0";
                }

                $sql .= " WHERE pr.ancestor_id = ?
                        AND pr.relation_level = 1";

                $result = \think\Db::query($sql, [$data['id']]);
                $count = isset($result[0]['count']) ? $result[0]['count'] : 0;

                return $count ?: 0;
            } catch (\Exception $e) {
                // 记录错误日志
                \app\common\library\FrontendLog::error('获取直推有效充值人数失败: ' . $e->getMessage());
                return 0;
            }
        }
        return 0;
    }

    /**
     * 获取二级下线有效充值人数（首笔充值大于等于门槛金额的二级下线人数）
     * @param array $data 玩家数据
     * @param bool $dropBound 是否排除掉绑下属
     * @return int 二级下线有效充值人数
     */
    public static function getSecondLevelDepositReferralsValidCount($data, $dropBound = false)
    {
        if (isset($data['id'])) {
            try {
                // 从环境变量中读取有效下级门槛金额，默认为25
                $validSubordinatesThreshold = \think\Env::get('app.valid_subordinates_threshold', 25);

                // 构建SQL查询，考虑掉绑设置
                $sql = "SELECT COUNT(DISTINCT pr.player_id) as count
                        FROM player_relations pr
                        JOIN (
                            SELECT player_id, amount
                            FROM deposit_orders
                            WHERE payment_status = 1
                            AND (player_id, id) IN (
                                SELECT player_id, MIN(id)
                                FROM deposit_orders
                                WHERE payment_status = 1
                                GROUP BY player_id
                            )
                            AND amount >= {$validSubordinatesThreshold}
                        ) d ON pr.player_id = d.player_id";

                // 如果需要排除掉绑下属，添加关联查询
                if ($dropBound) {
                    $sql .= " INNER JOIN players p ON pr.player_id = p.id AND p.self_unbind_status = 0";
                }

                $sql .= " WHERE pr.ancestor_id = ?
                        AND pr.relation_level = 2";

                $result = \think\Db::query($sql, [$data['id']]);
                $count = isset($result[0]['count']) ? $result[0]['count'] : 0;

                return $count ?: 0;
            } catch (\Exception $e) {
                // 记录错误日志
                \app\common\library\FrontendLog::error('获取二级下线有效充值人数失败: ' . $e->getMessage());
                return 0;
            }
        }
        return 0;
    }

    /**
     * 获取三级下线有效充值人数（首笔充值大于等于门槛金额的三级下线人数）
     * @param array $data 玩家数据
     * @param bool $dropBound 是否排除掉绑下属
     * @return int 三级下线有效充值人数
     */
    public static function getThridLevelDepositReferralsValidCount($data, $dropBound = false)
    {
        if (isset($data['id'])) {
            try {
                // 从环境变量中读取有效下级门槛金额，默认为25
                $validSubordinatesThreshold = \think\Env::get('app.valid_subordinates_threshold', 25);

                // 构建SQL查询，考虑掉绑设置
                $sql = "SELECT COUNT(DISTINCT pr.player_id) as count
                        FROM player_relations pr
                        JOIN (
                            SELECT player_id, amount
                            FROM deposit_orders
                            WHERE payment_status = 1
                            AND (player_id, id) IN (
                                SELECT player_id, MIN(id)
                                FROM deposit_orders
                                WHERE payment_status = 1
                                GROUP BY player_id
                            )
                            AND amount >= {$validSubordinatesThreshold}
                        ) d ON pr.player_id = d.player_id";

                // 如果需要排除掉绑下属，添加关联查询
                if ($dropBound) {
                    $sql .= " INNER JOIN players p ON pr.player_id = p.id AND p.self_unbind_status = 0";
                }

                $sql .= " WHERE pr.ancestor_id = ?
                        AND pr.relation_level = 3";

                $result = \think\Db::query($sql, [$data['id']]);
                $count = isset($result[0]['count']) ? $result[0]['count'] : 0;

                return $count ?: 0;
            } catch (\Exception $e) {
                // 记录错误日志
                \app\common\library\FrontendLog::error('获取三级下线有效充值人数失败: ' . $e->getMessage());
                return 0;
            }
        }
        return 0;
    }

    /**
     * 获取下线单线总投注金额
     *
     * @param int $playerId 玩家ID
     * @param int $maxLevel 最大关系级别（默认为3，即统计1-3级下线）
     * @param float $minAmount 最低单笔投注金额（默认为0）
     * @param bool $dropNextBound 是否排除掉绑下线
     * @return float 下线总投注金额
     */
    public static function getReferralsLevelBetAmount($playerId, $maxLevel = 3,  $dropNextBound = false)
    {
        try {
            // 记录调用参数
            FrontendLog::info('获取下线投注统计参数: playerId=' . $playerId . ', maxLevel=' . $maxLevel . ', dropNextBound=' . ($dropNextBound ? 'true' : 'false'));

            // 设置时间范围为3年前到现在
            $startTime = date('Y-m-d H:i:s', strtotime('-3 years'));
            $endTime = date('Y-m-d H:i:s');

            // 使用getLevelSubordinates方法获取指定级别的下线列表
            $subordinates = self::getLevelSubordinates($playerId, $startTime, $endTime, $maxLevel, $dropNextBound);

            if (empty($subordinates)) {
                FrontendLog::info('玩家ID=' . $playerId . '没有' . $maxLevel . '级下线' . ($dropNextBound ? '(排除掉绑)' : ''));
                return 0;
            }

            // 获取下线ID列表
            $subordinateIds = array_column($subordinates, 'id');

            // 查询这些下线的总投注金额
            $totalBet = \think\Db::table('players')
                ->whereIn('id', $subordinateIds)
                ->sum('total_bet');

            // 记录查询结果
            FrontendLog::info('玩家ID=' . $playerId . '的' . $maxLevel . '级下线总投注金额: ' . $totalBet);

            // 返回总投注金额
            return floatval($totalBet ?: 0);
        } catch (\Exception $e) {
            FrontendLog::error('获取下线投注统计失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return 0;
        }
    }

    /**
     * 获取下线单线总充值金额
     *
     * @param int $playerId 玩家ID
     * @param int $maxLevel 最大关系级别（默认为3，即统计1-3级下线）
     * @param float $minAmount 最低单笔充值金额（默认为0）
     * @param bool $dropNextBound 是否排除掉绑下线
     * @return float 下线总充值金额
     */
    public static function getReferralsLevelDepositAmount($playerId, $maxLevel = 3, $minAmount = 0, $dropNextBound = false)
    {
        try {
            // 记录调用参数
            FrontendLog::info('获取下线充值统计参数: playerId=' . $playerId . ', maxLevel=' . $maxLevel . ', minAmount=' . $minAmount . ', dropNextBound=' . ($dropNextBound ? 'true' : 'false'));

            // 设置时间范围为3年前到现在
            $startTime = date('Y-m-d H:i:s', strtotime('-3 years'));
            $endTime = date('Y-m-d H:i:s');

            // 使用getSubordinates方法获取下线列表
            $subordinates = self::getLevelSubordinates($playerId, $startTime, $endTime, $maxLevel, $dropNextBound);

            if (empty($subordinates)) {
                FrontendLog::info('玩家ID=' . $playerId . '没有' . $maxLevel . '级以内的下线' . ($dropNextBound ? '(排除掉绑)' : ''));
                return 0;
            }

            // 获取下线ID列表
            $subordinateIds = array_column($subordinates, 'id');

            // 查询这些下线的充值总额
            $sql = "SELECT SUM(amount) as total_amount
                    FROM deposit_orders
                    WHERE player_id IN (" . implode(',', $subordinateIds) . ")
                    AND payment_status = 1
                    AND amount >= ?";

            $params = [$minAmount];
            // FrontendLog::info('下线充值统计SQL: ' . $sql . ', 参数: ' . json_encode($params));

            $result = \think\Db::query($sql, $params);

            // 记录查询结果
            // FrontendLog::info('下线充值统计结果: ' . json_encode($result));

            // 返回总充值金额，确保结果存在且不为null
            if (isset($result[0]) && isset($result[0]['total_amount']) && $result[0]['total_amount'] !== null) {
                return floatval($result[0]['total_amount']);
            } else {
                FrontendLog::info('下线充值统计结果为空或格式不正确，可能是下线没有充值记录');
                return 0;
            }
        } catch (\Exception $e) {
            FrontendLog::error('获取下线充值统计失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return 0;
        }
    }
    /**
     * 获取下线总充值金额
     *
     * @param int $playerId 玩家ID
     * @param int $maxLevel 最大关系级别（默认为3，即统计1-3级下线）
     * @param float $minAmount 最低单笔充值金额（默认为0）
     * @param bool $dropNextBound 是否排除掉绑下线
     * @return float 下线总充值金额
     */
    public static function getReferralsDepositAmount($playerId, $maxLevel = 3, $minAmount = 0, $dropNextBound = false)
    {
        try {
            // 记录调用参数
            FrontendLog::info('获取下线充值统计参数: playerId=' . $playerId . ', maxLevel=' . $maxLevel . ', minAmount=' . $minAmount . ', dropNextBound=' . ($dropNextBound ? 'true' : 'false'));

            // 设置时间范围为3年前到现在
            $startTime = date('Y-m-d H:i:s', strtotime('-3 years'));
            $endTime = date('Y-m-d H:i:s');

            // 使用getSubordinates方法获取下线列表
            $subordinates = self::getSubordinates($playerId, $startTime, $endTime, $maxLevel, $dropNextBound);

            if (empty($subordinates)) {
                FrontendLog::info('玩家ID=' . $playerId . '没有' . $maxLevel . '级以内的下线' . ($dropNextBound ? '(排除掉绑)' : ''));
                return 0;
            }

            // 获取下线ID列表
            $subordinateIds = array_column($subordinates, 'id');

            // 查询这些下线的充值总额
            $sql = "SELECT SUM(amount) as total_amount
                    FROM deposit_orders
                    WHERE player_id IN (" . implode(',', $subordinateIds) . ")
                    AND payment_status = 1
                    AND amount >= ?";

            $params = [$minAmount];
            // FrontendLog::info('下线充值统计SQL: ' . $sql . ', 参数: ' . json_encode($params));

            $result = \think\Db::query($sql, $params);

            // 记录查询结果
            // FrontendLog::info('下线充值统计结果: ' . json_encode($result));

            // 返回总充值金额，确保结果存在且不为null
            if (isset($result[0]) && isset($result[0]['total_amount']) && $result[0]['total_amount'] !== null) {
                return floatval($result[0]['total_amount']);
            } else {
                FrontendLog::info('下线充值统计结果为空或格式不正确，可能是下线没有充值记录');
                return 0;
            }
        } catch (\Exception $e) {
            FrontendLog::error('获取下线充值统计失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return 0;
        }
    }

    /**
     * 获取下属数据
     * @param int $playerId 玩家ID
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @param int $maxLevel 最大关系级别（默认为3，即统计1-3级下线）
     * @return array 下属数据列表
     */
    public static function getLevelSubordinates($playerId, $startTime, $endTime, $maxLevel = 3, $dropNextBound = false)
    {
        // 使用player_relations表获取所有三级下属的玩家ID
        $subordinateIds = \think\Db::table('player_relations')
            ->where('ancestor_id', $playerId)
            ->where('relation_level', $maxLevel)
            ->column('player_id');

        // 如果没有下属，返回空数组
        if (empty($subordinateIds)) {
            return [];
        }

        // 使用ThinkPHP的数据库查询构建器获取玩家详细信息
        $db = \think\Db::table('players');

        // 查询下属玩家的详细信息
        $query = $db->alias('p')
            ->field([
                'p.id',
                'p.vip_level',
                'p.created_at as register_time',
                'p.total_bet as total_bet_amount',
                'p.total_deposit as total_deposit_amount'
            ])
            ->whereIn('p.id', $subordinateIds);
        // 如果需要排除掉绑状态的下属，添加筛选条件
        if ($dropNextBound) {
            $query->where('p.self_unbind_status', 0);
        }
        // 添加时间范围筛选
        // 查询在指定时间范围内注册的玩家
        if (!empty($startTime) && !empty($endTime)) {
            $query->where('p.created_at', 'between', [$startTime, $endTime]);
        }

        // 执行查询并按照注册时间降序排序
        $subordinates = $query->order('p.created_at', 'desc') // 按照注册时间降序，新的在最前面
            ->select();
            FrontendLog::info('查询下属数据SQL：' . $db->getLastSql().'   $subordinateIds='.count($subordinateIds));
        return $subordinates;
    }

    /**
     * 获取下属数据
     * @param int $playerId 玩家ID
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @param int $maxLevel 最大关系级别（默认为3，即统计1-3级下线）
     * @return array 下属数据列表
     */
    public static function getSubordinates($playerId, $startTime, $endTime, $maxLevel = 3, $dropNextBound = false)
    {
        // 使用player_relations表获取所有三级下属的玩家ID
        $subordinateIds = \think\Db::table('player_relations')
            ->where('ancestor_id', $playerId)
            ->where('relation_level', '<=', $maxLevel)
            ->column('player_id');

        // 如果没有下属，返回空数组
        if (empty($subordinateIds)) {
            return [];
        }

        // 使用ThinkPHP的数据库查询构建器获取玩家详细信息
        $db = \think\Db::table('players');

        // 查询下属玩家的详细信息
        $query = $db->alias('p')
            ->field([
                'p.id',
                'p.vip_level',
                'p.created_at as register_time',
                'p.total_bet as total_bet_amount',
                'p.total_deposit as total_deposit_amount'
            ])
            ->whereIn('p.id', $subordinateIds);
        // 如果需要排除掉绑状态的下属，添加筛选条件
        if ($dropNextBound) {
            $query->where('p.self_unbind_status', 0);
        }
        // 添加时间范围筛选
        // 查询在指定时间范围内注册的玩家
        if (!empty($startTime) && !empty($endTime)) {
            $query->where('p.created_at', 'between', [$startTime, $endTime]);
        }

        // 执行查询并按照注册时间降序排序
        $subordinates = $query->order('p.created_at', 'desc') // 按照注册时间降序，新的在最前面
            ->select();
            FrontendLog::info('查询下属数据SQL：' . $db->getLastSql().'   $subordinateIds='.count($subordinateIds));
        return $subordinates;
    }

    /**
     * 检查登录状态并处理重定向
     *
     * @param string $redirectRoute 重定向路由，默认为空（首页）
     * @return bool 如果需要重定向返回true，否则返回false
     */
    public static function checkLoginAndRedirect($redirectRoute = '')
    {
        // 检查登录状态
        $loginStatus = self::checkLoginStatus();

        // 如果未登录
        if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
            // 设置提示信息，将在首页显示
            \think\Session::set('toast_message', __('Please login first'));
            \think\Session::set('toast_type', 'warning');

            // 构建重定向URL
            $redirectUrl = empty($redirectRoute) ? '/' : $redirectRoute;
            $redirectUrl = url($redirectUrl, ['login' => 1, 't' => time()]);
            \app\common\library\FrontendLog::info('未登录用户尝试访问需要登录的页面，重定向到: ' . $redirectUrl);

            // 使用header函数进行重定向
            header('Location: ' . $redirectUrl);
            exit;
        }

        return false;
    }

    /**
     * 通过推荐人ID向上查询三级获取团队RTP值
     * @param int $referrerId 直接推荐人ID
     * @return array|null 包含team_rtp和level的数组，未找到则返回null
     */
    private static function getAncestorTeamRtpByReferrerId($referrerId)
    {
        // 首先查询直接推荐人的team_rtp
        $referrer = \think\Db::table('players')
            ->where('id', $referrerId)
            ->field('id, team_rtp, referrer_id')
            ->find();

        if ($referrer && $referrer['team_rtp'] > 0) {
            return ['team_rtp' => $referrer['team_rtp'], 'level' => 1];
        }

        // 如果直接推荐人没有设置team_rtp或team_rtp为0，查询二级推荐人
        if ($referrer && !empty($referrer['referrer_id'])) {
            $secondLevelReferrer = \think\Db::table('players')
                ->where('id', $referrer['referrer_id'])
                ->field('id, team_rtp, referrer_id')
                ->find();

            if ($secondLevelReferrer && $secondLevelReferrer['team_rtp'] > 0) {
                return ['team_rtp' => $secondLevelReferrer['team_rtp'], 'level' => 2];
            }

            // 如果二级推荐人没有设置team_rtp或team_rtp为0，查询三级推荐人
            if ($secondLevelReferrer && !empty($secondLevelReferrer['referrer_id'])) {
                $thirdLevelReferrer = \think\Db::table('players')
                    ->where('id', $secondLevelReferrer['referrer_id'])
                    ->field('id, team_rtp')
                    ->find();

                if ($thirdLevelReferrer && $thirdLevelReferrer['team_rtp'] > 0) {
                    return ['team_rtp' => $thirdLevelReferrer['team_rtp'], 'level' => 3];
                }
            }
        }

        // 如果三级内都没有找到有效的team_rtp，返回null
        return null;
    }

    /**
     * 获取上级玩家的掉绑设置，最多查询三级
     * @param int $playerId 玩家ID
     * @return array|null 包含掉绑设置和level的数组，未找到则返回null
     */
    private static function getAncestorUnbindSettings($playerId)
    {
        // 查询1：从player_relations表获取玩家的三级上级
        $ancestors = \think\Db::table('player_relations')
            ->where('player_id', $playerId)
            ->where('relation_level', '<=', 3)
            ->order('relation_level', 'asc')
            ->column('ancestor_id', 'relation_level');

        if (empty($ancestors)) {
            return null;
        }

        // 查询2：按照层级顺序检查上级是否有掉绑设置且sync_next=1
        foreach ($ancestors as $level => $ancestorId) {
            $settings = \think\Db::table('player_unbind_settings')
                ->where('player_id', $ancestorId)
                ->where('sync_next', 1)
                ->find();

            if ($settings) {
                $settings['level'] = $level;
                return $settings;
            }
        }

        return null;
    }

    /**
     * 获取上级玩家的佣金配置，最多查询三级
     * @param int $playerId 玩家ID
     * @return array|null 包含佣金配置和level的数组，未找到则返回null
     */
    private static function getAncestorCommissionConfig($playerId)
    {
        // 查询1：从player_relations表获取玩家的三级上级
        $ancestors = \think\Db::table('player_relations')
            ->where('player_id', $playerId)
            ->where('relation_level', '<=', 3)
            ->order('relation_level', 'asc')
            ->column('ancestor_id', 'relation_level');

        if (empty($ancestors)) {
            return null;
        }

        // 查询2：按照层级顺序检查上级是否有佣金配置且sync_next=1
        foreach ($ancestors as $level => $ancestorId) {
            $config = \think\Db::table('player_commission_configs')
                ->where('player_id', $ancestorId)
                ->where('sync_next', 1)
                ->find();

            if ($config) {
                $config['level'] = $level;
                return $config;
            }
        }

        return null;
    }
}
