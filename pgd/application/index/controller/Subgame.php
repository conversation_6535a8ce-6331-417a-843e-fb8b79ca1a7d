<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\index\model\GameProvider;
use app\index\model\GameRooms;
use app\common\library\FrontendLog;

class Subgame extends Frontend
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function _initialize()
    {
        parent::_initialize();
        // 移除HTML标签
        $this->request->filter('trim,strip_tags,htmlspecialchars');
    }

    /**
     * 显示子游戏页面
     */
    public function index()
    {
        // 获取分类参数
        $category = $this->request->param('category', 'slots');
        $page = $this->request->param('page', 1, 'intval');
        $search = $this->request->param('search', '');
        // 获取横向分类参数
        $hcategory = $this->request->param('hcategory', '');

        // 默认每页显示18个游戏
        $limit = 18;
        $offset = ($page - 1) * $limit;

        try {
            // 验证玩家登录状态
            $loginStatus = \app\index\model\Player::checkLoginStatus();
            $loggedIn = isset($loginStatus['code']) && $loginStatus['code'] === 1;

            // 将登录状态传递给视图
            // $this->view->assign('isLoggedIn', $loggedIn);
            if ($loggedIn && isset($loginStatus['data'])) {
                $this->view->assign('player', $loginStatus['data']);
            } else {
                // 未登录时设置默认值
                $this->view->assign('player', ['balance' => '0.00']);
            }

            // 获取最低投注金额并传递给视图
            $betMinAmount = config('bet.min_amount') ?: '0.5';
            $this->view->assign('betMinAmount', $betMinAmount);

            // 使用 GameRooms 模型的 getSubgameData 方法获取游戏数据
            $gameData = GameRooms::getSubgameData($category, $hcategory, $page, $limit, $search);

            // 生成分页导航数据
            $totalPages = $gameData['total_pages'];
            $currentPage = $gameData['current_page'];
            $pageNavigation = $this->generatePageNavigation($currentPage, $totalPages);

            // 将分页导航数据添加到游戏数据中
            $gameData['page_navigation'] = $pageNavigation;

            // 获取所有激活的游戏提供商
            $providers = GameProvider::getActiveProviders();

            // 添加Slots分类
            $categories = [
                [
                    'code' => 'slots',
                    'name' => 'Slots',
                    'icon' => '<img src="/assets/img/frontend/game_providers/Slots.png" alt="Slots">'
                ]
            ];

            // 添加其他提供商分类
            foreach ($providers as $provider) {
                // 删除提供商名称中的"仿"字和空格
                $providerName = trim(str_replace('仿', '', $provider['name']));

                $categories[] = [
                    'code' => $provider['name'], // 保持code不变，确保与数据库匹配
                    'name' => $providerName,     // 显示名称去除"仿"字
                    'icon' => '<img src="/assets/img/frontend/game_providers/' . $providerName . '.png" alt="' . $providerName . '">'
                ];
            }

            // 获取横向分类标签
            $horizontalCategories = [
                [
                    'code' => 'popular',
                    'name' => 'Popular',
                    'active' => false
                ],
                [
                    'code' => 'recente',
                    'name' => 'Recente',
                    'active' => false
                ],
                [
                    'code' => 'favoritos',
                    'name' => 'Favoritos',
                    'active' => false
                ]
            ];

            // 设置当前活动的横向分类
            foreach ($horizontalCategories as &$cat) {
                // 如果有指定横向分类，则激活对应的横向分类
                if (!empty($hcategory) && $cat['code'] === $hcategory) {
                    $cat['active'] = true;
                }
                // 如果没有指定横向分类，则默认激活第一个
                else if (empty($hcategory) && $cat === reset($horizontalCategories)) {
                    $cat['active'] = true;
                }
            }

            // 分配变量到视图
            $this->view->assign('gameData', $gameData);
            $this->view->assign('categories', $categories);
            $this->view->assign('horizontalCategories', $horizontalCategories);
            $this->view->assign('currentCategory', $category);
            $this->view->assign('search', $search);

        } catch (\Exception $e) {
            FrontendLog::error(__('get_game_data_failed') . ': ' . $e->getMessage());
        }

        return $this->view->fetch();
    }

    /**
     * 生成分页导航数据
     *
     * 规则：
     * 1. 每次最多显示5页
     * 2. 如果总页数大于5页，则显示省略号
     * 3. 如果当前页小于等于3，显示前5页，如：1,2,3,4,5
     * 4. 如果当前页大于3且距离最后一页大于2，显示当前页前后各2页，如：3,4,5,6,7
     * 5. 如果当前页距离最后一页小于等于2，显示最后5页，如：11,12,13,14,15
     *
     * @param int $currentPage 当前页码
     * @param int $totalPages 总页数
     * @return array 分页导航数据
     */
    protected function generatePageNavigation($currentPage, $totalPages)
    {
        // 如果总页数小于等于5，直接显示所有页码
        if ($totalPages <= 5) {
            $pages = range(1, $totalPages);
            return [
                'pages' => $pages,
                'show_first_ellipsis' => false,
                'show_last_ellipsis' => false
            ];
        }

        // 如果当前页小于等于3，显示前5页
        if ($currentPage <= 3) {
            $pages = range(1, 5);
            return [
                'pages' => $pages,
                'show_first_ellipsis' => false,
                'show_last_ellipsis' => true
            ];
        }

        // 如果当前页距离最后一页小于等于2，显示最后5页
        if ($currentPage >= $totalPages - 2) {
            $pages = range($totalPages - 4, $totalPages);
            return [
                'pages' => $pages,
                'show_first_ellipsis' => true,
                'show_last_ellipsis' => false
            ];
        }

        // 其他情况，显示当前页前后各2页
        $pages = range($currentPage - 2, $currentPage + 2);
        return [
            'pages' => $pages,
            'show_first_ellipsis' => true,
            'show_last_ellipsis' => true
        ];
    }

    /**
     * AJAX加载更多游戏
     */
    public function loadMoreGames()
    {
        $category = $this->request->param('category', 'slots');
        $hcategory = $this->request->param('hcategory', '');
        $page = $this->request->param('page', 2, 'intval');
        $limit = $this->request->param('limit', 18, 'intval');
        $search = $this->request->param('search', '');

        try {
            // 使用 GameRooms 模型的 getSubgameData 方法获取游戏数据
            $result = GameRooms::getSubgameData($category, $hcategory, $page, $limit, $search);

            // 生成分页导航数据
            $totalPages = $result['total_pages'];
            $currentPage = $result['current_page'];
            $pageNavigation = $this->generatePageNavigation($currentPage, $totalPages);

            // 构造返回数据
            $data = [
                'games' => $result['games'],
                'has_more' => $result['has_more'],
                'current_page' => $result['current_page'],
                'total_pages' => $result['total_pages'],
                'total_games' => $result['total_games'],
                'page_navigation' => $pageNavigation
            ];

            return json(['code' => 1, 'msg' => __('get_data_success'), 'data' => $data]);
        } catch (\Exception $e) {
            FrontendLog::error(__('load_more_games_failed') . ': ' . $e->getMessage());
            return json(['code' => 0, 'msg' => __('get_data_failed') . ': ' . $e->getMessage()]);
        }
    }
}
