<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\index\model\GameProvider;
use app\index\model\GameRooms;
use think\Config;
use think\Db;
use think\Lang;

/**
 * 搜索控制器
 */
class Search extends Frontend
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];
    protected $layout = '';

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 搜索页面
     */
    public function index()
    {
        // 获取初始热门游戏数据（前9个）
        $popularGames = $this->getPopularGames(12);
        $this->view->assign('popular_games', $popularGames);

        // 记录日志
        // \app\common\library\FrontendLog::info('搜索页面加载: 初始热门游戏数=' . count($popularGames));

        return $this->view->fetch();
    }

    /**
     * 搜索游戏接口
     */
    public function searchGames()
    {
        $query = $this->request->param('query', '');
        $offset = $this->request->param('offset', 0, 'intval');
        $limit = $this->request->param('limit', 12, 'intval');

        // 记录请求参数
        \app\common\library\FrontendLog::info("搜索游戏请求参数: query={$query}, offset={$offset}, limit={$limit}");

        // 验证搜索内容不为空
        if (empty($query)) {
            return json(['code' => 0, 'msg' => __('Search query cannot be empty'), 'data' => []]);
        }

        try {
            // 使用 GameRooms 模型进行搜索
            $games = GameRooms::searchGames($query);

            // 获取总数
            $totalCount = count($games);
            // 分页查询
            $games = array_slice($games, $offset, $limit);
            // 记录日志
            \app\common\library\FrontendLog::info('搜索游戏: '. ', offset=' . $offset . ', limit=' . $limit .
                ', 返回游戏数=' . count($games) . ', 总数=' . $totalCount);

            // 格式化游戏数据
            $formattedGames = [];
            foreach ($games as $game) {
                $formattedGames[] = GameRooms::formatGameData($game);
            }

            // 判断是否还有更多游戏
            $hasMore = $totalCount > ($offset + $limit);

            return json([
                'code' => 1,
                'msg' => __('Search results'),
                'data' => $formattedGames,
                'has_more' => $hasMore,
                'total' => $totalCount
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            \app\common\library\FrontendLog::error('搜索游戏失败: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => $e->getMessage(), 'data' => [], 'has_more' => false]);
        }
    }

    /**
     * 获取更多热门游戏接口
     */
    public function getMorePopularGames()
    {
        $offset = $this->request->param('offset', 0, 'intval');
        $limit = $this->request->param('limit', 12, 'intval');

        try {
            // 获取当前页的游戏
            $currentGames = GameRooms::getPopularGames($offset, $limit);

            // 如果没有游戏，返回空数组
            if (empty($currentGames)) {
                return json(['code' => 1, 'msg' => __('No more games'), 'data' => [], 'has_more' => false]);
            }

            // 获取下一页的游戏，用于判断是否还有更多
            $nextGames = GameRooms::getPopularGames($offset + $limit, 1);

            // 判断是否还有更多游戏
            $hasMore = !empty($nextGames);

            // 记录日志
            \app\common\library\FrontendLog::info('获取更多热门游戏: offset=' . $offset . ', limit=' . $limit .
                ', 返回游戏数=' . count($currentGames) . ', 是否还有更多=' . ($hasMore ? 'true' : 'false'));

            return json([
                'code' => 1,
                'msg' => __('Popular games'),
                'data' => $currentGames,
                'has_more' => $hasMore
            ]);
        } catch (\Exception $e) {
            // 记录错误日志
            \app\common\library\FrontendLog::error('获取更多热门游戏失败: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => $e->getMessage(), 'data' => [], 'has_more' => false]);
        }
    }

    /**
     * 获取热门游戏
     * 使用GameProvider模型中的getPopularGames方法
     */
    private function getPopularGames($limit = 12)
    {
        try {
            // 使用GameRooms模型中的getPopularGames方法获取热门游戏
            $popularGames = GameRooms::getPopularGames(0, $limit);

            // 记录日志
            \app\common\library\FrontendLog::info('搜索页面获取热门游戏: ' . count($popularGames) . '个');

            return $popularGames;
        } catch (\Exception $e) {
            // 记录错误日志
            \app\common\library\FrontendLog::error('搜索页面获取热门游戏失败: ' . $e->getMessage());
            return [];
        }
    }
}