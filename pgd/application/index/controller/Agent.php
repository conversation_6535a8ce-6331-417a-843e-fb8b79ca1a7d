<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\library\FrontendLog;
use app\index\model\Player;

class Agent extends Frontend
{
    protected $layout = '';

    public function _initialize()
    {
        parent::_initialize();

        // 加载Agent模块语言包
        // \think\Lang::load(APP_PATH . 'index/lang/' . $this->request->langset() . '/agent.php');
    }

    /**
     * 代理主页
     */
    public function index()
    {
        // 登录检查 - 使用Player模型中的统一方法
        if (Player::checkLoginAndRedirect()) {
            return;
        }

        // 获取登录状态
        $loginStatus = Player::checkLoginStatus();

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $relation1Count = Player::getDirectReferralsCountAttr($playerData);

        $balance = $playerData["balance"];
        $this->view->assign('balance', $balance);
        // 获取当前域名
        $domain = $this->request->domain();
        $this->view->assign('current_domain', $domain);

        $id = $playerData["id"];
        $ptype = "id=";
        $link_url = $domain."?".$ptype.$id;

        $this->view->assign('link_url', $link_url);
        $this->view->assign('id_code', $id);
        $this->view->assign('r_count', $relation1Count);
        $this->view->assign('active_tab', 'link');
        return $this->view->fetch();
    }

    /**
     * 我的数据
     */
    public function menus()
    {
        // 登录检查 - 使用Player模型中的统一方法
        if (Player::checkLoginAndRedirect()) {
            return;
        }

        // 获取登录状态
        $loginStatus = Player::checkLoginStatus();

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id']; // 获取玩家ID
        // 检查是否需要排除掉绑下属
        $dropBound = isset($playerData['no_unbind_subordinate']) && $playerData['no_unbind_subordinate'] == 0;

        // 获取各级下线人数，考虑掉绑设置
        $rCount1 = Player::getDirectReferralsCountAttr($playerData, $dropBound);
        $rCount2 = Player::getSecondLevelReferralsCountAttr($playerData, $dropBound);
        $rCount3 = Player::getThirdLevelReferralsCountAttr($playerData, $dropBound);
        $totalCount = $rCount1 + $rCount2 + $rCount3;

        // 获取各级下线充值人数，考虑掉绑设置
        $dCount1 = Player::getDirectDepositReferralsCountAttr($playerData, $dropBound);
        $dCount2 = Player::getSecondLevelDepositReferralsCountAttr($playerData, $dropBound);
        $dCount3 = Player::getThirdLevelDepositReferralsCountAttr($playerData, $dropBound);
        $totalDCount = $dCount1 + $dCount2 + $dCount3;

        // 获取各级下线有效充值人数，考虑掉绑设置
        $dvCount1 = Player::getDerectDepositReferralsValidCount($playerData, $dropBound);
        $dvCount2 = Player::getSecondLevelDepositReferralsValidCount($playerData, $dropBound);
        $dvCount3 = Player::getThridLevelDepositReferralsValidCount($playerData, $dropBound);
        $totalDVCount = $dvCount1 + $dvCount2 + $dvCount3;

        // 获取各级下线充值金额，考虑掉绑设置
        $depositAmount1 = Player::getReferralsLevelDepositAmount($playerId, 1, 0, $dropBound);
        $depositAmount2 = Player::getReferralsLevelDepositAmount($playerId, 2, 0, $dropBound);
        $depositAmount3 = Player::getReferralsLevelDepositAmount($playerId, 3, 0, $dropBound);
        $totalDeposit = $depositAmount1 + $depositAmount2 + $depositAmount3;

        // 获取各级下线投注金额，考虑掉绑设置
        $betAmount1 = Player::getReferralsLevelBetAmount($playerId, 1, $dropBound);
        $betAmount2 = Player::getReferralsLevelBetAmount($playerId, 2, $dropBound);
        $betAmount3 = Player::getReferralsLevelBetAmount($playerId, 3, $dropBound);
        $betTotalAmount = $betAmount1 + $betAmount2 + $betAmount3;

        $this->view->assign('active_tab', 'menus');

        // 准备数据统计信息（可以从数据库获取真实数据）
        $stats = [
            'total' => [
                'invited' => $totalCount,
                'depositors' => $totalDCount,
                'valid_depositors' => $totalDVCount,
                'deposit_amount' => $totalDeposit,
                'bet_amount' => $betTotalAmount,
                'commission' => '0,00',
            ],
            'level1' => [
                'invited' => $rCount1,
                'depositors' => $dCount1,
                'valid_depositors' => $dvCount1,
                'deposit_amount' => $depositAmount1,
                'bet_amount' => $betAmount1,
                'commission' => '0,00',
            ],
            'level2' => [
                'invited' => $rCount2,
                'depositors' => $dCount2,
                'valid_depositors' => $dvCount2,
                'deposit_amount' => $depositAmount2,
                'bet_amount' => $betAmount2,
                'commission' => '0,00',
            ],
            'level3' => [
                'invited' => $rCount3,
                'depositors' => $dCount3,
                'valid_depositors' => $dvCount3,
                'deposit_amount' => $depositAmount3,
                'bet_amount' => $betAmount3,
                'commission' => '0,00',
            ],
        ];

        // 构建适合共用组件的数据结构
        $dataSections = [
            [
                'title' => __('section_all'),
                'items' => [
                    ['label' => __('invited_people'), 'value' => $stats['total']['invited'], 'highlight' => false],
                    ['label' => __('depositors'), 'value' => $stats['total']['depositors'], 'highlight' => false],
                    ['label' => __('valid_depositors'), 'value' => $stats['total']['valid_depositors'], 'highlight' => false],
                    ['label' => __('deposit_total'), 'value' => $stats['total']['deposit_amount'], 'highlight' => true],
                    ['label' => __('total_bets'), 'value' => $stats['total']['bet_amount'], 'highlight' => true],
                    ['label' => __('commission'), 'value' => $stats['total']['commission'], 'highlight' => true],
                ]
            ],
            [
                'title' => __('level_1'),
                'items' => [
                    ['label' => __('invited_people'), 'value' => $stats['level1']['invited'], 'highlight' => false],
                    ['label' => __('depositors'), 'value' => $stats['level1']['depositors'], 'highlight' => false],
                    ['label' => __('valid_depositors'), 'value' => $stats['level1']['valid_depositors'], 'highlight' => false],
                    ['label' => __('deposit_total'), 'value' => $stats['level1']['deposit_amount'], 'highlight' => true],
                    ['label' => __('total_bets'), 'value' => $stats['level1']['bet_amount'], 'highlight' => true],
                    ['label' => __('commission'), 'value' => $stats['level1']['commission'], 'highlight' => true],
                ]
            ],
            [
                'title' => __('level_2'),
                'items' => [
                    ['label' => __('invited_people'), 'value' => $stats['level2']['invited'], 'highlight' => false],
                    ['label' => __('depositors'), 'value' => $stats['level2']['depositors'], 'highlight' => false],
                    ['label' => __('valid_depositors'), 'value' => $stats['level2']['valid_depositors'], 'highlight' => false],
                    ['label' => __('deposit_total'), 'value' => $stats['level2']['deposit_amount'], 'highlight' => true],
                    ['label' => __('total_bets'), 'value' => $stats['level2']['bet_amount'], 'highlight' => true],
                    ['label' => __('commission'), 'value' => $stats['level2']['commission'], 'highlight' => true],
                ]
            ],
            [
                'title' => __('level_3'),
                'items' => [
                    ['label' => __('invited_people'), 'value' => $stats['level3']['invited'], 'highlight' => false],
                    ['label' => __('depositors'), 'value' => $stats['level3']['depositors'], 'highlight' => false],
                    ['label' => __('valid_depositors'), 'value' => $stats['level3']['valid_depositors'], 'highlight' => false],
                    ['label' => __('deposit_total'), 'value' => $stats['level3']['deposit_amount'], 'highlight' => true],
                    ['label' => __('total_bets'), 'value' => $stats['level3']['bet_amount'], 'highlight' => true],
                    ['label' => __('commission'), 'value' => $stats['level3']['commission'], 'highlight' => true],
                ]
            ]
        ];

        $this->view->assign('dataSections', $dataSections);
        $this->view->assign('stats', $stats); // 仍然保留原始数据，以便兼容可能依赖它的其他部分
        return $this->view->fetch();
    }

    /**
     * 所有数据 - 获取当前玩家下属的信息
     */
    public function all_data()
    {
        // 验证登录状态
        $loginStatus = \app\index\model\Player::checkLoginStatus();
        if ($loginStatus['code'] == 0) {
            $this->redirect('index/login/index');
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 获取日期参数，默认为今天
        $startDate = $this->request->param('start_date', date('Y-m-d'));
        $endDate = $this->request->param('end_date', date('Y-m-d'));

        // 验证日期格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $startDate) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $endDate)) {
            $startDate = date('Y-m-d');
            $endDate = date('Y-m-d');
        }

        // 确保开始日期不大于结束日期
        if (strtotime($startDate) > strtotime($endDate)) {
            $temp = $startDate;
            $startDate = $endDate;
            $endDate = $temp;
        }

        // 添加时间部分
        $startTime = $startDate . ' 00:00:00';
        $endTime = $endDate . ' 23:59:59';


        // 检查是否需要排除掉绑下属
        $dropBound = isset($playerData['no_unbind_subordinate']) && $playerData['no_unbind_subordinate'] == 0;

        // 使用Player模型中封装的getSubordinates方法获取下属数据
        $subordinates = \app\index\model\Player::getSubordinates($playerId, $startTime, $endTime, 3, $dropBound);
        
      
        
        // 格式化数据
        foreach ($subordinates as &$sub) {
            // 格式化注册时间
            $sub['register_date'] = date('Y-m-d', strtotime($sub['register_time']));

            // 获取最后登录时间
            $player = \think\Db::table('players')
                ->field(['last_login_time', 'id', 'is_blacklisted', 'balance'])
                ->where('id', $sub['id'])
                ->find();

            // 格式化最后登录时间
            $sub['last_login_date'] = $player && $player['last_login_time'] ?
                date('Y-m-d', strtotime($player['last_login_time'])) : '-';

            // 获取下属个数
            $sub['subordinates_count'] = \think\Db::table('player_relations')
                ->where('ancestor_id', $sub['id'])
                ->where('relation_level', '<=', 3)
                ->count();

            // 获取玩家状态
            $sub['status'] = $player && $player['is_blacklisted'] ? __('inactive') : __('active');
            
            // 添加余额
            $sub['balance'] = $player ? $player['balance'] : 0;
            
            // 从player_relations表获取关系级别
            $relation = \think\Db::table('player_relations')
                ->where('ancestor_id', $playerId)
                ->where('player_id', $sub['id'])
                ->field('relation_level')
                ->find();
            
            $sub['level'] = $relation ? $relation['relation_level'] : 0;
            
            // 添加充值金额（用于统计计算）
            $sub['deposit_amount'] = floatval(str_replace([',', '.'], ['', '.'], $sub['total_deposit_amount']));
        }
        
        // 计算统计数据
        $stats = $this->calculateStats($subordinates);

        // 格式化日期显示
        $formattedStartDate = date('d/m/Y', strtotime($startDate));
        $formattedEndDate = date('d/m/Y', strtotime($endDate));
        $dateRange = $formattedStartDate . ' - ' . $formattedEndDate;
        
        // 格式化数据
        foreach ($subordinates as &$sub) {
            // 格式化金额，保留两位小数
            $sub['total_bet_amount'] = number_format($sub['total_bet_amount'], 2,  ',', '.');
            $sub['total_deposit_amount'] = number_format($sub['total_deposit_amount'], 2,  ',', '.');
        }

        // 将数据传递给视图
        $this->assign('player', $playerData);
        $this->assign('subordinates', $subordinates);
        $this->assign('stats', $stats);
        $this->assign('date_range', $dateRange);
        $this->assign('title', __('tab_all_data'));
        $this->assign('active_tab', 'all_data');
        
        return $this->view->fetch();
    }

    /**
     * 计算统计数据
     * @param array $subordinates 下属列表
     * @return array 统计数据
     */
    private function calculateStats($subordinates)
    {
        // 初始化统计数据
        $stats = [
            'direct_deposit_total' => '0.00',
            'direct_deposit_users' => 0,
            'other_deposit_total' => '0.00',
            'other_deposit_users' => 0,
            'total_deposit' => '0.00',
            'total_deposit_users' => 0,
            'average_deposit' => '0.00',
            // 添加其他可能在模板中使用的统计数据
            'total_registrations' => count($subordinates),
            'total_bet_amount' => '0.00',
            'total_deposit_amount' => '0.00',
            'total_balance' => '0.00',
            'total_deposit_order_count' => 0
        ];
        
        if (empty($subordinates)) {
            return $stats;
        }
        
        // 计算直属下级充值总额和人数
        $directDepositUsers = 0;
        $directDepositTotal = 0;
        
        // 计算其他下级充值总额和人数
        $otherDepositUsers = 0;
        $otherDepositTotal = 0;
        
        // 计算总投注额和总充值额
        $totalBetAmount = 0;
        $totalDepositAmount = 0;
        $totalBalance = 0;
        $totalDepositOrderCount = 0;
        
        foreach ($subordinates as &$sub) {
            // 计算总投注额和总充值额
            $totalBetAmount += floatval(str_replace([',', '.'], ['', '.'], $sub['total_bet_amount']));
            $totalDepositAmount += floatval(str_replace([',', '.'], ['', '.'], $sub['total_deposit_amount']));
            $totalBalance += floatval(str_replace([',', '.'], ['', '.'], $sub['balance'] ?? 0));
            
            // 获取玩家的充值订单数量
            $playerDepositCount = \think\Db::table('deposit_orders')
                ->where('player_id', $sub['id'])
                ->where('payment_status', 1)
                ->count();
            $totalDepositOrderCount += $playerDepositCount;
            
            // 判断是否为直属下级
            if (isset($sub['level']) && $sub['level'] == 1) {
                if (floatval($sub['deposit_amount']) > 0) {
                    $directDepositUsers++;
                    $directDepositTotal += floatval($sub['deposit_amount']);
                }
            } else {
                if (floatval($sub['deposit_amount']) > 0) {
                    $otherDepositUsers++;
                    $otherDepositTotal += floatval($sub['deposit_amount']);
                }
            }
        }
        
        // 计算总充值金额和总充值人数
        $totalDepositUsers = $directDepositUsers + $otherDepositUsers;
        $totalDeposit = $directDepositTotal + $otherDepositTotal;
        
        // 计算平均充值金额
        $averageDeposit = $totalDepositUsers > 0 ? $totalDeposit / $totalDepositUsers : 0;
        
        // 格式化金额，保留两位小数
        $stats['direct_deposit_total'] = number_format($directDepositTotal, 2, ',', '.');
        $stats['other_deposit_total'] = number_format($otherDepositTotal, 2, ',', '.');
        $stats['total_deposit'] = number_format($totalDeposit, 2, ',', '.');
        $stats['average_deposit'] = number_format($averageDeposit, 2, ',', '.');
        
        // 设置人数
        $stats['direct_deposit_users'] = $directDepositUsers;
        $stats['other_deposit_users'] = $otherDepositUsers;
        $stats['total_deposit_users'] = $totalDepositUsers;
        
        // 设置其他统计数据
        $stats['total_bet_amount'] = number_format($totalBetAmount, 2, ',', '.');
        $stats['total_deposit_amount'] = number_format($totalDepositAmount, 2, ',', '.');
        $stats['total_balance'] = number_format($totalBalance, 2, ',', '.');
        $stats['total_deposit_order_count'] = $totalDepositOrderCount;
        
        return $stats;
    }

    /**
     * 通过日期范围获取下属数据（AJAX方法）
     */
    public function fetchData()
    {
        // 登录检查
        $loginStatus = Player::checkLoginStatus();
        if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
            \app\common\library\FrontendLog::info('未登录用户尝试访问获取下属数据接口: IP=' . $this->request->ip());
            return json(['code' => 0, 'msg' => __('Please login first')]);
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 获取日期参数
        $startDate = $this->request->param('start_date');
        $endDate = $this->request->param('end_date');

        // 如果没有提供日期参数，使用今天的日期
        if (empty($startDate) || empty($endDate)) {
            $startDate = date('Y-m-d');
            $endDate = date('Y-m-d');
        }

        // 验证日期格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $startDate) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $endDate)) {
            return json(['code' => 101, 'msg' => __('Invalid date format')]);
        }

        // 确保开始日期不大于结束日期
        if (strtotime($startDate) > strtotime($endDate)) {
            $temp = $startDate;
            $startDate = $endDate;
            $endDate = $temp;
        }

        // 添加时间部分
        $startTime = $startDate . ' 00:00:00';
        $endTime = $endDate . ' 23:59:59';

        // 检查是否需要排除掉绑下属
        $dropBound = isset($playerData['no_unbind_subordinate']) && $playerData['no_unbind_subordinate'] == 0;

        // 从数据库获取当前玩家的下属信息
        $subordinates = \app\index\model\Player::getSubordinates($playerId, $startTime, $endTime, 3, $dropBound);

        // 如果没有数据，返回空结果
        if (empty($subordinates)) {
            return json(['code' => 102, 'msg' => __('No data found'), 'data' => []]);
        }

        // 格式化数据
        foreach ($subordinates as &$sub) {
            // 格式化注册时间
            $sub['register_date'] = date('Y-m-d', strtotime($sub['register_time']));
        }

        // 计算统计数据
        $directDepositTotal = 0;
        $directDepositUsers = 0;
        $otherDepositTotal = 0;
        $otherDepositUsers = 0;
        $totalDeposit = 0;
        $totalDepositUsers = 0;

        // 直接下属（一级）
        $directSubordinates = \think\Db::table('player_relations')
            ->where('ancestor_id', $playerId)
            ->where('relation_level', 1)
            ->column('player_id');  

        FrontendLog::log('直接下属: ' . implode(',', $directSubordinates));
        // 计算直接下属的充值总额和人数
        if (!empty($directSubordinates)) {
            foreach ($subordinates as &$sub) {
                if (in_array($sub['id'], $directSubordinates)) {
                    $directDepositTotal += floatval(str_replace(',', '', $sub['total_deposit_amount']));
                    $directDepositUsers++;
                    FrontendLog::log('直接下属充值总额: ' . $directDepositTotal."  => ". $sub['total_deposit_amount']);
                } else {
                    $otherDepositTotal += floatval(str_replace(',', '', $sub['total_deposit_amount']));
                    $otherDepositUsers++;

                    FrontendLog::log('其他下属充值总额: ' . $otherDepositTotal."  => ". $sub['total_deposit_amount']);
                }
            }
        }

        // 格式化数据
        foreach ($subordinates as &$sub) {
            // 格式化金额，保留两位小数
            $sub['total_bet_amount'] = number_format($sub['total_bet_amount'], 2,  ',', '.');
            $sub['total_deposit_amount'] = number_format($sub['total_deposit_amount'], 2,  ',', '.');
        }

        // 计算总充值和总人数
        $totalDeposit = $directDepositTotal + $otherDepositTotal;
        $totalDepositUsers = $directDepositUsers + $otherDepositUsers;

        // 计算平均充值
        $averageDeposit = $totalDepositUsers > 0 ? $totalDeposit / $totalDepositUsers : 0;

        // 格式化金额
        $directDepositTotal = number_format($directDepositTotal, 2, ',', '.');
        $otherDepositTotal = number_format($otherDepositTotal, 2,  ',', '.');
        $totalDeposit = number_format($totalDeposit, 2,  ',', '.');
        $averageDeposit = number_format($averageDeposit, 2,  ',', '.');

        // 返回数据
        return json([
            'code' => 1,
            'msg' => __('Success'),
            'data' => [
                'subordinates' => $subordinates,
                'stats' => [
                    'direct_deposit_total' => $directDepositTotal,
                    'direct_deposit_users' => $directDepositUsers,
                    'other_deposit_total' => $otherDepositTotal,
                    'other_deposit_users' => $otherDepositUsers,
                    'total_deposit' => $totalDeposit,
                    'total_deposit_users' => $totalDepositUsers,
                    'average_deposit' => $averageDeposit
                ]
            ]
        ]);
    }

    /**
     * 通过ID搜索下属数据
     */
    public function search()
    {
        // 登录检查
        $loginStatus = Player::checkLoginStatus();
        if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
            return json(['code' => 0, 'msg' => __('Please login first')]);
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 获取搜索ID
        $searchId = $this->request->param('id');

        // 验证ID格式
        if (!is_numeric($searchId) || $searchId <= 0) {
            return json(['code' => 0, 'msg' => __('Invalid ID format')]);
        }

        // 检查是否是下属
        $isSubordinate = \think\Db::table('player_relations')
            ->where('ancestor_id', $playerId)
            ->where('player_id', $searchId)
            ->where('relation_level', '<=', 3)
            ->find();

        if (!$isSubordinate) {
            return json(['code' => 0, 'msg' => __('Player not found or not your subordinate')]);
        }

        // 获取玩家信息
        $player = \think\Db::table('players')
            ->field([
                'id',
                'vip_level',
                'created_at as register_time',
                'total_bet as total_bet_amount',
                'total_deposit as total_deposit_amount'
            ])
            ->where('id', $searchId)
            ->find();

        if (!$player) {
            return json(['code' => 0, 'msg' => __('Player not found')]);
        }

        // 格式化数据
        $player['register_date'] = date('Y-m-d', strtotime($player['register_time']));

        // 获取最后登录时间
        $playerDetail = \think\Db::table('players')
            ->field(['last_login_time', 'id', 'is_blacklisted', 'balance'])
            ->where('id', $searchId)
            ->find();

        // 格式化最后登录时间
        $player['last_login_date'] = $playerDetail && $playerDetail['last_login_time'] ?
            date('Y-m-d', strtotime($playerDetail['last_login_time'])) : '-';

        // 获取下属个数
        $player['subordinates_count'] = \think\Db::table('player_relations')
            ->where('ancestor_id', $searchId)
            ->where('relation_level', '<=', 3)
            ->count();

        // 获取玩家状态
        $player['status'] = $playerDetail && $playerDetail['is_blacklisted'] ? __('inactive') : __('active');

        // 计算统计数据 - 这里只有一个玩家，所以统计数据会比较简单
        $directDepositTotal = 0;
        $directDepositUsers = 0;
        $otherDepositTotal = 0;
        $otherDepositUsers = 0;

        // 为subordinates页面准备的统计数据
        $totalRegistrations = 1; // 只有一个玩家
        $totalBetAmount = floatval(str_replace([',', '.'], ['', '.'], $player['total_bet_amount']));
        $totalDepositAmount = floatval(str_replace([',', '.'], ['', '.'], $player['total_deposit_amount']));
        $totalBalance = $playerDetail ? $playerDetail['balance'] : 0;

        // 检查是否是直接下属
        $isDirectSubordinate = \think\Db::table('player_relations')
            ->where('ancestor_id', $playerId)
            ->where('player_id', $searchId)
            ->where('relation_level', 1)
            ->find();

        if ($isDirectSubordinate) {
            $directDepositTotal = floatval(str_replace(',', '', $player['total_deposit_amount']));
            $directDepositUsers = 1;
            FrontendLog::log('直接下属充值总额: ' . $directDepositTotal."  => ". $player['total_deposit_amount']);
        } else {
            $otherDepositTotal = floatval(str_replace(',', '', $player['total_deposit_amount']));
            $otherDepositUsers = 1;
            FrontendLog::log('直接下属充值总额: ' . $directDepositTotal."  => ". $player['total_deposit_amount']);
        }

        // 计算总充值和总人数
        $totalDeposit = $directDepositTotal + $otherDepositTotal;
        $totalDepositUsers = $directDepositUsers + $otherDepositUsers;

        // 计算平均充值
        $averageDeposit = $totalDepositUsers > 0 ? $totalDeposit / $totalDepositUsers : 0;

        // 格式化金额
        $directDepositTotal = number_format($directDepositTotal, 2, ',', '.');
        $otherDepositTotal = number_format($otherDepositTotal, 2, ',', '.');
        $totalDeposit = number_format($totalDeposit, 2, ',', '.');
        $averageDeposit = number_format($averageDeposit, 2, ',', '.');

        // 格式化金额
        $totalBetAmount = number_format($totalBetAmount, 2, ',', '.');
        $totalDepositAmount = number_format($totalDepositAmount, 2, ',', '.');
        $totalBalance = number_format($totalBalance, 2, ',', '.');
        // 格式化金额，保留两位小数
        $player['total_bet_amount'] = number_format($player['total_bet_amount'], 2, ',', '.');
        $player['total_deposit_amount'] = number_format($player['total_deposit_amount'], 2, ',', '.');

        // 返回数据
        return json([
            'code' => 1,
            'msg' => __('Success'),
            'data' => [
                'subordinates' => [$player],
                'stats' => [
                    // all_data页面需要的统计数据
                    'direct_deposit_total' => $directDepositTotal,
                    'direct_deposit_users' => $directDepositUsers,
                    'other_deposit_total' => $otherDepositTotal,
                    'other_deposit_users' => $otherDepositUsers,
                    'total_deposit' => $totalDeposit,
                    'total_deposit_users' => $totalDepositUsers,
                    'average_deposit' => $averageDeposit,

                    // subordinates页面需要的统计数据
                    'total_registrations' => $totalRegistrations,
                    'total_bet_amount' => $totalBetAmount,
                    'total_deposit_amount' => $totalDepositAmount,
                    'total_balance' => $totalBalance
                ]
            ]
        ]);
    }

    /**
     * 下属资料
     */
    public function subordinates()
    {
        // 登录检查 - 使用Player模型中的统一方法
        if (Player::checkLoginAndRedirect()) {
            return;
        }

        // 获取登录状态
        $loginStatus = Player::checkLoginStatus();

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 获取日期参数，默认为今天
        $startDate = $this->request->param('start_date');
        $endDate = $this->request->param('end_date');

        // 如果没有提供日期参数，使用今天的日期
        if (empty($startDate) || empty($endDate)) {
            $startDate = date('Y-m-d');
            $endDate = date('Y-m-d');
        }

        // 验证日期格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $startDate) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $endDate)) {
            $startDate = date('Y-m-d');
            $endDate = date('Y-m-d');
        }

        // 确保开始日期不大于结束日期
        if (strtotime($startDate) > strtotime($endDate)) {
            $temp = $startDate;
            $startDate = $endDate;
            $endDate = $temp;
        }

        // 添加时间部分
        $startTime = $startDate . ' 00:00:00';
        $endTime = $endDate . ' 23:59:59';

        // 检查是否需要排除掉绑下属
        $dropBound = isset($playerData['no_unbind_subordinate']) && $playerData['no_unbind_subordinate'] == 0;

        // 从数据库获取当前玩家的下属信息（所有三级下属）
        $subordinates = \app\index\model\Player::getSubordinates($playerId, $startTime, $endTime, 3, $dropBound);

        $totalDepositOrderCount = 0;
        // 计算统计数据
        $totalRegistrations = count($subordinates);
        $totalBetAmount = 0;
        $totalDepositAmount = 0;
        $totalDepositUsers = 0;
        // 格式化数据
        foreach ($subordinates as &$sub) {
            // 格式化注册时间
            $sub['register_date'] = date('Y-m-d', strtotime($sub['register_time']));

            // 获取最后登录时间
            $player = \think\Db::table('players')
                ->field(['last_login_time', 'id'])
                ->where('id', $sub['id'])
                ->find();

            // 格式化最后登录时间
            $sub['last_login_date'] = $player && $player['last_login_time'] ?
                date('Y-m-d', strtotime($player['last_login_time'])) : '-';

            // 获取下属个数
            $sub['subordinates_count'] = \think\Db::table('player_relations')
                ->where('ancestor_id', $sub['id'])
                ->where('relation_level', '<=', 3)
                ->count();

            // 获取玩家状态
            $sub['status'] = \think\Db::table('players')
                ->field(['is_blacklisted'])
                ->where('id', $sub['id'])
                ->value('is_blacklisted') ? __('inactive') : __('active');

            $playerDepositCount = \think\Db::table('deposit_orders')
                ->where('player_id', $sub['id'])
                ->where('payment_status', 1)
                ->count();
            $totalDepositOrderCount += $playerDepositCount;

            $totalBetAmount += floatval(str_replace([',', '.'], ['', '.'], $sub['total_bet_amount']));
            $totalDepositAmount += floatval(str_replace([',', '.'], ['', '.'], $sub['total_deposit_amount']));
            if($sub['total_deposit_amount'] > 0) $totalDepositUsers++;
        }

        // 格式化日期显示
        $formattedStartDate = date('d/m/Y', strtotime($startDate));
        $formattedEndDate = date('d/m/Y', strtotime($endDate));
        $dateRange = $formattedStartDate . ' - ' . $formattedEndDate;


        // 格式化金额（无论是否有数据都需要格式化）
        $totalBetAmount = number_format($totalBetAmount, 2, ',', '.');
        $totalDepositAmount = number_format($totalDepositAmount, 2, ',', '.');

        // 将数据传递给视图
        $this->view->assign('subordinates', $subordinates);
        $this->view->assign('date_range', $dateRange);
        $this->view->assign('active_tab', 'subordinates');

        // 传递统计数据
        $this->view->assign('total_registrations', $totalRegistrations);
        $this->view->assign('total_deposit_users', $totalDepositUsers);
        $this->view->assign('total_bet_amount', $totalBetAmount);
        $this->view->assign('total_deposit_order_count', $totalDepositOrderCount);
        $this->view->assign('total_deposit_amount', $totalDepositAmount);
        return $this->view->fetch();
    }

    /**
     * 通过日期范围获取下属详细数据（AJAX方法）
     */
    public function fetchSubordinates()
    {
        // 登录检查
        $loginStatus = Player::checkLoginStatus();
        if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
            \app\common\library\FrontendLog::info('未登录用户尝试访问获取下属详细数据接口: IP=' . $this->request->ip());
            return json(['code' => 0, 'msg' => __('Please login first')]);
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 获取日期参数
        $startDate = $this->request->param('start_date');
        $endDate = $this->request->param('end_date');

        // 如果没有提供日期参数，使用今天的日期
        if (empty($startDate) || empty($endDate)) {
            $startDate = date('Y-m-d');
            $endDate = date('Y-m-d');
        }

        // 验证日期格式
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $startDate) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $endDate)) {
            $startDate = date('Y-m-d');
            $endDate = date('Y-m-d');
        }

        // 确保开始日期不大于结束日期
        if (strtotime($startDate) > strtotime($endDate)) {
            $temp = $startDate;
            $startDate = $endDate;
            $endDate = $temp;
        }

        // 添加时间部分
        $startTime = $startDate . ' 00:00:00';
        $endTime = $endDate . ' 23:59:59';

        // 检查是否需要排除掉绑下属
        $dropBound = isset($playerData['no_unbind_subordinate']) && $playerData['no_unbind_subordinate'] == 0;

        // 从数据库获取当前玩家的下属信息
        $subordinates = \app\index\model\Player::getSubordinates($playerId, $startTime, $endTime, 3, $dropBound);

        // 如果没有数据，返回空结果
        if (empty($subordinates)) {
            return json(['code' => 102, 'msg' => __('No data found'), 'data' => []]);
        }

        // 计算统计数据
        $totalRegistrations = count($subordinates);
        $totalBetAmount = 0;
        $totalDepositAmount = 0;
        $totalDepositUsers = 0;
        $totalDepositOrderCount = 0;
        
        // 格式化数据并计算统计数据
        foreach ($subordinates as &$sub) {
            // 格式化注册时间
            $sub['register_date'] = date('Y-m-d', strtotime($sub['register_time']));

            // 获取最后登录时间
            $player = \think\Db::table('players')
                ->field(['last_login_time', 'id'])
                ->where('id', $sub['id'])
                ->find();

            // 格式化最后登录时间
            $sub['last_login_date'] = $player && $player['last_login_time'] ?
                date('Y-m-d', strtotime($player['last_login_time'])) : '-';

            // 获取下属个数
            $sub['subordinates_count'] = \think\Db::table('player_relations')
                ->where('ancestor_id', $sub['id'])
                ->where('relation_level', '<=', 3)
                ->count();

            // 累加统计数据
            $totalBetAmount += floatval($sub['total_bet_amount']);
            $totalDepositAmount += floatval($sub['total_deposit_amount']);
            
            // 检查是否有充值记录
            $hasDeposit = \think\Db::table('deposit_orders')
                ->where('player_id', $sub['id'])
                ->where('payment_status', 1)
                ->count();
            
            if ($hasDeposit > 0) {
                $totalDepositUsers++;
                $totalDepositOrderCount += $hasDeposit;
            }
            
            // 设置状态
            $sub['status'] = $player && isset($player['is_blacklisted']) && $player['is_blacklisted'] == 1 ? 
                __('Blacklisted') : __('Normal');
        }

        // 格式化金额（无论是否有数据都需要格式化）
        $totalBetAmount = number_format($totalBetAmount, 2, ',', '.');
        $totalDepositAmount = number_format($totalDepositAmount, 2, ',', '.');

        // 准备返回数据
        $data = [
            'subordinates' => $subordinates,
            'stats' => [
                'total_registrations' => $totalRegistrations,
                'total_deposit_users' => $totalDepositUsers,
                'total_bet_amount' => $totalBetAmount,
                'total_deposit_order_count' => $totalDepositOrderCount,
                'total_deposit_amount' => $totalDepositAmount
            ]
        ];

        return json(['code' => 1, 'msg' => __('Success'), 'data' => $data]);
    }

    /**
     * 奖励
     */
    public function bonus()
    {
        // 登录检查 - 使用Player模型中的统一方法
        if (Player::checkLoginAndRedirect()) {
            return;
        }

        // 获取登录状态
        $loginStatus = Player::checkLoginStatus();

        // 获取玩家数据
        $playerData = $loginStatus['data'];

        $this->view->assign('active_tab', 'bonus');
        return $this->view->fetch();
    }

    /**
     * 获取玩家的下属ID列表
     * @param int $playerId 玩家ID
     * @return array 下属ID列表
     */
    private function getSubordinateIds($playerId)
    {
        // 获取玩家数据，检查是否需要排除掉绑下属
        $player = \think\Db::table('players')->where('id', $playerId)->find();
        $dropBound = isset($player['no_unbind_subordinate']) && $player['no_unbind_subordinate'] == 0;
        
        // 基础查询 - 获取所有三级以内的下属
        $query = \think\Db::table('player_relations')
            ->alias('pr')
            ->where('pr.ancestor_id', $playerId)
            ->where('pr.relation_level', '<=', 3);
        
        // 如果需要排除掉绑下属，添加关联查询
        if ($dropBound) {
            $query->join(['players' => 'p'], 'pr.player_id = p.id')
                  ->where('p.self_unbind_status', 0);
        }
        
        // 执行查询获取下属ID
        $relations = $query->column('pr.player_id');
        
        if (empty($relations)) {
            return [$playerId]; // 返回自己的ID，避免SQL错误
        }
        
        return $relations;
    }
}
