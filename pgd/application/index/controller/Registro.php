<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\index\model\Player;

class Registro extends Frontend
{
    protected $noNeedLogin = ['index'];
    protected $noNeedRight = '*';
    protected $layout = '';

    public function _initialize()
    {
        parent::_initialize();

        // 加载语言包
        $this->loadlang('security');
    }

    /**
     * 提款记录页面
     */
    public function index()
    {
        // 登录检查 - 使用Player模型中的统一方法
        if (Player::checkLoginAndRedirect($this)) {
            return;
        }

        // 获取登录状态
        $loginStatus = Player::checkLoginStatus();

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 获取日期范围参数
        $dateRange = $this->request->get('date_range', 'today');

        // 设置时间范围
        $startTime = '';
        $endTime = date('Y-m-d H:i:s');

        switch ($dateRange) {
            case 'yesterday':
                $startTime = date('Y-m-d 00:00:00', strtotime('-1 day'));
                $endTime = date('Y-m-d 23:59:59', strtotime('-1 day'));
                break;
            case 'week':
                $startTime = date('Y-m-d 00:00:00', strtotime('-7 days'));
                break;
            case 'month':
                $startTime = date('Y-m-d 00:00:00', strtotime('-30 days'));
                break;
            case 'half_year':
                $startTime = date('Y-m-d 00:00:00', strtotime('-180 days'));
                break;
            case 'year':
                $startTime = date('Y-m-d 00:00:00', strtotime('-365 days'));
                break;
            case 'all':
                $startTime = '2000-01-01 00:00:00'; // 足够早的日期以包含所有记录
                break;
            case 'today':
            default:
                $startTime = date('Y-m-d 00:00:00');
                break;
        }

        // 查询提款记录
        $records = [];
        $totalAmount = 0;

        try {
            $records = \think\Db::table('withdraw_orders')
                ->where('player_id', $playerId)
                ->where('created_at', 'between', [$startTime, $endTime])
                ->field('id, amount, audit_status, created_at, processed_at')
                ->order('created_at', 'desc')
                ->select();

            // 处理记录和计算总金额
            foreach ($records as &$record) {
                $totalAmount += $record['amount'];

                // 根据审核状态设置状态文本和状态码
                switch ($record['audit_status']) {
                    case 0: // 待加入工单
                    case 1: // 已加入工单,待审核
                        $record['status'] = 'pending';
                        break;
                    case 3: // 三方处理中
                        $record['status'] = 'processing';
                        break;
                    case 2: // 已审核
                    case 4: // 订单完成
                    case 8: // 虚拟支付
                        $record['status'] = 'completed';
                        break;
                    case 5: // 拒绝并退回金币
                    case 7: // 拒绝并没收金币
                        $record['status'] = 'rejected';
                        break;
                    case 6: // 处理失败并退回金币
                        $record['status'] = 'failed';
                        break;
                    default:
                        $record['status'] = 'pending';
                }

                // 格式化时间和金额
                $record['create_time'] = date('Y-m-d H:i:s', strtotime($record['created_at']));
                $record['amount'] = number_format($record['amount'], 2, '.', '');

                // 设置状态类名和显示文本
                $record['status_class'] = 'status-' . $record['status'];
                $record['status_text'] = __($record['status']);
            }
        } catch (\Exception $e) {
            // 发生错误时记录日志
            trace('获取提款记录错误: ' . $e->getMessage(), 'error');
        }

        // 将玩家数据和提款记录传递给视图
        $this->view->assign('player', $playerData);
        $this->view->assign('records', $records);
        $this->view->assign('totalAmount', number_format($totalAmount, 2, '.', ''));
        $this->view->assign('dateRange', $dateRange);

        return $this->view->fetch();
    }

    /**
     * 获取提款记录的AJAX方法
     * @return \think\response\Json
     */
    public function getWithdrawRecords()
    {
        // 判断是否是AJAX请求
        if (!$this->request->isAjax()) {
            return json(['code' => 0, 'msg' => __('Invalid request method')]);
        }

        // 验证登录状态
        $loginStatus = Player::checkLoginStatus();
        if ($loginStatus['code'] == 0) {
            return json(['code' => 0, 'msg' => __('Please login first')]);
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 获取日期范围参数
        $dateRange = $this->request->get('date_range', 'today');

        // 设置时间范围
        $startTime = '';
        $endTime = date('Y-m-d H:i:s');

        switch ($dateRange) {
            case 'yesterday':
                $startTime = date('Y-m-d 00:00:00', strtotime('-1 day'));
                $endTime = date('Y-m-d 23:59:59', strtotime('-1 day'));
                break;
            case 'week':
                $startTime = date('Y-m-d 00:00:00', strtotime('-7 days'));
                break;
            case 'month':
                $startTime = date('Y-m-d 00:00:00', strtotime('-30 days'));
                break;
            case 'half_year':
                $startTime = date('Y-m-d 00:00:00', strtotime('-180 days'));
                break;
            case 'year':
                $startTime = date('Y-m-d 00:00:00', strtotime('-365 days'));
                break;
            case 'all':
                $startTime = '2000-01-01 00:00:00'; // 足够早的日期以包含所有记录
                break;
            case 'today':
            default:
                $startTime = date('Y-m-d 00:00:00');
                break;
        }

        // 查询提款记录
        $records = [];
        $totalAmount = 0;

        try {
            $records = \think\Db::table('withdraw_orders')
                ->where('player_id', $playerId)
                ->where('created_at', 'between', [$startTime, $endTime])
                ->field('id, amount, audit_status, created_at, processed_at')
                ->order('created_at', 'desc')
                ->select();

            // 处理记录和计算总金额
            foreach ($records as &$record) {
                $totalAmount += $record['amount'];

                // 根据审核状态设置状态文本和状态码
                switch ($record['audit_status']) {
                    case 0: // 待加入工单
                    case 1: // 已加入工单,待审核
                        $record['status'] = 'pending';
                        break;
                    case 3: // 三方处理中
                        $record['status'] = 'processing';
                        break;
                    case 2: // 已审核
                    case 4: // 订单完成
                    case 8: // 虚拟支付
                        $record['status'] = 'completed';
                        break;
                    case 5: // 拒绝并退回金币
                    case 7: // 拒绝并没收金币
                        $record['status'] = 'rejected';
                        break;
                    case 6: // 处理失败并退回金币
                        $record['status'] = 'failed';
                        break;
                    default:
                        $record['status'] = 'pending';
                }

                // 格式化时间和金额
                $record['create_time'] = date('Y-m-d H:i:s', strtotime($record['created_at']));
                $record['amount'] = number_format($record['amount'], 2, '.', '');

                // 设置状态文本
                $record['status_text'] = __($record['status']);
            }
        } catch (\Exception $e) {
            // 发生错误时记录日志
            trace('获取提款记录错误: ' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => __('Failed to fetch records')]);
        }

        // 返回JSON格式的提款记录和总金额
        return json([
            'code' => 1,
            'msg' => __('Success'),
            'data' => [
                'records' => $records,
                'totalAmount' => number_format($totalAmount, 2, '.', '')
            ]
        ]);
    }
}