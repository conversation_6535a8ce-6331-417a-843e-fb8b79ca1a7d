<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\index\model\Player;
use think\Db;
use think\Session;

/**
 * 玩家中心
 */
class User extends Frontend
{
    protected $layout = '';
    // 修改为空数组，表示所有方法都需要登录
    protected $noNeedLogin = [];  // 所有方法都需要登录
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
        // 加载用户模块语言包
        // \think\Lang::load(APP_PATH . 'index/lang/' . $this->request->langset() . '/user.php');
        // 加载安全中心模块语言包
        // \think\Lang::load(APP_PATH . 'index/lang/' . $this->request->langset() . '/security.php');

        // 记录访问信息
        \app\common\library\FrontendLog::info('访问用户中心: controller=' . $this->request->controller() .
                                           ', action=' . $this->request->action() .
                                           ', IP=' . $this->request->ip());

        // 设置充值金额列表，用于充值弹窗
        try {
            $amountList = \app\index\model\Payment::getAmountList();
        } catch (\Exception $e) {
            // 记录错误日志
            \think\Log::record('获取充值金额列表失败: ' . $e->getMessage() . ' [' . $e->getFile() . ':' . $e->getLine() . ']', 'error');
            // 设置默认值
            $amountList = [10, 20, 50, 100, 200, 500, 1000, 2000];
        }
        $this->view->assign('amountList', $amountList);

        // 获取支付渠道列表并传递给视图
        try {
            $paymentChannels = \app\index\model\PaymentChannel::getActiveChannels('deposit');
        } catch (\Exception $e) {
            // 记录错误日志
            \think\Log::record('获取支付渠道列表失败: ' . $e->getMessage() . ' [' . $e->getFile() . ':' . $e->getLine() . ']', 'error');
            // 设置默认值
            $paymentChannels = [];
        }
        $this->view->assign('paymentChannels', $paymentChannels);
    }

    /**
     * 检查登录状态
     * 如果未登录，显示提示并重定向到首页
     */
    protected function checkLogin()
    {
        // 记录访问信息
        \app\common\library\FrontendLog::info('访问用户中心: controller=' . $this->request->controller() .
                                           ', action=' . $this->request->action() .
                                           ', IP=' . $this->request->ip());

        // 检查用户是否已登录
        $loginStatus = \app\index\model\Player::checkLoginStatus();

        // 记录登录状态
        \app\common\library\FrontendLog::info('用户中心登录状态: code=' . $loginStatus['code'] .
                                           ', isLoggedIn=' . ($loginStatus['isLoggedIn'] ?? 'false'));

        if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
            // 记录未登录访问
            \app\common\library\FrontendLog::warning('未登录用户尝试访问用户中心: IP=' . $this->request->ip());

            // 设置提示信息，将在首页显示
            \think\Session::set('toast_message', '请先登录后再访问用户中心');
            \think\Session::set('toast_type', 'warning');

            // 直接重定向到首页并带上login参数，显示登录弹窗
            $this->redirect(url('', ['login' => 1, 't' => time()]));

            exit;
        }
    }

    /**
     * 用户主页
     */
    public function index()
    {
        // 打印路由信息
        $routeInfo = [
            'module' => $this->request->module(),
            'controller' => $this->request->controller(),
            'action' => $this->request->action(),
            'route' => $this->request->route(),
            'param' => $this->request->param(),
            'get' => $this->request->get(),
            'post' => $this->request->post(),
            'url' => $this->request->url(true),
        ];

        // 记录路由信息到日志
        \think\Log::record('User/index路由信息: ' . json_encode($routeInfo, JSON_UNESCAPED_UNICODE), 'info');

        try {
            // 检查用户是否已登录
            $loginStatus = Player::checkLoginStatus();

            // 记录登录状态到日志
            // \think\Log::record('User/index登录状态: ' . json_encode($loginStatus, JSON_UNESCAPED_UNICODE), 'info');

            if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
                // 记录未登录访问
                \app\common\library\FrontendLog::warning('未登录用户尝试访问用户中心: IP=' . $this->request->ip());

                // 设置提示信息，将在首页显示
                \think\Session::set('toast_message', '请先登录后再访问用户中心');
                \think\Session::set('toast_type', 'warning');

                // 直接重定向到首页并带上login参数，显示登录弹窗
                $this->redirect(url('', ['login' => 1, 't' => time()]));
                exit;
            }

            // 记录登录状态到日志
            \think\Log::record('User/index登录状态: ' . json_encode($loginStatus, JSON_UNESCAPED_UNICODE), 'info');

            // 获取玩家数据
            $playerData = $loginStatus['data'];
            $userId = $playerData['id'];

            // 初始化默认值
            $vip_level = isset($playerData['vip_level']) ? $playerData['vip_level'] : 0;
            $total_deposit = isset($playerData['total_deposit']) ? $playerData['total_deposit'] : 0;
            $total_bet = isset($playerData['total_bet']) ? $playerData['total_bet'] : 0;
            // 获取数据库表前缀
            $vipConfigTable = "vip_config";

            // 获取当前VIP等级配置
            $currentVipConfig = Db::table($vipConfigTable)
                ->where('level', $vip_level)
                ->find();

            // 记录当前VIP配置
            \think\Log::record('当前VIP配置: ' . ($currentVipConfig ? json_encode($currentVipConfig, JSON_UNESCAPED_UNICODE) : 'null'), 'info');

            // 获取下一级VIP配置
            $nextVipLevel = $vip_level + 1;

            // 构建查询SQL
            $nextVipSql = Db::table($vipConfigTable)
                ->where('level', $nextVipLevel)
                ->buildSql();

            // 记录SQL语句
            \think\Log::record('查询下一级VIP配置SQL: ' . $nextVipSql, 'info');

            $nextVipConfig = Db::table($vipConfigTable)
                ->where('level', $nextVipLevel)
                ->find();

            // 如果没有找到下一级VIP配置，尝试获取最高级别的VIP配置
            if (!$nextVipConfig) {
                \think\Log::record('未找到下一级VIP配置，尝试获取最高级别的VIP配置', 'info');
                // 获取最高级别的VIP配置
                $maxVipConfig = Db::table($vipConfigTable)
                    ->order('level desc')
                    ->find();

                // 如果当前级别不是最高级别，则使用最高级别作为下一级
                if ($maxVipConfig && $maxVipConfig['level'] > $vip_level) {
                    $nextVipConfig = $maxVipConfig;
                }
            }
            // 计算升级到下一级VIP所需的额外充值额度
            $remainingDeposit = 0;
            if ($nextVipConfig) {
                $remainingDeposit = max(0, $nextVipConfig['deposit_requirement'] - $total_deposit);
            }

            // 计算VIP等级的进度
            $depositProgress = 0;

            // 如果有下一级VIP配置，使用下一级的要求计算进度
            if ($nextVipConfig && isset($nextVipConfig['deposit_requirement']) && $nextVipConfig['deposit_requirement'] > 0) {
                // 充值进度 = 当前总充值额度 / 下一级所需充值额度
                $depositProgress = min(100, ($total_deposit / $nextVipConfig['deposit_requirement']) * 100);
            }
            // 如果没有下一级配置，但有当前级别配置，使用当前级别的要求计算进度
            else if ($currentVipConfig && isset($currentVipConfig['deposit_requirement']) && $currentVipConfig['deposit_requirement'] > 0) {
                // 充值进度 = 当前总充值额度 / 当前等级所需充值额度
                $depositProgress = min(100, ($total_deposit / $currentVipConfig['deposit_requirement']) * 100);
            }

            $betProgress = 0;
            // 如果有下一级VIP配置，使用下一级的要求计算进度
            if ($nextVipConfig && isset($nextVipConfig['deposit_requirement']) && $nextVipConfig['deposit_requirement'] > 0) {
                // 充值进度 = 当前总充值额度 / 下一级所需充值额度
                $betProgress = min(100, ($total_bet / $nextVipConfig['deposit_requirement']) * 100);
                $completedAmount = $nextVipConfig['deposit_requirement'];
            }
            // 如果没有下一级配置，但有当前级别配置，使用当前级别的要求计算进度
            else if ($currentVipConfig && isset($currentVipConfig['deposit_requirement']) && $currentVipConfig['deposit_requirement'] > 0) {
                // 充值进度 = 当前总充值额度 / 当前等级所需充值额度
                $betProgress = min(100, ($total_bet / $currentVipConfig['deposit_requirement']) * 100);
                $completedAmount = $currentVipConfig['deposit_requirement'];
            }

            // 获取玩家打码任务完成情况
        //    $bettingTask = \think\Db::table('player_betting_tasks')
        //     ->where('player_id', $playerData['id'])
        //     ->find();  
          
           // 加载语言包
            $this->loadlang('user');

            $total_deposit = number_format($total_deposit, 2, ',', '.');
            $nextViprequire = number_format($nextVipConfig ? $nextVipConfig['deposit_requirement'] : 0, 2, ',', '.');

            $total_bet = number_format($total_bet, 2, ',', '.');
            $completedAmount = number_format($completedAmount, 2, ',', '.');
            // 组装前端需要的数据
            $userInfo = [
                'id' => $playerData['id'],
                'username' => $playerData['username'],
                'nickname' => isset($playerData['nickname']) && $playerData['nickname'] ? $playerData['nickname'] : $playerData['username'],
                'balance' => isset($playerData['balance']) ? $playerData['balance'] : '0.00',
                'vip_level' => $vip_level,
                'total_deposit' => $total_deposit,
                'remaining_deposit' => $remainingDeposit,
                'deposit_progress' => $depositProgress,
                'current_vip_deposit' => $currentVipConfig ? $currentVipConfig['deposit_requirement'] : 0,
                'current_vip_turnover' => $currentVipConfig ? $currentVipConfig['turnover_requirement'] : 0,
                'next_vip_deposit' => $nextViprequire,
                'completed_amount'=>$completedAmount,
                'require_amount'=> $total_bet,
                'amount_progress' => $betProgress
            ];

            $this->view->assign('userInfo', $userInfo);
            return $this->view->fetch();

        } catch (\Exception $e) {
            // 记录错误日志
            \think\Log::record('用户中心加载错误: ' . $e->getMessage() . ' [' . $e->getFile() . ':' . $e->getLine() . ']', 'error');

            // 提供基础用户信息
            $loginStatus = Player::checkLoginStatus();

            // 尝试获取VIP配置信息，即使在异常处理中
            $nextVipDeposit = 0;
            try {
                // 获取VIP配置表中的第一级配置
                $firstVipConfig = Db::table('vip_config')
                    ->where('level', 1)
                    ->find();

                if ($firstVipConfig && isset($firstVipConfig['deposit_requirement'])) {
                    $nextVipDeposit = $firstVipConfig['deposit_requirement'];
                    \think\Log::record('异常处理中获取到第一级VIP配置: ' . json_encode($firstVipConfig, JSON_UNESCAPED_UNICODE), 'info');
                } else {
                    // 如果没有找到第一级配置，尝试获取任意一级配置
                    $anyVipConfig = Db::table('vip_config')
                        ->order('level asc')
                        ->find();

                    if ($anyVipConfig && isset($anyVipConfig['deposit_requirement'])) {
                        $nextVipDeposit = $anyVipConfig['deposit_requirement'];
                        \think\Log::record('异常处理中获取到任意级别VIP配置: ' . json_encode($anyVipConfig, JSON_UNESCAPED_UNICODE), 'info');
                    }
                }
            } catch (\Exception $e) {
                \think\Log::record('异常处理中获取VIP配置失败: ' . $e->getMessage(), 'error');
                $nextVipDeposit = 1000; // 设置一个默认值
            }

             
            $userInfo = [
                'id' => isset($loginStatus['data']['id']) ? $loginStatus['data']['id'] : 0,
                'username' => isset($loginStatus['data']['username']) ? $loginStatus['data']['username'] : '游客',
                'nickname' => isset($loginStatus['data']['nickname']) ? $loginStatus['data']['nickname'] : '游客',
                'balance' => isset($loginStatus['data']['balance']) ? $loginStatus['data']['balance'] : '0.00',
                'vip_level' => 0,
                'total_deposit' => 0,
                'deposit_progress' => 0,
                'total_bet' => 0,
                'remaining_deposit' => $nextVipDeposit, // 使用获取到的值
                'current_vip_deposit' => 0,
                'current_vip_turnover' => 0,
                'next_vip_deposit' => $nextVipDeposit, // 使用获取到的值
                'amount_progress' => 0
            ];

            $this->view->assign('userInfo', $userInfo);

            // 检查玩家是否是首次充值（用于充值弹窗中的角标显示）
            $isNotFirstDeposit = false;
            try {
                // 获取当前登录玩家ID
                $playerId = $this->auth->id;

                // 记录查询参数
                \think\Log::record('查询充值记录参数: player_id=' . $playerId, 'info');

                // 构建查询SQL
                $sql = \think\Db::table('deposit_orders')
                    ->where('player_id', $playerId)
                    ->where('payment_status', 1) // 1表示支付成功
                    ->buildSql();

                // 记录SQL语句
                \think\Log::record('查询充值记录SQL: ' . $sql, 'info');

                // 执行查询
                $depositCount = \think\Db::table('deposit_orders')
                    ->where('player_id', $playerId)
                    ->where('payment_status', 1) // 1表示支付成功
                    ->count();

                // 记录查询结果
                \think\Log::record('查询充值记录结果: count=' . $depositCount, 'info');

                // 如果存在成功的充值记录，则不是首次充值
                $isNotFirstDeposit = $depositCount > 0;

                // 记录日志
                \app\common\library\FrontendLog::info('检查玩家是否首次充值: player_id=' . $playerId .
                                 ', deposit_count=' . $depositCount .
                                 ', is_not_first_deposit=' . ($isNotFirstDeposit ? 'true' : 'false'));

                
            } catch (\Exception $e) {
                // 记录错误日志
                \app\common\library\FrontendLog::error('检查玩家首次充值状态失败: ' . $e->getMessage());
                \think\Log::record('检查玩家首次充值状态失败: ' . $e->getMessage() . ' [' . $e->getFile() . ':' . $e->getLine() . ']', 'error');

                // 设置默认值
                $isNotFirstDeposit = false;
            }

            // 将结果传递给视图
            $this->view->assign('isNotFirstDeposit', $isNotFirstDeposit);

            // 记录传递给视图的变量
            \think\Log::record('传递给视图的isNotFirstDeposit: ' . ($isNotFirstDeposit ? 'true' : 'false'), 'info');


            return $this->view->fetch();
        }
    }

    /**
     * 用户资料页面
     */
    public function profile()
    {
        try {
            // 检查用户是否已登录
            $loginStatus = Player::checkLoginStatus();

            if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
                // 直接重定向到首页并带上login参数，显示登录弹窗
                $this->redirect(url('', ['login' => 1]));
                return;
            }

            // 获取玩家数据
            $playerData = $loginStatus['data'];

            // 传递用户数据到视图
            $this->view->assign('userInfo', $playerData);

            return $this->view->fetch();
        } catch (\Exception $e) {
            // 记录错误日志
            \think\Log::record('用户资料页面加载错误: ' . $e->getMessage() . ' [' . $e->getFile() . ':' . $e->getLine() . ']', 'error');
            return $this->view->fetch();
        }
    }

    /**
     * 获取用户个人资料（社交媒体链接等）
     * 由于暂时不需要存储分享链接，此方法仅返回成功状态
     */
    public function getProfile()
    {
        $result = [
            'code' => 1,
            'msg' => __('获取成功'),
            'data' => [
                'instagram' => '',
                'whatsapp' => '',
                'facebook' => '',
                'telegram' => ''
            ]
        ];

        return json($result);
    }

    /**
     * 保存用户个人资料（社交媒体链接等）
     * 由于暂时不需要存储分享链接，此方法仅返回成功状态
     */
    public function saveProfile()
    {
        // 获取提交的社交媒体链接(仅用于日志记录)
        $instagram = $this->request->post('instagram', '');
        $whatsapp = $this->request->post('whatsapp', '');
        $facebook = $this->request->post('facebook', '');
        $telegram = $this->request->post('telegram', '');

        // 记录日志，但不存储到数据库
        \think\Log::record('用户提交了社交媒体链接(未存储): instagram=' . $instagram .
                          ', whatsapp=' . $whatsapp .
                          ', facebook=' . $facebook .
                          ', telegram=' . $telegram, 'info');

        $result = [
            'code' => 1,
            'msg' => __('保存成功'),
            'data' => [
                'instagram' => $instagram,
                'whatsapp' => $whatsapp,
                'facebook' => $facebook,
                'telegram' => $telegram
            ]
        ];

        return json($result);
    }

    /**
     * 余额恢复页面
     */
    public function balance()
    {
        try {
            // 检查用户是否已登录
            $loginStatus = Player::checkLoginStatus();

            if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
                // 直接重定向到首页并带上login参数，显示登录弹窗
                $this->redirect(url('', ['login' => 1]));
                return;
            }

            // 获取玩家数据
            $playerData = $loginStatus['data'];

            // 传递用户数据到视图
            $this->view->assign('userInfo', $playerData);

            // 获取平台列表 (实际项目中应从数据库获取)
            $platforms = [
                [
                    'code' => 'todos',
                    'name' => '所有',
                    'icon' => '♠️',
                    'balance' => 0
                ]
                // 可以添加更多平台
            ];

            $this->view->assign('platforms', $platforms);

            return $this->view->fetch();
        } catch (\Exception $e) {
            // 记录错误日志
            \think\Log::record('余额恢复页面加载错误: ' . $e->getMessage() . ' [' . $e->getFile() . ':' . $e->getLine() . ']', 'error');
            return $this->view->fetch();
        }
    }

    /**
     * 安全中心页面
     */
    public function securitycenter()
    {
        try {
            // 检查用户是否已登录
            $loginStatus = Player::checkLoginStatus();

            if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
                // 直接重定向到首页并带上login参数，显示登录弹窗
                $this->redirect(url('', ['login' => 1]));
                return;
            }

            // 获取玩家数据
            $playerData = $loginStatus['data'];

            // 传递用户数据到视图
            $this->view->assign('userInfo', $playerData);

            return $this->view->fetch();
        } catch (\Exception $e) {
            // 记录错误日志
            \think\Log::record('安全中心页面加载错误: ' . $e->getMessage() . ' [' . $e->getFile() . ':' . $e->getLine() . ']', 'error');
            return $this->view->fetch();
        }
    }

    /**
     * 登录密码修改页面
     */
    public function loginpassword()
    {
        try {
            // 检查用户是否已登录
            $loginStatus = Player::checkLoginStatus();

            if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
                // 直接重定向到首页并带上login参数，显示登录弹窗
                $this->redirect(url('', ['login' => 1]));
                return;
            }

            // 获取玩家数据
            $playerData = $loginStatus['data'];

            // 传递用户数据到视图
            $this->view->assign('userInfo', $playerData);

            return $this->view->fetch();
        } catch (\Exception $e) {
            // 记录错误日志
            \think\Log::record('登录密码修改页面加载错误: ' . $e->getMessage() . ' [' . $e->getFile() . ':' . $e->getLine() . ']', 'error');
            return $this->view->fetch();
        }
    }

    /**
     * 取款密码修改页面
     */
    public function withdrawpassword()
    {
        try {
            // 检查用户是否已登录
            $loginStatus = Player::checkLoginStatus();

            if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
                // 直接重定向到首页并带上login参数，显示登录弹窗
                $this->redirect(url('', ['login' => 1]));
                return;
            }

            // 获取玩家数据
            $playerData = $loginStatus['data'];

            // 传递用户数据到视图
            $this->view->assign('userInfo', $playerData);

            return $this->view->fetch();
        } catch (\Exception $e) {
            // 记录错误日志
            \think\Log::record('取款密码修改页面加载错误: ' . $e->getMessage() . ' [' . $e->getFile() . ':' . $e->getLine() . ']', 'error');
            return $this->view->fetch();
        }
    }

    /**
     * 修改登录密码API
     */
    public function changePassword()
    {
        $result = ['code' => 0, 'msg' => __('Change password failure'), 'data' => null];

        try {
            // 检查用户是否已登录
            $loginStatus = Player::checkLoginStatus();

            if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
                $result['msg'] = __('用户未登录');
                return json($result);
            }

            // 获取提交的数据
            $oldPassword = $this->request->post('old_password', '');
            $newPassword = $this->request->post('new_password', '');
            $confirmPassword = $this->request->post('confirm_password', '');

            // 基本验证
            if (!$oldPassword || !$newPassword || !$confirmPassword) {
                $result['msg'] = __('Please fill in all fields');
                return json($result);
            }

            if ($newPassword !== $confirmPassword) {
                $result['msg'] = __('Password and confirm password don\'t match');
                return json($result);
            }

            // 密码复杂度验证
            if (!preg_match('/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,20}$/', $newPassword)) {
                $result['msg'] = __('Password does not meet requirements');
                return json($result);
            }

            // 这里应该有实际的密码修改逻辑，与API对接
            // 由于是示例，这里只返回成功

            $result = [
                'code' => 1,
                'msg' => __('Change password successful'),
                'data' => []
            ];

            return json($result);
        } catch (\Exception $e) {
            // 记录错误日志
            \think\Log::record('修改密码错误: ' . $e->getMessage() . ' [' . $e->getFile() . ':' . $e->getLine() . ']', 'error');
            $result['msg'] = __('Change password failure') . ': ' . $e->getMessage();
            return json($result);
        }
    }

    /**
     * 检查是否已设置取款密码
     */
    public function checkWithdrawPassword()
    {
        $result = ['code' => 0, 'msg' => __('Operation failed'), 'data' => null];

        try {
            // 检查用户是否已登录
            $loginStatus = Player::checkLoginStatus();

            if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
                $result['msg'] = __('用户未登录');
                return json($result);
            }

            // 获取玩家数据
            $playerData = $loginStatus['data'];
            $playerId = $playerData['id'];
            
            // 获取玩家类型
            $playerType = Session::get('player_type') ?: 'regular';
            $isVirtualPlayer = ($playerType === 'virtual');
            
            // 虚拟玩家始终返回已设置密码，以确保提款流程顺利
            if ($isVirtualPlayer) {
                $result = [
                    'code' => 1,
                    'msg' => __('Operation successful'),
                    'data' => [
                        'has_password' => true // 虚拟玩家始终返回已设置密码
                    ]
                ];
                
                // 记录日志
                \think\Log::record('虚拟玩家检查取款密码: player_id=' . $playerId . ', 自动返回已设置', 'info');
                return json($result);
            }
            
            // 普通玩家正常检查密码
            // 从数据库中查询玩家的取款密码
            $player = \think\Db::table('players')
                ->where('id', $playerId)
                ->field('withdraw_password')
                ->find();

            // 检查是否设置了取款密码
            $hasPassword = !empty($player) && !empty($player['withdraw_password']);

            $result = [
                'code' => 1,
                'msg' => __('Operation successful'),
                'data' => [
                    'has_password' => $hasPassword
                ]
            ];

            return json($result);
        } catch (\Exception $e) {
            // 记录错误日志
            \think\Log::record('检查取款密码错误: ' . $e->getMessage() . ' [' . $e->getFile() . ':' . $e->getLine() . ']', 'error');
            $result['msg'] = __('Operation failed') . ': ' . $e->getMessage();
            return json($result);
        }
    }

    /**
     * 设置取款密码API
     */
    public function setWithdrawPassword()
    {
        $result = ['code' => 0, 'msg' => __('Failed to set withdrawal password'), 'data' => null];

        try {
            // 检查用户是否已登录
            $loginStatus = Player::checkLoginStatus();

            if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
                $result['msg'] = __('用户未登录');
                return json($result);
            }

            // 获取提交的数据
            $currentPassword = $this->request->post('current_password', '');
            $newPassword = $this->request->post('new_password', '');

            // 基本验证
            if (!$newPassword) {
                $result['msg'] = __('Please enter a new password');
                return json($result);
            }

            // 验证密码格式（6位数字）
            if (!preg_match('/^\d{6}$/', $newPassword)) {
                $result['msg'] = __('Please enter a 6-digit withdrawal password');
                return json($result);
            }

            // 这里应该有实际的密码修改逻辑，与API对接
            // 由于是示例，这里只返回成功

            $result = [
                'code' => 1,
                'msg' => __('Withdrawal password set successfully'),
                'data' => []
            ];

            return json($result);
        } catch (\Exception $e) {
            // 记录错误日志
            \think\Log::record('设置取款密码错误: ' . $e->getMessage() . ' [' . $e->getFile() . ':' . $e->getLine() . ']', 'error');
            $result['msg'] = __('Failed to set withdrawal password') . ': ' . $e->getMessage();
            return json($result);
        }
    }

    /**
     * 恢复平台余额的API
     */
    public function recoverBalance()
    {
        $result = ['code' => 0, 'msg' => __('恢复失败'), 'data' => null];

        try {
            // 检查用户是否已登录
            $loginStatus = Player::checkLoginStatus();

            if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
                $result['msg'] = __('用户未登录');
                return json($result);
            }

            // 获取提交的平台代码
            $platform = $this->request->post('platform', 'todos');

            // 这里应该是实际的余额恢复逻辑，与游戏平台对接
            // 由于是示例，这里只返回成功

            $result = [
                'code' => 1,
                'msg' => __('Balance recovered successfully'),
                'data' => [
                    'platform' => $platform,
                    'recovered_amount' => 10
                ]
            ];

            return json($result);
        } catch (\Exception $e) {
            // 记录错误日志
            \think\Log::record('恢复余额错误: ' . $e->getMessage() . ' [' . $e->getFile() . ':' . $e->getLine() . ']', 'error');
            $result['msg'] = __('恢复失败') . ': ' . $e->getMessage();
            return json($result);
        }
    }
}
