<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\index\model\Player as PlayerModel;

/**
 * 玩家相关控制器
 */
class Player extends Frontend
{
    protected $layout = '';
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 刷新玩家余额
     */
    public function refreshBalance()
    {
        $result = PlayerModel::refreshBalance();
        return json($result);
    }

    /**
     * 玩家退出登录
     */
    public function logout()
    {
        // 使用模型方法退出登录
        $result = PlayerModel::logout();

        // 清除可能存在的缓存
        \think\Session::clear();

        // 重定向到首页
        $this->redirect('');

        return json($result);
    }
}