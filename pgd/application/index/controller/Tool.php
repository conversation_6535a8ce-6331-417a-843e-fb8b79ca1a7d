<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\index\model\GameProvider;
use think\Config;

class Tool extends Frontend
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function _initialize()
    {
        parent::_initialize();
        $this->request->filter('trim,strip_tags,htmlspecialchars');
        
        // 检查是否为安全环境
        if (!$this->checkSecureEnvironment()) {
            $this->error('无权访问此页面');
        }
    }
    
    /**
     * 更新游戏提供商JSON文件
     */
    public function updateGameData()
    {
        // 执行更新
        $result = GameProvider::updateGameProvidersJson();
        
        if (isset($result['error'])) {
            return json(['code' => 0, 'msg' => $result['error']]);
        }
        
        return json([
            'code' => 1, 
            'msg' => '更新成功', 
            'data' => $result
        ]);
    }
    
    /**
     * 检查是否为安全环境（开发环境或内部IP）
     */
    protected function checkSecureEnvironment()
    {
        // 如果是调试模式，允许访问
        if (Config::get('app_debug')) {
            return true;
        }
        
        // 检查IP白名单
        $ip = $this->request->ip();
        $whiteList = ['127.0.0.1', '::1'];
        
        // 如果配置了管理IP白名单，使用配置的白名单
        $configWhiteList = Config::get('admin_ip_whitelist');
        if ($configWhiteList && is_array($configWhiteList)) {
            $whiteList = array_merge($whiteList, $configWhiteList);
        }
        
        return in_array($ip, $whiteList);
    }
} 