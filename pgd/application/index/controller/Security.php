<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\index\model\Player;
use app\common\library\FrontendLog;
use think\Session;

class Security extends Frontend
{
    // 设置无需登录的方法，在方法内部进行登录检查
    protected $layout = '';

    // 定义返回码和消息映射
    protected $returnCodes = [
        // 通用返回码
        0 => 'Unknown error',                          // 未知错误
        1 => 'Success',                                // 成功

        // 请求相关
        404 => 'Invalid request method',               // 无效的请求方法
        140 => 'Please login first',                   // 请先登录

        // 提款相关
        100 => 'Please enter your name',               // 请输入姓名
        101 => 'Please enter your PIX account',        // 请输入PIX账户
        102 => 'Please enter your 11-digit CPF number', // 请输入11位CPF号码
        103 => 'Please enter a 6-digit withdrawal password', // 请输入6位提款密码
        104 => 'Betting requirement not met',          // 未满足投注要求
        105 => 'Please enter a valid amount',          // 请输入有效金额
        106 => 'Minimum withdrawal amount is %s',      // 最低提款金额为 %s
        108 => 'Maximum single withdrawal amount is %s', // 单笔最高提款金额为 %s
        109 => 'Daily withdrawal limit exceeded. Remaining: %s', // 超出每日提款限额。剩余额度: %s
        110 => 'Daily withdrawal times exceeded. Limit: %s', // 超出每日提款次数。限制: %s
        111 => 'Maximum withdrawal amount exceeded. Limit: %s', // 超出最大提款金额。限制: %s
        112 => 'Incorrect withdrawal PIN', // 提款PIN不正确，请重新输入
        113 => 'System error',                         // 系统错误
        114 => 'Withdrawal account not found',         // 未找到提款账户
        115 => 'System error',                         // 系统错误
        119 => 'Insufficient balance',                 // 余额不足
        120 => 'System error: No available withdrawal channel', // 系统错误：没有可用的提款通道
        121 => 'Failed to create withdrawal order',    // 创建提款订单失败
        122 => 'Failed to freeze balance',             // 冻结余额失败
        123 => 'Failed to submit withdrawal request',  // 提交提款请求失败
        124 => 'Withdrawal password not set',          // 未设置提款密码
        125 => 'Failed to fetch accounts',             // 获取账户失败
        126 => 'Password verification failed',         // 密码验证失败
        127 => 'Failed to add account',                // 添加账户失败
        128 => 'Network error',                        // 网络错误
    ];

    public function _initialize()
    {
        parent::_initialize();

        // 加载语言包
        $this->loadlang('security');
    }

    /**
     * 获取返回消息
     * @param int $code 返回码
     * @param array $params 替换参数
     * @return string 翻译后的消息
     */
    protected function getReturnMsg($code, $params = [])
    {
        if (isset($this->returnCodes[$code])) {
            $msg = $this->returnCodes[$code];
            if (!empty($params)) {
                return __($msg, $params);
            }
            return __($msg);
        }
        return __('Unknown error');
    }

    /**
     * 获取返回JSON
     * @param int $code 返回码
     * @param array $params 替换参数
     * @param array $data 附加数据
     * @return \think\response\Json
     */
    protected function getReturnJson($code, $params = [], $data = null)
    {
        // 获取消息文本
        $msg = $this->getReturnMsg($code, $params);

        // 构建返回数据
        $result = ['code' => $code, 'msg' => $msg];

        // 如果有附加数据，添加到返回结果中
        if ($data !== null) {
            $result['data'] = $data;
        }

        // 返回JSON响应
        return json($result);
    }

    /**
     * 获取安全中心语言包
     * 用于前端JavaScript获取语言包数据
     */
    // public function getLang()
    // {
    //     // 加载语言包
    //     $this->loadlang('security');

    //     // 获取所有语言变量
    //     $lang = \think\Lang::get();

    //     // 返回语言包数据
    //     return json(['code' => 1, 'msg' => '', 'data' => $lang]);
    // }

    /**
     * 安全中心主页
     */
    public function index()
    {
        // 登录检查 - 使用Player模型中的统一方法
        if (Player::checkLoginAndRedirect($this)) {
            return;
        }

        // 记录访问日志
        FrontendLog::info('访问安全中心: controller=' . $this->request->controller() .
            ', action=' . $this->request->action() .
            ', IP=' . $this->request->ip());

        // 获取登录状态
        $loginStatus = Player::checkLoginStatus();
        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 获取玩家类型
        $playerType = Session::get('player_type') ?: 'regular';
        $isVirtualPlayer = ($playerType === 'virtual');
        
        // 获取玩家提款账户
        $withdrawAccounts = [];
        
        if ($isVirtualPlayer) {
            // 虚拟玩家提供一个虚拟提款账户
            FrontendLog::info('为虚拟玩家提供虚拟提款账户: player_id=' . $playerId);
            
            // 使用辅助方法生成虚拟提款账户
            $withdrawAccounts[] = $this->generateVirtualWithdrawAccount($playerId);
        } else {
            // 普通玩家正常获取提款账户
            try {
                $withdrawAccounts = \think\Db::table('player_withdraw_accounts')
                    ->where('player_id', $playerId)
                    ->select();
            } catch (\Exception $e) {
                // 发生错误时记录日志
                FrontendLog::error('获取玩家提款账户错误: ' . $e->getMessage(), 'error');
            }
        }

        // 获取URL中的tab参数
        $tab = $this->request->get('tab', 'withdraw');

        // 验证tab参数是否有效
        $validTabs = ['withdraw', 'withdrawal_records', 'account_management'];
        if (!in_array($tab, $validTabs)) {
            $tab = 'withdraw'; // 如果无效，使用默认标签
        }

        // 将玩家数据传递给视图
        $this->view->assign('player', $playerData);
        $this->view->assign('active_tab', $tab); // 使用URL中的tab参数
        // 判断提款账户是否为空，确保传递给视图的是一个数组
        $this->view->assign('withdrawAccounts', !empty($withdrawAccounts) ? $withdrawAccounts : []); // 添加提款账户数据
        // 初始化其他可能需要的变量，以避免模板中未定义变量的错误
        $this->view->assign('records', []);
        $this->view->assign('totalAmount', '0.00');
        $this->view->assign('dateRange', 'today');

        // 获取提款金额限制
        $minWithdrawal = config('withdrawal.min_amount');
        $maxWithdrawal = config('withdrawal.max_amount');
        // 尝试获取有效的提款通道
        $defaultChannel = \think\Db::table('payment_channels')
                ->where('is_active', 1)
                ->where('channel_type', 'in', ['withdraw', 'both'])
                ->find();
        if($defaultChannel)
        {
            $minWithdrawal = $defaultChannel['min_amount'];
            $maxWithdrawal = $defaultChannel['max_amount'];
        }

        // 将提款金额限制传递给视图
        $this->view->assign('minWithdrawal', $minWithdrawal);
        $this->view->assign('maxWithdrawal', $maxWithdrawal);

        try
        {
            // 获取玩家类型
            $playerType = Session::get('player_type') ?: 'regular';
            $isVirtualPlayer = ($playerType === 'virtual');
            
            // 虚拟玩家不受打码任务限制
            if ($isVirtualPlayer) {
                FrontendLog::log('虚拟玩家不受打码任务限制，玩家ID: ' . $playerId);
                
                // 虚拟玩家的打码任务始终视为已完成
                $hasCompletedBetting = true;
                $completedAmount = 1000; // 设置一个足够大的值
                $requiredAmount = 1000;
                $betProgress = 100; // 100%完成
                
                // 将变量传递给视图
                $this->view->assign('has_completed_betting', $hasCompletedBetting);
                $this->view->assign('bet_progress', $betProgress);
            } else {
                // 普通玩家正常检查打码任务
                FrontendLog::log('开始获取玩家打码任务完成情况，玩家ID: ' . $playerId);

                // 获取玩家打码任务完成情况
                $bettingTask = \think\Db::table('player_betting_tasks')
                    ->where('player_id', $playerId)
                    ->find();

                $completedAmount = 0;
                $requiredAmount =  0;
                $betProgress = 0;
                // 记录查询到的任务信息
                if ($bettingTask) {
                    FrontendLog::log('查询到打码任务: ' . json_encode($bettingTask));
                } else {
                    FrontendLog::log('未找到玩家打码任务记录');
                }

                // 判断任务是否完成
                $hasCompletedBetting = true;
                $completedAmount =  isset($bettingTask['completed_betting_amount']) ? floatval($bettingTask['completed_betting_amount']) : 0;
                $requiredAmount = isset($bettingTask['required_betting_amount']) ? floatval($bettingTask['required_betting_amount']) : 0;

                if ($bettingTask && $completedAmount < $requiredAmount) {
                    $hasCompletedBetting = false;
                }

                if($bettingTask && $requiredAmount > 0)
                {
                    $betProgress = min(100, ($completedAmount / $requiredAmount) * 100);
                }
                else
                {
                    $betProgress = 100;
                }

                // 将变量传递给视图
                $this->view->assign('has_completed_betting', $hasCompletedBetting);
                $this->view->assign('bet_progress', $betProgress);
            }
        } catch (\Exception $e) {
            // 发生错误时记录日志
            FrontendLog::error('获取玩家打码任务完成情况错误: ' . $e->getMessage(), 'error');
        }

        // 格式化提款通知消息
        if ($isVirtualPlayer) {
            // 虚拟玩家使用不同的提款说明
            $withdrawalNotice = __('virtual_withdrawal_notice');
        } else {
            // 普通玩家使用正常的提款说明
            $withdrawalNotice = __('withdrawal_notice', [$minWithdrawal, $maxWithdrawal]);
        }
        $this->view->assign('withdrawalNotice', $withdrawalNotice);

        // 明确指定模板文件
        return $this->view->fetch('index');
    }

    /**
     * 生成虚拟玩家提款账户
     * @param int $playerId 玩家ID
     * @return array 虚拟提款账户数组
     */
    protected function generateVirtualWithdrawAccount($playerId)
    {
        // 使用玩家ID作为盐值生成固定的11位数字账户号码
        $salt = $playerId; // 使用玩家ID作为盐值
        $baseNumber = '9' . str_pad(abs(crc32($salt) % **********), 10, '0', STR_PAD_LEFT); // 生成以9开头的11位数字
        
        // 确保生成的11位数字
        $accountNumber = substr($baseNumber, 0, 11);
        
        // 创建一个虚拟提款账户
        $account = [
            'id' => 'virtual_' . $playerId,
            'player_id' => $playerId,
            'account_type' => 'CPF',
            'account_name' => '虚拟玩家账户',
            'account_number' => $accountNumber,
            'is_default' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        FrontendLog::info('生成虚拟玩家账户号码: ' . $accountNumber);
        
        return $account;
    }
    
    /**
     * 提款记录页面
     */
    public function withdrawal_records()
    {
        // 登录检查 - 使用Player模型中的统一方法
        if (Player::checkLoginAndRedirect($this)) {
            return;
        }

        // 获取登录状态
        $loginStatus = Player::checkLoginStatus();

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];
        
        // 获取玩家类型
        $playerType = Session::get('player_type') ?: 'regular';
        $isVirtualPlayer = ($playerType === 'virtual');

        // 获取日期范围参数
        $dateRange = $this->request->get('date_range', 'today');

        // 设置时间范围
        $startTime = '';
        $endTime = date('Y-m-d H:i:s');

        switch ($dateRange) {
            case 'yesterday':
                $startTime = date('Y-m-d 00:00:00', strtotime('-1 day'));
                $endTime = date('Y-m-d 23:59:59', strtotime('-1 day'));
                break;
            case 'week':
                $startTime = date('Y-m-d 00:00:00', strtotime('-7 days'));
                break;
            case 'month':
                $startTime = date('Y-m-d 00:00:00', strtotime('-30 days'));
                break;
            case 'half_year':
                $startTime = date('Y-m-d 00:00:00', strtotime('-180 days'));
                break;
            case 'year':
                $startTime = date('Y-m-d 00:00:00', strtotime('-365 days'));
                break;
            case 'all':
                $startTime = '2000-01-01 00:00:00'; // 足够早的日期以包含所有记录
                break;
            case 'today':
            default:
                $startTime = date('Y-m-d 00:00:00');
                break;
        }

        // 查询提款记录
        $records = [];
        $totalAmount = 0;
        
        if (!$isVirtualPlayer) {
            // 普通玩家 - 从数据库获取记录
            try {
                $records = \think\Db::table('withdraw_orders')
                    ->where('player_id', $playerId)
                    ->where('created_at', 'between', [$startTime, $endTime])
                    ->field('id, amount, audit_status, created_at, processed_at')
                    ->order('created_at', 'desc')
                    ->select();

                // 处理记录和计算总金额
                foreach ($records as &$record) {
                    $totalAmount += $record['amount'];

                    // 根据审核状态设置状态文本和状态码
                    switch ($record['audit_status']) {
                        case 0: // 待加入工单
                        case 1: // 已加入工单,待审核
                            $record['status'] = 'pending';
                            break;
                        case 3: // 三方处理中
                            $record['status'] = 'processing';
                            break;
                        case 2: // 已审核
                        case 4: // 订单完成
                        case 8: // 虚拟支付
                            $record['status'] = 'completed';
                            break;
                        case 5: // 拒绝并退回金币
                        case 7: // 拒绝并没收金币
                            $record['status'] = 'rejected';
                            break;
                        case 6: // 处理失败并退回金币
                            $record['status'] = 'failed';
                            break;
                        default:
                            $record['status'] = 'pending';
                    }

                    // 格式化时间和金额
                    $record['create_time'] = date('Y-m-d H:i:s', strtotime($record['created_at']));
                    $record['amount'] = number_format($record['amount'], 2, ',', '.');

                    // 设置状态类名和显示文本
                    $record['status_class'] = 'status-' . $record['status'];
                    $record['status_text'] = __($record['status']);
                }
            } catch (\Exception $e) {
                // 发生错误时记录日志
                FrontendLog::error('获取提款记录错误: ' . $e->getMessage(), 'error');
            }
        } else {
            // 虚拟玩家 - 从 Session 中获取记录
            $virtualWithdrawals = Session::get('virtual_withdrawals') ?: [];
            
            // 过滤符合时间范围的记录
            foreach ($virtualWithdrawals as $withdrawal) {
                $createdTime = strtotime($withdrawal['created_at']);
                $startTimeStamp = strtotime($startTime);
                $endTimeStamp = strtotime($endTime);
                
                if ($createdTime >= $startTimeStamp && $createdTime <= $endTimeStamp && $withdrawal['player_id'] == $playerId) {
                    // 添加到记录中
                    $record = [
                        'id' => $withdrawal['id'],
                        'amount' => $withdrawal['amount'],
                        'created_at' => $withdrawal['created_at'],
                        'processed_at' => $withdrawal['created_at'], // 虚拟玩家的提款处理时间与创建时间相同
                        'status' => 'completed', // 默认设置为完成状态
                        'status_class' => 'status-completed',
                        'status_text' => __('completed'),
                        'create_time' => date('Y-m-d H:i:s', $createdTime),
                    ];
                    
                    // 格式化金额显示
                    $record['amount'] = number_format($withdrawal['amount'], 2, ',', '.');
                    
                    $records[] = $record;
                    $totalAmount += $withdrawal['amount'];
                }
            }
            
            // 按创建时间降序排序
            usort($records, function($a, $b) {
                return strtotime($b['created_at']) - strtotime($a['created_at']);
            });
        }

        // 将玩家数据和提款记录传递给视图
        $this->view->assign('player', $playerData);
        $this->view->assign('active_tab', 'withdrawal_records');
        $this->view->assign('records', $records);
        $this->view->assign('totalAmount', number_format($totalAmount, 2, '.', ''));
        $this->view->assign('dateRange', $dateRange);
        
        // 从配置文件中读取最低投注要求
        $betMinAmount = config('bet.min_amount') ?: '0.00';
        // 将最低投注要求传递给视图
        $this->view->assign('betMinAmount', $betMinAmount);

        // 获取提款金额限制
        $minWithdrawal = config('withdrawal.min_amount');
        $maxWithdrawal = config('withdrawal.max_amount');
        // 尝试获取有效的提款通道
        $defaultChannel = \think\Db::table('payment_channels')
                ->where('is_active', 1)
                ->where('channel_type', 'in', ['withdraw', 'both'])
                ->find();
        if($defaultChannel)
        {
            $minWithdrawal = $defaultChannel['min_amount'];
            $maxWithdrawal = $defaultChannel['max_amount'];
        }
        // 将提款金额限制传递给视图
        $this->view->assign('minWithdrawal', $minWithdrawal);
        $this->view->assign('maxWithdrawal', $maxWithdrawal);

        // 格式化提款通知消息
        $withdrawalNotice = __('withdrawal_notice', [$minWithdrawal, $maxWithdrawal]);
        $this->view->assign('withdrawalNotice', $withdrawalNotice);

        return $this->view->fetch('withdrawal_records');
    }

    /**
     * 账户管理页面
     */
    public function account_management()
    {
        // 登录检查 - 使用Player模型中的统一方法
        if (Player::checkLoginAndRedirect($this)) {
            return;
        }

        FrontendLog::info('玩家访问账户管理页面');

        // 获取登录状态
        $loginStatus = Player::checkLoginStatus();

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

         // 获取玩家提款账户
         $withdrawAccounts = [];
        //  try {
        //      $withdrawAccounts = \think\Db::table('player_withdraw_accounts')
        //          ->where('player_id', $playerId)
        //          ->select();
        //  } catch (\Exception $e) {
        //      // 发生错误时记录日志
        //      \think\Log::record('获取玩家提款账户错误: ' . $e->getMessage(), 'error');
        //  }
        // 将玩家数据传递给视图
        $this->view->assign('player', $playerData);
        $this->view->assign('active_tab', 'account_management');
        // 判断提款账户是否为空，确保传递给视图的是一个数组
        $this->view->assign('accounts', !empty($withdrawAccounts) ? $withdrawAccounts : []);
        return $this->view->fetch('account_management');
    }

    /**
     * 提交提款请求
     * @return \think\response\Json
     */
    public function submitWithdraw()
    {
        FrontendLog::log('提款请求：玩家提交提款请求'.json_encode($this->request->post()));
        // 如果不是POST请求，直接返回错误
        if (!$this->request->isPost()) {
            return $this->getReturnJson(404);
        }

        // 验证登录状态
        $loginStatus = Player::checkLoginStatus();
        if ($loginStatus['code'] == 0) {
            FrontendLog::log('提款请求：未登录用户尝试提交提款请求');
            return $this->getReturnJson(140);
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

         // 获取请求参数
         $amount = $this->request->post('amount', '', 'trim,strip_tags');
         $pin = $this->request->post('pin', '', 'trim,strip_tags');
         $accountId = $this->request->post('account_id', 0, 'intval');
        // 验证余额是否足够
        if ($playerData['balance'] < $amount) {
            FrontendLog::log('提款请求：余额不足: balance=' . $playerData['balance'] . ', amount=' . $amount);
            return $this->getReturnJson(119);
        }

        // 参数验证
        if (empty($amount) || !is_numeric($amount) || $amount <= 0) {
            FrontendLog::log('提款请求：提款金额不正确: amount=' . $amount);
            return $this->getReturnJson(105);
        }
        // 转换为浮点数
        $amount = (float)$amount;

        // 验证提款密码
        if (empty($pin) || strlen($pin) != 6 || !is_numeric($pin)) {
            FrontendLog::log('提款请求：提款密码不正确: pin=' . $pin);
            return $this->getReturnJson(103);
        }

        // 验证最低提款金额
        $minWithdrawal = config('withdrawal.min_amount');
        // 验证最高提款金额
        $maxWithdrawal = config('withdrawal.max_amount');
        // 尝试获取有效的提款通道
        $defaultChannel = \think\Db::table('payment_channels')
                ->where('is_active', 1)
                ->where('channel_type', 'in', ['withdraw', 'both'])
                ->find();
        if($defaultChannel)
        {
            $minWithdrawal = $defaultChannel['min_amount'];
            $maxWithdrawal = $defaultChannel['max_amount'];
        }
        // 获取默认支付通道
        $channelCode = 1; // 设置默认通道代码为1
        if ($defaultChannel) {
            $channelCode = $defaultChannel['code'];
        } else {
            // 如果没有有效的通道，尝试获取任何提款通道
            $anyChannel = \think\Db::table('payment_channels')
                ->where('channel_type', 'in', ['withdraw', 'both'])
                ->find();

            if ($anyChannel) {
                $channelCode = $anyChannel['code'];
            }
        }

        if ($amount < $minWithdrawal) {
            FrontendLog::log('提款请求：提款金额低于最低提款金额: amount=' . $amount . ', min=' . $minWithdrawal);
            return $this->getReturnJson(106, [$minWithdrawal]);
        }

         // 获取玩家打码任务完成情况
         $bettingTask = \think\Db::table('player_betting_tasks')
         ->where('player_id', $playerId)
         ->find();

         $completedAmount = 0;
         $requiredAmount = 0;
        // 记录查询到的任务信息
        if ($bettingTask)
        {
            // 判断任务是否完成
            $completedAmount = isset($bettingTask['completed_betting_amount']) ? floatval($bettingTask['completed_betting_amount']) : 0;
            $requiredAmount = isset($bettingTask['required_betting_amount']) ? floatval($bettingTask['required_betting_amount']) : 0;
        }

        if ($bettingTask && $completedAmount < $requiredAmount) {
            FrontendLog::log('提款请求：玩家打码任务未完成: completed=' . $completedAmount . ', required=' . $requiredAmount);
            return $this->getReturnJson(104);
        }



        // 获取玩家VIP等级
        $vipLevel = isset($playerData['vip_level']) ? intval($playerData['vip_level']) : 0;

        // 获取VIP配置
        $vipConfig = null;
        try {
            $vipConfig = \think\Db::table('vip_config')
                ->where('level', $vipLevel)
                ->find();
            FrontendLog::info('提款请求：获取VIP配置: vipLevel=' . $vipLevel . ', vipConfig=' . json_encode($vipConfig, JSON_UNESCAPED_UNICODE));
            // 如果没有找到对应等级的配置，使用默认配置（0级）
            if (!$vipConfig) {
                $vipConfig = \think\Db::table('vip_config')
                    ->where('level', 0)
                    ->find();

                if (!$vipConfig) {
                    // 如果连默认配置都没有，使用系统默认值
                    $vipConfig = [
                        'single_withdraw_limit' => $maxWithdrawal,
                        'daily_max_withdraw_amount' => $maxWithdrawal,
                        'daily_withdrawal_times' => 10,
                        'max_withdraw_amount' => $maxWithdrawal * 100
                    ];
                }
            }
        } catch (\Exception $e) {
            FrontendLog::error('提款请求:获取VIP配置错误: ' . $e->getMessage(), 'error');
            // 使用系统默认值
            $vipConfig = [
                'single_withdraw_limit' => $maxWithdrawal,
                'daily_max_withdraw_amount' => $maxWithdrawal,
                'daily_withdrawal_times' => 10,
                'max_withdraw_amount' => $maxWithdrawal * 100
            ];
        }

        // 获取玩家类型
        $playerType = Session::get('player_type') ?: 'regular';
        $isVirtualPlayer = ($playerType === 'virtual');

        // 验证单笔提款上限
        $singleWithdrawLimit = isset($vipConfig['single_withdraw_limit']) && $vipConfig['single_withdraw_limit'] > 0
            ? $vipConfig['single_withdraw_limit']
            : $maxWithdrawal;

        // 虚拟玩家不受单笔提款金额限制
        if (!$isVirtualPlayer && $amount > $singleWithdrawLimit) {
            FrontendLog::log('提款请求：提款金额超过单笔提款上限: amount=' . $amount . ', limit=' . $singleWithdrawLimit);
            return $this->getReturnJson(108, [$singleWithdrawLimit]);
        } elseif ($isVirtualPlayer && $amount > $singleWithdrawLimit) {
            FrontendLog::log('虚拟玩家提款请求：金额超过单笔提款上限，但不受限制: amount=' . $amount . ', limit=' . $singleWithdrawLimit);
        }

        // 验证每日提款限额
        try {
            // 获取24小时内的提款记录
            $yesterday = date('Y-m-d H:i:s', strtotime('-24 hours'));
            $now = date('Y-m-d H:i:s');


            // 获取24小时内成功提款总额
            $last24HoursTotal = \think\Db::table('withdraw_orders')
                ->where('player_id', $playerId)
                ->where('processed_at', '>=', $yesterday)
                ->where('processed_at', '<=', $now)
                ->where('audit_status', 4) // 只计算成功订单
                ->sum('amount');

            // 获取24小时内成功提款次数
            $last24HoursTimes = \think\Db::table('withdraw_orders')
                ->where('player_id', $playerId)
                ->where('processed_at', '>=', $yesterday)
                ->where('processed_at', '<=', $now)
                ->where('audit_status', 4) // 只计算成功订单
                ->count();
            // 获取所有成功提款总额
            $totalWithdrawAmount = \think\Db::table('withdraw_orders')
                ->where('player_id', $playerId)
                ->where('audit_status', 4) // 只计算成功订单
                ->sum('amount');

            // 验证每日最大提款金额
            $dailyMaxWithdrawAmount = isset($vipConfig['daily_max_withdraw_amount']) && $vipConfig['daily_max_withdraw_amount'] > 0
                ? $vipConfig['daily_max_withdraw_amount']
                : config('withdrawal.daily_limit');
            frontendLog::info('提款请求：每日最大提款金额: dailyMaxWithdrawAmount=' . $dailyMaxWithdrawAmount.
            ', last24HoursTotal=' . $last24HoursTotal .
            ', last24HoursTimes=' . $last24HoursTimes .
            ', totalWithdrawAmount=' .$totalWithdrawAmount);
            
            // 虚拟玩家不受每日提款金额限制
            if (!$isVirtualPlayer && ($last24HoursTotal + $amount) > $dailyMaxWithdrawAmount) {
                FrontendLog::info('提款请求：提款金额超过每日提款上限: amount=' . $amount . ', last24HoursTotal=' . $last24HoursTotal . ', dailyMaxWithdrawAmount=' . $dailyMaxWithdrawAmount);
                return $this->getReturnJson(109, [number_format($dailyMaxWithdrawAmount - $last24HoursTotal, 2)]);
            } elseif ($isVirtualPlayer && ($last24HoursTotal + $amount) > $dailyMaxWithdrawAmount) {
                FrontendLog::info('虚拟玩家提款请求：金额超过每日提款上限，但不受限制: amount=' . $amount . ', last24HoursTotal=' . $last24HoursTotal . ', dailyMaxWithdrawAmount=' . $dailyMaxWithdrawAmount);
            }

            // 验证每日提款次数
            $dailyWithdrawalTimes = isset($vipConfig['daily_withdrawal_times']) ? intval($vipConfig['daily_withdrawal_times']) : 10;
            
            // 虚拟玩家不受每日提款次数限制
            if (!$isVirtualPlayer && $last24HoursTimes >= $dailyWithdrawalTimes) {
                FrontendLog::info('提款请求：提款次数超过每日提款次数上限: last24HoursTimes=' . $last24HoursTimes . ', dailyWithdrawalTimes=' . $dailyWithdrawalTimes);
                return $this->getReturnJson(110, [$dailyWithdrawalTimes]);
            } elseif ($isVirtualPlayer && $last24HoursTimes >= $dailyWithdrawalTimes) {
                FrontendLog::info('虚拟玩家提款请求：次数超过每日提款次数上限，但不受限制: last24HoursTimes=' . $last24HoursTimes . ', dailyWithdrawalTimes=' . $dailyWithdrawalTimes);
            }

            // 验证最大提款金额
            $maxWithdrawAmount = isset($vipConfig['max_withdraw_amount']) && $vipConfig['max_withdraw_amount'] > 0
                ? $vipConfig['max_withdraw_amount']
                : PHP_FLOAT_MAX;

            // 虚拟玩家不受最大提款金额限制
            if (!$isVirtualPlayer && ($totalWithdrawAmount + $amount) > $maxWithdrawAmount) {
                FrontendLog::info('提款请求：提款金额超过最大提款金额: amount=' . $amount . ', totalWithdrawAmount=' . $totalWithdrawAmount . ', maxWithdrawAmount=' . $maxWithdrawAmount);
                return $this->getReturnJson(111, [number_format($maxWithdrawAmount, 2)]);
            } elseif ($isVirtualPlayer && ($totalWithdrawAmount + $amount) > $maxWithdrawAmount) {
                FrontendLog::info('虚拟玩家提款请求：金额超过最大提款金额，但不受限制: amount=' . $amount . ', totalWithdrawAmount=' . $totalWithdrawAmount . ', maxWithdrawAmount=' . $maxWithdrawAmount);
            }
        } catch (\Exception $e) {
            FrontendLog::error('提款请求：验证每日提款限额错误: ' . $e->getMessage(), 'error');
            // 继续处理，不阻止提款
        }



        // 获取玩家类型
        $playerType = Session::get('player_type') ?: 'regular';
        $isVirtualPlayer = ($playerType === 'virtual');
                
        FrontendLog::info('提款请求：玩家类型: ' . $playerType);
        
        // 如果是虚拟玩家，跳过提款密码验证
        if (!$isVirtualPlayer) {
            // 验证提款密码是否正确
            try {
                // 获取玩家的取款密码
                $player = \think\Db::table('players')
                    ->where('id', $playerId)
                    ->field('withdraw_password')
                    ->find();

                if (!$player || empty($player['withdraw_password'])) {
                    FrontendLog::info('提款请求:提款密码未设置');
                    return $this->getReturnJson(124);
                }

                // 验证密码是否正确
                if ($pin !== $player['withdraw_password']) {
                    FrontendLog::info('提款请求：提款密码不正确: pin=' . $pin);
                    return $this->getReturnJson(112);
                }
            } catch (\Exception $e) {
                FrontendLog::error('提款请求：验证提款密码错误: ' . $e->getMessage(), 'error');
                return $this->getReturnJson(113);
            }
        } else {
            // 虚拟玩家只需要验证PIN是否为6位数字
            if (empty($pin) || strlen($pin) != 6 || !is_numeric($pin)) {
                FrontendLog::info('提款请求：虚拟玩家提款密码格式不正确: pin=' . $pin);
                return $this->getReturnJson(103);
            }
            FrontendLog::info('提款请求：虚拟玩家提款密码验证通过');
        }

        // 验证提款账户是否存在
        $withdrawAccount = null;
        try {
            // 获取玩家类型，判断是否为虚拟玩家
            $playerType = Session::get('player_type') ?: 'regular';
            $isVirtualPlayer = ($playerType === 'virtual');
            
            if ($isVirtualPlayer) {
                // 虚拟玩家使用虚拟提款账户
                FrontendLog::info('提款请求：虚拟玩家使用虚拟提款账户: player_id=' . $playerId);
                
                // 使用辅助方法生成虚拟提款账户
                $withdrawAccount = $this->generateVirtualWithdrawAccount($playerId);
            } else {
                // 普通玩家需要验证提款账户
                $withdrawAccount = \think\Db::table('player_withdraw_accounts')
                    ->where('id', $accountId)
                    ->where('player_id', $playerId)
                    ->find();

                if (!$withdrawAccount) {
                    FrontendLog::info('提款请求：提款账户不存在: accountId=' . $accountId);
                    return $this->getReturnJson(114);
                }
            }
        } catch (\Exception $e) {
            FrontendLog::error('提款请求：验证提款账户错误: ' . $e->getMessage(), 'error');
            return $this->getReturnJson(115);
        }

        // 计算手续费
        $serviceFee = 0.00;

        // 获取玩家最后一条备注
        $latestRemark = null;
        try {
            $latestRemark = \think\Db::table('player_remarks')
                ->where('player_id', $playerId)
                ->order('id', 'desc')
                ->value('content');

            if ($latestRemark) {
                FrontendLog::log('提款请求：获取到玩家最后一条备注: ' . $latestRemark);
            } else {
                FrontendLog::log('提款请求：玩家没有备注记录');
            }
        } catch (\Exception $e) {
            FrontendLog::error('提款请求：获取玩家备注错误: ' . $e->getMessage(), 'error');
            // 继续处理，不影响提款流程
        }

        // 创建提款订单
        try {
            // 获取玩家类型
            $playerType = Session::get('player_type') ?: 'regular';
            $isVirtualPlayer = ($playerType === 'virtual');
            
            // 虚拟玩家和普通玩家的处理逻辑不同
            if (!$isVirtualPlayer) {
                // 普通玩家 - 正常流程
                // 生成订单号
                // 准备订单数据
                $orderData = [
                    'player_id' => $playerId,
                    'referrer_id' => $playerData['referrer_id'], // 上级ID
                    'agent_id' => $playerData['agent_id'], // 业务员ID,
                    'channel_code' => $channelCode, // 通道代码
                    'amount' => $amount, // 提现金额
                    'service_fee' => $serviceFee, // 计算的手续费
                    'audit_status' => 1, // 默认设置为1（已加入工单,待审核）
                    'remark' => $latestRemark, // 填充玩家最后一条备注
                    'operator_name' => null, // 操作人
                    'operator_type' => 0, // 操作人类型: 0-管理员
                    'third_order_no' => null, // 第三方订单号
                    'created_at' => date('Y-m-d H:i:s'), // 提交时间
                ];

                // 保存订单数据
                $result = \think\Db::table('withdraw_orders')->insert($orderData);
                FrontendLog::log('提款请求：玩家提交提款请求: player_id=' . $playerId . ', amount=' . $amount . ', account_id=' . $accountId , 'info');
                if (!$result) {
                    return $this->getReturnJson(121);
                }

                // 冻结用户余额
                $updateResult = \think\Db::table('players')
                    ->where('id', $playerId)
                    ->setDec('balance', $amount); // 减少余额

                if (!$updateResult) {
                    // 如果冻结余额失败，回滚订单
                    \think\Db::table('withdraw_orders')
                        ->where('player_id', $playerId)
                        ->where('amount', $amount)
                        ->where('created_at', $orderData['created_at'])
                        ->delete();

                    return $this->getReturnJson(122);
                }

                // 记录余额变动日志
                try {
                    // 获取提款交易类型ID
                    $withdrawTransactionType = 9; // 默认值，9代表提款

                    // 准备余额变动日志数据
                    $balanceLogData = [
                        'player_id' => $playerId,
                        'amount' => -$amount, // 负数表示减少
                        'balance_before' => $playerData['balance'], // 变动前余额
                        'balance_after' => $playerData['balance'] - $amount, // 变动后余额
                        'balance_type' => 1, // 账户余额
                        'transaction_type' => $withdrawTransactionType, // 交易类型代码
                        'remark' => '提款申请: ', // 备注中包含订单号
                        'created_at' => date('Y-m-d H:i:s')
                    ];

                    // 插入余额变动日志
                    \think\Db::table('player_balance_logs')->insert($balanceLogData);
                    FrontendLog::log('提款请求：记录余额变动日志成功: player_id=' . $playerId . ', amount=' . (-$amount), 'info');
                } catch (\Exception $e) {
                    FrontendLog::error('提款请求：记录余额变动日志错误: ' . $e->getMessage(), 'error');
                    // 继续处理，不影响提款流程
                }
            } else {
                // 虚拟玩家 - 简化流程
                FrontendLog::log('提款请求：虚拟玩家提交提款请求: player_id=' . $playerId . ', amount=' . $amount . ', account_id=' . $accountId , 'info');
                
                // 减少虚拟玩家余额
                $updateResult = \think\Db::table('virtual_players')
                    ->where('id', $playerId)
                    ->setDec('balance', $amount); // 减少余额
                
                if (!$updateResult) {
                    FrontendLog::error('提款请求：虚拟玩家余额扣除失败: player_id=' . $playerId . ', amount=' . $amount, 'error');
                    return $this->getReturnJson(122);
                }
                
                // 使用session保存虚拟玩家的提款状态
                $virtualWithdrawals = Session::get('virtual_withdrawals') ?: [];
                
                // 生成一个纯数字的ID，以增加真实性
                $timestamp = time();
                $randomNum = mt_rand(10, 99);
                $withdrawId = intval($timestamp . $randomNum); // 合并时间戳和随机数生成纯数字ID
                
                $virtualWithdrawal = [
                    'id' => $withdrawId,
                    'player_id' => $playerId,
                    'amount' => $amount,
                    'account_id' => $accountId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'status' => '处理中' // 可以根据需要设置不同的状态
                ];
                
                $virtualWithdrawals[] = $virtualWithdrawal;
                Session::set('virtual_withdrawals', $virtualWithdrawals);
                
                FrontendLog::log('提款请求：虚拟玩家提款状态已保存到session: ' . json_encode($virtualWithdrawal), 'info');
                
                // 记录余额变动日志（仅记录，不实际写入数据库）
                try {
                    $withdrawTransactionType = 9; // 默认值，9代表提款
                    $virtualBalanceLogs = Session::get('virtual_balance_logs') ?: [];
                    
                    // 生成一个纯数字的ID，以增加真实性
                    $timestamp = time();
                    $randomNum = mt_rand(10, 99);
                    $logId = intval($timestamp . $randomNum) + 1; // 合并时间戳和随机数生成纯数字ID，+1以区别于提款记录ID
                    
                    $balanceLogData = [
                        'id' => $logId,
                        'player_id' => $playerId,
                        'amount' => -$amount, // 负数表示减少
                        'balance_before' => $playerData['balance'], // 变动前余额
                        'balance_after' => $playerData['balance'] - $amount, // 变动后余额
                        'balance_type' => 1, // 账户余额
                        'transaction_type' => $withdrawTransactionType, // 交易类型代码
                        'remark' => '虚拟玩家提款', 
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    
                    $virtualBalanceLogs[] = $balanceLogData;
                    Session::set('virtual_balance_logs', $virtualBalanceLogs);
                    
                    FrontendLog::log('提款请求：虚拟玩家余额变动日志已保存到session: ' . json_encode($balanceLogData), 'info');
                } catch (\Exception $e) {
                    FrontendLog::error('提款请求：记录虚拟玩家余额变动日志错误: ' . $e->getMessage(), 'error');
                }
            }
            
            // 提交成功
            return $this->getReturnJson(1, ['amount' => $amount]);
        } catch (\Exception $e) {
            FrontendLog::error('提款请求：提交提款请求错误: ' . $e->getMessage(), 'error');
            return $this->getReturnJson(123);
        }
    }

    /**
     * 设置提款密码页面
     */
    public function password_setup()
    {
        $loginStatus = Player::checkLoginStatus();

        // 登录检查 - 简化逻辑
        if ($loginStatus['code'] == 0) {
            // 显示登录提示
            $this->redirect(url('', ['login' => 1]));
            return;
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];

        // 将玩家数据传递给视图
        $this->view->assign('player', $playerData);

        return $this->view->fetch('password_setup');
    }

    /**
     * 设置提款密码
     * @return \think\response\Json
     */
    public function setupWithdrawPassword()
    {
        // 如果不是POST请求，直接返回错误
        if (!$this->request->isPost()) {
            return $this->getReturnJson(404);
        }

        // 验证登录状态
        $loginStatus = Player::checkLoginStatus();
        if ($loginStatus['code'] == 0) {
            return $this->getReturnJson(140);
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 获取请求参数
        $password = $this->request->post('password', '', 'trim,strip_tags');

        // 参数验证
        if (empty($password) || strlen($password) != 6 || !is_numeric($password)) {
            return $this->getReturnJson(103);
        }

        try {
            // 更新玩家提款密码
            // 实际应用中应该对密码进行加密处理
            $result = \think\Db::table('players')
                ->where('id', $playerId)
                ->update(['withdraw_password' => $password]);

            if ($result) {
                return $this->getReturnJson(1);
            } else {
                return $this->getReturnJson(127);
            }
        } catch (\Exception $e) {
            return $this->getReturnJson(128, [], ['msg' => __('Failed to set password') . ': ' . $e->getMessage()]);
        }
    }

    /**
     * 获取账户列表的AJAX方法
     * @return \think\response\Json
     */
    public function getAccounts()
    {
        // 判断是否是AJAX请求
        if (!$this->request->isAjax()) {
            return $this->getReturnJson(404);
        }

        // 验证登录状态
        $loginStatus = Player::checkLoginStatus();
        if ($loginStatus['code'] == 0) {
            return $this->getReturnJson(140);
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 获取玩家提款账户
        $withdrawAccounts = [];
        try {
            $withdrawAccounts = \think\Db::table('player_withdraw_accounts')
                ->where('player_id', $playerId)
                ->select();
        } catch (\Exception $e) {
            // 发生错误时记录日志
            \think\Log::record('获取玩家提款账户错误: ' . $e->getMessage(), 'error');
            return $this->getReturnJson(125);
        }

        return $this->getReturnJson(1, [], $withdrawAccounts);
    }

    /**
     * 验证取款密码
     * @return \think\response\Json
     */
    public function verifyWithdrawPassword()
    {
        // 如果不是POST请求，直接返回错误
        if (!$this->request->isPost()) {
            return $this->getReturnJson(404);
        }

        // 验证登录状态
        $loginStatus = Player::checkLoginStatus();
        if ($loginStatus['code'] == 0) {
            return $this->getReturnJson(140);
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 获取请求参数
        $password = $this->request->post('password', '', 'trim,strip_tags');

        // 参数验证
        if (empty($password) || strlen($password) != 6 || !is_numeric($password)) {
            return $this->getReturnJson(103);
        }

        // 验证密码是否正确
        try {
            // 获取玩家的取款密码
            $player = \think\Db::table('players')
                ->where('id', $playerId)
                ->field('withdraw_password')
                ->find();

            if (!$player || empty($player['withdraw_password'])) {
                return $this->getReturnJson(124);
            }

            // 验证密码是否正确
            if ($password !== $player['withdraw_password']) {
                return $this->getReturnJson(112);
            }

            // 密码验证成功
            return $this->getReturnJson(1);
        } catch (\Exception $e) {
            // 发生错误时记录日志
            \think\Log::record('验证取款密码错误: ' . $e->getMessage(), 'error');
            return $this->getReturnJson(126);
        }
    }

    /**
     * 添加PIX账户
     * @return \think\response\Json
     */
    public function addPixAccount()
    {
        // 如果不是POST请求，直接返回错误
        if (!$this->request->isPost()) {
            return $this->getReturnJson(404);
        }

        // 验证登录状态
        $loginStatus = Player::checkLoginStatus();
        if ($loginStatus['code'] == 0) {
            return $this->getReturnJson(140);
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 获取请求参数
        $accountName = $this->request->post('name', '', 'trim,strip_tags'); // 真实姓名
        $accountType = $this->request->post('type', '', 'trim,strip_tags'); // 账户类型: cpf, phone, email, cnpj
        $accountNumber = $this->request->post('account', '', 'trim,strip_tags'); // 账号
        $pixNumber = $this->request->post('cpf', '', 'trim,strip_tags'); // PIX号码

        // 参数验证
        if (empty($accountName)) {
            return $this->getReturnJson(100);
        }

        if (empty($accountNumber)) {
            return $this->getReturnJson(101);
        }

        // 如果是CPF类型，验证CPF号码
        if ($accountType == 'cpf' && empty($pixNumber)) {
            return $this->getReturnJson(102);
        }

        // 验证CPF格式
        if ($accountType == 'cpf' && !preg_match('/^\d{11}$/', $pixNumber)) {
            return $this->getReturnJson(102);
        }

        // 检查是否已存在同类型的PIX账户
        try {

            // 创建新账户
            $accountData = [
                'player_id' => $playerId,
                'account_name' => $accountName,
                'account_type' => $accountType,
                'account_number' => $accountNumber,
                'pix_number' => $accountType === 'cpf' ? $pixNumber : $accountNumber,
                'is_verified' => 0, // 默认未验证
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 检查是否是第一个账户，如果是则设为默认账户
            $accountCount = \think\Db::table('player_withdraw_accounts')
                ->where('player_id', $playerId)
                ->count();

            // 保存账户数据
            $result = \think\Db::table('player_withdraw_accounts')->insert($accountData);

            if ($result) {
                return $this->getReturnJson(1);
            } else {
                return $this->getReturnJson(104);
            }
        } catch (\Exception $e) {
            // 发生错误时记录日志
            \think\Log::record('添加PIX账户错误: ' . $e->getMessage(), 'error');
            return $this->getReturnJson(105);
        }
    }

    /**
     * 获取提款记录的AJAX方法
     * @return \think\response\Json
     */
    public function getWithdrawRecords()
    {
        // 判断是否是AJAX请求
        if (!$this->request->isAjax()) {
            return $this->getReturnJson(404);
        }

        // 验证登录状态
        $loginStatus = Player::checkLoginStatus();
        if ($loginStatus['code'] == 0) {
            return $this->getReturnJson(140);
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 获取日期范围参数
        $dateRange = $this->request->get('date_range', 'today');

        // 设置时间范围
        $startTime = '';
        $endTime = date('Y-m-d H:i:s');

        switch ($dateRange) {
            case 'yesterday':
                $startTime = date('Y-m-d 00:00:00', strtotime('-1 day'));
                $endTime = date('Y-m-d 23:59:59', strtotime('-1 day'));
                break;
            case 'week':
                $startTime = date('Y-m-d 00:00:00', strtotime('-7 days'));
                break;
            case 'month':
                $startTime = date('Y-m-d 00:00:00', strtotime('-30 days'));
                break;
            case 'half_year':
                $startTime = date('Y-m-d 00:00:00', strtotime('-180 days'));
                break;
            case 'year':
                $startTime = date('Y-m-d 00:00:00', strtotime('-365 days'));
                break;
            case 'all':
                $startTime = '2000-01-01 00:00:00'; // 足够早的日期以包含所有记录
                break;
            case 'today':
            default:
                $startTime = date('Y-m-d 00:00:00');
                break;
        }

        // 查询提款记录
        $records = [];
        $totalAmount = 0;

        try {
            $records = \think\Db::table('withdraw_orders')
                ->where('player_id', $playerId)
                ->where('created_at', 'between', [$startTime, $endTime])
                ->field('id, amount, audit_status,third_order_no, channel_code,created_at, processed_at')
                ->order('created_at', 'desc')
                ->select();

            // 处理记录和计算总金额
            foreach ($records as &$record) {
                $totalAmount += $record['amount'];

                // 根据审核状态设置状态文本和状态码
                switch ($record['audit_status']) {
                    case 0: // 待加入工单
                    case 1: // 已加入工单,待审核
                        $record['status'] = 'pending';
                        break;
                    case 3: // 三方处理中
                        $record['status'] = 'processing';
                        break;
                    case 2: // 已审核
                    case 4: // 订单完成
                    case 8: // 虚拟支付
                        $record['status'] = 'completed';
                        break;
                    case 5: // 拒绝并退回金币
                    case 7: // 拒绝并没收金币
                        $record['status'] = 'rejected';
                        break;
                    case 6: // 处理失败并退回金币
                        $record['status'] = 'failed';
                        break;
                    default:
                        $record['status'] = 'pending';
                }

                // 格式化时间和金额
                $record['create_time'] = date('Y-m-d H:i:s', strtotime($record['created_at']));
                $record['amount'] = number_format($record['amount'], 2, '.', '');

                // 设置状态文本
                $record['status_text'] = __($record['status']);
            }
        } catch (\Exception $e) {
            // 发生错误时记录日志
            \think\Log::record('获取提款记录错误: ' . $e->getMessage(), 'error');
            return $this->getReturnJson(0, ['msg' => __('Failed to fetch records')]);
        }
        $domain = $this->request->domain();
        // 返回JSON格式的提款记录和总金额
        return $this->getReturnJson(1, [], [
            'records' => $records,
            'totalAmount' => number_format($totalAmount, 2, '.', ''),
            'operatorName' => $domain
        ]);
    }
}
