<?php

namespace app\index\controller;
use app\common\controller\Frontend;
use think\Request;

class Createiframe extends Frontend
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function _initialize()
    {
        parent::_initialize();

        // 确保所有页面加载event语言包
        $this->loadLang('createiframe');
        $this->request->filter('trim,strip_tags,htmlspecialchars');
    }

    /**
     * 创建游戏iframe
     * 接收参数方式：
     * 1. GET方式: /index/createiframe/index?gamecode=xxx
     * 2. 路由方式: /index/createiframe/index/gamecode/xxx
     */
    public function index()
    {
        // 获取游戏ID参数
        $gameid = $this->request->param('gameid', 0);
        // $gamecode = $this->request->param('gamecode', '');

        // 如果没有提供游戏参数，返回错误提示
        if (empty($gameid)) {
            $this->view->assign('error_msg', '游戏ID或代码不能为空');
            return $this->view->fetch();
        }

        try {
            // 获取游戏URL
            // 如果有gameid，从creategame控制器获取游戏代码，然后获取URL
            if (!empty($gameid)) {
                // 创建creategame控制器实例
                $creategameController = new \app\index\controller\Creategame();

                // 获取游戏代码
                $gamecode = $creategameController->getGameCode($gameid);

                if (empty($gamecode)) {
                    throw new \Exception('找不到对应的游戏代码');
                }

                // 实例化Index控制器，调用getGameURL方法
                $indexController = new Index();

                // 获取玩家类型，判断是否是虚拟玩家
                $playerType = \think\Session::get('player_type');
                $isVirtual = ($playerType == 'virtual');

                // 调用getGameURL方法，传递虚拟玩家标识
                $gameURL = $indexController->getGameURL($gamecode, $isVirtual);

                // 记录日志
                \app\common\library\FrontendLog::info('游戏URL请求: gamecode=' . $gamecode . ', player_type=' . $playerType . ', is_virtual=' . ($isVirtual ? 'true' : 'false'));

                if ($gameURL === -1) {
                    throw new \Exception('获取游戏URL失败');
                }

                // 记录日志
                \app\common\library\FrontendLog::info('游戏iframe生成成功: gameid=' . $gameid . ', gameurl=' . $gameURL);

                // 将URL赋值给模板
                $this->view->assign('game_url', $gameURL);
                $this->view->assign('gameid', $gameid);
            }

        } catch (\Exception $e) {
            // 发生异常时记录错误
            \app\common\library\FrontendLog::error('游戏iframe生成失败: ' . $e->getMessage());
            $this->view->assign('error_msg', '游戏加载失败: ' . $e->getMessage());
        }

        // 返回iframe视图
        return $this->view->fetch();
    }


}