<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\index\model\Player;
use app\index\model\Announcement;
use app\index\controller\common\IpBlacklist;
use think\Session;
use app\common\library\FrontendLog;
use fast\Http;

class Index extends Frontend
{
    protected $noNeedLogin = ['index', 'playerLogin', 'playerRegister', 'depositPopup', 'getDepositLink'];
    // protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';


    public function _initialize()
    {
        parent::_initialize();
        //移除HTML标签
        $this->request->filter('trim,strip_tags,htmlspecialchars');
    }


    public function index()
    {
        // 获取并保存URL中的推广参数到session
        $this->saveReferralParamsToSession();

        // 检查是否有直接登录令牌
        $directLoginToken = $this->request->param('direct_login_token', '');
        if (!empty($directLoginToken)) {
            // 处理直接登录令牌
            $this->processDirectLoginToken($directLoginToken);
        }

        // 验证玩家登录状态
        $loginStatus = Player::checkLoginStatus();
        $loggedIn = isset($loginStatus['code']) && $loginStatus['code'] === 1;

        // 将登录状态传递给视图
        $this->view->assign('isLoggedIn', $loggedIn);
        if ($loggedIn && isset($loginStatus['data'])) {
            $this->view->assign('player', $loginStatus['data']);
        }
        // 获取社交媒体链接配置并传递给视图
        $socialMedia = [
            'instagram' => config('index.social_media.instagram'),
            'telegram' => config('index.social_media.telegram')
        ];
        $this->view->assign('socialMedia', $socialMedia);

        // 检查是否需要显示登录弹窗（从其他页面重定向过来）
        $showLoginPopup = $this->request->param('login', 0);
        $this->view->assign('showLoginPopup', $showLoginPopup == 1);

        // 检查是否有需要显示的 toast 消息
        $toastMessage = Session::get('toast_message');
        $toastType = Session::get('toast_type', 'info');

        if ($toastMessage) {
            // 将 toast 消息传递给视图
            $this->view->assign('toastMessage', $toastMessage);
            $this->view->assign('toastType', $toastType);

            // 清除会话中的 toast 消息，避免重复显示
            Session::delete('toast_message');
            Session::delete('toast_type');

            // 记录日志
            FrontendLog::info('显示 toast 消息: ' . $toastMessage . ', 类型: ' . $toastType);
        }

        // 获取公告数据（带错误处理）
        try {
            // 先获取所有公告（用于调试）
            $allAnnouncements = Announcement::getAllAnnouncements();
            // 再获取符合条件的活跃公告
            $announcements = Announcement::getActiveAnnouncements(3);
            $this->view->assign('announcements', $announcements);

            // 如果没有符合条件的活跃公告，则使用所有公告的前3条
            if (empty($announcements) && !empty($allAnnouncements)) {
                FrontendLog::info('没有活跃公告，使用所有公告的前3条');
                $announcements = array_slice($allAnnouncements, 0, 3);
                $this->view->assign('announcements', $announcements);
            }

            // 检查是否显示公告弹窗 - 同一IP每天只显示一次
            $showNoticePopup = false;
            $clientIP = $this->request->ip();
            // 使用md5处理IP地址以避免特殊字符
            $sessionKey = 'notice_popup_' . md5($clientIP);
            $lastPopupTime = \think\Session::get($sessionKey);
            $today = date('Y-m-d');

            // 首先检查是否有上次显示记录
            if (!$lastPopupTime) {
                $showNoticePopup = true;
            }
            // 然后检查记录的时间是否是今天
            else if (substr($lastPopupTime, 0, 10) !== $today) {
                $showNoticePopup = true;
            }


            // 如果需要显示，记录当前时间到session
            if ($showNoticePopup) {
                $currentTime = date('Y-m-d H:i:s');
                \think\Session::set($sessionKey, $currentTime);
                FrontendLog::info('设置公告弹窗已显示记录: IP=' . $clientIP . ', 时间=' . $currentTime);

                // 如果没有活跃公告，不显示弹窗
                if (empty($announcements)) {
                    $showNoticePopup = false;
                }
            }

            // 如果请求显示登录弹窗，则不显示公告弹窗
            if ($showLoginPopup) {
                $showNoticePopup = false;
            }
            // $showNoticePopup = true;
            $this->view->assign('showNoticePopup', $showNoticePopup);

        } catch (\Exception $e) {
            // 出错时使用空数组
            $this->view->assign('announcements', []);
            $this->view->assign('showNoticePopup', false);
            // 记录详细错误日志
            FrontendLog::error('错误堆栈: ' . $e->getTraceAsString());
        }

        // 调试：检查视图变量
        FrontendLog::info('视图变量检查: ' . json_encode([
            'isLoggedIn' => isset($this->view->isLoggedIn) ? $this->view->isLoggedIn : '未设置',
            'announcements_count' => isset($this->view->announcements) ? count($this->view->announcements) : '未设置',
            'showNoticePopup' => isset($this->view->showNoticePopup) ? $this->view->showNoticePopup : '未设置'
        ]));

        // 获取激活的游戏提供商列表
        try {
            // 获取激活的游戏提供商名称列表
            $gameList = \app\index\model\GameProvider::getActiveProviders(true);

            // 获取完整的游戏列表数据（包括Popular分类和游戏数据）
            $activeGameList = \app\index\model\GameProvider::getActiveGameList(9);
        } catch (\Exception $e) {
            // 出错时使用空数组
            $gameList = [];
            $activeGameList = [];
            // 记录错误日志
            FrontendLog::error('获取游戏提供商列表失败: ' . $e->getMessage());
            FrontendLog::error('错误堆栈: ' . $e->getTraceAsString());
        }

        $gameData = $this->loadAndAssignGameData();
        // 将游戏列表传递给视图
       $this->view->assign('gameList', $gameData['gameList']);
       $this->view->assign('activeGameList', $gameData['activeGameList']);

        // 获取最低投注金额并传递给视图
        $betMinAmount = config('bet.min_amount') ?: '0.5';
        $this->view->assign('betMinAmount', $betMinAmount);

        // 获取充值金额列表并传递给视图
        $amountList = \app\index\model\Payment::getAmountList();
        $this->view->assign('amountList', $amountList);

        // 获取支付渠道列表并传递给视图
        $paymentChannels = \app\index\model\PaymentChannel::getActiveChannels('deposit');
        $this->view->assign('paymentChannels', $paymentChannels);

        // 检查玩家是否是首次充值（用于充值弹窗中的角标显示）
        $isFirstDeposit = false;
        if ($loggedIn && isset($loginStatus['data'])) {
            try {
                // 查询是否存在成功的充值记录
                $depositCount = \think\Db::table('deposit_orders')
                    ->where('player_id', $loginStatus['data']['id'])
                    ->where('payment_status', 1) // 1表示支付成功
                    ->count();

                // 如果不存在成功的充值记录，则是首次充值
                $isFirstDeposit = $depositCount == 0;
            } catch (\Exception $e) {
                // 记录错误日志
                FrontendLog::error('检查玩家首次充值状态失败: ' . $e->getMessage());
            }
        }
        $this->view->assign('isFirstDeposit', $isFirstDeposit);
        // 从.env文件读取首充阈值，默认为25
        $firstDepositThreshold = \think\Env::get('app.first_deposit_threshold', 25);
        $this->view->assign('firstDepositThreshold', $firstDepositThreshold);

        // 返回视图
        return $this->view->fetch();
    }

    /**
     * 玩家登录处理
     */
    public function playerLogin()
    {
        if ($this->request->isPost()) {
            $phone_number = $this->request->post('phone_number');
            $password = $this->request->post('password');
            FrontendLog::log('登录数据'.json_encode($this->request->post()));
            // 调用Player模型的checkLogin方法并直接返回结果
            $result = Player::checkLogin($phone_number, $password);
            return json($result);
        }

        return json(['code' => 0, 'msg' => '非法请求']);
    }

    /**
     * 玩家退出登录
     */
    public function playerLogout()
    {
        // 记录日志
        \app\common\library\FrontendLog::info('玩家退出登录开始: IP=' . $this->request->ip() . ', 请求类型=' . ($this->request->isAjax() ? 'AJAX' : '普通'));

        try {
            // 获取当前会话中的所有数据，用于日志记录
            $sessionData = \think\Session::get();
            \app\common\library\FrontendLog::info('退出前的会话数据: ' . json_encode($sessionData, JSON_UNESCAPED_UNICODE));

            // 使用模型方法退出登录
            $result = Player::logout();

            // 清除所有会话数据
            \think\Session::clear();

            // 清除可能存在的缓存
            if (function_exists('session_destroy')) {
                session_destroy();
            }

            // 清除所有cookie
            $cookies = $this->request->cookie();
            foreach ($cookies as $name => $value) {
                cookie($name, null);
            }

            // 特别处理think_var语言cookie
            cookie('think_var', null);

            // 设置提示信息，将在首页显示
            \think\Session::set('toast_message', '您已成功退出登录');
            \think\Session::set('toast_type', 'success');

            // 记录日志
            \app\common\library\FrontendLog::info('玩家退出登录成功: IP=' . $this->request->ip());

            // 添加时间戳参数，防止缓存
            $timestamp = time();
            $redirectUrl = '?t=' . $timestamp;

            // 根据请求类型返回不同响应
            if ($this->request->isAjax()) {
                // AJAX请求返回JSON
                return json(['code' => 1, 'msg' => '退出成功', 'url' => $redirectUrl]);
            } else {
                // 普通请求重定向到首页
                $this->redirect($redirectUrl);
                return null;
            }
        } catch (\Exception $e) {
            // 记录错误日志
            \app\common\library\FrontendLog::error('玩家退出登录出错: ' . $e->getMessage() . ', IP=' . $this->request->ip());

            // 即使出错，也尝试清除会话
            \think\Session::clear();

            // 设置提示信息，将在首页显示
            \think\Session::set('toast_message', '退出登录过程中出现错误，但您已被登出');
            \think\Session::set('toast_type', 'warning');

            // 添加时间戳参数，防止缓存
            $timestamp = time();
            $redirectUrl = '?t=' . $timestamp;

            // 根据请求类型返回不同响应
            if ($this->request->isAjax()) {
                return json(['code' => 0, 'msg' => '退出过程中出错，请刷新页面', 'url' => $redirectUrl]);
            } else {
                $this->redirect($redirectUrl);
                return null;
            }
        }
    }

    /**
     * 检查登录状态API
     */
    public function checkLoginStatus()
    {
        // 使用模型检查登录状态并直接返回结果
        $result = Player::checkLoginStatus();
        return json($result);
    }

    /**
     * 刷新余额API
     */
    public function refreshBalance()
    {
        // 使用模型刷新余额并直接返回结果
        $result = Player::refreshBalance();
        return json($result);
    }

    /**
     * API方法: 记录用户已关闭弹窗
     * 当用户手动关闭弹窗时，前端会调用此方法
     */
    public function markNoticePopupClosed()
    {
        try {
            // 记录请求来源信息
            $clientIP = $this->request->ip();
            $userAgent = $this->request->server('HTTP_USER_AGENT');
            $referer = $this->request->server('HTTP_REFERER', '无');

            FrontendLog::info('收到关闭公告弹窗请求: IP=' . $clientIP . ', UA=' . $userAgent . ', 来源=' . $referer);

            $sessionKey = 'notice_popup_' . md5($clientIP);
            $currentTime = date('Y-m-d H:i:s');

            // 检查是否已经有记录
            $lastPopupTime = \think\Session::get($sessionKey);
            if ($lastPopupTime) {
                FrontendLog::info('此IP今天已经关闭过弹窗: ' . $lastPopupTime . '，更新时间');
            }

            // 记录关闭时间
            \think\Session::set($sessionKey, $currentTime);
            FrontendLog::info('用户手动关闭公告弹窗已记录: IP=' . $clientIP . ', 时间=' . $currentTime);

            // 返回成功响应
            return json(['code' => 1, 'msg' => '记录成功', 'data' => [
                'time' => $currentTime,
                'session_key' => $sessionKey
            ]]);
        } catch (\Exception $e) {
            FrontendLog::error('记录公告弹窗关闭状态出错: ' . $e->getMessage());
            FrontendLog::error('错误堆栈: ' . $e->getTraceAsString());
            return json(['code' => 0, 'msg' => '记录失败，服务器错误']);
        }
    }

    /**
     * 充值弹窗
     * @return \think\response\View
     */
    public function depositPopup()
    {
        // 确保加载index语言包
        // \think\Lang::load(APP_PATH . 'index/lang/' . $this->request->langset() . '/index.php');

        // 检查登录状态
        $loginStatus = Player::checkLoginStatus();
        if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
            return json(['code' => 0, 'msg' => __('Please login first')]);
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];

        // 获取充值金额列表
        $amountList = \app\index\model\Payment::getAmountList();

        // 获取支付渠道列表
        $paymentChannels = \app\index\model\PaymentChannel::getActiveChannels('deposit');

        // 检查玩家是否是首次充值
        $isNotFirstDeposit = false;
        try {
            // 查询是否存在成功的充值记录
            $depositCount = \think\Db::table('deposit_orders')
                ->where('player_id', $playerData['id'])
                ->where('payment_status', 1) // 1表示支付成功
                ->count();

            // 如果存在成功的充值记录，则不是首次充值
            $isNotFirstDeposit = $depositCount > 0;
        } catch (\Exception $e) {
            // 记录错误日志
            FrontendLog::error('检查玩家首次充值状态失败: ' . $e->getMessage());
        }

        // 将数据传递给视图
        $this->view->assign('amountList', $amountList);
        $this->view->assign('paymentChannels', $paymentChannels);
        $this->view->assign('isNotFirstDeposit', $isNotFirstDeposit);
        $this->view->assign('player', $playerData);

        // 从.env文件读取首充阈值，默认为25
        $firstDepositThreshold = \think\Env::get('app.first_deposit_threshold', 25);
        $this->view->assign('firstDepositThreshold', $firstDepositThreshold);

        // 返回视图
        return $this->view->fetch('pop/deposit_popup');
    }

    /**
     * 获取充值链接
     * @return \think\response\Json
     */
    public function getDepositLink()
    {
        // 检查是否为POST请求
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        // 检查登录状态
        $loginStatus = Player::checkLoginStatus();
        if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
            return json(['code' => 0, 'msg' => '请先登录后再充值']);
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];

        // 检查玩家是否是首次充值（total_deposit为0）
        $useBonus = isset($playerData['total_deposit']) && floatval($playerData['total_deposit']) == 0;

        // 记录日志
        FrontendLog::info('玩家充值奖励检查: player_id=' . $playerData['id'] .
                         ', total_deposit=' . ($playerData['total_deposit'] ?? '未知') .
                         ', use_bonus=' . ($useBonus ? 'true' : 'false'));

        // 获取请求参数
        $amount = $this->request->post('amount', 0);
        $paymentMethod = $this->request->post('payment_method', '');

        // 参数验证
        if (empty($amount) || $amount < 10 || $amount > 20000) {
            return json(['code' => 0, 'msg' => '充值金额必须在10-20000之间']);
        }

        if (empty($paymentMethod)) {
            return json(['code' => 0, 'msg' => '请选择支付方式']);
        }

        // 从.env获取支付网关配置
        $merchantId = \think\Env::get('payment.merchant_id', '');
        $secretKey = \think\Env::get('payment.secret_key', '');
        $gatewayUrl = \think\Env::get('payment.gateway_url', '');

        if (empty($merchantId) || empty($secretKey) || empty($gatewayUrl)) {
            FrontendLog::error('支付网关配置缺失: merchant_id=' . $merchantId .
                             ', secret_key=' . (empty($secretKey) ? '缺失' : '已配置') .
                             ', gateway_url=' . $gatewayUrl);
            return json(['code' => 0, 'msg' => '支付网关配置错误，请联系客服']);
        }

        // 生成商户订单号
        $merchantOrderNo = date('YmdHis') . rand(1000, 9999) . $loginStatus['data']['id'];

        // 根据支付方式获取支付类型 - 强制使用 PIX_QRCODE
        $payType = 'PIX_QRCODE'; // 始终使用PIX扫码支付

        // 准备请求参数
        $params = [
            'merchantId' => $merchantId,
            'merchantOrderNo' => $merchantOrderNo,
            'amount' => $amount * 100, // 转换为分
            'payType' => $payType,
            'currency' => 'BRL', // 巴西雷亚尔
            'content' => '游戏充值',
            'clientIp' => $this->request->ip(),
            'callback' => $this->getCallbackUrl($this->request) . '/deposit/callback',
            'redirect' => $this->request->domain()
        ];

        // 生成签名
        $params['sign'] = $this->generatePaymentSign($params, $secretKey);

        try {
            // 发送请求到支付网关
            $apiUrl = rtrim($gatewayUrl, '/') . '/api/open/merchant/trade/create';
            $response = $this->sendPaymentRequest($apiUrl, $params);

            // 解析响应
            $responseData = json_decode($response, true);

            if (!$responseData || !isset($responseData['success'])) {
                FrontendLog::error('支付网关响应解析失败: ' . $response);
                return json(['code' => 0, 'msg' => '支付网关响应异常，请稍后再试']);
            }

            if ($responseData['success'] !== true) {
                FrontendLog::error('支付网关返回错误: ' . ($responseData['message'] ?? '未知错误') .
                                 ', errorCode=' . ($responseData['errorCode'] ?? ''));
                return json(['code' => 0, 'msg' => '支付创建失败: ' . ($responseData['message'] ?? '未知错误')]);
            }

            // 获取支付链接
            $paymentLink = $responseData['data']['payUrl'] ?? '';

            if (empty($paymentLink)) {
                FrontendLog::error('支付网关未返回支付链接: ' . json_encode($responseData));
                return json(['code' => 0, 'msg' => '未获取到支付链接，请稍后再试']);
            }
            // 创建充值订单记录
            $this->createDepositOrder($loginStatus['data']['id'], $amount, $useBonus, $paymentMethod, $merchantOrderNo, $responseData['data']['orderNo'] ?? '');

            // 记录日志
            FrontendLog::info('玩家请求充值链接成功: player_id=' . $loginStatus['data']['id'] .
                             ', amount=' . $amount . ', method=' . $paymentMethod .
                             ', use_bonus=' . ($useBonus ? 'true' : 'false') .
                             ', merchant_order_no=' . $merchantOrderNo .
                             ', link=' . $paymentLink);

            return json(['code' => 1, 'msg' => '获取成功', 'data' => ['link' => $paymentLink]]);

        } catch (\Exception $e) {
            FrontendLog::error('请求支付网关异常: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '支付系统异常，请稍后再试']);
        }
    }

    /**
     * 生成支付签名
     * @param array $params 请求参数
     * @param string $secretKey 密钥
     * @return string 签名
     */
    private function generatePaymentSign($params, $secretKey)
    {
        // 1. 过滤空值参数
        $filteredParams = array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        });

        // 2. 按键名升序排序
        ksort($filteredParams);

        // 3. 拼接参数
        $signStr = '';
        foreach ($filteredParams as $key => $value) {
            $signStr .= $key . '=' . $value . '&';
        }

        // 4. 添加密钥
        $signStr .= 'secret=' . $secretKey;

        // 5. MD5加密（小写）
        return md5($signStr);
    }

    /**
     * 发送支付请求
     * @param string $url 请求地址
     * @param array $params 请求参数
     * @return string 响应内容
     */
    private function sendPaymentRequest($url, $params)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded;charset=UTF-8'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $error = curl_error($ch);

        if ($error) {
            FrontendLog::error('CURL请求失败: ' . $error);
            throw new \Exception('网络请求失败: ' . $error);
        }

        curl_close($ch);
        return $response;
    }

    /**
     * 创建充值订单记录
     * @param int $playerId 玩家ID
     * @param float $amount 充值金额
     * @param bool $useBonus 是否使用奖励
     * @param string $paymentMethod 支付方式
     * @param string $merchantOrderNo 商户订单号
     * @param string $thirdOrderNo 第三方订单号
     * @return bool 是否成功
     */
    private function createDepositOrder($playerId, $amount, $useBonus, $paymentMethodId, $merchantOrderNo, $thirdOrderNo)
    {
        try {
            // 获取玩家信息
            $player = Player::get($playerId);
            if (!$player) {
                FrontendLog::error('创建充值订单失败: 玩家不存在, player_id=' . $playerId);
                return false;
            }

            // 计算赠送金额
            $bonusRate = 0.2; // 20%的赠送比例
            // 从环境变量中读取首充赠送门槛金额，默认为25
            $firstDepositThreshold = \think\Env::get('app.first_deposit_threshold', 25);
            $giftAmount = ($useBonus && $amount >= $firstDepositThreshold) ? round($amount * $bonusRate, 2) : 0;

            // 创建充值订单
            $order = new \app\index\model\DepositOrder();
            $order->player_id = $player->id;
            $order->referrer_id = $player->referrer_id; // 使用玩家的上级ID
            $order->agent_id = $player->agent_id; // 使用玩家代理id
            $order->channel_code = $paymentMethodId;
            $order->amount = $amount;
            $order->gift_amount = $giftAmount;
            $order->service_fee = 0; // 手续费默认为0
            $order->third_order_no = $thirdOrderNo;
            $order->merchant_order_no = $merchantOrderNo; // 保存商户订单号
            $order->payment_status = 0; // 设为待支付状态
            $order->remark = '玩家前台充值';
            $order->created_at = date('Y-m-d H:i:s');

            // 记录日志
            FrontendLog::info('创建充值订单: player_id=' . $player->id .
                             ', amount=' . $amount .
                             ', merchant_order_no=' . $merchantOrderNo .
                             ', third_order_no=' . $thirdOrderNo);

            // 保存订单
            if (!$order->save()) {
                FrontendLog::error('保存充值订单失败: player_id=' . $player->id .
                                 ', amount=' . $amount .
                                 ', merchant_order_no=' . $merchantOrderNo);
                return false;
            }

            return true;
        } catch (\Exception $e) {
            FrontendLog::error('创建充值订单异常: ' . $e->getMessage() .
                             ', player_id=' . $playerId .
                             ', amount=' . $amount .
                             ', merchant_order_no=' . $merchantOrderNo);
            return false;
        }
    }

    /**
     * 获取充值历史记录
     * @return \think\response\Json
     */
    public function getDepositHistory()
    {
        // 检查登录状态
        $loginStatus = Player::checkLoginStatus();
        if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
            return json(['code' => 0, 'msg' => '请先登录后再查看充值记录']);
        }

        // 获取玩家数据
        $playerData = $loginStatus['data'];

        // 获取请求参数
        $page = $this->request->get('page', 1);
        $pageSize = 10;
        $startDate = $this->request->get('start_date', '');
        $endDate = $this->request->get('end_date', '');
        FrontendLog::info('充值记录请求参数: page=' . $page . ', start_date=' . $startDate . ', end_date=' . $endDate);

        // 构建查询条件
        $where = [
            'player_id' => $playerData['id']
        ];

        // 添加日期筛选条件
        if ($startDate) {
            $where['paid_at'] = ['>=', $startDate . ' 00:00:00'];
        }
        if ($endDate) {
            if (isset($where['paid_at']) && is_array($where['paid_at'])) {
                $where['paid_at'][] = ['<=', $endDate . ' 23:59:59'];
            } else {
                $where['paid_at'] = ['<=', $endDate . ' 23:59:59'];
            }
        }

        try {
            // 启用SQL调试
            \think\Db::listen(function($sql, $time, $explain){
                FrontendLog::info('充值记录SQL: ' . $sql . ' [耗时:' . $time . 's]');
            });

            // 查询总充值金额
            $totalAmount = \think\Db::table('deposit_orders')
                ->where($where)
                ->where('payment_status', 1) // 只计算成功的充值
                ->sum('amount');

            // 查询充值记录总数
            $total = \think\Db::table('deposit_orders')
                ->where($where)
                ->count();

            // 查询分页记录，关联支付渠道表获取渠道名称
            $records = \think\Db::table('deposit_orders')
                ->alias('d')
                ->join(['payment_channels'=>'p'], 'd.channel_code = p.code', 'left')
                ->where($where)
                ->field('d.*, p.name as channel_name')
                ->order('d.paid_at desc')
                ->page($page, $pageSize)
                ->select();

            // 处理记录数据，添加状态文本
            foreach ($records as &$record) {
                // 添加状态显示文本
                switch ($record['payment_status']) {
                    case 0:
                        $record['status_text'] = __('pending');
                        break;
                    case 1:
                        $record['status_text'] = __('completed');
                        break;
                    case 2:
                        $record['status_text'] = __('failed');
                        break;
                    default:
                        $record['status_text'] = __('unknown');
                }

                // 格式化金额（保留两位小数，使用巴西格式）
                $record['formatted_amount'] = number_format($record['amount'], 2, ',', '.');

                // 确保渠道名称存在
                if (empty($record['channel_name'])) {
                    $record['channel_name'] = $record['channel_code'] ?? __('未知渠道');
                }
                frontendLog::info('充值记录渠道名称: ' . $record['channel_name']);
            }

            // 处理返回结果
            $data = [
                'list' => $records,
                'total' => $total,
                'totalAmount' => $totalAmount,
                'formattedTotalAmount' => number_format($totalAmount, 2, ',', '.'),
                'page' => $page,
                'pageSize' => $pageSize,
                'totalPage' => ceil($total / $pageSize)
            ];

            return json(['code' => 1, 'msg' => '获取成功', 'data' => $data]);
        } catch (\Exception $e) {
            FrontendLog::error('获取充值记录失败: ' . $e->getMessage());
            return json(['code' => 0, 'msg' => '获取充值记录失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 玩家注册处理
     */
    public function playerRegister()
    {
        // 检查是否为POST请求
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        // 获取表单数据
        $username = $this->request->post('username', '');
        $password = $this->request->post('password', '');
        $mobile = $this->request->post('mobile', '');
        $agree = $this->request->post('agree', 0);
        $inviteCodeFromPlayer = $this->request->post('invite_code', '');


        // 表单验证
        if (empty($username)) {
            return json(['code' => 0, 'msg' => __('username_empty')]);
        }

        if (empty($password)) {
            return json(['code' => 0, 'msg' => __('password_empty')]);
        }

        if ($agree != 1) {
            return json(['code' => 0, 'msg' => __('agree_error')]);
        }

        // 从session中获取推广参数
        $sessionIcId = Session::get('referral_ic');
        $sessionInviteCode = Session::get('referral_id');
        if(empty($inviteCodeFromPlayer) && empty($sessionIcId) && empty($sessionInviteCode)){
            // 生成玩家推广链接
            FrontendLog::info('注册失败: 邀请人和邀请码都为空');
            return ['code' => 110, 'msg' => 'É obrigatório ser convidado por um vendedor ou ter um código de convite'];
        }
        // 调用Player模型的register方法进行注册
        $result = Player::register($username, $password, $mobile, $inviteCodeFromPlayer);

        return json($result);
    }

    /**
     * 处理直接登录令牌
     * 从缓存中获取玩家信息并设置会话
     * @param string $token 直接登录令牌
     * @return bool 是否成功处理
     */
    protected function processDirectLoginToken($token)
    {
        // 记录日志
        \app\common\library\FrontendLog::info('处理直接登录令牌: ' . $token);

        // 从缓存中获取玩家信息
        $playerInfo = \think\Cache::get('direct_login_' . $token);

        // 如果缓存中没有找到玩家信息，或者已过期
        if (!$playerInfo) {
            \app\common\library\FrontendLog::warning('直接登录令牌无效或已过期: ' . $token);
            return false;
        }

        // 检查令牌是否已过期（5分钟有效期）
        if (time() - $playerInfo['time'] > 300) {
            \think\Cache::rm('direct_login_' . $token);
            \app\common\library\FrontendLog::warning('直接登录令牌已过期: ' . $token);
            return false;
        }

        // 设置前台登录所需的session变量
        \think\Session::set('player_id', $playerInfo['player_id']);
        \think\Session::set('player_phone_number', $playerInfo['phone_number']);
        \think\Session::set('player_lastactive', time());
        \think\Session::set('player_type', 'regular'); // 设置为普通玩家类型

        // 删除缓存中的令牌，防止重复使用
        \think\Cache::rm('direct_login_' . $token);

        // 记录日志
        \app\common\library\FrontendLog::info('直接登录成功: player_id=' . $playerInfo['player_id']);

        return true;
    }

    /**
     * 获取游戏URL
     * @param string $gamecode 游戏代码
     * @param bool $isVirtual 是否是虚拟玩家，默认为false
     * @return string|int 成功返回游戏URL，失败返回-1
     */
    public function getGameURL($gamecode, $isVirtual = false)
    {
        if (empty($gamecode)) {
            return -1;
        }

        try {
            $clientIP = $this->request->ip();

            // 根据是否为虚拟玩家选择不同的接口配置
            if ($isVirtual) {
                // 使用vtgame的接口信息
                $game_endpoint = rtrim(\think\Env::get('vtgame.endpoint'), '/');
                $operatorToken = \think\Env::get('vtgame.operator_token', '');
                FrontendLog::info("使用虚拟游戏接口: " . $game_endpoint);
            } else {
                // 使用普通游戏接口信息
                $game_endpoint = rtrim(\think\Env::get('game.endpoint'), '/');
                $operatorToken = \think\Env::get('game.operator_token', '');
                FrontendLog::info("使用普通游戏接口: " . $game_endpoint);
            }

            // 将游戏代码保存到请求参数中，以便getUniquePlaySession方法可以访问
            //$this->request->withGet(['gamecode' => $gamecode, 'is_virtual' => $isVirtual ? 1 : 0]);

            $path = "/$gamecode/index.html";
            // 从.env文件中获取app.stage的值
            $appStage = \think\Env::get('app.stage', '');
            // 记录获取到的app.stage值
            FrontendLog::info("获取到的app.stage值: " . ($appStage ?: '空'));
            // 将app.stage的值填充到url变量op中
            $extra_args = 'op=' . $appStage . '&btt=1&ops='.$this->getUniquePlaySession($gamecode, $isVirtual).'&f='.$this->request->domain();
            $trace_id = $this->generateGUID();
            $game_endpoint_url = $game_endpoint . '/external-game-launcher/api/v1/GetLaunchURLHTML?trace_id='. $trace_id;
            $bodyPost = [
                'operator_token' => $operatorToken,
                'trace_id' => $trace_id,
                'path' => $path,
                'extra_args' => $extra_args,
                'client_ip' => $clientIP
            ];
            // 设置代理服务器选项
            // 此选项由本地开发环境暂留
            $options = [
                // CURLOPT_PROXY => 'socks5h://*************',  // 代理服务器地址
                // CURLOPT_PROXYPORT => 1235,                   // 代理服务器端口
                // CURLOPT_PROXYTYPE => CURLPROXY_SOCKS5,       // 指定代理类型为 SOCKS5
                // CURLOPT_TIMEOUT => 30,                       // 请求超时时间（秒）
                // CURLOPT_CONNECTTIMEOUT => 10                 // 连接超时时间（秒）
            ];

            // 记录开始时间
            $startTime = microtime(true);

            // 发送带代理的 POST 请求
            $response = Http::post($game_endpoint_url, $bodyPost, $options);

            // 计算耗时（毫秒）
            $endTime = microtime(true);
            $requestTime = round(($endTime - $startTime) * 1000, 2);

            // 添加耗时信息到响应头
            header('X-Http-Post-Time: ' . $requestTime . 'ms');

            // 记录请求和响应信息到控制台
            FrontendLog::info("请求地址: " . $game_endpoint_url);
            FrontendLog::info("请求参数: " . print_r($bodyPost, true));
            FrontendLog::info("响应内容: " . $response);

            // 解析响应内容
            $responseData = json_decode($response, true);

            // 检查响应是否有效
            if (isset($responseData['url']) && !empty($responseData['url'])) {
                $gameURL = $responseData['url'];
                FrontendLog::info('解析到游戏URL: ' . $gameURL);
                return $gameURL; // 成功时直接返回URL字符串
            } else {
                return -1; // 失败时返回-1
            }
        } catch (\Exception $e) {
            return -1; // 异常时返回-1
        }
    }



    /**
     * 获取支付回调URL
     * 使用公共库中的方法
     * @return string 回调URL
     */
    private function getCallbackUrl()
    {
        return \app\common\library\CallbackUrl::getCallbackUrl($this->request);
    }

    private function generateGUID() {
        $charid = strtoupper(md5(uniqid(mt_rand(), true)));
        $hyphen = chr(45); // "-"
        $uuid = substr($charid, 0, 8) . $hyphen
              . substr($charid, 8, 4) . $hyphen
              . substr($charid, 12, 4) . $hyphen
              . substr($charid, 16, 4) . $hyphen
              . substr($charid, 20, 12);
        return $uuid;
    }

    private function getUniquePlaySession($gamecode, $isVirtual = false) {
        // 生成不包含点号的唯一ID
        $prefix = 'ops_';
        $time = microtime(true);
        $timePart = substr(str_replace('.', '', (string)$time), 0, 14);
        $randomPart = substr(str_shuffle('abcdefghijklmnopqrstuvwxyz0123456789'), 0, 6);
        $sessionToken = $prefix . $timePart . $randomPart;

        try {
            // 获取游戏代码和虚拟玩家标识
            if (empty($gamecode)) {
                return $sessionToken;
            }

            // 查询游戏房间信息
            $gameRoom = \think\Db::table('game_rooms')
                ->where('code', $gamecode)
                ->find();

            if (!$gameRoom) {
                return $sessionToken;
            }

            // 获取玩家ID和余额（从session中获取）
            $playerId = \think\Session::get('player_id');
            $playerType = \think\Session::get('player_type');
            $currentBalance = 0.00;

            // 如果有玩家ID，尝试获取玩家余额
            if ($playerId > 0) {
                if ($playerType == 'virtual' || $isVirtual) {
                    // 虚拟玩家
                    $player = \think\Db::table('virtual_players')->where('id', $playerId)->find();
                    if ($player) {
                        $currentBalance = $player['balance'];
                    }
                } else {
                    // 普通玩家
                    $player = \think\Db::table('players')->where('id', $playerId)->find();
                    if ($player) {
                        $currentBalance = $player['balance'];
                    }
                }
            }

            // 创建游戏会话记录
            $gameSession = [
                'player_id' => $playerId,
                'session_token' => $sessionToken,
                'game_id' => $gameRoom['game_room_id'],
                'provider_id' => $gameRoom['provider_id'],
                'balance_before' => $currentBalance,
                'balance_after' => $currentBalance, // 初始时与开始前余额相同
                'bet_amounts' => 0.00, // 初始投注金额为0
                'win_amounts' => 0.00, // 初始赢得金额为0
                'transfer_amounts' => 0.00, // 初始输赢金额为0
                'betting_amounts' => 0.00, // 初始打码量为0
                'created_at' => date('Y-m-d H:i:s'),
                'is_real' => ($playerType == 'virtual' || $isVirtual) ? 0 : 1 // 根据玩家类型设置is_real字段
            ];

            // 插入游戏会话记录
            $sessionId = \think\Db::table('game_sessions')->insertGetId($gameSession);

        } catch (\Exception $e) {
            // 记录错误但不影响会话令牌的生成
            FrontendLog::error("创建游戏会话记录异常: " . $e->getMessage());
        }

        return $sessionToken;
    }

    /**
     * 根据标签获取游戏数据
     * 用于首页标签切换时加载不同分类的游戏
     * @return \think\response\Json
     */
    public function getGamesByTab()
    {
        // 获取标签参数
        $tab = $this->request->param('tab', 'popular');
        try {
            $result = [];

            // 根据不同标签返回不同的游戏数据
            switch ($tab) {
                case 'popular':
                    // 重新加载游戏数据，确保与首页初始化时完全一致
                    $gameData = $this->loadAndAssignGameData();

                    // 返回完整的游戏列表数据，与首页初始化时保持一致
                    $result = $gameData['activeGameList'];
                    // 直接返回结果，不需要继续执行后面的代码
                    return json(['code' => 1, 'msg' => __('Success'), 'data' => $result]);
                    break;
                case 'recent':
                    // 获取最近游戏数据（这里可能需要从用户会话或数据库中获取）
                    // 这里简单返回一些热门游戏作为示例
                    $recentGames = \app\index\model\GameRooms::getPopularGames(0, 6);

                    // 构建分类数据
                    $result[] = [
                        'code' => 'recent',
                        'name' => __('Recent'),
                        'games' => $recentGames,
                        'total_games' => count($recentGames),
                        'has_more' => false
                    ];
                    break;

                case 'favorite':
                    // 获取收藏游戏数据（这里可能需要从用户会话或数据库中获取）
                    // 这里简单返回一些热门游戏作为示例
                    $favoriteGames = \app\index\model\GameRooms::getPopularGames(0, 4);

                    // 构建分类数据
                    $result[] = [
                        'code' => 'favorite',
                        'name' => __('Favorite'),
                        'games' => $favoriteGames,
                        'total_games' => count($favoriteGames),
                        'has_more' => false
                    ];
                    break;
                default:
                    // 默认返回热门游戏，重新加载游戏数据
                    $gameData = $this->loadAndAssignGameData();

                    // 构建分类数据
                    $result[] = [
                        'code' => 'popular',
                        'name' => __('Popular'),
                        'games' => $gameData['popularGames'],
                        'total_games' => count($gameData['popularGames']) + 10,
                        'has_more' => true
                    ];
                    break;
            }

            // 记录返回数据
            $resultCount = count($result);
            return json(['code' => 1, 'msg' => __('Success'), 'data' => $result]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 加载游戏数据
     * 封装获取首页游戏数据的逻辑，方便在不同标签间切换时重新加载
     * @return array 返回加载的游戏数据
     */
    private function loadAndAssignGameData()
    {
        $result = [
            'gameList' => [],
            'activeGameList' => [],
            'popularGames' => []
        ];

        try {
            // 获取激活的游戏提供商名称列表
            $result['gameList'] = \app\index\model\GameProvider::getActiveProviders(true);

            // 获取完整的游戏列表数据（包括Popular分类和游戏数据）
            $result['activeGameList'] = \app\index\model\GameProvider::getActiveGameList(9);

            // 获取热门游戏数据
            $result['popularGames'] = \app\index\model\GameRooms::getPopularGames(0, 9);
        } catch (\Exception $e) {
            // 出错时使用空数组
            $result['gameList'] = [];
            $result['activeGameList'] = [];
            $result['popularGames'] = [];

            // 记录错误日志
            \app\common\library\FrontendLog::error('获取游戏提供商列表失败: ' . $e->getMessage());
            \app\common\library\FrontendLog::error('错误堆栈: ' . $e->getTraceAsString());
        }

        return $result;
    }

    /**
     * 获取并保存URL中的推广参数到session
     * 主要处理ic=和id=参数
     */
    protected function saveReferralParamsToSession()
    {
        // 获取URL中的推广参数
        $icParam = $this->request->param('ic', '');
        $idParam = $this->request->param('id', '');

        // 记录当前完整URL和参数
        $currentUrl = $this->request->url(true);
        FrontendLog::info('访问首页，完整URL: ' . $currentUrl);
        FrontendLog::info('推广参数: ic=' . $icParam . ', id=' . $idParam );

        // 保存ic参数到session
        if (!empty($icParam)) {
            Session::set('referral_ic', $icParam);
            FrontendLog::info('保存ic参数到session: ' . $icParam);
        }

        // 保存id参数到session
        if (!empty($idParam)) {
            Session::set('referral_id', $idParam);
            FrontendLog::info('保存id参数到session: ' . $idParam);
        }

    }
}

