<?php

namespace app\index\controller;

use app\common\controller\Frontend;

class Creategame extends Frontend
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function _initialize()
    {
        parent::_initialize();

        // 确保所有页面加载event语言包
        $this->loadLang('creategame');
        $this->request->filter('trim,strip_tags,htmlspecialchars');
    }

    /**
     * 事件主页
     */
    public function index()
    {
         // 获取游戏代码参数
         $gameid = $this->request->param('gameid', 0);

         // 如果没有gamecode，返回错误
         if (empty($gameid)) {
             return json(['code' => 0, 'msg' => '游戏id不能为空']);
         }

         // 获取游戏代码
         $gamecode = $this->getGameCode($gameid);

         if (empty($gamecode)) {
             return json(['code' => 0, 'msg' => '找不到对应的游戏代码']);
         }

         // 实例化Index控制器，调用getGameURL方法
         $indexController = new Index();
         $gameURL = $indexController->getGameURL($gamecode);

         // 检查返回结果
         if ($gameURL === -1) {
             return json(['code' => 0, 'msg' => '获取游戏URL失败']);
         }

         // 重定向到游戏URL
         $this->redirect($gameURL);
    }

    /**
     * 根据游戏ID获取游戏代码
     * @param int $gameid 游戏ID
     * @return string 游戏代码
     */
    public function getGameCode($gameid)
    {
        // 这里应该根据gameid从数据库获取对应的游戏代码
        // 示例实现，实际使用时请替换为真实的数据库查询
        $gameCode = '';

        try {
            \app\common\library\FrontendLog::info(' this is getGameCode: ' . $gameid);

            // 从game_rooms表查询，而不是games表
            $game = \think\Db::table('game_rooms')
                ->where('game_room_id', $gameid)
                ->find();

            if ($game) {
                $gameCode = $game['code'] ?? '';
                \app\common\library\FrontendLog::info('成功获取游戏代码: ' . $gameCode);
            } else {
                \app\common\library\FrontendLog::error('游戏ID不存在: ' . $gameid);
            }
        } catch (\Exception $e) {
            \app\common\library\FrontendLog::error('获取游戏代码失败: ' . $e->getMessage());
        }

        return $gameCode;
    }

    /**
     * 获取游戏重定向URL
     * @param int $gameid 游戏ID
     * @return string 重定向URL
     */
    public function getRedirectUrl($gameid)
    {
        if (empty($gameid)) {
            throw new \Exception('游戏ID不能为空');
        }

        // 获取游戏代码
        $gamecode = $this->getGameCode($gameid);

        if (empty($gamecode)) {
            throw new \Exception('找不到对应的游戏代码');
        }

        // 实例化Index控制器，调用getGameURL方法
        $indexController = new Index();
        $gameURL = $indexController->getGameURL($gamecode);

        // 检查返回结果
        if ($gameURL === -1) {
            \app\common\library\FrontendLog::error('获取游戏URL失败: ' . $gamecode);
            throw new \Exception('获取游戏URL失败');
        }

        \app\common\library\FrontendLog::info('成功获取游戏URL: ' . $gameURL);
        return $gameURL;
    }
}