<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\index\model\Event as EventModel;

class Event extends Frontend
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();

        // 确保所有页面加载event语言包
        $this->loadLang('event');
        $this->request->filter('trim,strip_tags,htmlspecialchars');
    }

    /**
     * 事件主页
     */
    public function index()
    {
        // 获取活动横幅数据
        $banners = [
            [
                'id' => 1,
                'image' => '/assets/img/frontend/banner/banner1.png',
                'title' => 'First Deposit Bonus',
                'link' => '/index/event/event_item?current=1',
                'tag' => 'NEW',
                'status' => 'active',
                'startDate' => date('Y-m-d', strtotime('-5 days')),
                'endDate' => date('Y-m-d', strtotime('+30 days'))
            ],
            [
                'id' => 2,
                'image' => '/assets/img/frontend/banner/banner2.png',
                'title' => 'Weekly Bonus',
                'link' => '/index/event/event_item?current=2',
                'tag' => 'POPULAR',
                'status' => 'active',
                'startDate' => date('Y-m-d', strtotime('-10 days')),
                'endDate' => date('Y-m-d', strtotime('+20 days'))
            ],
            [
                'id' => 3,
                'image' => '/assets/img/frontend/banner/banner3.png',
                'title' => 'VIP Birthday',
                'link' => '/index/event/event_item?current=3',
                'tag' => 'VIP_exclusive',
                'status' => 'coming',
                'startDate' => date('Y-m-d', strtotime('+5 days')),
                'endDate' => date('Y-m-d', strtotime('+15 days'))
            ]
        ];

        $this->view->assign('banners', $banners);
        return $this->view->fetch();
    }

    /**
     * 任务页面
     */
    public function mission()
    {
        // 设置为空数组来测试空状态
        $missions = [];

        $this->view->assign('missions', $missions);
        return $this->view->fetch();
    }

    /**
     * VIP页面
     */
    public function vip()
    {
        // 检查用户是否登录 - 使用Player模型中的统一方法
        if (\app\index\model\Player::checkLoginAndRedirect()) {
            return;
        }

        // 获取登录状态
        $loginStatus = \app\index\model\Player::checkLoginStatus();

        // 获取当前登录用户数据
        $playerData = $loginStatus['data'];

        // 从player表获取玩家充值数据
        $playerDeposit = model('Player')->where('id', $playerData['id'])->value('total_deposit') ?: 0;

        // 使用Event模型获取VIP等级列表
        $vipList = \app\index\model\Event::getVipList();

        // 根据玩家充值金额确定当前VIP等级
        $vipLevel = 0;
        $nextLevelDeposit = 0;
        foreach ($vipList as $key => $vip) {
            if ($playerDeposit >= $vip['deposit_requirement']) {
                $vipLevel = $vip['level'];
                // 如果有下一个等级，获取下一级所需充值
                if (isset($vipList[$key+1])) {
                    $nextLevelDeposit = $vipList[$key+1]['deposit_requirement'] - $playerDeposit;
                }
            } else {
                // 如果没有找到匹配的VIP等级，设置当前等级所需的剩余充值额
                if ($vipLevel === 0) {
                    $nextLevelDeposit = $vip['deposit_requirement'] - $playerDeposit;
                }
                break;
            }
        }

        $this->view->assign([
            'vipList' => $vipList,
            'vipLevel' => $vipLevel,
            'playerDeposit' => $playerDeposit,
            'nextLevelDeposit' => $nextLevelDeposit,
        ]);

        return $this->view->fetch();
    }

    /**
     * 退税页面
     */
    public function rebate()
    {
        // 设置为空数组来测试空状态
        $rebates = [];

        $this->view->assign('rebates', $rebates);
        return $this->view->fetch();
    }

    /**
     * 等待页面
     */
    public function pendente()
    {
        // 设置为空数组来测试空状态
        $pending_rewards = [];

        $this->view->assign('pending_rewards', $pending_rewards);
        return $this->view->fetch();
    }

    /**
     * 历史页面
     */
    public function history()
    {
        // 设置为空数组来测试空状态
        $history_records = [];

        $this->view->assign('history_records', $history_records);
        return $this->view->fetch();
    }

    /**
     * 事件详情
     */
    public function item()
    {
        $id = $this->request->param('id', 0);
        $this->view->assign('id', $id);
        return $this->view->fetch();
    }

    /**
     * 活动详情页面
     */
    public function event_item()
    {
        // 检查用户是否登录 - 使用Player模型中的统一方法
        if (\app\index\model\Player::checkLoginAndRedirect()) {
            return;
        }

        // 获取登录状态
        $loginStatus = \app\index\model\Player::checkLoginStatus();

        // 获取当前登录用户数据
        $playerData = $loginStatus['data'];
        $dropBound = isset($playerData['no_unbind_subordinate']) && $playerData['no_unbind_subordinate'] == 0;

        // 获取current参数，默认为1
        $current = $this->request->param('current', 1);

        // 获取当前域名
        $domain = $this->request->domain();

        // 获取玩家ID
        $id = $playerData["id"];
        $ptype = "id=";
        $link_url = $domain."?".$ptype.$id;

        // 获取直接下属数量x
        $relation1Count = \app\index\model\Player::getDirectReferralsCountAttr($playerData, $dropBound);

        // 获取宝箱配置数据
        $treasureBoxConfig = [];
        $showTreasureBox = true; // 默认显示宝箱
        $validSubordinates = 0; // 默认有效下属数量为0

        if ($current == 1) {
            // 获取玩家有效下属数量 - 无论是否显示宝箱都需要计算
            $playerData = \app\index\model\Player::checkLoginStatus()['data'] ?? [];
            $playerId = isset($playerData['id']) ? $playerData['id'] : 0;
            $dropBound = isset($playerData['no_unbind_subordinate']) && $playerData['no_unbind_subordinate'] == 0;
            $validSubordinates = $playerId ? \app\index\model\Player::getDerectDepositReferralsValidCount($playerData, $dropBound) : 0;

            // 检查玩家所属渠道是否允许显示宝箱
            $channelId = isset($playerData['channel_id']) ? $playerData['channel_id'] : 0;
            if ($channelId) {
                $channel = \think\Db::table('channels')
                    ->where('id', $channelId)
                    ->find();

                // 如果渠道存在且allow_treasure_box为0，则不显示宝箱
                if ($channel && isset($channel['allow_treasure_box']) && $channel['allow_treasure_box'] == 0) {
                    $showTreasureBox = false;
                }
            }

            // 只有当允许显示宝箱时才获取宝箱配置
            if ($showTreasureBox) {
                // 1. 查询treasure_box_config表获取所有is_active的宝箱配置
                $treasureBoxConfig = \think\Db::table('treasure_box_config')
                    ->where('is_active', 1)
                    ->order('invite_count asc')
                    ->select();

                // 2. 查询玩家已有的宝箱记录
                $boxRecords = [];
                if ($playerId) {
                    $boxRecords = \think\Db::table('treasure_box_records')
                        ->where('player_id', $playerId)
                        ->select();
                }

                // 3. 创建宝箱记录映射
                $boxRecordsMap = [];
                foreach ($boxRecords as $record) {
                    $boxRecordsMap[$record['config_id']] = $record;
                }

                // 4. 为每个宝箱配置添加状态信息
                foreach ($treasureBoxConfig as &$box) {
                    // 默认所有宝箱为locked状态
                    $box['status'] = 'locked';
                    $box['record_id'] = 0;
                    $box['is_claimed'] = 0;

                    // 如果找到对应的宝箱记录，更新状态
                    if (isset($boxRecordsMap[$box['id']])) {
                        $box['record_id'] = $boxRecordsMap[$box['id']]['id'];
                        $box['is_claimed'] = $boxRecordsMap[$box['id']]['is_claimed'];
                        $box['claimed_at'] = $boxRecordsMap[$box['id']]['claimed_at'];

                        // 如果已领取，设置状态为claimed，否则设置为available
                        // 因为records表中存在的记录都是可以领取的
                        $box['status'] = ($box['is_claimed'] == 1) ? 'claimed' : 'available';
                    }
                }
            }

            // 将有效下属数量传递给视图 - 无论是否显示宝箱
            $this->assign('valid_subordinates', $validSubordinates);

            // 将是否显示宝箱的标志传递给视图
            $this->assign('showTreasureBox', $showTreasureBox);
        }

        // 如果是第二个页面，获取VIP配置数据
        $vipList = [];
        if ($current == 2) {
            $vipList = \app\index\model\Event::getVipList();
        }

        // 分配变量到视图
        $this->view->assign([
            'current' => $current,
            'link_url' => $link_url,
            'id_code' => $id,
            'r_count' => $relation1Count,
            'vipList' => $vipList,
            'treasureBoxConfig' => $treasureBoxConfig,
            'title' => __('event_title_' . $current),
            'validSubordinatesThreshold' => \think\Env::get('app.valid_subordinates_threshold', 25),
            'firstDepositThreshold' => \think\Env::get('app.first_deposit_threshold', 25)
        ]);

        return $this->view->fetch();
    }

    /**
     * 领取宝箱奖励
     * @return \think\response\Json
     */
    public function claimTreasureBox()
    {
        // 检查用户是否登录
        if (\app\index\model\Player::checkLoginAndRedirect()) {
            return json(['code' => 0, 'msg' => __('Please login first')]);
        }

        // 获取登录状态
        $loginStatus = \app\index\model\Player::checkLoginStatus();
        if ($loginStatus['code'] == 0 || !isset($loginStatus['data'])) {
            return json(['code' => 0, 'msg' => __('Please login first')]);
        }

        // 获取当前登录用户数据
        $playerData = $loginStatus['data'];
        $playerId = $playerData['id'];

        // 检查玩家所属渠道是否允许宝箱功能
        $channelId = isset($playerData['channel_id']) ? $playerData['channel_id'] : 0;
        if ($channelId) {
            $channel = \think\Db::table('channels')
                ->where('id', $channelId)
                ->find();

            // 如果渠道存在且allow_treasure_box为0，则不允许领取宝箱
            if ($channel && isset($channel['allow_treasure_box']) && $channel['allow_treasure_box'] == 0) {
                return json(['code' => 0, 'msg' => __('Treasure box feature is disabled')]);
            }
        }

        // 获取宝箱ID
        $treasureBoxId = $this->request->post('treasure_box_id', 0);
        if (!$treasureBoxId) {
            return json(['code' => 0, 'msg' => __('Invalid treasure box ID')]);
        }

        try {
            // 开始事务
            \think\Db::startTrans();

            // 获取宝箱配置信息
            $treasureBox = \think\Db::table('treasure_box_config')
                ->where('id', $treasureBoxId)
                ->find();

            if (!$treasureBox) {
                \think\Db::rollback();
                return json(['code' => 0, 'msg' => __('Treasure box not found')]);
            }

            // 不需要检查有效邀请人数，因为记录存在即表示可以领取
            // 数据库触发器会确保只有满足条件的玩家才会有宝箱记录

            // 检查是否已经领取过
            $record = \think\Db::table('treasure_box_records')
                ->where('player_id', $playerId)
                ->where('config_id', $treasureBoxId)
                ->find();

            if (!$record) {
                // 如果记录不存在，可能是因为触发器尚未创建记录
                // 返回错误信息，前端会自动刷新页面
                \think\Db::rollback();
                return json(['code' => 0, 'msg' => __('Treasure box record not found, please try again later')]);
            }

            // 如果记录存在，检查是否已领取
            if ($record['is_claimed'] == 1) {
                \think\Db::rollback();
                return json(['code' => 0, 'msg' => __('Already claimed')]);
            }

            // 更新记录为已领取
            \think\Db::table('treasure_box_records')
                ->where('id', $record['id'])
                ->update([
                    'is_claimed' => 1,
                    'claimed_at' => date('Y-m-d H:i:s')
                ]);

            // 更新玩家奖励余额
            // $rewardAmount = $treasureBox['reward_amount'];
            // \think\Db::table('players')
            //     ->where('id', $playerId)
            //     ->setInc('reward_balance', $rewardAmount);


            // 提交事务
            \think\Db::commit();

            // 记录日志
            \app\common\library\FrontendLog::info("玩家 {$playerId} 成功领取宝箱奖励 {$treasureBoxId}");

            // 返回成功信息
            return json([
                'code' => 1,
                'msg' => __('Reward claimed successfully'),
                'data' => [
                    'treasure_box_id' => $treasureBoxId,
                    // 'reward_amount' => $rewardAmount,
                    // 'new_reward_balance' => \think\Db::table('players')->where('id', $playerId)->value('reward_balance')
                ]
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            \think\Db::rollback();

            // 记录错误日志
            \app\common\library\FrontendLog::error("领取宝箱奖励失败: " . $e->getMessage());

            // 返回错误信息
            return json(['code' => 0, 'msg' => __('Failed to claim reward') . ': ' . $e->getMessage()]);
        }
    }
}
