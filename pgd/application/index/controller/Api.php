namespace app\index\controller;

class Api extends Frontend
{
    // 允许的操作映射
    protected $allowedActions = [
        // 用户操作
        'user_login' => ['index/index', 'playerLogin'],
        'user_register' => ['index/index', 'playerRegister'],
        'user_logout' => ['index/index', 'playerLogout'],
        'check_status' => ['index/index', 'checkStatus'],
        'refresh_balance' => ['index/index', 'refreshBalance'],
        
        // 公告操作
        'close_notice' => ['index/index', 'markNoticePopupClosed'],
    ];
} 