<?php

namespace app\index\controller\common;

use think\Cache;
use think\Db;
use app\common\library\FrontendLog;

/**
 * 前端IP黑名单管理类
 * 用于防止API接口被刷
 */
class IpBlacklist
{
    /**
     * 黑名单缓存键
     */
    protected static $cacheKey = 'frontend_ip_blacklist';
    
    /**
     * 可疑IP缓存键前缀
     */
    protected static $suspiciousCachePrefix = 'frontend_suspicious_ip_';
    
    /**
     * 检查IP是否在黑名单中
     * 
     * @param string $ip IP地址
     * @return bool 
     */
    public static function isBlacklisted($ip)
    {
        $blacklist = self::getBlacklist();
        return in_array($ip, $blacklist);
    }
    
    /**
     * 获取完整黑名单
     * 
     * @return array
     */
    public static function getBlacklist()
    {
        // 从缓存获取黑名单
        $blacklist = Cache::get(self::$cacheKey);
        
        // 如果缓存中没有，则从数据库加载
        if ($blacklist === false) {
            // 这里使用frontend_ip_blacklist表
            try {
                $blacklist = Db::table('frontend_ip_blacklist')
                    ->where('status', 1)
                    ->column('ip');
            } catch (\Exception $e) {
                // 如果表不存在或查询出错，使用空数组
                FrontendLog::error('获取前端IP黑名单失败: ' . $e->getMessage());
                $blacklist = [];
            }
            
            // 缓存黑名单，有效期1小时
            Cache::set(self::$cacheKey, $blacklist, 3600);
        }
        
        return $blacklist ?: [];
    }
    
    /**
     * 添加IP到黑名单
     * 
     * @param string $ip IP地址
     * @param string $reason 原因
     * @return bool
     */
    public static function addToBlacklist($ip, $reason = '')
    {
        // 检查IP是否已在黑名单中
        if (self::isBlacklisted($ip)) {
            return true;
        }
        
        // 记录日志
        FrontendLog::warning("添加IP [{$ip}] 到前端黑名单，原因: {$reason}");
        
        try {
            // 添加到数据库
            $result = Db::table('frontend_ip_blacklist')->insert([
                'ip' => $ip,
                'reason' => $reason,
                'status' => 1,
                'createtime' => time()
            ]);
            
            if ($result) {
                // 更新缓存
                $blacklist = self::getBlacklist();
                $blacklist[] = $ip;
                Cache::set(self::$cacheKey, $blacklist, 3600);
                
                // 清除该IP的可疑记录
                self::clearSuspiciousRecord($ip);
                
                return true;
            }
        } catch (\Exception $e) {
            FrontendLog::error('添加IP到前端黑名单失败: ' . $e->getMessage());
            
            // 如果数据库操作失败，至少在缓存中添加
            $blacklist = self::getBlacklist();
            $blacklist[] = $ip;
            Cache::set(self::$cacheKey, $blacklist, 3600);
        }
        
        return false;
    }
    
    /**
     * 记录可疑请求
     * 
     * @param string $ip IP地址
     * @param string $type 可疑类型
     * @param int $threshold 阈值，达到此值将加入黑名单
     * @return bool 是否已达到阈值
     */
    public static function recordSuspiciousRequest($ip, $type = 'general', $threshold = 5)
    {
        // 如果IP已在黑名单中，直接返回true
        if (self::isBlacklisted($ip)) {
            return true;
        }
        
        // 缓存键
        $cacheKey = self::$suspiciousCachePrefix . $type . '_' . $ip;
        
        // 获取当前计数
        $count = Cache::get($cacheKey) ?: 0;
        $count++;
        
        // 更新计数，有效期1小时
        Cache::set($cacheKey, $count, 3600);
        
        // 记录日志
        if ($count >= $threshold) {
            FrontendLog::alert("前端 - IP [{$ip}] 可疑请求（{$type}）次数达到阈值 {$threshold}，当前: {$count}");
            return true;
        } else if ($count >= ($threshold * 0.7)) {
            // 接近阈值时记录警告
            FrontendLog::warning("前端 - IP [{$ip}] 可疑请求（{$type}）次数接近阈值，当前: {$count}");
        }
        
        return false;
    }
    
    /**
     * 清除可疑记录
     * 
     * @param string $ip IP地址
     * @param string $type 可疑类型，为空则清除所有类型
     */
    public static function clearSuspiciousRecord($ip, $type = '')
    {
        if ($type) {
            // 清除特定类型的记录
            $cacheKey = self::$suspiciousCachePrefix . $type . '_' . $ip;
            Cache::rm($cacheKey);
        } else {
            // 清除所有类型的记录
            // 由于缓存系统没有提供按前缀删除的方法，这里不做处理
            // 实际应用中可以结合具体缓存系统实现
        }
    }
    
    /**
     * 检查是否为可疑的User-Agent
     * 
     * @param string $userAgent User-Agent字符串
     * @return bool
     */
    public static function isSuspiciousUserAgent($userAgent)
    {
        if (empty($userAgent)) {
            return true;
        }
        
        // 检查是否为常见爬虫或自动化工具
        $patterns = [
            '/bot/i',
            '/spider/i',
            '/crawl/i',
            '/curl/i',
            '/wget/i',
            '/^$/i',  // 空User-Agent
            '/python/i',
            '/php/i',
            '/go-http/i',
            '/java/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return true;
            }
        }
        
        return false;
    }
} 