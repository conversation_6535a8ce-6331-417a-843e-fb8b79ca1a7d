<?php

namespace app\index\controller;

use app\common\controller\Frontend;

class Customerservice extends Frontend
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function _initialize()
    {
        parent::_initialize();

        // 加载语言包
        $this->loadlang('customerservice');

        // 加载js-cookie库
        $this->view->assign('jsfile', ['https://cdn.jsdelivr.net/npm/js-cookie@3.0.1/dist/js.cookie.min.js']);
    }

    /**
     * 客服主页
     */
    public function index()
    {
        // 从数据库获取客服配置信息
        $supportConfig = \think\Db::table('customer_service_config')
            ->where('link_type', '运营客服')
            ->where('status', 1)
            ->find();

        $telegramConfig = \think\Db::table('customer_service_config')
            ->where('link_type', '官方Telegram')
            ->where('status', 1)
            ->find();

        // 将客服配置信息传递给视图
        $this->view->assign('supportConfig', $supportConfig);
        $this->view->assign('telegramConfig', $telegramConfig);

        // 记录日志
        \app\common\library\FrontendLog::info('客服页面加载: 运营客服=' . ($supportConfig ? $supportConfig['link_url'] : '未配置') .
                                           ', 官方Telegram=' . ($telegramConfig ? $telegramConfig['link_url'] : '未配置'));

        return $this->view->fetch();
    }

    /**
     * 支持页面
     */
    public function suporte()
    {
        return $this->view->fetch();
    }

    /**
     * 消息页面
     */
    public function noticie()
    {
        return $this->view->fetch();
    }

    /**
     * 通知页面
     */
    public function notificacao()
    {
        return $this->view->fetch();
    }

    /**
     * 滚动面板
     */
    public function painel()
    {
        return $this->view->fetch();
    }

    /**
     * 奖金页面
     */
    public function bon()
    {
        return $this->view->fetch();
    }
}