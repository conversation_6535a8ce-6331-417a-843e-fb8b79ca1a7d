<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\index\model\GameProvider;

class Game extends Frontend
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = '*';
    protected $layout = '';

    public function _initialize()
    {
        parent::_initialize();
        // 移除HTML标签
        $this->request->filter('trim,strip_tags,htmlspecialchars');
    }

    /**
     * 显示游戏提供商页面
     */
    public function provider()
    {
        $name = $this->request->param('name', '');
        
        if (empty($name)) {
            $this->error('游戏提供商不存在');
        }
        
        // 从数据库中查询游戏提供商
        $provider = GameProvider::where('name', $name)->where('is_active', 1)->find();
        
        if (!$provider) {
            $this->error('游戏提供商不存在或未激活');
        }
        
        // 从JSON文件中获取游戏列表
        $jsonFile = ROOT_PATH . 'public' . DS . 'assets' . DS . 'json' . DS . 'frontend' . DS . 'game_providers_list.json';
        
        $games = [];
        
        if (file_exists($jsonFile)) {
            $jsonData = json_decode(file_get_contents($jsonFile), true);
            
            foreach ($jsonData as $key => $data) {
                if ($key === $name || $data['name'] === $name || $data['code'] === $provider['code']) {
                    $games = isset($data['games']) ? $data['games'] : [];
                    break;
                }
            }
        }
        
        $this->view->assign('provider', $provider);
        $this->view->assign('games', $games);
        $this->view->assign('total_games', count($games));
        
        return $this->view->fetch();
    }
    
    /**
     * AJAX加载更多游戏
     */
    public function loadMoreGames()
    {
        $code = $this->request->param('code', '');
        $offset = $this->request->param('offset', 9, 'intval');
        $limit = $this->request->param('limit', 6, 'intval');
        
        if (empty($code)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }
        
        // 获取更多游戏
        $result = GameProvider::getMoreGames($code, $offset, $limit);
        
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $result]);
    }
} 