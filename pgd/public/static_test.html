<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Static Test Page</title>
    <style>
        body {
            background-color: #f5f5f5;
            color: #333;
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 400px;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        button {
            background-color: #ff0000;
            color: white;
            border: none;
            padding: 10px 20px;
            margin-top: 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #cc0000;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Static Test Page</h1>
        <p>This page logs a message to console and shows an alert when loaded.</p>
        <button id="testButton">Click Me</button>
    </div>

    <script>
        // Log message to console when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded successfully!');
            alert('Welcome to the static test page!');
        });

        // Add click event for button
        document.getElementById('testButton').addEventListener('click', function() {
            console.log('Button clicked!');
            alert('You clicked the button!');
        });
    </script>
</body>
</html> 