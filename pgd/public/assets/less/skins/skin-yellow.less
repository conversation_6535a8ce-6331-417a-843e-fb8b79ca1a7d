/*
 * Skin: Yellow
 * ------------
 */
@import "../bootstrap-less/mixins.less";
@import "../bootstrap-less/variables.less";
@import "../fastadmin/variables.less";
@import "../fastadmin/mixins.less";

.skin-yellow {
    //Navbar
    .main-header {
        background-color: @yellow;

        .navbar {
            .navbar-variant(@yellow; #fff; #f6f6f6; rgba(0, 0, 0, .05));

            .sidebar-toggle {
                color: #fff;

                &:hover {
                    background-color: darken(@yellow, 5%);
                }
            }

            @media (max-width: @screen-header-collapse) {
                .dropdown-menu {
                    li {
                        &.divider {
                            background-color: rgba(255, 255, 255, 0.1);
                        }

                        a {
                            color: #fff;

                            &:hover {
                                background: darken(@yellow, 5%);
                            }
                        }
                    }
                }
            }
        }

        //Logo
        .logo {
            .logo-variant(darken(@yellow, 5%));
            @media (max-width: @screen-header-collapse) {
                .logo-variant(@yellow; #fff);
                border-right: none;
            }
        }

        li.user-header {
            background-color: @yellow;
        }
    }

    //Content Header
    .content-header {
        background: transparent;
    }

    //Create the sidebar skin
    .skin-dark-sidebar(@yellow);

    .sidebar-menu > li {
        > a {
            border-left: 3px solid transparent;
            padding-left: 12px;
        }
    }

    @media (min-width: @screen-sm) {
        &.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
            margin-left: -3px;
        }
    }

    &.multiplenav {
        @media (max-width: @screen-header-collapse) {
            .sidebar .mobilenav a.btn-app {
                background: lighten(@sidebar-dark-bg, 10%);
                color: #fff;

                &.active {
                    background: @yellow;
                    color: #fff;
                }
            }
        }
    }
}
