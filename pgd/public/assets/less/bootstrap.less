/*!
 * Bootstrap v3.3.6 (http://getbootstrap.com)
 * Copyright 2011-2015 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */

// Core variables and mixins
@import "bootstrap/variables.less";
@import "bootstrap-less/variables.less";
@import "bootstrap/mixins.less";

// Reset and dependencies
@import "bootstrap/normalize.less";
@import "bootstrap/print.less";
@import "bootstrap/glyphicons.less";

// Core CSS
@import "bootstrap/scaffolding.less";
@import "bootstrap/type.less";
@import "bootstrap/code.less";
@import "bootstrap/grid.less";
@import "bootstrap/tables.less";
@import "bootstrap/forms.less";
@import "bootstrap/buttons.less";

// Components
@import "bootstrap/component-animations.less";
@import "bootstrap/dropdowns.less";
@import "bootstrap/button-groups.less";
@import "bootstrap/input-groups.less";
@import "bootstrap/navs.less";
@import "bootstrap/navbar.less";
@import "bootstrap/breadcrumbs.less";
@import "bootstrap/pagination.less";
@import "bootstrap/pager.less";
@import "bootstrap/labels.less";
@import "bootstrap/badges.less";
@import "bootstrap/jumbotron.less";
@import "bootstrap/thumbnails.less";
@import "bootstrap/alerts.less";
@import "bootstrap/progress-bars.less";
@import "bootstrap/media.less";
@import "bootstrap/list-group.less";
@import "bootstrap/panels.less";
@import "bootstrap/responsive-embed.less";
@import "bootstrap/wells.less";
@import "bootstrap/close.less";

// Components w/ JavaScript
@import "bootstrap/modals.less";
@import "bootstrap/tooltip.less";
@import "bootstrap/popovers.less";
@import "bootstrap/carousel.less";

// Utility classes
@import "bootstrap/utilities.less";
@import "bootstrap/responsive-utilities.less";

