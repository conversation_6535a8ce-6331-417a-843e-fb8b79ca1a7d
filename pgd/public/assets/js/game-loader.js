/**
 * 游戏加载器JS
 * 用于在前端页面中加载游戏iframe
 */

const GameLoader = {
    /**
     * 在指定容器中加载游戏
     * @param {string|number} gameParam - 游戏ID或游戏代码
     * @param {string} containerId - 容器ID
     * @param {object} options - 附加选项
     */
    loadGame: function(gameParam, containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('容器不存在:', containerId);
            return;
        }

        if (!gameParam) {
            console.error('游戏参数不能为空');
            return;
        }

        // 默认选项
        const defaultOptions = {
            width: '100%',
            height: '600px',
            fullscreen: false,
            autoplay: true,
            // 参数类型，默认自动检测：
            // - 'id': 使用游戏ID
            // - 'code': 使用游戏代码
            // - 'auto': 自动检测（数字为ID，字符串为代码）
            paramType: 'auto'
        };

        // 合并选项
        const finalOptions = Object.assign({}, defaultOptions, options);

        // 判断参数类型
        let paramType = finalOptions.paramType;
        if (paramType === 'auto') {
            // 如果是纯数字，视为ID；否则视为代码
            paramType = !isNaN(gameParam) && String(gameParam).match(/^\d+$/) ? 'id' : 'code';
        }

        // 创建游戏iframe
        const iframe = document.createElement('iframe');

        // 根据参数类型构建URL
        if (paramType === 'id') {
            iframe.src = `/index/createiframe/index?gameid=${encodeURIComponent(gameParam)}`;
        } else {
            iframe.src = `/index/createiframe/index?gamecode=${encodeURIComponent(gameParam)}`;
        }

        iframe.width = finalOptions.width;
        iframe.height = finalOptions.height;
        iframe.frameBorder = '0';
        iframe.scrolling = 'no';
        iframe.style.border = 'none';
        iframe.allowFullscreen = finalOptions.fullscreen;

        // 添加loading提示
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'game-loading';
        // loadingDiv.innerHTML = 'Carregando o jogo, por favor aguarde...';
        loadingDiv.style.textAlign = 'center';
        loadingDiv.style.padding = '20px';

        // 清空容器并添加loading
        container.innerHTML = '';
        container.appendChild(loadingDiv);

        // iframe加载完成后移除loading
        iframe.onload = function() {
            container.removeChild(loadingDiv);
        };

        // 创建一个包装容器，用于定位iframe和返回按钮
        const wrapperDiv = document.createElement('div');
        wrapperDiv.style.position = 'relative';
        wrapperDiv.style.width = finalOptions.width;
        wrapperDiv.style.height = finalOptions.height;

        // 添加iframe到包装容器
        wrapperDiv.appendChild(iframe);

        // 创建返回按钮
        const backButton = document.createElement('div');
        backButton.className = 'game-back-button';
        backButton.innerHTML = '<i class="fa fa-arrow-left"></i>';
        backButton.style.position = 'absolute';
        backButton.style.top = '10px';
        backButton.style.left = '10px';
        backButton.style.zIndex = '1000';
        backButton.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        backButton.style.color = 'white';
        backButton.style.padding = '20px'; // 增大内边距，使按钮更大
        backButton.style.borderRadius = '50%'; // 保持圆形
        backButton.style.cursor = 'pointer';
        backButton.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.3)';
        backButton.style.transition = 'all 0.3s ease';
        backButton.style.width = '40px'; // 设置固定宽度
        backButton.style.height = '40px'; // 设置固定高度
        backButton.style.display = 'flex'; // 使用flex布局
        backButton.style.alignItems = 'center'; // 垂直居中
        backButton.style.justifyContent = 'center'; // 水平居中

        // 添加悬停效果
        backButton.onmouseover = function() {
            this.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
            this.style.transform = 'scale(1.1)';
        };
        backButton.onmouseout = function() {
            this.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            this.style.transform = 'scale(1)';
        };

        // 添加点击事件 - 返回首页
        backButton.onclick = function() {
            window.location.href = '/index';
        };

        // 添加返回按钮到包装容器
        wrapperDiv.appendChild(backButton);

        // 将整个包装容器添加到指定容器
        container.appendChild(wrapperDiv);
    },

    /**
     * 通过iframe方式加载游戏 (通过游戏代码)
     * @param {string} gameCode - 游戏代码
     * @param {function} callback - 回调函数
     * @deprecated 此方法已废弃，请使用loadGame方法代替
     */
    // getGameUrl: function(gameCode, callback) {
    //     console.warn('getGameUrl方法已废弃，请使用loadGame方法代替');
    //     // 直接通过iframe加载游戏
    //     if (typeof callback === 'function') {
    //         callback(new Error('此方法已废弃，请使用loadGame方法代替'), null);
    //     }
    // },

    /**
     * 在新窗口中打开游戏
     * @param {string|number} gameParam - 游戏ID或游戏代码
     * @param {string} paramType - 参数类型，'id'或'code'，默认自动检测
     */
    openGameInNewWindow: function(gameParam, paramType = 'auto') {
        if (!gameParam) {
            console.error('游戏参数不能为空');
            return;
        }

        // 判断参数类型
        if (paramType === 'auto') {
            // 如果是纯数字，视为ID；否则视为代码
            paramType = !isNaN(gameParam) && String(gameParam).match(/^\d+$/) ? 'id' : 'code';
        }

        // 根据参数类型构建URL
        let url;
        if (paramType === 'id') {
            url = `/index/createiframe/index?gameid=${encodeURIComponent(gameParam)}`;
        } else {
            url = `/index/createiframe/index?gamecode=${encodeURIComponent(gameParam)}`;
        }

        window.open(url, '_blank', 'width=1024,height=768');
    }
};
