/**
 * 语言工具类 - 简单的多语言处理工具
 */
var LanguageUtils = {
    /**
     * 切换网站语言
     * @param {string} language - 语言代码 (zh-cn 或 pt-br)
     */
    switchLanguage: function(language) {
        // 检查Cookies库是否可用
        if (typeof Cookies === 'undefined') {
            console.error('Cookies库未加载，无法切换语言');
            return;
        }
        
        try {
            // 使用Cookie存储语言设置
            Cookies.set('think_var', language);
            
            // 刷新页面应用新语言
            location.reload();
        } catch (e) {
            console.error('切换语言时发生错误:', e);
        }
    },
    
    /**
     * 获取当前语言
     * @returns {string} 当前语言代码
     */
    getCurrentLanguage: function() {
        if (typeof Cookies === 'undefined') {
            console.error('Cookies库未加载，返回默认语言');
            return 'zh-cn';
        }
        
        try {
            return Cookies.get('think_var') || 'zh-cn';
        } catch (e) {
            console.error('获取当前语言时发生错误:', e);
            return 'zh-cn';
        }
    },
    
    /**
     * 更新语言选择器的激活状态
     */
    updateLanguageSelector: function() {
        try {
            const currentLang = this.getCurrentLanguage();
            document.querySelectorAll('.language-item').forEach(item => {
                const itemLang = item.getAttribute('data-language');
                if (itemLang === currentLang) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        } catch (e) {
            console.error('更新语言选择器状态时发生错误:', e);
        }
    },
    
    /**
     * 初始化语言工具
     */
    init: function() {
        try {
            // 检查依赖
            if (typeof Cookies === 'undefined') {
                console.error('初始化语言工具失败: Cookies库未加载');
                return;
            }
            
            // 为所有语言选择器项绑定点击事件
            document.querySelectorAll('.language-item').forEach(item => {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    const language = item.getAttribute('data-language');
                    this.switchLanguage(language);
                });
            });
            
            // 更新选择器状态
            this.updateLanguageSelector();
            
            console.log('语言工具初始化成功');
        } catch (e) {
            console.error('初始化语言工具时发生错误:', e);
        }
    }
}; 