document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const searchInput = document.getElementById('searchInput');
    const clearHistoryBtn = document.getElementById('clearHistory');
    const historyItems = document.getElementById('historyItems');
    const searchHistory = document.getElementById('searchHistory');
    const backBtn = document.getElementById('backBtn');
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');

    // 获取本地存储中的搜索历史
    let searchHistoryList = JSON.parse(localStorage.getItem('searchHistory') || '[]');

    // 初始化搜索历史显示
    function updateSearchHistory() {
        historyItems.innerHTML = '';

        if (searchHistoryList.length > 0) {
            searchHistory.style.display = 'block';
            searchHistoryList.forEach(term => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-item';
                historyItem.textContent = term;

                // 添加删除按钮
                const deleteBtn = document.createElement('span');
                deleteBtn.className = 'delete-btn';
                deleteBtn.addEventListener('click', (e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    // 从历史记录中删除
                    searchHistoryList = searchHistoryList.filter(item => item !== term);
                    localStorage.setItem('searchHistory', JSON.stringify(searchHistoryList));
                    updateSearchHistory();
                });

                historyItem.appendChild(deleteBtn);

                historyItem.addEventListener('click', () => {
                    searchInput.value = term;
                    performSearch(term);
                });
                historyItems.appendChild(historyItem);
            });
        } else {
            searchHistory.style.display = 'none';
        }
    }

    // 添加搜索历史
    function addToSearchHistory(term) {
        // 如果已存在，先移除旧的
        searchHistoryList = searchHistoryList.filter(item => item !== term);

        // 添加到历史最前面
        searchHistoryList.unshift(term);

        // 只保留最近10条
        if (searchHistoryList.length > 10) {
            searchHistoryList.pop();
        }

        // 保存到本地存储
        localStorage.setItem('searchHistory', JSON.stringify(searchHistoryList));

        // 更新显示
        updateSearchHistory();
    }

    // 加载热门游戏
    let popularGamesOffset = 0;
    const popularGamesLimit = 12; // 初始显示12个游戏（4行3列）
    let popularGamesLoading = false;
    let allPopularGamesLoaded = false;

    // 从API加载热门游戏
    function loadPopularGames(offset = 0, limit = 12) {
        if (popularGamesLoading || allPopularGamesLoaded) return;

        const popularGamesGrid = document.getElementById('popular-games-grid');
        const loadingMore = document.getElementById('popular-loading-more');
        const popularEmptyState = document.getElementById('popular-empty-state');

        if (offset === 0) {
            popularGamesGrid.innerHTML = `<div class="loading">${__('Loading')}</div>`;
            popularEmptyState.style.display = 'none';
        } else {
            loadingMore.style.display = 'block';
        }

        popularGamesLoading = true;

        // 从API加载热门游戏数据
        fetch(`/index/search/getMorePopularGames?offset=${offset}&limit=${limit}`)
            .then(response => response.json())
            .then(data => {
                if (offset === 0) {
                    popularGamesGrid.innerHTML = '';
                }

                if (data.code === 1 && data.data && data.data.length > 0) {
                    const games = data.data;

                    // 添加游戏卡片到网格
                    games.forEach(game => {
                        const gameCard = document.createElement('div');
                        gameCard.className = 'game-card';
                        gameCard.setAttribute('data-id', game.id);
                        gameCard.setAttribute('data-code', game.code);

                        gameCard.innerHTML = `
                            <div class="game-image">
                                <div class="game-icon">
                                    <img src="${game.image || ''}" alt="${game.name || '游戏'+game.id}" class="game-thumbnail">
                                </div>
                            </div>
                        `;

                        // 添加游戏卡片点击事件
                        gameCard.addEventListener('click', function() {
                            console.log('游戏卡片点击:', game.id, game.code);
                            // 跳转到游戏页面
                            window.location.href = `/index/createiframe/index?gameid=${game.id}&gamecode=${game.code}`;
                        });

                        popularGamesGrid.appendChild(gameCard);
                    });

                    // 更新偏移量
                    popularGamesOffset += games.length;

                    // 检查是否还有更多游戏
                    allPopularGamesLoaded = !data.has_more;

                    // 检查空状态
                    checkEmptyState('popular-games-grid', 'popular-empty-state');
                } else {
                    if (offset === 0) {
                        popularGamesGrid.innerHTML = '';
                        popularEmptyState.style.display = 'flex';
                    }
                    allPopularGamesLoaded = true;
                }
            })
            .catch(error => {
                console.error('加载热门游戏失败:', error);
                if (offset === 0) {
                    popularGamesGrid.innerHTML = '';
                    popularEmptyState.style.display = 'flex';
                }
                allPopularGamesLoaded = true;
            })
            .finally(() => {
                popularGamesLoading = false;
                loadingMore.style.display = 'none';
            });
    }

    // 加载最近游戏
    function loadRecentGames() {
        const recentGamesContainer = document.getElementById('recent-games');
        const recentEmptyState = document.getElementById('recent-empty-state');

        // 从本地存储获取最近玩过的游戏
        const recentGames = JSON.parse(localStorage.getItem('recentGames') || '[]');

        if (recentGames.length > 0) {
            // 创建游戏网格
            const gameGrid = document.createElement('div');
            gameGrid.className = 'game-grid';
            gameGrid.id = 'recent-games-grid';

            // 添加游戏卡片
            recentGames.forEach(game => {
                const gameCard = document.createElement('div');
                gameCard.className = 'game-card';
                gameCard.setAttribute('data-id', game.id);

                gameCard.innerHTML = `
                    <div class="game-image">
                        <div class="game-icon">
                            <img src="${game.image || ''}" alt="${game.name || __('Game')}" class="game-thumbnail">
                        </div>
                    </div>
                `;

                // 添加游戏卡片点击事件
                gameCard.addEventListener('click', function() {
                    console.log(__('Recent game clicked:'), game.id);
                    // 这里可以添加启动游戏的逻辑
                });

                gameGrid.appendChild(gameCard);
            });

            // 清除现有内容并添加游戏网格
            recentGamesContainer.innerHTML = '';
            recentGamesContainer.appendChild(gameGrid);
            recentEmptyState.style.display = 'none';
        } else {
            // 如果没有最近游戏，显示空状态
            recentEmptyState.style.display = 'flex';
        }
    }

    // 加载收藏游戏
    function loadFavoriteGames() {
        const favoriteGamesContainer = document.getElementById('favorite-games');
        const favoriteEmptyState = document.getElementById('favorite-empty-state');

        // 从本地存储获取收藏的游戏
        const favoriteGames = JSON.parse(localStorage.getItem('favoriteGames') || '[]');

        if (favoriteGames.length > 0) {
            // 创建游戏网格
            const gameGrid = document.createElement('div');
            gameGrid.className = 'game-grid';
            gameGrid.id = 'favorite-games-grid';

            // 添加游戏卡片
            favoriteGames.forEach(game => {
                const gameCard = document.createElement('div');
                gameCard.className = 'game-card';
                gameCard.setAttribute('data-id', game.id);

                gameCard.innerHTML = `
                    <div class="game-image">
                        <div class="game-icon">
                            <img src="${game.image || ''}" alt="${game.name || __('Game')}" class="game-thumbnail">
                        </div>
                    </div>
                `;

                // 添加游戏卡片点击事件
                gameCard.addEventListener('click', function() {
                    console.log(__('Favorite game clicked:'), game.id);
                    // 这里可以添加启动游戏的逻辑
                });

                gameGrid.appendChild(gameCard);
            });

            // 清除现有内容并添加游戏网格
            favoriteGamesContainer.innerHTML = '';
            favoriteGamesContainer.appendChild(gameGrid);
            favoriteEmptyState.style.display = 'none';
        } else {
            // 如果没有收藏游戏，显示空状态
            favoriteEmptyState.style.display = 'flex';
        }
    }

    // 检查是否显示空状态
    function checkEmptyState(gridId, emptyStateId) {
        const grid = document.getElementById(gridId);
        const emptyState = document.getElementById(emptyStateId);

        // 如果网格中没有游戏卡片，显示空状态
        if (!grid || grid.children.length === 0 ||
            (grid.children.length === 1 && grid.children[0].className === 'loading')) {
            emptyState.style.display = 'flex';
        } else {
            emptyState.style.display = 'none';
        }
    }

    // 搜索相关变量
    let searchQuery = '';
    let searchOffset = 0;
    let searchLimit = 12; // 每次加载12个搜索结果
    let searchLoading = false;
    let allSearchResultsLoaded = false;

    // 执行搜索
    function performSearch(query, offset = 0, append = false) {
        // 防御性检查：确保查询不为空
        if (!query || !query.trim()) {
            console.error('搜索查询为空');
            return;
        }

        // 记录搜索开始
        console.log(`开始搜索: query=${query}, offset=${offset}, append=${append}`);

        try {
            // 如果是新搜索（不是加载更多），则重置变量
            if (offset === 0) {
                searchOffset = 0;
                allSearchResultsLoaded = false;
                searchQuery = query.trim();

                // 添加到搜索历史
                try {
                    addToSearchHistory(searchQuery);
                } catch (e) {
                    console.error('添加搜索历史时出错:', e);
                }

                // 切换到搜索结果标签
                try {
                    const searchTab = document.querySelector('.tab[data-tab="search"]');
                    if (searchTab) {
                        // 手动切换标签，而不是使用click()
                        // 更新标签激活状态
                        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                        searchTab.classList.add('active');

                        // 更新内容区域激活状态
                        document.querySelectorAll('.tab-content').forEach(content => {
                            content.classList.remove('active');
                        });
                        const searchResultsTab = document.getElementById('search-results');
                        if (searchResultsTab) {
                            searchResultsTab.classList.add('active');
                        } else {
                            console.error('找不到搜索结果内容区域');
                        }

                        console.log('已切换到搜索结果标签');
                    } else {
                        console.error('找不到搜索结果标签');
                    }
                } catch (e) {
                    console.error('切换到搜索结果标签时出错:', e);
                }
            }

            // 如果正在加载或已加载全部，则返回
            if (searchLoading) {
                console.log('搜索正在进行中，忽略新的搜索请求');
                return;
            }

            if (allSearchResultsLoaded && append) {
                console.log('已加载所有搜索结果，忽略加载更多请求');
                return;
            }

            // 设置加载状态
            searchLoading = true;

            // 获取DOM元素
            const searchResultsTab = document.getElementById('search-results');
            if (!searchResultsTab) {
                console.error('找不到搜索结果标签页');
                searchLoading = false;
                return;
            }

            const searchEmptyState = document.getElementById('search-empty-state');
            if (!searchEmptyState) {
                console.error('找不到搜索空状态元素');
                // 继续执行，这不是致命错误
            }

            // 显示加载状态
            if (offset === 0) {
                // 清空搜索结果
                searchResultsTab.innerHTML = `<div class="loading">${__('Loading')}</div>`;

                // 隐藏空状态
                if (searchEmptyState) {
                    searchEmptyState.style.display = 'none';
                }
            } else if (append) {
                // 显示加载更多指示器
                const loadingMore = document.getElementById('search-loading-more');
                if (loadingMore) {
                    loadingMore.style.display = 'block';
                }
            }

            // 构建URL，确保参数正确编码
            // 使用传入的query参数，而不是searchQuery变量
            const url = `/index/search/searchGames?query=${encodeURIComponent(query)}&offset=${offset}&limit=${searchLimit}`;
            console.log(`发送搜索请求: ${url}`);

            // 添加调试信息
            console.log(`搜索参数: query=${query}, offset=${offset}, limit=${searchLimit}, append=${append}`);
            console.log(`搜索状态: searchLoading=${searchLoading}, allSearchResultsLoaded=${allSearchResultsLoaded}, searchQuery=${searchQuery}`);

            // 发送AJAX请求获取搜索结果
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log(`搜索请求成功: code=${data.code}, 结果数=${data.data ? data.data.length : 0}`);

                    try {
                        // 重新获取DOM元素，以防它们已经改变
                        const currentSearchResultsTab = document.getElementById('search-results');
                        if (!currentSearchResultsTab) {
                            console.error('处理搜索结果时找不到搜索结果标签页');
                            return;
                        }

                        const currentSearchEmptyState = document.getElementById('search-empty-state');

                        // 如果是新搜索，清空搜索结果
                        if (offset === 0) {
                            currentSearchResultsTab.innerHTML = '';
                        }

                        // 处理搜索结果
                        if (data.code === 1 && data.data && data.data.length > 0) {
                            // 获取或创建游戏网格
                            let gameGrid;
                            if (offset === 0) {
                                // 创建新的游戏网格
                                gameGrid = document.createElement('div');
                                gameGrid.className = 'game-grid';
                                gameGrid.id = 'search-results-grid';
                                currentSearchResultsTab.appendChild(gameGrid);

                                // 创建加载更多元素
                                try {
                                    // 先检查是否已存在，如果存在则移除
                                    const existingLoadingMore = document.getElementById('search-loading-more');
                                    if (existingLoadingMore) {
                                        existingLoadingMore.remove();
                                    }

                                    // 创建新的加载更多元素
                                    const loadingMoreElement = document.createElement('div');
                                    loadingMoreElement.className = 'loading-more';
                                    loadingMoreElement.id = 'search-loading-more';
                                    loadingMoreElement.style.display = 'none';
                                    loadingMoreElement.innerHTML = '<div class="loading-spinner"></div>';

                                    // 添加到DOM
                                    currentSearchResultsTab.appendChild(loadingMoreElement);
                                    console.log('成功添加search-loading-more元素');
                                } catch (e) {
                                    console.error('添加search-loading-more元素时出错:', e);
                                }
                            } else {
                                // 获取现有的游戏网格
                                gameGrid = document.getElementById('search-results-grid');
                                if (!gameGrid) {
                                    console.error('找不到搜索结果网格');
                                    return;
                                }
                            }

                            // 添加游戏卡片到网格
                            data.data.forEach(game => {
                                try {
                                    const gameCard = document.createElement('div');
                                    gameCard.className = 'game-card';
                                    gameCard.setAttribute('data-id', game.id || '');
                                    gameCard.setAttribute('data-code', game.code || '');

                                    gameCard.innerHTML = `
                                        <div class="game-image">
                                            <div class="game-icon">
                                                <img src="${game.image || ''}" alt="${game.name || __('Game')}" class="game-thumbnail">
                                            </div>
                                        </div>
                                    `;

                                    // 添加游戏卡片点击事件
                                    gameCard.addEventListener('click', function() {
                                        try {
                                            console.log(__('Game card clicked:'), game.id, game.code);
                                            // 跳转到游戏页面
                                            if (game.id && game.code) {
                                                window.location.href = `/index/createiframe/index?gameid=${game.id}&gamecode=${game.code}`;
                                            } else {
                                                console.error('游戏ID或代码为空，无法跳转');
                                            }
                                        } catch (e) {
                                            console.error('游戏卡片点击事件处理出错:', e);
                                        }
                                    });

                                    gameGrid.appendChild(gameCard);
                                } catch (e) {
                                    console.error('创建游戏卡片时出错:', e);
                                }
                            });

                            // 更新偏移量
                            searchOffset += data.data.length;

                            // 检查是否还有更多游戏
                            allSearchResultsLoaded = !data.has_more;

                            // 隐藏空状态
                            if (currentSearchEmptyState) {
                                currentSearchEmptyState.style.display = 'none';
                            }

                            console.log(`已添加${data.data.length}个游戏卡片，当前偏移量=${searchOffset}，是否还有更多=${!allSearchResultsLoaded}`);
                        } else {
                            if (offset === 0) {
                                // 显示空状态
                                if (currentSearchEmptyState) {
                                    currentSearchEmptyState.style.display = 'flex';
                                    const emptyText = currentSearchEmptyState.querySelector('.empty-text');
                                    if (emptyText) {
                                        emptyText.textContent = __('No games found for') + ' "' + searchQuery + '"';
                                    }
                                }
                            }
                            allSearchResultsLoaded = true;
                            console.log('没有找到游戏或搜索结果为空');
                        }
                    } catch (e) {
                        console.error('处理搜索结果时出错:', e);
                    }
                })
                .catch(error => {
                    console.error(__('Search failed'), ':', error);
                    try {
                        // 重新获取DOM元素
                        const currentSearchResultsTab = document.getElementById('search-results');
                        const currentSearchEmptyState = document.getElementById('search-empty-state');

                        if (offset === 0 && currentSearchResultsTab) {
                            currentSearchResultsTab.innerHTML = '';
                            if (currentSearchEmptyState) {
                                currentSearchEmptyState.style.display = 'flex';
                                const emptyText = currentSearchEmptyState.querySelector('.empty-text');
                                if (emptyText) {
                                    emptyText.textContent = __('Search failed');
                                }
                            }
                        }
                    } catch (e) {
                        console.error('处理搜索错误时出错:', e);
                    }
                    allSearchResultsLoaded = true;
                })
                .finally(() => {
                    try {
                        // 重置加载状态
                        searchLoading = false;
                        console.log('搜索请求完成，重置加载状态');

                        // 隐藏加载更多指示器
                        const loadingMoreElement = document.getElementById('search-loading-more');
                        if (loadingMoreElement) {
                            loadingMoreElement.style.display = 'none';
                        }

                        // 检查是否需要添加加载更多指示器
                        if (searchQuery && !allSearchResultsLoaded) {
                            console.log('搜索未完成，添加加载更多指示器');
                            try {
                                // 检查搜索结果标签是否仍然存在
                                const searchResultsTab = document.getElementById('search-results');
                                if (!searchResultsTab) {
                                    console.error('添加加载更多指示器时找不到搜索结果标签页');
                                    return;
                                }

                                // 检查是否已存在加载更多指示器
                                let loadingMoreElement = document.getElementById('search-loading-more');
                                if (!loadingMoreElement) {
                                    // 创建加载更多指示器
                                    loadingMoreElement = document.createElement('div');
                                    loadingMoreElement.className = 'loading-more';
                                    loadingMoreElement.id = 'search-loading-more';
                                    loadingMoreElement.innerHTML = '<div class="loading-spinner"></div>';

                                    // 添加到DOM
                                    searchResultsTab.appendChild(loadingMoreElement);
                                    console.log('成功添加search-loading-more元素');

                                    // 设置无限滚动
                                    setupSearchInfiniteScroll();
                                }
                            } catch (e) {
                                console.error('添加加载更多指示器时出错:', e);
                            }
                        }
                    } catch (e) {
                        console.error('搜索完成后处理时出错:', e);
                    }
                });
        } catch (e) {
            console.error('执行搜索时出错:', e);
            searchLoading = false;
        }
    }

    // 设置搜索结果的无限滚动
    function setupSearchInfiniteScroll() {
        const searchResultsTab = document.getElementById('search-results');
        if (!searchResultsTab) {
            console.error('搜索结果标签页不存在');
            return;
        }

        // 使用IntersectionObserver监听底部加载器
        const loadingMoreElement = document.getElementById('search-loading-more');
        if (!loadingMoreElement) {
            console.error('搜索加载更多元素不存在');
            return;
        }

        console.log('设置搜索结果无限滚动监听');

        // 添加防抖动功能，避免频繁触发
        let scrollTimeout = null;
        const scrollDebounce = function(callback, delay) {
            if (scrollTimeout) clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(callback, delay);
        };

        // 创建IntersectionObserver
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                // 检查searchResultsTab是否仍然存在
                const currentSearchResultsTab = document.getElementById('search-results');
                if (!currentSearchResultsTab) return;

                if (entry.isIntersecting && !searchLoading && !allSearchResultsLoaded &&
                    currentSearchResultsTab.classList.contains('active') && searchQuery) {
                    // 使用防抖动，避免频繁触发
                    scrollDebounce(() => {
                        console.log('IntersectionObserver检测到滚动到底部，加载更多搜索结果，当前偏移量:', searchOffset);
                        performSearch(searchQuery, searchOffset, true); // 加载更多搜索结果
                    }, 300);
                }
            });
        }, { threshold: 0.1, rootMargin: '100px' });

        // 开始观察
        observer.observe(loadingMoreElement);

        // 添加滚动事件监听，作为备用方案
        const tabContents = document.querySelector('.tab-contents');
        if (tabContents) {
            // 创建新的滚动监听器
            const searchScrollHandler = function() {
                // 检查searchResultsTab是否仍然存在
                const currentSearchResultsTab = document.getElementById('search-results');
                if (!currentSearchResultsTab) return;

                if (searchLoading || allSearchResultsLoaded || !currentSearchResultsTab.classList.contains('active') || !searchQuery) {
                    return;
                }

                const scrollHeight = this.scrollHeight;
                const scrollTop = this.scrollTop;
                const clientHeight = this.clientHeight;

                // 当滚动到距离底部100px时，加载更多
                if (scrollHeight - scrollTop - clientHeight < 100) {
                    // 使用防抖动，避免频繁触发
                    scrollDebounce(() => {
                        console.log('滚动事件检测到滚动到底部，加载更多搜索结果，当前偏移量:', searchOffset);
                        performSearch(searchQuery, searchOffset, true); // 加载更多搜索结果
                    }, 300);
                }
            };

            // 添加滚动监听器
            if (!tabContents._searchScrollHandler) {
                tabContents.addEventListener('scroll', searchScrollHandler);
                tabContents._searchScrollHandler = searchScrollHandler;
                console.log('已添加搜索结果滚动事件监听');
            }
        } else {
            console.error('找不到.tab-contents元素');
        }
    }

    // 检测滚动到底部，加载更多游戏
    function setupInfiniteScroll() {
        const popularGamesTab = document.getElementById('popular-games');
        if (!popularGamesTab) {
            console.error('热门游戏标签页不存在');
            return;
        }

        // 使用IntersectionObserver监听底部加载器
        const loadingMoreElement = document.getElementById('popular-loading-more');
        if (!loadingMoreElement) {
            console.error('加载更多元素不存在');
            return;
        }

        console.log('设置无限滚动监听');

        // 添加防抖动功能，避免频繁触发
        let scrollTimeout = null;
        const scrollDebounce = function(callback, delay) {
            if (scrollTimeout) clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(callback, delay);
        };

        // 创建IntersectionObserver
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                // 检查popularGamesTab是否仍然存在
                const currentPopularGamesTab = document.getElementById('popular-games');
                if (!currentPopularGamesTab) return;

                if (entry.isIntersecting && !popularGamesLoading && !allPopularGamesLoaded &&
                    currentPopularGamesTab.classList.contains('active')) {
                    // 使用防抖动，避免频繁触发
                    scrollDebounce(() => {
                        console.log('IntersectionObserver检测到滚动到底部，加载更多游戏，当前偏移量:', popularGamesOffset);
                        loadPopularGames(popularGamesOffset, 12); // 加载额外的12个游戏
                    }, 300);
                }
            });
        }, { threshold: 0.1, rootMargin: '100px' });

        // 开始观察
        observer.observe(loadingMoreElement);

        // 添加滚动事件监听，作为备用方案
        const tabContents = document.querySelector('.tab-contents');
        if (tabContents) {
            // 移除之前的滚动监听器（如果有）
            if (tabContents._scrollHandler) {
                tabContents.removeEventListener('scroll', tabContents._scrollHandler);
            }

            // 创建新的滚动监听器
            const scrollHandler = function() {
                // 检查popularGamesTab是否仍然存在
                const currentPopularGamesTab = document.getElementById('popular-games');
                if (!currentPopularGamesTab) return;

                if (popularGamesLoading || allPopularGamesLoaded || !currentPopularGamesTab.classList.contains('active')) {
                    return;
                }

                const scrollHeight = this.scrollHeight;
                const scrollTop = this.scrollTop;
                const clientHeight = this.clientHeight;

                // 当滚动到距离底部100px时，加载更多
                if (scrollHeight - scrollTop - clientHeight < 100) {
                    // 使用防抖动，避免频繁触发
                    scrollDebounce(() => {
                        console.log('滚动事件检测到滚动到底部，加载更多游戏，当前偏移量:', popularGamesOffset);
                        loadPopularGames(popularGamesOffset, 12); // 加载额外的12个游戏
                    }, 300);
                }
            };

            // 添加滚动监听器
            tabContents.addEventListener('scroll', scrollHandler);
            tabContents._scrollHandler = scrollHandler;

            console.log('已添加滚动事件监听');
        } else {
            console.error('找不到.tab-contents元素');
        }

        // 添加调试按钮，用于手动加载更多游戏（仅在开发环境中使用）
        if (document.getElementById('debug-load-more-btn') === null) {
            const debugBtn = document.createElement('button');
            debugBtn.id = 'debug-load-more-btn';
            debugBtn.textContent = 'Carregar mais jogos';
            debugBtn.style.position = 'fixed';
            debugBtn.style.bottom = '10px';
            debugBtn.style.right = '10px';
            debugBtn.style.zIndex = '9999';
            debugBtn.style.padding = '5px 10px';
            debugBtn.style.backgroundColor = '#f00';
            debugBtn.style.color = '#fff';
            debugBtn.style.border = 'none';
            debugBtn.style.borderRadius = '5px';
            debugBtn.style.display = 'none'; // 默认隐藏

            debugBtn.addEventListener('click', function() {
                if (!popularGamesLoading && !allPopularGamesLoaded) {
                    console.log('手动加载更多游戏，当前偏移量:', popularGamesOffset);
                    loadPopularGames(popularGamesOffset, 12);
                }
            });

            document.body.appendChild(debugBtn);

            // 添加快捷键Ctrl+Shift+D显示/隐藏调试按钮
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                    debugBtn.style.display = debugBtn.style.display === 'none' ? 'block' : 'none';
                }
            });
        }
    }

    // 搜索输入框事件
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch(this.value);
        }
    });

    // 搜索图标点击事件已在下方添加

    // 清除所有历史记录
    clearHistoryBtn.addEventListener('click', function() {
        searchHistoryList = [];
        localStorage.setItem('searchHistory', JSON.stringify(searchHistoryList));
        updateSearchHistory();
    });

    // 返回按钮
    backBtn.addEventListener('click', function() {
        window.location.href = '/';
    });

    // 标签切换
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            try {
                // 获取标签ID
                const tabId = this.getAttribute('data-tab');
                if (!tabId) {
                    console.error('标签没有data-tab属性');
                    return;
                }

                console.log(`点击标签: ${tabId}`);

                // 更新标签激活状态
                tabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                // 获取对应的内容元素
                // 搜索标签的内容ID是'search-results'，其他标签的内容ID是'tabId-games'
                const contentId = tabId === 'search' ? 'search-results' : tabId + '-games';
                const targetContent = document.getElementById(contentId);
                if (!targetContent) {
                    console.error(`找不到标签内容: ${contentId}`);
                    return;
                }

                // 更新内容区域激活状态
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });
                targetContent.classList.add('active');

                // 根据切换的标签加载相应内容
                if (tabId === 'search') {
                    // 如果有搜索查询，执行搜索
                    if (searchQuery) {
                        const searchResultsGrid = document.getElementById('search-results-grid');
                        if (!searchResultsGrid) {
                            console.log('搜索标签：没有搜索结果网格，执行搜索');
                            // 使用setTimeout避免可能的递归调用
                            setTimeout(function() {
                                try {
                                    performSearch(searchQuery);
                                } catch (e) {
                                    console.error('执行搜索时出错:', e);
                                }
                            }, 0);
                        } else {
                            console.log('搜索标签：已有搜索结果网格，不重新搜索');
                        }
                    } else {
                        console.log('搜索标签：没有搜索查询，不执行搜索');
                    }
                } else if (tabId === 'popular') {
                    const popularGamesGrid = document.getElementById('popular-games-grid');
                    if (popularGamesGrid && popularGamesGrid.children.length === 0) {
                        console.log('热门标签：没有游戏，加载热门游戏');
                        // 使用setTimeout避免可能的递归调用
                        setTimeout(function() {
                            try {
                                loadPopularGames();
                            } catch (e) {
                                console.error('加载热门游戏时出错:', e);
                            }
                        }, 0);
                    } else {
                        console.log('热门标签：已有游戏，不重新加载');
                    }
                } else if (tabId === 'recent') {
                    console.log('最近标签：加载最近游戏');
                    // 使用setTimeout避免可能的递归调用
                    setTimeout(function() {
                        try {
                            loadRecentGames();
                        } catch (e) {
                            console.error('加载最近游戏时出错:', e);
                        }
                    }, 0);
                } else if (tabId === 'favorite') {
                    console.log('收藏标签：加载收藏游戏');
                    // 使用setTimeout避免可能的递归调用
                    setTimeout(function() {
                        try {
                            loadFavoriteGames();
                        } catch (e) {
                            console.error('加载收藏游戏时出错:', e);
                        }
                    }, 0);
                }
            } catch (e) {
                console.error('标签切换时出错:', e);
            }
        });
    });

    // 添加搜索图标点击事件
    const searchIcon = document.querySelector('.search-icon');
    if (searchIcon) {
        searchIcon.addEventListener('click', function() {
            console.log('搜索图标点击');
            if (searchInput && searchInput.value.trim()) {
                performSearch(searchInput.value);
            } else {
                console.error('搜索输入为空');
                // 如果搜索框为空，显示提示
                try {
                    if (window.Toast && typeof window.Toast.info === 'function') {
                        window.Toast.info('Por favor, insira palavras-chave de pesquisa');
                    } else {
                        console.warn('Por favor, insira palavras-chave de pesquisa');
                    }
                } catch (e) {
                    console.warn('Por favor, insira palavras-chave de pesquisa');
                }
            }
        });
    } else {
        console.error('找不到搜索图标');
    }

    // 添加搜索输入框回车事件
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                console.log('搜索输入框回车');
                if (searchInput.value.trim()) {
                    performSearch(searchInput.value);
                } else {
                    console.error('搜索输入为空');
                }
            }
        });
    }

    // 添加清除历史按钮点击事件
    if (clearHistoryBtn) {
        clearHistoryBtn.addEventListener('click', function() {
            console.log('清除搜索历史');
            searchHistoryList = [];
            localStorage.setItem('searchHistory', JSON.stringify(searchHistoryList));
            updateSearchHistory();
        });
    }

    // 添加返回按钮点击事件
    if (backBtn) {
        backBtn.addEventListener('click', function() {
            console.log('返回按钮点击');
            window.history.back();
        });
    }

    // 初始化
    updateSearchHistory();
    setupInfiniteScroll();
    // 默认加载热门游戏
    loadPopularGames();
    loadRecentGames();
    loadFavoriteGames();
});
