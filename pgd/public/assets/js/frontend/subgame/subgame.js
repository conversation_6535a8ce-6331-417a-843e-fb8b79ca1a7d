/**
 * 子游戏页面专用JavaScript
 * 内存优化版本：使用事件委托、减少DOM操作、添加防抖功能、清理资源
 */

// 立即执行函数表达式 (IIFE)，避免全局变量污染
(function() {
    // 缓存常用DOM元素和值
    let DOM = {};
    let state = {
        urlParams: null,
        currentCategory: '',
        currentHCategory: '',
        currentPage: 1,
        currentSearch: '',
        minBetAmount: 0.5
    };

    // 存储定时器ID，用于清理
    let timeoutIds = [];

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', init);

    // 页面卸载前清理资源
    window.addEventListener('beforeunload', cleanupResources);

    /**
     * 清理资源，防止内存泄漏
     */
    function cleanupResources() {
        // 清除所有定时器
        timeoutIds.forEach(id => clearTimeout(id));
        timeoutIds = [];

        // 移除事件监听器
        if (DOM.container) {
            DOM.container.removeEventListener('click', handleContainerClick);
        }

        // 注意：这些事件处理函数是通过匿名函数绑定的，无法直接移除
        // 但在页面卸载时，它们会随着DOM元素的销毁而被垃圾回收

        // 清空DOM缓存
        DOM = {};
    }

    /**
     * 初始化函数
     */
    function init() {
        // 初始化状态
        initState();

        // 缓存DOM元素
        cacheDOM();

        // 使用事件委托绑定事件
        bindEvents();

        // 初始化UI状态
        updateUIState();
    }

    /**
     * 初始化状态
     */
    function initState() {
        state.urlParams = new URLSearchParams(window.location.search);
        state.currentCategory = state.urlParams.get('category') || 'slots';
        state.currentHCategory = state.urlParams.get('hcategory') || '';
        state.currentPage = parseInt(state.urlParams.get('page') || '1');
        state.currentSearch = state.urlParams.get('search') || '';
        state.minBetAmount = parseFloat(window.betMinAmount || '0.5');
    }

    /**
     * 缓存DOM元素
     */
    function cacheDOM() {
        DOM = {
            container: document.querySelector('.container'),
            backBtn: document.getElementById('backBtn'),
            searchInput: document.getElementById('searchInput'),
            searchIcon: document.querySelector('.search-icon'),
            gameGrid: document.getElementById('gameGrid'),
            pagination: document.querySelector('.pagination'),
            prevBtn: document.getElementById('prevBtn'),
            nextBtn: document.getElementById('nextBtn'),
            pageNumbers: document.querySelectorAll('.page-number'),
            categoryItems: document.querySelectorAll('.category-item'),
            hCategoryItems: document.querySelectorAll('.h-category-item')
        };
    }

    /**
     * 使用事件委托绑定事件
     */
    function bindEvents() {
        // 使用事件委托处理容器内的所有点击事件
        if (DOM.container) {
            DOM.container.addEventListener('click', handleContainerClick);
        }

        // 搜索相关事件
        if (DOM.searchInput) {
            // 创建一个搜索输入框的事件处理函数
            const handleSearchKeypress = function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    handleSearch();
                }
            };

            // 绑定事件
            DOM.searchInput.addEventListener('keypress', handleSearchKeypress);
        }

        if (DOM.searchIcon) {
            // 绑定搜索图标点击事件
            DOM.searchIcon.addEventListener('click', handleSearch);
        }
    }

    /**
     * 更新UI状态
     */
    function updateUIState() {
        // 更新分页按钮状态
        updatePaginationState();
    }

    /**
     * 处理容器内的点击事件（事件委托）
     * @param {Event} e 点击事件对象
     */
    function handleContainerClick(e) {
        // 返回按钮点击
        if (e.target === DOM.backBtn || DOM.backBtn && DOM.backBtn.contains(e.target)) {
            e.preventDefault();
            window.location.href = '/';
            return;
        }

        // 分类项点击
        const categoryItem = e.target.closest('.category-item');
        if (categoryItem) {
            e.preventDefault();
            handleCategoryClick(categoryItem);
            return;
        }

        // 横向分类项点击
        const hCategoryItem = e.target.closest('.h-category-item');
        if (hCategoryItem) {
            e.preventDefault();
            handleHCategoryClick(hCategoryItem);
            return;
        }

        // 游戏卡片点击
        const gameCard = e.target.closest('.game-card');
        if (gameCard) {
            e.preventDefault();
            handleGameCardClick(gameCard);
            return;
        }

        // 分页按钮点击
        if (e.target === DOM.prevBtn || DOM.prevBtn && DOM.prevBtn.contains(e.target)) {
            e.preventDefault();
            handlePrevPageClick();
            return;
        }

        if (e.target === DOM.nextBtn || DOM.nextBtn && DOM.nextBtn.contains(e.target)) {
            e.preventDefault();
            handleNextPageClick();
            return;
        }

        // 页码点击
        const pageNumber = e.target.closest('.page-number');
        if (pageNumber) {
            e.preventDefault();
            handlePageNumberClick(pageNumber);
            return;
        }
    }

    /**
     * 处理分类点击
     * @param {HTMLElement} categoryItem 被点击的分类元素
     */
    function handleCategoryClick(categoryItem) {
        const category = categoryItem.getAttribute('data-category');
        if (!category) return;

        // 构建URL，保持横向分类
        let url = `/index/subgame/index?category=${category}`;
        if (state.currentHCategory) {
            url += `&hcategory=${state.currentHCategory}`;
        }

        // 跳转到对应分类页面
        navigateTo(url);
    }

    /**
     * 处理横向分类点击
     * @param {HTMLElement} hCategoryItem 被点击的横向分类元素
     */
    function handleHCategoryClick(hCategoryItem) {
        const hCategory = hCategoryItem.getAttribute('data-category');
        if (!hCategory) return;

        // 构建URL，保持主分类
        const url = `/index/subgame/index?category=${state.currentCategory}&hcategory=${hCategory}`;

        // 跳转到对应分类页面
        navigateTo(url);
    }

    /**
     * 处理游戏卡片点击
     * @param {HTMLElement} gameCard 被点击的游戏卡片元素
     */
    function handleGameCardClick(gameCard) {
        const gameId = gameCard.getAttribute('data-id');
        const gameCode = gameCard.getAttribute('data-code');

        if (!gameId) return;

        try {
            // 判断登录状态
            if (typeof window.authPopup !== 'undefined' && !document.querySelector('.header-right.logged-in')) {
                // 未登录状态，显示登录弹窗
                if (window.authPopup && typeof window.authPopup.show === 'function') {
                    window.authPopup.show('login');
                } else {
                    console.error(window.__('login_popup_not_initialized'));
                    // 跳转到登录页面
                    window.location.href = '/index/user/login';
                }
                return;
            }

            // 已登录状态，获取玩家余额
            let playerBalance = 0;
            
            // 从window对象获取余额，这个值应该在页面加载时设置
            if (typeof window.playerBalance !== 'undefined') {
                playerBalance = parseFloat(window.playerBalance || '0');
            } 

            console.log('玩家余额:', playerBalance, '最小投注金额:', state.minBetAmount);

            // 检查余额是否足够
            if (playerBalance >= state.minBetAmount) {
                // 余额足够，跳转到游戏iframe页面
                let url = `/index/createiframe/index?gameid=${gameId}`;
                if (gameCode) {
                    url += `&gamecode=${gameCode}`;
                }
                navigateTo(url);
            } else {
                // 余额不足，显示提示
                showInsufficientBalanceMessage();
            }
        } catch (error) {
            console.error('处理游戏卡片点击时出错:', error);
            // 显示友好的错误提示
            showErrorMessage('Falha ao carregar o jogo, tente novamente mais tarde');
        }
    }

    /**
     * 处理上一页按钮点击
     */
    function handlePrevPageClick() {
        if (state.currentPage > 1) {
            navigateToPage(state.currentPage - 1);
        }
    }

    /**
     * 处理下一页按钮点击
     */
    function handleNextPageClick() {
        const totalPages = DOM.pageNumbers.length;
        if (state.currentPage < totalPages) {
            navigateToPage(state.currentPage + 1);
        }
    }

    /**
     * 处理页码点击
     * @param {HTMLElement} pageNumber 被点击的页码元素
     */
    function handlePageNumberClick(pageNumber) {
        const page = parseInt(pageNumber.getAttribute('data-page'));
        if (page && page !== state.currentPage) {
            navigateToPage(page);
        }
    }

    /**
     * 处理搜索
     */
    function handleSearch() {
        const query = DOM.searchInput ? DOM.searchInput.value.trim() : '';

        // 构建URL
        let url = `/index/subgame/index?category=${state.currentCategory}`;
        url += `&search=${encodeURIComponent(query)}`;

        // 添加横向分类参数（如果存在）
        if (state.currentHCategory) {
            url += `&hcategory=${state.currentHCategory}`;
        }

        // 跳转到搜索结果页面
        navigateTo(url);
    }

    /**
     * 更新分页按钮状态
     */
    function updatePaginationState() {
        if (!DOM.pagination) return;

        // 更新上一页按钮状态
        if (DOM.prevBtn) {
            if (state.currentPage <= 1) {
                DOM.prevBtn.classList.add('disabled');
                DOM.prevBtn.style.opacity = '0.5';
                DOM.prevBtn.style.cursor = 'not-allowed';
            } else {
                DOM.prevBtn.classList.remove('disabled');
                DOM.prevBtn.style.opacity = '1';
                DOM.prevBtn.style.cursor = 'pointer';
            }
        }

        // 更新下一页按钮状态
        if (DOM.nextBtn) {
            const totalPages = DOM.pageNumbers.length;
            if (state.currentPage >= totalPages) {
                DOM.nextBtn.classList.add('disabled');
                DOM.nextBtn.style.opacity = '0.5';
                DOM.nextBtn.style.cursor = 'not-allowed';
            } else {
                DOM.nextBtn.classList.remove('disabled');
                DOM.nextBtn.style.opacity = '1';
                DOM.nextBtn.style.cursor = 'pointer';
            }
        }
    }

    /**
     * 跳转到指定页码
     * @param {number} page 页码
     */
    function navigateToPage(page) {
        let url = `/index/subgame/index?category=${state.currentCategory}&page=${page}`;

        // 添加横向分类参数（如果存在）
        if (state.currentHCategory) {
            url += `&hcategory=${state.currentHCategory}`;
        }

        // 添加搜索参数（如果存在）
        if (state.currentSearch) {
            url += `&search=${encodeURIComponent(state.currentSearch)}`;
        }

        // 跳转
        navigateTo(url);
    }

    /**
     * 显示余额不足提示
     */
    function showInsufficientBalanceMessage() {
        if (typeof Toast !== 'undefined') {
            Toast.error(window.__('Saldo insuficiente, valor mínimo de aposta:') + ' ' + state.minBetAmount);
        } else {
            console.error(window.__('insufficient_balance') + ' ' + state.minBetAmount);
        }
    }

    /**
     * 显示错误提示
     * @param {string} message 错误信息
     */
    function showErrorMessage(message) {
        if (window.Toast && typeof window.Toast.error === 'function') {
            window.Toast.error(message);
        } else {
            console.warn('Toast对象不可用');
        }
    }

    /**
     * 页面跳转
     * @param {string} url 目标URL
     */
    function navigateTo(url) {
        try {
            window.location.href = url;
        } catch (error) {
            console.error('页面跳转失败:', error, url);
            showErrorMessage('页面跳转失败，请稍后再试');
        }
    }

    // 注意：由于我们不再使用防抖函数，可以将其移除
    // 如果将来需要防抖功能，可以使用以下实现，它会正确跟踪和清理定时器
    /*
    function debounce(func, wait) {
        let timeoutId;
        return function() {
            const context = this;
            const args = arguments;

            // 清除之前的定时器
            if (timeoutId) {
                clearTimeout(timeoutId);
                // 从跟踪数组中移除
                const index = timeoutIds.indexOf(timeoutId);
                if (index !== -1) {
                    timeoutIds.splice(index, 1);
                }
            }

            // 创建新的定时器
            timeoutId = setTimeout(function() {
                func.apply(context, args);

                // 执行后从跟踪数组中移除
                const index = timeoutIds.indexOf(timeoutId);
                if (index !== -1) {
                    timeoutIds.splice(index, 1);
                }
            }, wait);

            // 添加到跟踪数组
            timeoutIds.push(timeoutId);
        };
    }
    */
})();
