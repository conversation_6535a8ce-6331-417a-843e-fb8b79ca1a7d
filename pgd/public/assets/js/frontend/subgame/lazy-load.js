/**
 * 直接加载所有图片 - 取消懒加载
 * 页面加载时立即加载所有图片
 */

(function() {
    // 页面加载完成后直接加载所有图片
    document.addEventListener('DOMContentLoaded', function() {
        loadAllImages();
    });

    /**
     * 立即加载所有图片
     */
    function loadAllImages() {
        // 获取所有图片元素
        const allImages = document.querySelectorAll('.game-thumbnail');

        allImages.forEach(img => {
            // 检查是否有data-src属性
            const dataSrc = img.getAttribute('data-src');

            if (dataSrc) {
                // 如果有data-src属性，将其设置为src
                img.src = dataSrc;
                img.removeAttribute('data-src');

                // 设置样式使图片可见
                img.style.opacity = '1';

                // 添加loaded类
                img.classList.add('loaded');
            }

            // 添加错误处理
            img.onerror = function() {
                // 图片加载失败时隐藏图片
                img.style.opacity = '0';
                img.classList.add('error');

                // 清除事件处理函数
                img.onerror = null;
            };
        });
    }
})();
