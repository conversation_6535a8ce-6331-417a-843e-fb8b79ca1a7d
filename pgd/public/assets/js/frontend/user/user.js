// 用户中心页面的JavaScript功能
$(function() {
    // 为VIP状态区域添加点击事件
    $('.vip-header').on('click', function(e) {
        e.preventDefault();
        window.location.href = '/index/event/vip';
    });

    // 定义一个通用的提示函数
    function showMessage(message, type = 'success') {
        if (typeof Toast !== 'undefined') {
            if (type === 'success') {
                Toast.success(message);
            } else if (type === 'error') {
                Toast.error(message);
            } else {
                Toast.info(message);
            }
        } else {
            console.log(message);
        }
    }

    // 初始化复制ID功能
    $('#copyIdBtn').on('click', async function(e) {
        const idValue = $('.id-value').text();
        // 使用现代的 Clipboard API
        await navigator.clipboard.writeText(idValue);
        showMessage('ID copiado');
    });

    // 刷新余额功能
    $('#refreshBalanceBtn').click(function() {
        // 添加旋转动画
        $(this).css({
            'transition': 'transform 0.5s',
            'transform': 'rotate(360deg)'
        });

        // 调用后端API刷新余额
        $.ajax({
            url: '/index/player/refreshBalance',
            type: 'GET',
            dataType: 'json',
            success: function(res) {
                if (res && res.code === 1 && res.data) {
                    // 更新余额显示
                    $('.balance-value').text(res.data.balance || '0.00');
                } else {
                    // 如果接口调用失败，显示错误提示
                    showMessage(res.msg || (Config.lang.user['Refresh failed'] || '刷新余额失败'), 'error');
                }
            },
            error: function() {
                // 处理网络错误
                showMessage(Config.lang.user['Network error'] || '网络错误，请稍后再试', 'error');
            },
            complete: function() {
                // 恢复按钮状态
                setTimeout(function() {
                    $('#refreshBalanceBtn').css({
                        'transition': 'none',
                        'transform': 'rotate(0deg)'
                    });
                }, 500);
            }
        });
    });

    // 专门处理登出功能
    function handleLogout() {
        console.log('执行登出操作');

        // 清除客户端缓存
        localStorage.clear();
        sessionStorage.clear();

        // 清除所有cookie
        const cookies = document.cookie.split(";");
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i];
            const eqPos = cookie.indexOf("=");
            const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
            document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/";
        }

        // 添加时间戳，防止缓存
        const timestamp = new Date().getTime();

        // 显示加载提示
        showMessage(Config.lang.user['Logout successful'] || '正在退出登录...', 'info');

        // 发送AJAX请求到登出接口
        fetch('/index/index/playerLogout?t=' + timestamp, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            },
            credentials: 'include' // 确保发送cookie
        })
        .then(response => {
            console.log('登出请求已发送，状态:', response.status);
            return response.json().catch(() => {
                // 如果不是JSON响应，返回一个默认对象
                return { code: 1, url: '/index' };
            });
        })
        .then(data => {
            console.log('登出响应数据:', data);
            // 获取重定向URL，如果没有则使用默认值
            const redirectUrl = (data && data.url) ? data.url : '';
            // 添加时间戳参数，防止缓存
            const finalUrl = redirectUrl + (redirectUrl.includes('?') ? '&' : '?') + 't=' + timestamp;
            console.log('重定向到:', finalUrl);
            // 重定向到首页
            window.location.href = finalUrl;
        })
        .catch(error => {
            console.error('登出请求出错:', error);
            // 出错时也重定向到首页
            window.location.href = '?t=' + timestamp;
        });
    }

    // 为登出按钮添加点击事件
    $('[data-page="index/playerLogout"]').click(function(e) {
        e.preventDefault();
        e.stopPropagation();
        handleLogout();
    });

    // 导出登出函数，使其可以在全局使用
    window.handleUserLogout = handleLogout;
});
