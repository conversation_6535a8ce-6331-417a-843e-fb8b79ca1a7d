/**
 * 搜索页面功能JS
 */
document.addEventListener('DOMContentLoaded', function() {
    // 元素引用
    const searchInput = document.getElementById('search-input');
    const clearButton = document.querySelector('.clear-btn');
    const searchForm = document.querySelector('.search-form');
    const searchHistoryContainer = document.querySelector('.search-history-container');
    const searchHistoryList = document.querySelector('.search-history-list');
    const clearAllButton = document.querySelector('.clear-all');
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');
    
    // 搜索输入框功能
    searchInput.addEventListener('input', function() {
        if (this.value.trim() !== '') {
            clearButton.style.display = 'block';
        } else {
            clearButton.style.display = 'none';
        }
    });
    
    clearButton.addEventListener('click', function() {
        searchInput.value = '';
        this.style.display = 'none';
        searchInput.focus();
    });
    
    // 搜索表单提交
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const searchTerm = searchInput.value.trim();
        
        if (searchTerm !== '') {
            // 执行搜索
            performSearch(searchTerm);
            
            // 添加到搜索历史
            addToSearchHistory(searchTerm);
            
            // 切换到搜索结果标签
            activateTab(0);
        }
    });
    
    // 搜索历史功能
    function addToSearchHistory(term) {
        // 检查是否已存在该搜索词
        let histories = getSearchHistory();
        
        // 如果已存在，先移除旧的
        histories = histories.filter(item => item !== term);
        
        // 添加到开头
        histories.unshift(term);
        
        // 限制历史记录数量为10个
        if (histories.length > 10) {
            histories = histories.slice(0, 10);
        }
        
        // 保存到本地存储
        localStorage.setItem('searchHistory', JSON.stringify(histories));
        
        // 更新显示
        renderSearchHistory();
    }
    
    function getSearchHistory() {
        const history = localStorage.getItem('searchHistory');
        return history ? JSON.parse(history) : [];
    }
    
    function renderSearchHistory() {
        const histories = getSearchHistory();
        
        searchHistoryList.innerHTML = '';
        
        histories.forEach(term => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            historyItem.innerHTML = `
                ${term}
                <span class="delete-history">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </span>
            `;
            
            historyItem.addEventListener('click', function(e) {
                // 如果点击的是删除按钮
                if (e.target.closest('.delete-history')) {
                    e.stopPropagation();
                    removeFromSearchHistory(term);
                } else {
                    // 点击历史记录项本身，执行搜索
                    searchInput.value = term;
                    clearButton.style.display = 'block';
                    performSearch(term);
                    activateTab(0);
                }
            });
            
            searchHistoryList.appendChild(historyItem);
        });
        
        // 显示或隐藏搜索历史容器
        if (histories.length > 0) {
            searchHistoryContainer.style.display = 'block';
        } else {
            searchHistoryContainer.style.display = 'none';
        }
    }
    
    function removeFromSearchHistory(term) {
        let histories = getSearchHistory();
        histories = histories.filter(item => item !== term);
        localStorage.setItem('searchHistory', JSON.stringify(histories));
        renderSearchHistory();
    }
    
    clearAllButton.addEventListener('click', function() {
        localStorage.removeItem('searchHistory');
        renderSearchHistory();
    });
    
    // 标签页切换功能
    tabs.forEach((tab, index) => {
        tab.addEventListener('click', function() {
            activateTab(index);
        });
    });
    
    function activateTab(index) {
        tabs.forEach(tab => tab.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));
        
        tabs[index].classList.add('active');
        tabContents[index].classList.add('active');
        
        // 加载对应标签的内容
        loadTabContent(index);
    }
    
    // 执行搜索，显示结果
    function performSearch(term) {
        const searchResultsTab = document.getElementById('search-results');
        
        // 模拟搜索加载状态
        searchResultsTab.innerHTML = '<div class="loading">搜索中...</div>';
        
        // 这里应该是实际的API调用
        // 模拟异步搜索请求
        setTimeout(() => {
            // 模拟数据 - 在实际应用中，这里应该是API返回的数据
            const mockResults = [
                { id: 1, name: '王者荣耀', image: '/assets/images/placeholder-game.jpg', rating: 4.8 },
                { id: 2, name: '和平精英', image: '/assets/images/placeholder-game.jpg', rating: 4.6 },
                { id: 3, name: '原神', image: '/assets/images/placeholder-game.jpg', rating: 4.7 },
                { id: 4, name: '英雄联盟手游', image: '/assets/images/placeholder-game.jpg', rating: 4.5 }
            ];
            
            if (mockResults.length > 0) {
                renderGameGrid(searchResultsTab, mockResults);
            } else {
                searchResultsTab.innerHTML = `
                    <div class="no-results">
                        <div class="no-results-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="11" cy="11" r="8"></circle>
                                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                                <line x1="8" y1="11" x2="14" y2="11"></line>
                            </svg>
                        </div>
                        <p>未找到与"${term}"相关的游戏</p>
                    </div>
                `;
            }
        }, 800);
    }
    
    // 加载标签内容
    function loadTabContent(index) {
        const tabContent = tabContents[index];
        
        // 只有在标签内容为空时才加载
        if (!tabContent.hasChildNodes() || tabContent.querySelector('.loading')) {
            tabContent.innerHTML = '<div class="loading">加载中...</div>';
            
            // 根据标签类型加载不同内容
            setTimeout(() => {
                let mockData = [];
                
                switch (index) {
                    case 0: // 搜索结果 - 已经在performSearch中处理
                        if (!searchInput.value.trim()) {
                            tabContent.innerHTML = `
                                <div class="initial-message">
                                    <div class="search-illustration">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="11" cy="11" r="8"></circle>
                                            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                                        </svg>
                                    </div>
                                    <p>输入关键词开始搜索</p>
                                </div>
                            `;
                        }
                        break;
                    case 1: // 热门游戏
                        mockData = [
                            { id: 1, name: '王者荣耀', image: '/assets/images/placeholder-game.jpg', rating: 4.8 },
                            { id: 2, name: '和平精英', image: '/assets/images/placeholder-game.jpg', rating: 4.6 },
                            { id: 3, name: '原神', image: '/assets/images/placeholder-game.jpg', rating: 4.7 },
                            { id: 4, name: '英雄联盟手游', image: '/assets/images/placeholder-game.jpg', rating: 4.5 },
                            { id: 5, name: '我的世界', image: '/assets/images/placeholder-game.jpg', rating: 4.4 },
                            { id: 6, name: '第五人格', image: '/assets/images/placeholder-game.jpg', rating: 4.3 }
                        ];
                        renderGameGrid(tabContent, mockData);
                        break;
                    case 2: // 最近更新
                        mockData = [
                            { id: 7, name: '天涯明月刀', image: '/assets/images/placeholder-game.jpg', rating: 4.2 },
                            { id: 8, name: '逆水寒', image: '/assets/images/placeholder-game.jpg', rating: 4.1 },
                            { id: 9, name: '阴阳师', image: '/assets/images/placeholder-game.jpg', rating: 4.3 },
                            { id: 10, name: '荒野乱斗', image: '/assets/images/placeholder-game.jpg', rating: 4.0 }
                        ];
                        renderGameGrid(tabContent, mockData);
                        break;
                    case 3: // 收藏
                        // 从本地存储获取收藏
                        const favorites = getFavorites();
                        
                        if (favorites.length > 0) {
                            renderGameGrid(tabContent, favorites);
                        } else {
                            tabContent.innerHTML = `
                                <div class="no-results">
                                    <div class="no-results-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                                        </svg>
                                    </div>
                                    <p>您还没有收藏任何游戏</p>
                                </div>
                            `;
                        }
                        break;
                }
            }, 500);
        }
    }
    
    // 渲染游戏网格
    function renderGameGrid(container, games) {
        const gameGrid = document.createElement('div');
        gameGrid.className = 'game-grid';
        
        games.forEach(game => {
            const gameCard = document.createElement('div');
            gameCard.className = 'game-card';
            gameCard.dataset.id = game.id;
            
            gameCard.innerHTML = `
                <img src="${game.image}" alt="${game.name}" class="game-image">
                <div class="game-info">
                    <h3 class="game-name">${game.name}</h3>
                    <div class="game-meta">
                        <div class="game-rating">
                            <span class="star-icon">★</span>
                            ${game.rating}
                        </div>
                    </div>
                </div>
            `;
            
            gameCard.addEventListener('click', function() {
                // 游戏点击事件，通常会跳转到游戏详情页
                window.location.href = `/game/${game.id}`;
            });
            
            gameGrid.appendChild(gameCard);
        });
        
        container.innerHTML = '';
        container.appendChild(gameGrid);
    }
    
    // 获取收藏的游戏
    function getFavorites() {
        const favorites = localStorage.getItem('favorites');
        return favorites ? JSON.parse(favorites) : [];
    }
    
    // 初始化
    function init() {
        // 渲染搜索历史
        renderSearchHistory();
        
        // 默认激活第一个标签
        activateTab(0);
    }
    
    init();
}); 