/**
 * 管理员刷新JSON数据脚本
 */
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否为管理员
    let isAdmin = false;
    try {
        // 从localStorage检查管理员标记（实际生产环境中应使用更安全的方式）
        isAdmin = localStorage.getItem('isAdmin') === 'true';
    } catch (e) {
        console.error('检查管理员状态出错:', e);
    }
    
    // 如果是管理员，添加刷新按钮
    if (isAdmin) {
        const refreshButton = document.createElement('button');
        refreshButton.textContent = 'Atualizar dados do jogo';
        refreshButton.className = 'admin-refresh-btn';
        refreshButton.style.position = 'fixed';
        refreshButton.style.bottom = '80px';
        refreshButton.style.right = '20px';
        refreshButton.style.zIndex = '999';
        refreshButton.style.background = '#ff9900';
        refreshButton.style.color = 'white';
        refreshButton.style.border = 'none';
        refreshButton.style.borderRadius = '5px';
        refreshButton.style.padding = '10px';
        refreshButton.style.boxShadow = '0 2px 5px rgba(0,0,0,0.3)';
        
        // 点击刷新按钮
        refreshButton.addEventListener('click', function() {
            if (confirm('确定要刷新游戏JSON数据吗？这将根据游戏图片目录重新生成游戏数据。')) {
                this.disabled = true;
                this.textContent = 'Atualizando...';
                
                // 调用接口更新JSON数据
                fetch('/admin/game_provider/refreshJson', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        console.warn('刷新成功！' + (data.msg || ''));
                        window.location.reload();
                    } else {
                        console.warn('刷新失败：' + (data.msg || '未知错误'));
                        this.disabled = false;
                        this.textContent = '刷新游戏数据';
                    }
                })
                .catch(error => {
                    console.error('刷新JSON出错:', error);
                    this.disabled = false;
                    this.textContent = 'Atualizar dados do jogo';
                });
            }
        });
        
        // 添加到页面
        document.body.appendChild(refreshButton);
        console.log('管理员刷新按钮已添加');
    }
    
    // 添加临时激活管理员模式的快捷键（开发调试用）
    document.addEventListener('keydown', function(e) {
        // Ctrl+Shift+A 激活管理员模式
        if (e.ctrlKey && e.shiftKey && e.key === 'A') {
            try {
                const currentStatus = localStorage.getItem('isAdmin') === 'true';
                localStorage.setItem('isAdmin', !currentStatus);
            } catch (e) {
                console.error('切换管理员状态出错:', e);
            }
        }
    });
}); 