/**
 * Banner轮播功能
 * 处理首页轮播图的切换效果
 */
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有轮播图
    const bannerSlides = document.querySelectorAll('.banner-slide');
    const bannerSlider = document.querySelector('.banner-slider');
    
    if (!bannerSlides.length || !bannerSlider) return;
    
    // 当前显示的轮播图索引
    let currentIndex = 0;
    
    // 预加载所有图片
    bannerSlides.forEach(function(slide) {
        const bgUrl = slide.getAttribute('data-bg');
        if (bgUrl) {
            // 设置背景
            slide.style.backgroundImage = `url(${bgUrl})`;
        }
    });
    
    // 切换到指定索引的轮播图
    function showSlide(index) {
        // 确保索引在有效范围内
        if (index < 0) index = bannerSlides.length - 1;
        if (index >= bannerSlides.length) index = 0;
        
        // 更新当前索引
        currentIndex = index;
        
        // 移除所有轮播图的active类
        bannerSlides.forEach(function(slide) {
            slide.classList.remove('active');
        });
        
        // 为当前轮播图添加active类
        bannerSlides[currentIndex].classList.add('active');
    }
    
    // 切换到下一张轮播图
    function nextSlide() {
        showSlide(currentIndex + 1);
    }
    
    // 切换到上一张轮播图
    function prevSlide() {
        showSlide(currentIndex - 1);
    }
    
    // 添加触摸滑动支持
    let touchStartX = 0;
    let touchEndX = 0;
    
    // 触摸开始事件
    bannerSlider.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    }, { passive: true });
    
    // 触摸结束事件
    bannerSlider.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    }, { passive: true });
    
    // 处理滑动手势
    function handleSwipe() {
        const SWIPE_THRESHOLD = 50; // 滑动阈值
        
        if (touchStartX - touchEndX > SWIPE_THRESHOLD) {
            // 向左滑动，显示下一张
            nextSlide();
        } else if (touchEndX - touchStartX > SWIPE_THRESHOLD) {
            // 向右滑动，显示上一张
            prevSlide();
        }
    }
    
    // 自动轮播
    let slideInterval = setInterval(nextSlide, 5000);
    
    // 鼠标悬停时暂停轮播
    bannerSlider.addEventListener('mouseenter', function() {
        clearInterval(slideInterval);
    });
    
    // 鼠标离开时恢复轮播
    bannerSlider.addEventListener('mouseleave', function() {
        slideInterval = setInterval(nextSlide, 5000);
    });
    
    // 初始化显示第一张轮播图
    showSlide(0);
    
    // 导出轮播功能到全局
    window.bannerSlider = {
        next: nextSlide,
        prev: prevSlide,
        show: showSlide
    };
}); 