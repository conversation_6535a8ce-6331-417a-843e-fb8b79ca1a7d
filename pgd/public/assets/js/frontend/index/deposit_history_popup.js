/**
 * 充值记录弹窗脚本
 */
$(function() {
    // 充值记录弹窗功能
    window.depositHistoryPopup = {
        currentPage: 1,
        totalPages: 1,
        records: [],
        isLoading: false,
        hasMoreRecords: true,
        dateRange: {
            start: '',
            end: ''
        },

        init: function() {
            this.bindEvents();
            // 初始化日期范围
            this.setDateRange('last15days');
            // 初始化复制功能
            this.initClipboard();
        },

        // 初始化复制功能
        initClipboard: function() {
            // 使用事件委托，因为复制按钮是动态生成的
            $(document).on('click', '.copy-btn', function() {
                const text = $(this).data('clipboard-text');
                if (text) {
                    // 使用现代的剪贴板API
                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        navigator.clipboard.writeText(text)
                            .then(() => {
                                // 显示复制成功提示
                                const $btn = $(this);
                                $btn.addClass('copied');
                                setTimeout(function() {
                                    $btn.removeClass('copied');
                                }, 1500);
                            })
                            .catch(err => {
                                console.error('复制失败:', err);
                                // 回退到旧方法
                                fallbackCopy(text, this);
                            });
                    } else {
                        // 回退到旧方法
                        fallbackCopy(text, this);
                    }
                }
            });

            // 回退复制方法
            function fallbackCopy(text, button) {
                // 创建临时输入框
                const tempInput = document.createElement('input');
                tempInput.value = text;
                document.body.appendChild(tempInput);
                tempInput.select();

                try {
                    document.execCommand('copy');
                    // 显示复制成功提示
                    const $btn = $(button);
                    $btn.addClass('copied');
                    setTimeout(function() {
                        $btn.removeClass('copied');
                    }, 1500);
                } catch (err) {
                    console.error('复制失败:', err);
                }

                document.body.removeChild(tempInput);
            }
        },

        bindEvents: function() {
            // 返回和关闭按钮事件
            $("#closeDepositHistoryPopup").click(function() {
                window.depositHistoryPopup.hide();
            });

            // 日期筛选下拉菜单事件
            $("#dateFilterSelect").change(function() {
                window.depositHistoryPopup.setDateRange($(this).val());
                window.depositHistoryPopup.resetList();
            });

            // 滚动加载更多记录
            $("#depositHistoryList").on('scroll', function() {
                if (window.depositHistoryPopup.isLoading || !window.depositHistoryPopup.hasMoreRecords) return;

                const scrollHeight = $(this).prop('scrollHeight');
                const scrollPosition = $(this).scrollTop() + $(this).height();

                // 当滚动到距离底部150px时加载更多
                if (scrollHeight - scrollPosition < 150) {
                    window.depositHistoryPopup.loadMoreRecords();
                }
            });
        },

        // 重置列表并加载第一页
        resetList: function() {
            this.currentPage = 1;
            this.records = [];
            this.hasMoreRecords = true;
            this.loadRecords(true);
        },

        // 设置日期范围
        setDateRange: function(rangeType) {
            const today = new Date();
            let startDate = new Date();
            let endDate = new Date();

            switch(rangeType) {
                case 'today':
                    // 今天
                    break;
                case 'yesterday':
                    // 昨天
                    startDate.setDate(today.getDate() - 1);
                    endDate.setDate(today.getDate() - 1);
                    break;
                case 'last7days':
                    // 最近7天
                    startDate.setDate(today.getDate() - 7);
                    break;
                case 'last15days':
                    // 最近15天
                    startDate.setDate(today.getDate() - 15);
                    break;
                case 'last30days':
                    // 最近30天
                    startDate.setDate(today.getDate() - 30);
                    break;
                case 'all':
                    // 所有记录
                    startDate = null;
                    endDate = null;
                    break;
            }

            this.dateRange.start = startDate ? this.formatDate(startDate) : '';
            this.dateRange.end = endDate ? this.formatDate(endDate) : '';
        },

        // 格式化日期为 YYYY-MM-DD
        formatDate: function(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },

        show: function() {
            // 先检查是否有其他弹窗正在显示，如果有则先隐藏
            const otherPopups = document.querySelectorAll('.payment-iframe-popup, .payment-iframe-overlay, .auth-popup.active, .notice-popup[style*="display: flex"], .agreement-popup.active, #depositPopupOverlay[style*="display: flex"]');
            if (otherPopups.length > 0) {
                console.log('发现其他弹窗正在显示，先隐藏它们');
                otherPopups.forEach(popup => {
                    popup.style.display = 'none';
                });
            }

            // 获取弹窗元素
            const historyPopupOverlay = document.getElementById('depositHistoryPopupOverlay');
            if (historyPopupOverlay) {
                // 强制设置样式，确保显示
                historyPopupOverlay.style.cssText = 'display: flex !important; visibility: visible !important; opacity: 1 !important; z-index: 1000000 !important; position: fixed !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; background-color: rgba(0, 0, 0, 0.8) !important;';

                // 计算并设置弹窗高度 - 窗口高度减去头部高度
                const headerHeight = $('.fixed-header-section').height() || 195;
                const windowHeight = $(window).height();
                const popupHeight = windowHeight - headerHeight;

                // 设置高度，但确保最小为视口高度的80%，与充值弹窗保持一致
                const minHeight = Math.max(popupHeight, windowHeight * 0.8);

                // 确保内部元素也有正确的样式
                const popup = historyPopupOverlay.querySelector('.deposit-popup');
                if (popup) {
                    popup.style.cssText = 'position: fixed !important; bottom: 0 !important; left: 0 !important; right: 0 !important; width: 100% !important; max-width: 516px !important; margin: 0 auto !important; height: ' + minHeight + 'px !important; min-height: 80vh !important; overflow-y: hidden !important; background-color:var(--darker-bg-color) !important; display: flex !important; flex-direction: column !important; z-index: 1000001 !important; visibility: visible !important; opacity: 1 !important; pointer-events: auto !important;';
                }

                const container = historyPopupOverlay.querySelector('.deposit-popup-container');
                if (container) {
                    container.style.cssText = 'width: 100% !important; height: 100% !important; display: flex !important; flex-direction: column !important; border-radius: 12px 12px 0 0 !important; overflow: hidden !important; z-index: 1000002 !important; visibility: visible !important; opacity: 1 !important; background-color:var(--darker-bg-color) !important; pointer-events: auto !important;';
                }

                // 将弹窗移动到body的最后，确保它在最上层
                document.body.appendChild(historyPopupOverlay);

                // 添加no-scroll类
                $("body").addClass("no-scroll");
            } else {
                console.error('找不到充值记录弹窗元素');
            }

            // 重置列表状态
            this.resetList();
        },

        hide: function() {
            // 查找所有可能的弹窗元素
            const historyPopupOverlay = document.getElementById('depositHistoryPopupOverlay') || document.querySelector('.deposit-history-popup-overlay');

            // 隐藏充值记录弹窗
            if (historyPopupOverlay) {
                historyPopupOverlay.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; z-index: -1 !important; pointer-events: none !important;';
                console.log('充值记录弹窗已隐藏');
            }

            // 检查页面是否有黑色背景遮罩
            const blackOverlays = document.querySelectorAll('div[style*="background-color: rgba(0, 0, 0"]');
            blackOverlays.forEach(overlay => {
                if (overlay.id !== 'depositPopupOverlay' && overlay.id !== 'depositHistoryPopupOverlay') {
                    console.log('发现额外的黑色遮罩元素:', overlay);
                    overlay.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; z-index: -1 !important; pointer-events: none !important;';
                }
            });

            // 恢复body滚动
            $("body").removeClass("no-scroll");
            document.body.classList.remove('no-scroll');
        },

        // 加载更多记录
        loadMoreRecords: function() {
            this.currentPage++;
            this.loadRecords(false);
        },

        loadRecords: function(isReset = true) {
            // 防止重复加载
            if (this.isLoading) return;
            this.isLoading = true;

            // 如果是重置，则显示加载中状态
            if (isReset) {
                $("#depositHistoryList").html('<div class="loading">loading...</div>');
                $("#emptyState").hide();
            } else {
                // 添加加载更多提示
                $("#depositHistoryList").append('<div class="loading-more">loadingmore...</div>');
            }

            // AJAX请求获取充值记录
            $.ajax({
                url: '/index/index/getDepositHistory',
                type: 'GET',
                data: {
                    page: this.currentPage,
                    start_date: this.dateRange.start,
                    end_date: this.dateRange.end
                },
                dataType: 'json',
                success: (res) => {
                    // 移除加载提示
                    $(".loading, .loading-more").remove();

                    if (res.code === 1 && res.data) {
                        const newRecords = res.data.list || [];
                        this.totalPages = res.data.totalPage || 1;

                        // 判断是否还有更多记录
                        this.hasMoreRecords = this.currentPage < this.totalPages;

                        // 更新总金额
                        $("#totalDepositAmount").text(res.data.formattedTotalAmount || 'R$0,00');

                        if (isReset) {
                            // 重置记录
                            this.records = newRecords;
                        } else {
                            // 添加新记录到列表
                            this.records = this.records.concat(newRecords);
                        }

                        if (this.records.length > 0) {
                            // 有数据，渲染记录
                            $("#emptyState").hide();
                            this.renderRecords(isReset);
                        } else {
                            // 无数据，显示空状态
                            if (isReset) {
                                $("#depositHistoryList").empty();
                                $("#emptyState").show();
                            }
                        }
                    } else {
                        // 如果返回错误或无数据
                        if (isReset) {
                            this.records = [];
                            $("#depositHistoryList").empty();
                            $("#emptyState").show();
                        }
                        this.hasMoreRecords = false;
                    }

                    this.isLoading = false;
                },
                error: () => {
                    // 移除加载提示
                    $(".loading, .loading-more").remove();

                    // 加载出错
                    if (isReset) {
                        this.records = [];
                        $("#depositHistoryList").html(`
                            <div class="empty-records">
                                <div class="empty-records-icon">❌</div>
                                <div>加载失败，请稍后重试</div>
                            </div>
                        `);
                        $("#emptyState").hide();
                    }

                    this.isLoading = false;
                    this.hasMoreRecords = false;
                }
            });
        },

        renderRecords: function(isReset = true) {
            if (this.records.length === 0) {
                if (isReset) {
                    $("#depositHistoryList").empty();
                    $("#emptyState").show();
                }
                return;
            }

            let html = '';
            this.records.forEach(record => {
                // 获取状态文本和状态类
                let statusText = record.status_text || '未知';
                let statusClass = 'unknown';

                // 根据payment_status设置正确的状态类
                switch(record.payment_status) {
                    case 1:
                    case 'success':
                    case 'completed':
                        statusClass = 'success';
                        break;
                    case 0:
                    case 'pending':
                    case 'processing':
                        statusClass = 'pending';
                        break;
                    case 2:
                    case 'failed':
                    case 'error':
                    case 'rejected':
                        statusClass = 'failed';
                        break;
                    default:
                        statusClass = 'unknown';
                }

                // 格式化金额，如果没有formatted_amount就使用amount
                const amount = record.formatted_amount || record.amount || '0.00';

                // 格式化时间，优先使用paid_at，如果没有则使用create_time
                const time = record.paid_at || record.created_at || '';

                // 获取支付渠道代码和名称
                const channelCode = record.channel_code || 'unknown';
                const channelName = record.channel_name || channelCode;

                // 获取第三方订单号
                const thirdOrderNo = record.third_order_no || '';

                html += `
                    <div class="history-item">
                        <div class="history-item-row">
                            <div class="history-item-left">
                                 <img src="/assets/img/frontend/payment/${channelCode}.png" alt="${channelName}" class="channel-icon">
                                <span class="channel-name">${channelName}</span>
                            </div>
                            <div class="history-item-right">
                                <span class="deposit-amount">${amount}</span>
                            </div>
                        </div>
                        <div class="history-item-row">
                            <div class="history-item-left">
                                <span class="deposit-time">${time}</span>
                            </div>
                            <div class="history-item-middle">
                                <div class="order-container">
                                    <span class="order-no">${thirdOrderNo}</span>
                                    <button class="copy-btn" data-clipboard-text="${thirdOrderNo}" title="复制订单号"></button>
                                </div>
                            </div>
                            <div class="history-item-right">
                                <span class="deposit-status status-${statusClass}">${statusText}</span>
                            </div>
                        </div>
                    </div>
                `;
            });

            if (isReset) {
                $("#depositHistoryList").html(html);
            } else {
                // 追加新记录
                $("#depositHistoryList").append(html);
            }
        }
    };

    // 初始化充值记录弹窗
    window.depositHistoryPopup.init();
});

// 用于从外部调用显示充值记录弹窗的函数
function showDepositHistory() {
    window.depositHistoryPopup.show();
}
