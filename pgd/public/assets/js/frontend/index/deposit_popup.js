const { Toast } = require("bootstrap");

// 定义全局充值弹窗对象
window.depositPopup = {
    show: function() {
        

        // 先检查是否有其他弹窗正在显示，如果有则先隐藏
        const otherPopups = document.querySelectorAll('.payment-iframe-popup, .payment-iframe-overlay, .auth-popup.active, .notice-popup[style*="display: flex"], .agreement-popup.active, #depositHistoryPopupOverlay[style*="display: flex"]');
        if (otherPopups.length > 0) {
            
            otherPopups.forEach(popup => {
                popup.style.display = 'none';
            });
        }

        // 查找弹窗元素
        const depositPopupOverlay = document.getElementById('depositPopupOverlay');
        if (depositPopupOverlay) {
            // 先隐藏充值记录弹窗
            const historyPopup = document.getElementById('depositHistoryPopupOverlay');
            if (historyPopup) {
                historyPopup.style.display = 'none';
            }

            // 强制设置样式，确保显示
            depositPopupOverlay.style.cssText = 'display: flex !important; visibility: visible !important; opacity: 1 !important; z-index: 1000000 !important; position: fixed !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; background-color: rgba(0, 0, 0, 0.8) !important;';
            document.body.classList.add('no-scroll');

            // 确保内部元素也有正确的样式
            const popup = depositPopupOverlay.querySelector('.deposit-popup');
            if (popup) {
                // 获取计算好的高度
                const popupHeight = window.getPopupHeight(true);

                // 设置样式，使用计算好的高度
                popup.style.cssText = 'position: fixed !important; bottom: 0 !important; left: 0 !important; right: 0 !important; width: 100% !important; max-width: 516px !important; margin: 0 auto !important; height: ' + popupHeight + ' !important; overflow-y: hidden !important; background-color: var(--darker-bg-color)  !important; display: flex !important; flex-direction: column !important; z-index: 1000001 !important; visibility: visible !important; opacity: 1 !important;';
            }

            const container = depositPopupOverlay.querySelector('.deposit-popup-container');
            if (container) {
                container.style.cssText = 'width: 100% !important; height: 100% !important; display: flex !important; flex-direction: column !important; border-radius: 12px 12px 0 0 !important; overflow: hidden !important; z-index: 1000002 !important; visibility: visible !important; opacity: 1 !important; background-color: var(--darker-bg-color)  !important;';
            }

            // 确保充值记录按钮事件绑定
            const historyBtn = depositPopupOverlay.querySelector('#depositHistoryBtn');
            if (historyBtn) {
                historyBtn.onclick = function() {
                    // 隐藏充值弹窗
                    depositPopupOverlay.style.display = 'none';

                    // 显示充值记录弹窗
                    if (typeof window.depositHistoryPopup !== 'undefined' && typeof window.depositHistoryPopup.show === 'function') {
                        window.depositHistoryPopup.show();
                    } else if (typeof showDepositHistory === 'function') {
                        showDepositHistory();
                    }
                };
            }

            // 输出弹窗的计算样式，用于调试
            

            // 将弹窗移动到body的最后，确保它在最上层
            document.body.appendChild(depositPopupOverlay);

            // 确保payment-item和amount-grid样式正确应用
            setTimeout(function() {
                // 强制应用payment-item的样式
                const paymentItems = document.querySelectorAll('.payment-item');
                if (paymentItems && paymentItems.length > 0) {
                    
                    // 样式已在deposit_popup_override.css中定义，这里只是确保它们被应用
                }

                // 确保amount-grid不滚动并完全展开
                const amountGrid = document.querySelector('.amount-grid');
                if (amountGrid) {
                    
                    amountGrid.style.overflow = 'visible';
                    amountGrid.style.height = 'auto';
                    amountGrid.style.maxHeight = 'none';
                }
            }, 100);

            // 延迟检查弹窗是否真的显示了
            setTimeout(function() {
                const computedStyle = window.getComputedStyle(depositPopupOverlay);
                

                // 如果弹窗仍然没有显示，尝试再次强制显示
                if (computedStyle.display !== 'flex' || computedStyle.visibility === 'hidden' || computedStyle.opacity === '0') {
                    

                    // 再次强制设置样式
                    depositPopupOverlay.style.cssText = 'display: flex !important; visibility: visible !important; opacity: 1 !important; z-index: 1000000 !important; position: fixed !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; background-color: rgba(0, 0, 0, 0.8) !important;';

                    // 使用修复函数确保弹窗正确显示
                    if (typeof window.fixDepositPopup === 'function') {
                        window.fixDepositPopup();
                    }

                    // 确保内部元素也有正确的样式
                    if (popup) {
                        // 计算弹窗高度 - 窗口高度减去头部高度
                        const headerHeight = document.querySelector('.fixed-header-section') ?
                            document.querySelector('.fixed-header-section').offsetHeight : 195;
                        const windowHeight = window.innerHeight;
                        const popupHeight = windowHeight - headerHeight;

                        // 设置高度，但确保最小为视口高度的80%
                        const minHeight = Math.max(popupHeight, windowHeight * 0.8);

                        popup.style.cssText = 'position: fixed !important; bottom: 0 !important; left: 0 !important; right: 0 !important; width: 100% !important; max-width: 516px !important; margin: 0 auto !important; height: ' + minHeight + 'px !important; min-height: 80vh !important; overflow-y: hidden !important; background-color: var(--darker-bg-color)  !important; display: flex !important; flex-direction: column !important; z-index: 1000001 !important; visibility: visible !important; opacity: 1 !important;';
                    }

                    if (container) {
                        container.style.cssText = 'width: 100% !important; height: 100% !important; display: flex !important; flex-direction: column !important; border-radius: 12px 12px 0 0 !important; overflow: hidden !important; z-index: 1000002 !important; visibility: visible !important; opacity: 1 !important; background-color: var(--darker-bg-color)  !important;';
                    }
                }
            }, 500);
        } else {
            console.error('找不到充值弹窗覆盖层元素');

            // 尝试查找弹窗元素，即使ID不匹配
            const existingOverlay = document.querySelector('.deposit-popup-overlay');
            if (existingOverlay) {
                
                existingOverlay.id = 'depositPopupOverlay';

                // 将弹窗移动到body的最后，确保它在最上层
                document.body.appendChild(existingOverlay);

                // 强制设置样式
                existingOverlay.style.cssText = 'display: flex !important; visibility: visible !important; opacity: 1 !important; z-index: 1000000 !important; position: fixed !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; background-color: rgba(0, 0, 0, 0.8) !important;';
                document.body.classList.add('no-scroll');

                // 确保内部元素也有正确的样式
                const popup = existingOverlay.querySelector('.deposit-popup');
                if (popup) {
                    // 获取计算好的高度
                    const popupHeight = window.getPopupHeight(true);

                    // 设置样式，使用计算好的高度
                    popup.style.cssText = 'position: fixed !important; bottom: 0 !important; left: 0 !important; right: 0 !important; width: 100% !important; max-width: 516px !important; margin: 0 auto !important; height: ' + popupHeight + ' !important; overflow-y: hidden !important; background-color: var(--darker-bg-color)  !important; display: flex !important; flex-direction: column !important; z-index: 1000001 !important; visibility: visible !important; opacity: 1 !important;';
                }

                const container = existingOverlay.querySelector('.deposit-popup-container');
                if (container) {
                    container.style.cssText = 'width: 100% !important; height: 100% !important; display: flex !important; flex-direction: column !important; border-radius: 12px 12px 0 0 !important; overflow: hidden !important; z-index: 1000002 !important; visibility: visible !important; opacity: 1 !important; background-color: var(--darker-bg-color)  !important;';
                }

                // 确保充值记录按钮事件绑定
                const historyBtn = existingOverlay.querySelector('#depositHistoryBtn');
                if (historyBtn) {
                    historyBtn.onclick = function() {
                        // 隐藏充值弹窗
                        existingOverlay.style.display = 'none';

                        // 显示充值记录弹窗
                        if (typeof window.depositHistoryPopup !== 'undefined' && typeof window.depositHistoryPopup.show === 'function') {
                            window.depositHistoryPopup.show();
                        } else if (typeof showDepositHistory === 'function') {
                            showDepositHistory();
                        }
                    };
                }
            } else {
                console.error('在DOM中找不到任何充值弹窗元素');
            }
        }
    },
    hide: function() {
        

        // 查找所有可能的弹窗元素
        const depositPopupOverlay = document.getElementById('depositPopupOverlay') || document.querySelector('.deposit-popup-overlay');
        const depositHistoryPopupOverlay = document.getElementById('depositHistoryPopupOverlay') || document.querySelector('.deposit-history-popup-overlay');

        // 隐藏充值弹窗
        if (depositPopupOverlay) {
            depositPopupOverlay.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; z-index: -1 !important; pointer-events: none !important;';
            
        }

        // 隐藏充值记录弹窗
        if (depositHistoryPopupOverlay) {
            depositHistoryPopupOverlay.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; z-index: -1 !important; pointer-events: none !important;';
            
        }

        // 检查页面是否有黑色背景遮罩
        const blackOverlays = document.querySelectorAll('div[style*="background-color: rgba(0, 0, 0"]');
        blackOverlays.forEach(overlay => {
            if (overlay.id !== 'depositPopupOverlay' && overlay.id !== 'depositHistoryPopupOverlay') {
                
                overlay.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; z-index: -1 !important; pointer-events: none !important;';
            }
        });

        // 恢复body滚动
        document.body.classList.remove('no-scroll');
    }
};

// 提供旧的API接口以保持兼容性
window.showDepositPopup = function() {
    window.depositPopup.show();
};

window.hideDepositPopup = function() {
    window.depositPopup.hide();
};

// 确保在DOM加载完成后初始化
// 将函数添加到全局作用域，使其在其他文件中可访问
window.initDepositPopup = function() {
    

    // 检查充值弹窗相关元素是否存在
    const depositPopupOverlay = document.getElementById('depositPopupOverlay');
    const depositPopup = document.querySelector('.deposit-popup');

    
    

    // 尝试查找弹窗元素，即使ID不匹配
    if (!depositPopupOverlay) {
        const existingOverlay = document.querySelector('.deposit-popup-overlay');
        if (existingOverlay) {
            
            existingOverlay.id = 'depositPopupOverlay';
        }
    }

    // 再次检查元素是否存在
    const updatedOverlay = document.getElementById('depositPopupOverlay') || document.querySelector('.deposit-popup-overlay');
    const updatedPopup = depositPopup;

    if (!updatedOverlay || !updatedPopup) {
        console.error('找不到充值弹窗元素，将在1秒后重试');
        setTimeout(window.initDepositPopup, 1000);
        return;
    }

    // 初始化充值弹窗功能
    window.initDepositPopupFeatures(updatedOverlay, updatedPopup);

    // 标记为已初始化
    if (window.depositPopup) {
        window.depositPopup._initialized = true;
        
    }
}

// 封装获取弹窗高度的函数
window.getPopupHeight = function(useHeaderHeight = true) {
    // 使用固定高度或根据屏幕高度计算
    const useFixedHeight = true; // 设置为true使用固定高度，false使用计算高度
    let popupHeight;
    
    if (useFixedHeight) {
        // 使用固定高度 - 在这里设置固定高度，例如720px
        popupHeight = '720px';
    } else {
        // 计算弹窗高度 - 窗口高度减去头部高度
        let headerHeight = 0;
        if (useHeaderHeight) {
            headerHeight = document.querySelector('.fixed-header-section') ?
                document.querySelector('.fixed-header-section').offsetHeight : 195;
        } else {
            headerHeight = 60; // 简化的头部高度
        }
        
        const windowHeight = window.innerHeight;
        const calculatedHeight = windowHeight - headerHeight;
        
        // 设置高度，但确保最小为视口高度的90%
        const minHeight = Math.max(calculatedHeight, windowHeight * 0.9);
        popupHeight = minHeight + 'px';
    }
    
    return popupHeight;
};

// 添加调试辅助函数，用于检查和修复弹窗显示问题
window.fixDepositPopup = function() {
    

    // 查找弹窗元素
    const overlay = document.getElementById('depositPopupOverlay') || document.querySelector('.deposit-popup-overlay');

    if (!overlay) {
        console.error('找不到充值弹窗元素，无法修复');
        return false;
    }

    // 检查弹窗当前状态
    const computedStyle = window.getComputedStyle(overlay);
    console.log('当前弹窗状态:', {
        display: computedStyle.display,
        visibility: computedStyle.visibility,
        opacity: computedStyle.opacity,
        zIndex: computedStyle.zIndex
    });

    // 检查是否有其他弹窗正在显示，如果有则隐藏
    const otherPopups = document.querySelectorAll('.payment-iframe-popup, .payment-iframe-overlay, .auth-popup.active, .notice-popup[style*="display: flex"], .agreement-popup.active');
    if (otherPopups.length > 0) {
        
        otherPopups.forEach(popup => {
            popup.style.display = 'none';
        });
    }

    // 将弹窗移动到body的最后，确保它在最上层
    document.body.appendChild(overlay);

    // 强制设置弹窗样式
    overlay.style.cssText = 'display: flex !important; visibility: visible !important; opacity: 1 !important; z-index: 1000000 !important; position: fixed !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; background-color: rgba(0, 0, 0, 0.8) !important; pointer-events: auto !important;';

    // 确保内部元素也有正确的样式
    const popup = overlay.querySelector('.deposit-popup');
    if (popup) {
        // 获取计算好的高度
        const popupHeight = window.getPopupHeight(true);

        // 设置样式，使用计算好的高度
        popup.style.cssText = 'position: fixed !important; bottom: 0 !important; left: 0 !important; right: 0 !important; width: 100% !important; max-width: 516px !important; margin: 0 auto !important; height: ' + popupHeight + ' !important; overflow-y: hidden !important; background-color: var(--darker-bg-color)  !important; display: flex !important; flex-direction: column !important; z-index: 1000001 !important; visibility: visible !important; opacity: 1 !important; pointer-events: auto !important;';
    }

    const container = overlay.querySelector('.deposit-popup-container');
    if (container) {
        container.style.cssText = 'width: 100% !important; height: 100% !important; display: flex !important; flex-direction: column !important; border-radius: 12px 12px 0 0 !important; overflow: hidden !important; z-index: 1000002 !important; visibility: visible !important; opacity: 1 !important; background-color: var(--darker-bg-color)  !important; pointer-events: auto !important;';
    }

    // 禁止body滚动
    document.body.classList.add('no-scroll');

    // 确保payment-item和amount-grid样式正确应用
    setTimeout(function() {
        // 强制应用payment-item的样式
        const paymentItems = document.querySelectorAll('.payment-item');
        if (paymentItems && paymentItems.length > 0) {
            
            // 样式已在deposit_popup_override.css中定义，这里只是确保它们被应用
        }

        // 确保amount-grid不滚动并完全展开
        const amountGrid = document.querySelector('.amount-grid');
        if (amountGrid) {
            
            amountGrid.style.overflow = 'visible';
            amountGrid.style.height = 'auto';
            amountGrid.style.maxHeight = 'none';
        }
    }, 100);

    
    return true;
};

// 添加一个特殊的函数，用于在页面加载完成后确保弹窗正确显示
window.ensureDepositPopupVisible = function() {
    

    // 查找弹窗元素
    const overlay = document.getElementById('depositPopupOverlay') || document.querySelector('.deposit-popup-overlay');

    if (!overlay) {
        console.error('找不到充值弹窗元素');
        return false;
    }

    // 检查弹窗是否已经显示
    if (overlay.style.display === 'flex') {
        

        // 检查弹窗是否真的可见
        const rect = overlay.getBoundingClientRect();
        

        // 如果弹窗不在可见区域内，尝试修复
        if (rect.width === 0 || rect.height === 0 || rect.top < -10000 || rect.left < -10000) {
            
            return window.fixDepositPopup();
        }

        // 检查弹窗的计算样式
        const computedStyle = window.getComputedStyle(overlay);
        if (computedStyle.display !== 'flex' || computedStyle.visibility === 'hidden' || computedStyle.opacity === '0') {
            
            return window.fixDepositPopup();
        }

        
        return true;
    } else {
        
        overlay.style.display = 'flex';
        return window.fixDepositPopup();
    }
};

// 在页面加载完成后调用初始化函数
document.addEventListener('DOMContentLoaded', function() {
    

    // 检查是否已经初始化
    if (window.depositPopup && window.depositPopup._initialized) {
        
        return;
    }

    // 延迟初始化，确保DOM完全加载
    setTimeout(window.initDepositPopup, 500);

    // 添加全局快捷键，用于在出现问题时手动修复弹窗
    document.addEventListener('keydown', function(e) {
        // 按下 Ctrl+Shift+D 组合键修复弹窗
        if (e.ctrlKey && e.shiftKey && e.key === 'D') {
            
            window.fixDepositPopup();
        }
    });

    // 添加自动检查弹窗显示状态的功能
    // 每500毫秒检查一次，如果弹窗应该显示但没有正确显示，则尝试修复
    setInterval(function() {
        const overlay = document.getElementById('depositPopupOverlay') || document.querySelector('.deposit-popup-overlay');
        if (overlay && overlay.style.display === 'flex') {
            // 弹窗应该显示，检查是否真的可见
            const computedStyle = window.getComputedStyle(overlay);
            if (computedStyle.display !== 'flex' || computedStyle.visibility === 'hidden' || computedStyle.opacity === '0') {
                
                window.fixDepositPopup();
            }
        }
    }, 500);

    // 在页面加载完成后延迟2秒，检查是否有弹窗需要显示
    setTimeout(function() {
        const overlay = document.getElementById('depositPopupOverlay') || document.querySelector('.deposit-popup-overlay');
        if (overlay && overlay.style.display === 'flex') {
            
            window.ensureDepositPopupVisible();
        }
    }, 2000);
});

// 初始化充值弹窗功能
// 将函数添加到全局作用域，使其在其他文件中可访问
window.initDepositPopupFeatures = function(overlayElement, popupContainerElement) {
    

    // 全局函数：显示支付iframe
    window.showPaymentIframe = function(url) {
        // 检查是否已经存在支付iframe，如果存在则先移除
        const existingPopup = document.querySelector('.payment-iframe-popup');
        const existingOverlay = document.getElementById('paymentIframeOverlay');

        if (existingPopup) {
            existingPopup.remove();
        }

        if (existingOverlay) {
            existingOverlay.remove();
        }

        // 创建支付弹窗覆盖层
        const paymentOverlay = document.createElement('div');
        paymentOverlay.className = 'payment-iframe-overlay';
        paymentOverlay.id = 'paymentIframeOverlay';
        paymentOverlay.style.cssText = 'position:fixed; top:0; left:0; width:100%; height:100%; z-index:2000000; background-color:rgba(0,0,0,0.5);';

        // 创建支付弹窗容器
        const paymentPopup = document.createElement('div');
        paymentPopup.className = 'payment-iframe-popup';
        paymentPopup.id = 'paymentIframePopup';

        // 获取deposit-popup的宽度
        const depositPopupWidth = document.querySelector('.deposit-popup')?.clientWidth || 500;

        // 使用简单的定位方式，确保从下方居中弹出
        paymentPopup.style.cssText = `
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            max-width: ${depositPopupWidth}px;
            height: 100vh;
            margin: 0 auto;
            background-color:var(--darker-bg-color);
            overflow: hidden;
            z-index: 2000001; /* 确保比覆盖层高一级，且比depositPopup的z-index更高 */
            transform: translateY(100%);
            transition: transform 0.3s ease;
        `;

        // 添加到DOM后强制重绘，然后添加动画效果
        setTimeout(() => {
            paymentPopup.style.transform = 'translateY(0)';
        }, 10);

        // 创建iframe容器
        const iframeContainer = document.createElement('div');
        iframeContainer.className = 'payment-iframe-container';
        iframeContainer.id = 'paymentIframeContainer';
        iframeContainer.style.cssText = 'width:100%; height:100vh; overflow:hidden; position:relative; background-color:var(--darker-bg-color); z-index: 1;';

        // 创建header
        const iframeHeader = document.createElement('div');
        iframeHeader.className = 'payment-iframe-header';
        iframeHeader.style.cssText = `
            width: 100%;
            height: 56px;
            background-color: var(--darker-bg-color);
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            position: relative;
        `;

        // 创建返回按钮
        const backButton = document.createElement('div');
        backButton.className = 'payment-iframe-back back-btn';
        backButton.innerHTML = '<i class="fa fa-arrow-left"></i>';
        backButton.style.cssText = `
            font-size: 24px;
            cursor: pointer;
        `;

        // 创建标题
        const headerTitle = document.createElement('div');
        headerTitle.className = 'payment-iframe-title header-title';
        headerTitle.textContent = 'Página de pagamento';
        headerTitle.style.cssText = `
            font-size: 18px;
            font-weight: bold;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        `;

        // 添加返回按钮点击事件
        backButton.addEventListener('click', function() {
            window.closePaymentIframe();
        });

        // 将标题和返回按钮添加到header
        iframeHeader.appendChild(backButton);
        iframeHeader.appendChild(headerTitle);

        // 将header添加到iframe容器
        iframeContainer.appendChild(iframeHeader);

        // 创建iframe
        const iframe = document.createElement('iframe');
        iframe.src = url;
        iframe.style.cssText = 'width:100%; height:calc(100vh - 56px); border:none; display:block; background-color:#fff;';
        iframe.id = 'paymentIframe';

        // 添加iframe加载事件，显示加载状态
        const loadingDiv = document.createElement('div');
        loadingDiv.style.cssText = `
            position:absolute;
            top:56px;
            left:0;
            width:100%;
            height:calc(100vh - 56px);
            display:flex;
            justify-content:center;
            align-items:center;
            background-color:#fff;
            z-index:2; /* 确保加载状态显示在iframe上方 */
        `;
        loadingDiv.innerHTML = '<div style="text-align:center;"><div style="width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid var(--darker-bg-color) ;border-radius:50%;animation:spin 1s linear infinite;margin:0 auto;"></div><p style="margin-top:10px;color:var(--darker-bg-color) ;">Carregando...</p></div>';
        iframeContainer.appendChild(loadingDiv);

        iframe.onload = function() {
            // 加载完成后隐藏加载状态
            loadingDiv.style.display = 'none';
        };

        // 将iframe添加到容器
        iframeContainer.appendChild(iframe);

        // 将容器添加到弹窗
        paymentPopup.appendChild(iframeContainer);

        // 将弹窗添加到body
        try {
            document.body.appendChild(paymentPopup);
            document.body.appendChild(paymentOverlay);


            // 检查元素的可见性和位置
            setTimeout(() => {
                const popupElement = document.querySelector('.payment-iframe-popup');
                const overlayElement = document.getElementById('paymentIframeOverlay');
                const depositPopupElement = document.querySelector('.deposit-popup-overlay');

                
                
                
                

                if (popupElement) {
                    const computedStyle = window.getComputedStyle(popupElement);
                    console.log('支付弹窗计算样式:', {
                        display: computedStyle.display,
                        visibility: computedStyle.visibility,
                        position: computedStyle.position,
                        left: computedStyle.left,
                        bottom: computedStyle.bottom,
                        width: computedStyle.width,
                        height: computedStyle.height,
                        zIndex: computedStyle.zIndex
                    });
                }

                if (overlayElement) {
                    const computedStyle = window.getComputedStyle(overlayElement);
                    console.log('支付覆盖层计算样式:', {
                        display: computedStyle.display,
                        visibility: computedStyle.visibility,
                        position: computedStyle.position,
                        zIndex: computedStyle.zIndex
                    });
                }

                if (depositPopupElement) {
                    const computedStyle = window.getComputedStyle(depositPopupElement);
                    console.log('充值弹窗计算样式:', {
                        display: computedStyle.display,
                        visibility: computedStyle.visibility,
                        position: computedStyle.position,
                        zIndex: computedStyle.zIndex
                    });
                }
            }, 100);
        } catch (error) {
            console.error('Error adding elements to body:', error);
        }

        // 添加CSS动画
        try {
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideUp {
                    from { transform: translateY(100%); }
                    to { transform: translateY(0); }
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
            
        } catch (error) {
            console.error('Error adding animation styles:', error);
        }

        // 禁止body滚动
        document.body.style.overflow = 'hidden';

        // 点击覆盖层关闭iframe
        paymentOverlay.addEventListener('click', function(event) {
            if (event.target === paymentOverlay) {
                window.closePaymentIframe();
            }
        });
    };

    // 全局函数：关闭支付iframe
    window.closePaymentIframe = function() {
        // 获取支付弹窗覆盖层和弹窗容器
        const paymentOverlay = document.getElementById('paymentIframeOverlay');
        const paymentPopup = document.getElementById('paymentIframePopup');

        if (paymentPopup) {
            // 添加关闭动画
            paymentPopup.style.transform = 'translateY(100%)';

            // 延迟移除元素，等待动画完成
            setTimeout(function() {
                if (paymentPopup) paymentPopup.remove();
                if (paymentOverlay) paymentOverlay.remove();

                // 恢复body滚动
                document.body.style.overflow = '';

                // 重新打开充值弹窗
                setTimeout(function() {
                    window.depositPopup.show();
                }, 100);
            }, 300);
        } else {
            // 如果没有找到弹窗容器，直接移除覆盖层
            if (paymentOverlay) paymentOverlay.remove();

            // 恢复body滚动
            document.body.style.overflow = '';

            // 重新打开充值弹窗
            setTimeout(function() {
                window.depositPopup.show();
            }, 100);
        }
    };

    // 全局函数：支付成功回调
    window.paymentSuccess = function() {
        // 关闭支付iframe
        window.closePaymentIframe();

        // 显示支付成功提示
        // window.showToast('充值成功！', 'success');

        // 刷新用户余额（如果需要）
        if (typeof window.updateUserBalance === 'function') {
            window.updateUserBalance();
        }
    };
    // 初始化变量
    let selectedAmount = 0;
    let selectedPaymentMethod = '';
    let bonusAmount = 0;
    let totalAmount = 0;
    let useBonus = true;

    // 获取DOM元素
    const popupOverlayElement = document.querySelector('.deposit-popup-overlay');
    const popupElement = document.querySelector('.deposit-popup');
    const closeButton = document.querySelector('#backFromDepositPopup');
    const amountItems = document.querySelectorAll('.amount-item');
    const paymentItems = document.querySelectorAll('.payment-item');
    const customAmountInput = document.getElementById('customAmountValue');
    const confirmButton = document.getElementById('confirmPayment');
    const bonusCheckbox = document.getElementById('offerBonusCheckbox');

    // 初始化隐藏弹窗（如果已经有inline script处理了，这段代码不会有影响）
    if (popupOverlayElement) {
        popupOverlayElement.style.display = 'none';
    }

    // 查找并绑定充值记录按钮的点击事件
    const depositHistoryBtn = document.getElementById('depositHistoryBtn');
    if (depositHistoryBtn) {
        depositHistoryBtn.addEventListener('click', function() {
            // 检查是否存在支付iframe，如果存在则先移除
            const existingPopup = document.getElementById('paymentIframePopup');
            const existingOverlay = document.getElementById('paymentIframeOverlay');

            if (existingPopup || existingOverlay) {
                // 如果存在iframe，先关闭iframe
                window.closePaymentIframe();
                // 不返回，继续执行打开充值记录的操作
            }

            // 这里添加打开充值记录的代码
            
            // 如果有充值记录弹窗，可以在这里打开
            if (typeof window.depositHistoryPopup !== 'undefined' &&
                typeof window.depositHistoryPopup.show === 'function') {
                window.depositHistoryPopup.show();
            }
        });
    }

    // 扩展全局弹窗对象功能
    if (window.depositPopup) {
        

        // 添加重置弹窗方法
        window.depositPopup.resetPopup = function() {
            

            // 重置选择状态
            if (amountItems) {
                amountItems.forEach(item => item.classList.remove('active'));
            }
            if (paymentItems) {
                paymentItems.forEach(item => item.classList.remove('active'));
            }

            selectedAmount = 0;
            selectedPaymentMethod = '';

            // 更新汇总显示 - 现在summaryAmount是个输入框
            const summaryAmountInput = document.getElementById('summaryAmount');
            if (summaryAmountInput) {
                summaryAmountInput.value = 0;
            }

            this.updateSummary();
        };

        // 添加更新摘要方法
        window.depositPopup.updateSummary = function() {
            

            const summaryAmount = document.getElementById('summaryAmount');
            const summaryBonus = document.getElementById('summaryBonus');
            const summaryTotal = document.getElementById('summaryTotal');

            if (summaryAmount && summaryBonus && summaryTotal) {
                // 获取当前输入框的值
                const currentAmount = parseInt(summaryAmount.value) || 0;

                const bonusRate = 0.2; // 默认奖励比率
                const bonusValue = useBonus ? Math.floor(currentAmount * bonusRate) : 0;
                const totalValue = currentAmount + bonusValue;

                // 不需要设置summaryAmount.value，因为它是用户输入的
                summaryBonus.textContent = bonusValue;
                summaryTotal.textContent = totalValue;
            }
        };

        // 扩展显示方法
        const originalShow = window.depositPopup.show;
        

        window.depositPopup.show = function() {
            
            
            

            // 检查弹窗元素是否存在
            const depositPopupOverlay = document.getElementById('depositPopupOverlay');
            if (!depositPopupOverlay) {
                console.error('找不到充值弹窗覆盖层元素');

                // 尝试查找弹窗元素，即使ID不匹配
                const existingOverlay = document.querySelector('.deposit-popup-overlay');
                if (existingOverlay) {
                    
                    existingOverlay.id = 'depositPopupOverlay';

                    // 将弹窗移动到body的最后，确保它在最上层
                    document.body.appendChild(existingOverlay);

                    // 强制设置样式
                    existingOverlay.style.cssText = 'display: flex !important; visibility: visible !important; opacity: 1 !important; z-index: 1000000 !important; position: fixed !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; background-color: rgba(0, 0, 0, 0.8) !important;';
                    document.body.classList.add('no-scroll');

                    // 确保内部元素也有正确的样式
                    const popup = existingOverlay.querySelector('.deposit-popup');
                    if (popup) {
                        // 获取计算好的高度
                        const popupHeight = window.getPopupHeight(true);

                        // 设置样式，使用计算好的高度
                        popup.style.cssText = 'position: fixed !important; bottom: 0 !important; left: 0 !important; right: 0 !important; width: 100% !important; max-width: 516px !important; margin: 0 auto !important; height: ' + popupHeight + ' !important; overflow-y: hidden !important; background-color: var(--darker-bg-color)  !important; display: flex !important; flex-direction: column !important; z-index: 1000001 !important; visibility: visible !important; opacity: 1 !important;';
                    }

                    const container = existingOverlay.querySelector('.deposit-popup-container');
                    if (container) {
                        container.style.cssText = 'width: 100% !important; height: 100% !important; display: flex !important; flex-direction: column !important; border-radius: 12px 12px 0 0 !important; overflow: hidden !important; z-index: 1000002 !important; visibility: visible !important; opacity: 1 !important; background-color: var(--darker-bg-color)  !important;';
                    }
                }
            }

            try {
                // 调用原始显示方法
                if (typeof originalShow === 'function') {
                    // 使用当前上下文(this)调用原始方法，而不是window.depositPopup
                    originalShow.call(this);
                    
                } else {
                    console.error('原始显示方法不是一个函数，尝试直接设置弹窗显示');
                    // 如果原始方法不可用，直接设置弹窗显示
                    const overlay = document.getElementById('depositPopupOverlay');
                    if (overlay) {
                        overlay.style.display = 'flex';
                        document.body.classList.add('no-scroll');
                        
                    }
                }
            } catch (error) {
                console.error('调用原始显示方法时出错:', error);
                // 出错时尝试直接设置弹窗显示
                const overlay = document.getElementById('depositPopupOverlay');
                if (overlay) {
                    overlay.style.display = 'flex';
                    document.body.classList.add('no-scroll');
                    
                }
            }

            // 计算并设置弹窗高度 - 窗口高度减去头部高度
            const popupElementToResize = document.querySelector('.deposit-popup');
            if (popupElementToResize) {
                // 获取计算好的高度 - 使用简化的头部高度
                const popupHeight = window.getPopupHeight(false);

                // 设置高度
                popupElementToResize.style.height = popupHeight;

                // 确保弹窗最大宽度与container一致
                const containerWidth = document.querySelector('.container')?.clientWidth;
                if (containerWidth) {
                    popupElementToResize.style.maxWidth = `${containerWidth}px`;
                }
                
                
            } else {
                console.error('找不到.deposit-popup元素，无法设置尺寸');
            }
        };

        // 扩展隐藏方法
        const originalHide = window.depositPopup.hide;
        

        window.depositPopup.hide = function() {
            
            
            

            try {
                // 调用原始隐藏方法
                if (typeof originalHide === 'function') {
                    // 使用当前上下文(this)调用原始方法，而不是window.depositPopup
                    originalHide.call(this);
                    
                } else {
                    console.error('原始隐藏方法不是一个函数，尝试直接设置弹窗隐藏');
                    // 如果原始方法不可用，直接设置弹窗隐藏
                    const overlay = document.getElementById('depositPopupOverlay');
                    if (overlay) {
                        overlay.style.display = 'none';
                        document.body.classList.remove('no-scroll');
                        
                    }
                }
            } catch (error) {
                console.error('调用原始隐藏方法时出错:', error);
                // 出错时尝试直接设置弹窗隐藏
                const overlay = document.getElementById('depositPopupOverlay');
                if (overlay) {
                    overlay.style.display = 'none';
                    document.body.classList.remove('no-scroll');
                    
                }
            }

            // 重置弹窗
            try {
                if (typeof this.resetPopup === 'function') {
                    this.resetPopup();
                    
                } else {
                    console.error('resetPopup方法不存在或不是一个函数');
                }
            } catch (error) {
                console.error('重置弹窗时出错:', error);
            }
        };
    } else {
        console.error('全局充值弹窗对象不存在，无法扩展功能');
    }

    // 事件绑定 - 仅在元素存在时才绑定
    if (closeButton) {
        closeButton.addEventListener('click', function() {
            // 检查是否存在支付iframe，如果存在则先移除
            const existingPopup = document.getElementById('paymentIframePopup');
            const existingOverlay = document.getElementById('paymentIframeOverlay');

            if (existingPopup || existingOverlay) {
                // 如果存在iframe，先关闭iframe
                window.closePaymentIframe();
                return; // 不继续执行关闭充值弹窗的操作
            }

            // 如果不存在iframe，正常关闭充值弹窗
            if (typeof window.depositPopup !== 'undefined' && typeof window.depositPopup.hide === 'function') {
                window.depositPopup.hide();
            }
        });
    }

    // 点击背景关闭弹窗
    if (popupOverlayElement) {
        popupOverlayElement.addEventListener('click', function(event) {
            if (event.target === popupOverlayElement) {
                // 检查是否存在支付iframe，如果存在则先移除
                const existingPopup = document.getElementById('paymentIframePopup');
                const existingOverlay = document.getElementById('paymentIframeOverlay');

                if (existingPopup || existingOverlay) {
                    // 如果存在iframe，先关闭iframe
                    window.closePaymentIframe();
                    return; // 不继续执行关闭充值弹窗的操作
                }

                // 如果不存在iframe，正常关闭充值弹窗
                if (typeof window.depositPopup !== 'undefined' && typeof window.depositPopup.hide === 'function') {
                    window.depositPopup.hide();
                }
            }
        });
    }

    // 金额选择事件
    if (amountItems && amountItems.length > 0) {
        amountItems.forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有金额项的选中状态
                amountItems.forEach(i => i.classList.remove('active'));

                // 添加当前项的选中状态
                this.classList.add('active');

                // 获取选中的金额值
                const amountValue = this.getAttribute('data-amount');

                if (amountValue === 'custom') {
                    // 如果是自定义金额，显示输入框并聚焦
                    if (customAmountInput) {
                        customAmountInput.style.display = 'block';
                        customAmountInput.focus();
                    }
                    selectedAmount = 0;
                } else {
                    // 如果是预设金额，隐藏输入框并设置选中金额
                    if (customAmountInput) {
                        customAmountInput.style.display = 'none';
                    }
                    selectedAmount = parseInt(amountValue, 10) || 0;
                }

                // 更新摘要信息
                // 如果存在summaryAmount输入框，更新其值
                const summaryAmountInput = document.getElementById('summaryAmount');
                if (summaryAmountInput) {
                    summaryAmountInput.value = selectedAmount;
                }

                updateSummary();
            });
        });
    }

    // 自定义金额输入事件
    if (customAmountInput) {
        customAmountInput.addEventListener('input', function() {
            // 移除非数字字符
            this.value = this.value.replace(/[^0-9]/g, '');

            // 获取输入的金额值
            selectedAmount = parseInt(this.value, 10) || 0;

            // 更新摘要信息
            updateSummary();
        });
    }

    // 支付方式选择事件
    if (paymentItems && paymentItems.length > 0) {
        paymentItems.forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有支付方式的选中状态
                paymentItems.forEach(i => i.classList.remove('active'));

                // 添加当前支付方式的选中状态
                this.classList.add('active');

                // 获取选中的支付方式 - 尝试多个可能的属性名
                selectedPaymentMethod = this.getAttribute('data-payment') || 
                                       this.getAttribute('data-channel-code') || 
                                       this.getAttribute('data-channel-id');
                
                console.log('选择支付方式:', selectedPaymentMethod); // 添加调试日志

                // 启用确认按钮
                checkConfirmButton();
            });
        });
        
        // 默认选中第一个支付方式
        const firstPayment = paymentItems[0];
        if (firstPayment) {
            firstPayment.click(); // 触发点击事件
            // 或者手动设置
            firstPayment.classList.add('active');
            selectedPaymentMethod = firstPayment.getAttribute('data-payment') || 
                                   firstPayment.getAttribute('data-channel-code') || 
                                   firstPayment.getAttribute('data-channel-id');
        }
    }

    // 使用奖励复选框事件
    if (bonusCheckbox) {
        bonusCheckbox.addEventListener('change', function() {
            useBonus = this.checked;
            updateSummary();
        });
    }

    // 更新摘要信息
    function updateSummary() {
        // 获取当前输入框的值（确保使用最新的值）
        const summaryAmountInput = document.getElementById('summaryAmount');
        if (summaryAmountInput) {
            selectedAmount = parseInt(summaryAmountInput.value) || 0;
        }

        // 设置充值金额显示
        const depositAmountDisplay = document.querySelector('.deposit-amount .value');
        if (depositAmountDisplay) {
            depositAmountDisplay.textContent = selectedAmount;
        }

        // 计算奖励金额（使用20%的奖励比例，与其他地方保持一致）
        const bonusRate = 0.2; // 20%的赠送比例
        bonusAmount = useBonus ? Math.floor(selectedAmount * bonusRate) : 0;

        const bonusAmountDisplay = document.querySelector('.bonus-amount .value');
        if (bonusAmountDisplay) {
            bonusAmountDisplay.textContent = bonusAmount;
        }

        // 计算总金额
        totalAmount = selectedAmount + bonusAmount;

        const totalAmountDisplay = document.querySelector('.total-amount .value');
        if (totalAmountDisplay) {
            totalAmountDisplay.textContent = totalAmount;
        }

        // 更新摘要区域的显示
        const summaryBonus = document.getElementById('summaryBonus');
        const summaryTotal = document.getElementById('summaryTotal');

        if (summaryBonus) {
            summaryBonus.textContent = bonusAmount;
        }

        if (summaryTotal) {
            summaryTotal.textContent = totalAmount;
        }

        // 检查确认按钮是否可用
        checkConfirmButton();
    }

    // 检查确认按钮状态
    function checkConfirmButton() {
        if (!confirmButton) return;

        if (selectedAmount >= 10 && selectedAmount <= 20000 && selectedPaymentMethod) {
            confirmButton.classList.remove('disabled');
            confirmButton.disabled = false;
        } else {
            confirmButton.classList.add('disabled');
            confirmButton.disabled = true;
        }
    }

    // 确认按钮点击事件
    if (confirmButton) {
        confirmButton.addEventListener('click', function() {
            if (this.disabled) return;

            // 显示加载状态
            this.classList.add('loading');
            this.textContent =  "Processando pagamento";

            // 获取奖励选项状态
            const needBonus = bonusCheckbox && bonusCheckbox.checked ? 1 : 0;

            // 准备请求数据
            const paymentData = {
                amount: selectedAmount,
                payment_method: selectedPaymentMethod,
                need_bonus: needBonus
            };
            
            // 使用getDepositLink接口（获取支付链接）
            // 准备请求数据
            const requestData = {
                amount: selectedAmount,
                payment_method: selectedPaymentMethod,
                use_bonus: needBonus
            };

            // 发送充值请求
            fetch('/index/index/getDepositLink', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                
                return response.json();
            })
            .then(data => {
                // 恢复按钮状态
                confirmButton.classList.remove('loading');
                confirmButton.textContent = "depositar agora";;

                

                if (data.code === 1) {
                    // 支付成功处理
                    // 使用iframe从下方弹出支付页面
                    if (data.data && data.data.link) {
                        
                        // 在关闭弹窗后，延迟一小段时间再打开支付页面
                        setTimeout(function() {
                            
                            showPaymentIframe(data.data.link);
                        }, 300);
                    } else {
                        console.error('No link found in response data');
                    }
                } else {
                    // 支付失败处理
                    console.error('Payment request failed:', data.msg || '未知错误');
                    window.showToast('Pedido de pagamento falhou: ' + (data.msg || '未知错误'), 'error');
                }
            })
            .catch(error => {
                // 错误处理
                confirmButton.classList.remove('loading');
                confirmButton.textContent = "depositar agora";;
                console.error('支付请求错误:', error);
                window.showToast('Falha no processamento do pagamento, por favor tente mais tarde', 'error');
                
            });
        });
    }

    // 如果summaryAmount是输入框，监听其输入事件
    const summaryAmountInput = document.getElementById('summaryAmount');
    if (summaryAmountInput) {
        summaryAmountInput.addEventListener('input', function() {
            // 移除非数字字符
            this.value = this.value.replace(/[^0-9]/g, '');

            const amount = parseInt(this.value) || 0;
            selectedAmount = amount;

            // 清除之前选中的金额按钮
            if (amountItems) {
                amountItems.forEach(item => item.classList.remove('active'));
            }

            // 更新摘要信息
            updateSummary();

            // 检查确认按钮状态
            checkConfirmButton();
        });
    }
}

// 添加兼容性函数，确保window.showToast 可用
window.showToast = window.showToast || function(message, type) {
    // 检查是否有Toast对象可用
    if (typeof window.Toast !== 'undefined') {
        if (type === 'error') {
            window.Toast.error(message);
        } else if (type === 'success') {
            window.Toast.success(message);
        } else {
            window.Toast.info(message);
        }
    } else {
        // 如果没有Toast对象，使用简单的alert
        console.log('Toast消息: ' + message + ' (类型: ' + type + ')');
    }
};
