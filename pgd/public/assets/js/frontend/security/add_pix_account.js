/**
 * 添加PIX账户相关功能
 */

// 使用全局Toast对象的兼容函数
function showToast(message, type = 'info') {
    if (window.Toast && typeof window.Toast.show === 'function') {
        switch(type) {
            case 'success':
                window.Toast.success(message);
                break;
            case 'error':
                window.Toast.error(message);
                break;
            case 'warning':
                window.Toast.warning(message);
                break;
            default:
                window.Toast.info(message);
        }
    } else {
        console.warn('Toast对象不可用');
    }
}

// 确保弹窗只在点击时显示，而不是在页面加载时就显示
document.addEventListener('DOMContentLoaded', function() {
    // 初始化弹窗，但不显示
    const addPixAccountPopup = document.getElementById('addPixAccountPopup');
    if (addPixAccountPopup) {
        addPixAccountPopup.style.display = 'none';
    }

    // 关闭添加PIX账户弹窗
    const addPixAccountCloseBtn = document.getElementById('addPixAccountCloseBtn');
    if (addPixAccountCloseBtn) {
        addPixAccountCloseBtn.addEventListener('click', function() {
            const popup = document.getElementById('addPixAccountPopup');
            if (popup) {
                popup.style.display = 'none';

                // 重置表单
                const accountNameInput = document.getElementById('pixAccountName');
                const accountValueInput = document.getElementById('pixAccountValue');
                const accountCpfInput = document.getElementById('pixAccountCpf');

                if (accountNameInput) accountNameInput.value = '';
                if (accountValueInput) accountValueInput.value = '';
                if (accountCpfInput) accountCpfInput.value = '';
            }
        });
    }

    // 确认添加PIX账户
    const confirmAddPixAccountBtn = document.getElementById('confirmAddPixAccountBtn');
    if (confirmAddPixAccountBtn) {
        confirmAddPixAccountBtn.addEventListener('click', function() {
            const accountNameInput = document.getElementById('pixAccountName');
            const accountTypeSelect = document.getElementById('pixAccountTypeSelect');
            const accountValueInput = document.getElementById('pixAccountValue');
            const accountCpfInput = document.getElementById('pixAccountCpf');

            if (!accountNameInput || !accountTypeSelect || !accountValueInput || !accountCpfInput) {
                return;
            }

            const accountName = accountNameInput.value.trim();
            const accountTypeElement = accountTypeSelect.querySelector('.select-value');
            const accountType = accountTypeElement ? accountTypeElement.textContent : 'CPF';
            const accountValue = accountValueInput.value.trim();
            const accountCpf = accountCpfInput.value.trim();

            // 验证输入
            if (!accountName) {
                showToast(__('Por favor, insira o nome'));
                return;
            }

            if (!accountValue) {
                showToast(__('Por favor, insira a conta PIX'));
                return;
            }

            // 如果是CPF类型，验证是否是11位数字
            if (accountType === 'CPF') {
                // 检查是否只包含数字
                if (!/^\d+$/.test(accountValue)) {
                    showToast(__('Por favor, insira apenas números para o CPF'));
                    return;
                }

                // 检查是否是11位
                if (accountValue.length !== 11) {
                    showToast(__('Por favor, insira o número de CPF de 11 dígitos'));
                    return;
                }
            } else if (!accountCpf) {
                // 非CPF类型，但没有填写CPF
                showToast(__('Por favor, insira o número de CPF de 11 dígitos'));
                return;
            }

            // 提交数据
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/index/security/addPixAccount', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const res = JSON.parse(xhr.responseText);
                        if (res.code === 1) {
                            showToast(res.msg || __('Adicionado com sucesso'));

                            const popup = document.getElementById('addPixAccountPopup');
                            if (popup) {
                                popup.style.display = 'none';

                                // 重置表单
                                accountNameInput.value = '';
                                accountValueInput.value = '';
                                accountCpfInput.value = '';
                            }

                            // 刷新账户列表
                            if (typeof loadAccounts === 'function') {
                                loadAccounts();
                            }
                        } else {
                            showToast(res.msg || __('Falha na adição'));
                        }
                    } catch (e) {
                        showToast(__('Erro de resposta do servidor'));
                    }
                } else {
                    showToast(__('Erro de rede, tente novamente mais tarde'));
                }
            };

            xhr.onerror = function() {
                showToast(__('Erro de rede, tente novamente mais tarde'));
            };

            const data = 'name=' + encodeURIComponent(accountName) +
                        '&type=' + encodeURIComponent(accountType) +
                        '&account=' + encodeURIComponent(accountValue) +
                        '&cpf=' + encodeURIComponent(accountCpf);

            xhr.send(data);
        });
    }

    // 账户类型选择
    const pixAccountTypeSelect = document.getElementById('pixAccountTypeSelect');
    const pixAccountTypeDropdown = document.getElementById('pixAccountTypeDropdown');
    const pixAccountValue = document.getElementById('pixAccountValue');
    const pixAccountCpf = document.getElementById('pixAccountCpf');

    if (pixAccountTypeSelect && pixAccountTypeDropdown) {
        // 打开下拉菜单
        pixAccountTypeSelect.addEventListener('click', function() {
            pixAccountTypeDropdown.style.display = pixAccountTypeDropdown.style.display === 'none' || pixAccountTypeDropdown.style.display === '' ? 'block' : 'none';
        });

        // 选择账户类型
        pixAccountTypeDropdown.addEventListener('click', function(e) {
            if (e.target.classList.contains('select-option')) {
                const selectedValue = e.target.getAttribute('data-value');
                const selectValue = pixAccountTypeSelect.querySelector('.select-value');

                if (selectValue) {
                    selectValue.textContent = selectedValue;
                }

                // 更新选中状态
                const options = pixAccountTypeDropdown.querySelectorAll('.select-option');
                options.forEach(option => {
                    option.classList.remove('selected');
                });
                e.target.classList.add('selected');

                // 根据选择的类型更新输入框提示
                updateInputPlaceholder(selectedValue);

                // 关闭下拉菜单
                pixAccountTypeDropdown.style.display = 'none';
            }
        });

        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            if (!pixAccountTypeSelect.contains(e.target) && !pixAccountTypeDropdown.contains(e.target)) {
                pixAccountTypeDropdown.style.display = 'none';
            }
        });

        // 根据账户类型更新输入框提示
        function updateInputPlaceholder(accountType) {
            if (!pixAccountValue || !pixAccountCpf) return;

            switch(accountType) {
                case 'CPF':
                    pixAccountValue.setAttribute('placeholder', __('Por favor, insira o número de CPF de 11 dígitos'));
                    pixAccountCpf.style.display = 'none';

                    // 为CPF类型添加输入限制
                    pixAccountValue.setAttribute('maxlength', '11');

                    // 清除之前可能的事件监听器
                    pixAccountValue.removeEventListener('input', restrictToDigits);

                    // 添加只允许输入数字的事件监听器
                    pixAccountValue.addEventListener('input', restrictToDigits);
                    break;
                case 'PHONE':
                case 'EMAIL':
                case 'EVP':
                case 'CNPJ':
                default:
                    pixAccountValue.setAttribute('placeholder', __('Por favor, insira a conta PIX'));
                    pixAccountCpf.style.display = 'block';

                    // 移除CPF类型的输入限制
                    pixAccountValue.removeAttribute('maxlength');

                    // 移除数字限制的事件监听器
                    pixAccountValue.removeEventListener('input', restrictToDigits);
                    break;
            }
        }

        // 限制只能输入数字的函数
        function restrictToDigits() {
            // 将非数字字符替换为空
            this.value = this.value.replace(/\D/g, '');

            // 确保不超过11位
            if (this.value.length > 11) {
                this.value = this.value.substring(0, 11);
            }
        }

        // 初始化
        updateInputPlaceholder('CPF');

        // 初始化时为pixAccountValue添加输入事件监听
        if (pixAccountValue) {
            // 默认情况下，CPF类型是预选的，所以添加输入限制
            pixAccountValue.setAttribute('maxlength', '11');
            pixAccountValue.addEventListener('input', restrictToDigits);
        }
    }
});
