/**
 * 账户管理页面JavaScript
 */
document.addEventListener('DOMContentLoaded', function() {
    // 检查语言包是否已加载
    if (window.SecurityLang && Object.keys(window.SecurityLang).length > 0) {
        // 语言包已加载，直接初始化
        initPage();
    } else {
        // 监听语言包加载完成事件
        document.addEventListener('securityLangLoaded', initPage);

        // 如果5秒后仍未加载完成，也初始化页面
        setTimeout(function() {
            if (!window.pageInitialized) {
                console.warn('语言包加载超时，使用默认值初始化页面');
                initPage();
            }
        }, 2000);
    }
});

/**
 * 初始化页面
 */
function initPage() {
    // 防止重复初始化
    if (window.pageInitialized) return;
    window.pageInitialized = true;

    // 加载账户列表
    loadAccounts();

    // 初始化取款密码弹窗（只初始化事件监听器，不显示弹窗）
    initWithdrawPasswordPopup();
}

/**
 * 加载账户列表
 */
function loadAccounts() {
    const accountsContainer = document.getElementById('accounts-container');
    if (!accountsContainer) return;

    // 显示加载状态
    accountsContainer.innerHTML = `<div class="loading-container" style="text-align: center; padding: 30px;"><div class="loading-spinner"></div><div class="loading-text">${__('loading') || '加载中...'}</div></div>`;

    // 发送AJAX请求获取账户数据
    fetch('/index/security/getAccounts', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1 && data.data) {
            // 更新账户列表
            updateAccountsList(data.data);
        } else {
            // 显示错误消息
            accountsContainer.innerHTML = `<div class="error-message" style="text-align: center; padding: 20px;">${data.msg || __('error') || '错误'}</div>`;
        }
    })
    .catch(error => {
        console.error('获取账户失败:', error);
        // 显示错误消息
        accountsContainer.innerHTML = `<div class="error-message" style="text-align: center; padding: 20px;">${__('error') || '错误'}</div>`;
    });
}

/**
 * 更新账户列表
 */
function updateAccountsList(accounts) {
    const accountsContainer = document.getElementById('accounts-container');
    if (!accountsContainer) return;

    // 初始化HTML内容
    let accountsHTML = `
        <div class="accounts-header">
            <div class="accounts-title">Conta para Recebimento (${accounts.length}/1)</div>
            ${accounts.length > 0 ? `
            <div class="toggle-visibility" id="toggleVisibility">
                <i class="fa fa-eye-slash eye-icon" id="eyeIcon"></i>
            </div>` : ''}
        </div>
    `;

    if (accounts.length === 0) {
        // 无账户状态
        accountsHTML += `
            <div class="account-item">
                <div class="account-content">
                    <div class="account-icon">
                        <img src="/assets/img/frontend/common/ipx.png" alt="PIX" class="account-type-icon">
                    </div>
                    <div class="account-details">
                        <div class="account-name">pix Foto</div>
                    </div>
                </div>
            </div>
        `;

        // 添加底部固定的添加账户按钮
        accountsHTML += `
            <div class="add-account-item">
                <div class="account-content">
                    <div class="account-icon">
                        <img src="/assets/img/frontend/common/ipx.png" alt="PIX" class="account-type-icon">
                    </div>
                    <div class="account-details">
                        <div class="account-name">pix Foto</div>
                    </div>
                </div>
                <div class="add-account-action">
                    <div class="add-btn">Adicionar conta</div>
                </div>
            </div>
        `;

        accountsContainer.innerHTML = accountsHTML;

        // 添加事件监听器
        addAccountEventListeners();
        return;
    }

    accounts.forEach(account => {
        // 处理账户号码，默认显示完整号码
        const accountNumber = account.account_number || '';
        const maskedNumber = accountNumber.length > 4 ?
            '****' + accountNumber.substring(accountNumber.length - 4) :
            accountNumber;

        accountsHTML += `
            <div class="account-item ${account.is_default ? 'default' : ''}">
                <div class="account-content">
                    <div class="account-icon">
                        <img src="/assets/img/frontend/common/ipx.png" alt="PIX" class="account-type-icon">
                    </div>
                    <div class="account-details">
                        <div class="account-top-row">
                            <span class="account-name">${account.account_name || ''}(${account.account_type || 'PIX'})</span>
                        </div>
                        <div class="account-bottom-row">
                            <span class="account-number full-number" style="display: none;">${accountNumber}</span>
                            <span class="account-number masked-number">${maskedNumber}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    accountsContainer.innerHTML = accountsHTML;

    // 添加事件监听器
    addAccountEventListeners();
}

/**
 * 添加账户事件监听器
 */
function addAccountEventListeners() {
    // 设为默认按钮
    const setDefaultBtns = document.querySelectorAll('.set-default');
    setDefaultBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const accountId = this.getAttribute('data-id');
            // 这里可以添加设为默认的AJAX请求
            console.log('设为默认账户:', accountId);
        });
    });

    // 删除按钮
    const deleteBtns = document.querySelectorAll('.delete');
    deleteBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const accountId = this.getAttribute('data-id');
            // 这里可以添加删除账户的AJAX请求
            console.log('删除账户:', accountId);
        });
    });

    // 添加账户按钮
    const addBtn = document.querySelector('.add-btn');
    if (addBtn) {
        addBtn.addEventListener('click', function() {
            // 显示取款密码弹窗
            showWithdrawPasswordPopup();
        });
    }

    // 眼睛图标切换账户号码显示
    const toggleVisibility = document.getElementById('toggleVisibility');
    const eyeIcon = document.getElementById('eyeIcon');
    if (toggleVisibility && eyeIcon) {
        toggleVisibility.addEventListener('click', function() {
            toggleAccountNumberVisibility();
        });
    }

    // 点击账号号码切换显示
    const accountNumbers = document.querySelectorAll('.account-bottom-row');
    accountNumbers.forEach(row => {
        row.addEventListener('click', function(e) {
            // 阻止事件冒泡，避免触发其他点击事件
            e.stopPropagation();

            // 只切换当前点击的账号
            const fullNumber = this.querySelector('.full-number');
            const maskedNumber = this.querySelector('.masked-number');

            if (fullNumber && maskedNumber) {
                const isShowingMasked = maskedNumber.style.display !== 'none';

                // 切换显示状态
                fullNumber.style.display = isShowingMasked ? 'block' : 'none';
                maskedNumber.style.display = isShowingMasked ? 'none' : 'block';
            }
        });
    });

    // 全局切换账号可见性的函数
    function toggleAccountNumberVisibility() {
        const fullNumbers = document.querySelectorAll('.full-number');
        const maskedNumbers = document.querySelectorAll('.masked-number');

        // 检查当前显示状态
        const isShowingMasked = maskedNumbers[0] && maskedNumbers[0].style.display !== 'none';

        // 切换显示状态
        fullNumbers.forEach(el => {
            el.style.display = isShowingMasked ? 'block' : 'none';
        });

        maskedNumbers.forEach(el => {
            el.style.display = isShowingMasked ? 'none' : 'block';
        });

        // 切换眼睛图标
        if (eyeIcon) {
            eyeIcon.className = isShowingMasked ?
                'fa fa-eye eye-icon' :
                'fa fa-eye-slash eye-icon';
        }
    }
}

/**
 * 初始化取款密码弹窗
 */
function initWithdrawPasswordPopup() {
    const popup = document.getElementById('withdrawPasswordPopup');
    const closeBtn = document.getElementById('withdrawPasswordCloseBtn');
    const overlay = popup.querySelector('.popup-overlay');
    const nextBtn = document.getElementById('withdrawPasswordNextBtn');
    const forgotPasswordLink = document.getElementById('forgotPasswordLink');
    const pinBoxes = document.querySelectorAll('.pin-box');
    const passwordInput = document.querySelector('.pin-input-hidden');

    // 关闭弹窗
    function closePopup() {
        popup.style.display = 'none';
        // 重置密码输入
        passwordInput.value = '';
        pinBoxes.forEach(box => {
            box.classList.remove('filled');
            box.classList.remove('pin-box-highlight');
            box.textContent = ''; // 清空文本内容
        });
    }

    // 点击关闭按钮
    closeBtn.addEventListener('click', closePopup);

    // 点击遮罩层关闭
    overlay.addEventListener('click', closePopup);

    // 点击忘记密码链接
    forgotPasswordLink.addEventListener('click', function() {
        closePopup();
        // 跳转到忘记密码页面
        window.location.href = '/index/security/password_setup';
    });

    // 点击下一步按钮
    nextBtn.addEventListener('click', function() {
        // 只有当弹窗显示时才检查密码长度
        if (popup.style.display === 'flex' || popup.style.display === 'block') {
            const password = passwordInput.value;
            if (password.length === 6) {
                // 验证密码
                verifyWithdrawPassword(password);
            } else {
                // 显示错误提示
                showToast(__('password_error_valid') || '请输入6位数密码');
            }
        }
    });

    // 点击PIN网格时聚焦隐藏输入框
    const pinGrid = document.querySelector('.pin-grid');
    if (pinGrid) {
        pinGrid.addEventListener('click', function() {
            passwordInput.focus();
        });
    }

    // 监听隐藏输入框的输入事件
    passwordInput.addEventListener('input', function() {
        updatePinBoxes();

        // 移除自动点击下一步的逻辑，改为只更新PIN框显示
        // 用户需要手动点击"下一步"按钮才能提交
    });

    // 更新密码框显示
    function updatePinBoxes() {
        const password = passwordInput.value;
        pinBoxes.forEach((box, index) => {
            if (index < password.length) {
                box.classList.add('filled');
                box.textContent = '•'; // 添加星号显示
            } else {
                box.classList.remove('filled');
                box.textContent = ''; // 清空内容
            }

            if (index === password.length) {
                box.classList.add('pin-box-highlight');
            } else {
                box.classList.remove('pin-box-highlight');
            }
        });
    }
}

/**
 * 显示取款密码弹窗
 */
function showWithdrawPasswordPopup() {
    const popup = document.getElementById('withdrawPasswordPopup');
    const pinBoxes = document.querySelectorAll('.pin-box');
    const passwordInput = document.querySelector('.pin-input-hidden');

    // 重置密码输入
    passwordInput.value = '';
    pinBoxes.forEach(box => {
        box.classList.remove('filled');
        box.classList.remove('pin-box-highlight');
        box.textContent = ''; // 清空文本内容
    });

    // 激活第一个密码框
    pinBoxes[0].classList.add('pin-box-highlight');

    // 显示弹窗
    popup.style.display = 'flex';

    // 聚焦输入框
    setTimeout(() => {
        passwordInput.focus();
    }, 300);
}

/**
 * 验证取款密码
 */
function verifyWithdrawPassword(password) {
    // 发送AJAX请求验证密码
    fetch('/index/security/verifyWithdrawPassword', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            password: password
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            // 关闭弹窗
            document.getElementById('withdrawPasswordPopup').style.display = 'none';

            // 显示添加PIX账户弹窗
            openAddPixAccountPopup();
        } else {
            // 显示错误提示
            showToast(data.msg || __('senha_incorreta') || '密码错误');
        }
    })
    .catch(() => {
        // 显示错误提示
        showToast(__('Connection failed') || '连接失败');
    });
}

/**
 * 打开添加PIX账户弹窗
 */
function openAddPixAccountPopup() {
    // 获取弹窗元素
    const popup = document.getElementById('addPixAccountPopup');

    // 重置表单
    const accountNameInput = document.getElementById('pixAccountName');
    const accountValueInput = document.getElementById('pixAccountValue');
    const accountCpfInput = document.getElementById('pixAccountCpf');

    if (accountNameInput) accountNameInput.value = '';
    if (accountValueInput) accountValueInput.value = '';
    if (accountCpfInput) accountCpfInput.value = '';

    // 显示添加PIX账户弹窗
    popup.style.display = 'flex'; // 使用flex而不是block，更好地居中显示
}

/**
 * 显示Toast提示
 */
function showToast(message, type = 'info') {
    if (window.Toast && typeof window.Toast.show === 'function') {
        switch(type) {
            case 'success':
                window.Toast.success(message);
                break;
            case 'error':
                window.Toast.error(message);
                break;
            case 'warning':
                window.Toast.warning(message);
                break;
            default:
                window.Toast.info(message);
        }
    } else {
        console.warn('Toast对象不可用');
    }
}



