/**
 * 安全中心标签页控制
 */
document.addEventListener('DOMContentLoaded', function() {
    initTabs();
    initBackButton();
    initPopStateHandler();
    initBalanceVisibility();
});

/**
 * 初始化标签页功能
 */
function initTabs() {
    const tabItems = document.querySelectorAll('.tab-nav .tab-item');
    const balanceSection = document.querySelector('.balance-section');

    tabItems.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();

            const tabId = this.getAttribute('data-tab');
            if (!tabId) return;

            // 如果点击的是当前标签，不做任何操作
            if (this.classList.contains('active')) {
                return;
            }

            // 跳转到对应页面
            switch(tabId) {
                case 'withdraw':
                    window.location.href = '/index/security/index';
                    break;
                case 'withdrawal_records':
                    window.location.href = '/index/security/withdrawal_records';
                    break;
                case 'account_management':
                    window.location.href = '/index/security/account_management';
                    break;
                default:
                    window.location.href = '/index/security/index';
            }
        });
    });
}

/**
 * 初始化余额区域显示状态
 */
function initBalanceVisibility() {
    const balanceSection = document.querySelector('.balance-section');
    if (balanceSection) {
        const path = window.location.pathname;
        // 在withdrawal_records和account_management页面中隐藏余额区域
        if (path.includes('withdrawal_records') || path.includes('account_management')) {
            balanceSection.style.display = 'none';
        }
    }
}

/**
 * 初始化返回按钮
 */
function initBackButton() {
    const backBtn = document.getElementById('backBtn');
    if (backBtn) {
        backBtn.addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = backBtn.getAttribute('data-url');
        });
    }
}

/**
 * 初始化浏览器后退/前进按钮处理
 */
function initPopStateHandler() {
    // 不再需要特殊处理，浏览器会自动处理页面跳转
}
