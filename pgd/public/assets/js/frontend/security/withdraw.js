/**
 * 提款页面JavaScript
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成');

    // 先加载通用PIN输入框组件
    loadScript('/assets/js/frontend/common/pin-input.js', function() {
        console.log('PIN输入组件加载完成');
        initWithdrawForm();

        // 添加直接的点击事件处理程序
        setTimeout(function() {
            const submitBtn = document.getElementById('withdrawSubmitBtn') || document.querySelector('.submit-btn');
            if (submitBtn) {
                console.log('找到提交按钮，添加直接点击事件');
                submitBtn.onclick = function() {
                    console.log('提交按钮被点击（延迟添加的处理程序）');
                };
            } else {
                console.error('未找到提交按钮（延迟检查）');
            }
        }, 1000);
    });
});

/**
 * 加载脚本辅助函数
 */
function loadScript(url, callback) {
    console.log('开始加载脚本:', url);
    // 检查脚本是否已加载
    const existingScript = document.querySelector(`script[src="${url}"]`);
    if (existingScript) {
        console.log('脚本已存在，直接执行回调');
        callback();
        return;
    }

    // 创建新脚本元素
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    script.onload = function() {
        console.log('脚本加载完成:', url);
        callback();
    };
    script.onerror = function() {
        console.error('脚本加载失败:', url);
    };
    document.head.appendChild(script);
}

/**
 * 初始化提款表单
 */
function initWithdrawForm() {
    console.log('初始化提款表单');

    // 获取DOM元素
    const paymentMethodSelector = document.getElementById('payment-method-selector');
    const accountDropdown = document.getElementById('account-dropdown');
    const amountInput = document.querySelector('.amount-input-field');
    const submitBtn = document.getElementById('withdrawSubmitBtn') || document.querySelector('.submit-btn');
    const pinGrid = document.getElementById('withdrawPinGrid');

    // 确保PIN网格存在
    if (!pinGrid) {
        console.error('未找到PIN网格元素');
        return;
    }

    // 初始化PIN输入框
    console.log('初始化PIN输入框');
    let withdrawPinInput;
    try {
        withdrawPinInput = initPinInput({
            gridSelector: '#withdrawPinGrid',
            inputId: 'withdrawPinInput',
            maxLength: 6,
            autoFocus: true,
            onChange: function(value) {
                console.log('PIN输入变化:', value);
            }
        });
        console.log('PIN输入框初始化成功');
    } catch (error) {
        console.error('PIN输入框初始化失败:', error);
    }

    // 确保PIN输入框可以点击并获取焦点
    if (pinGrid) {
        pinGrid.addEventListener('click', function() {
            console.log('PIN网格被点击');
            if (withdrawPinInput) {
                withdrawPinInput.focus();
                console.log('PIN输入框获取焦点');
            }
        });
    }

    // 初始化金额输入
    if (amountInput) {
        amountInput.addEventListener('input', function() {
            // 限制只能输入数字和小数点
            this.value = this.value.replace(/[^0-9.]/g, '');

            // 确保只有一个小数点
            const decimalCount = (this.value.match(/\./g) || []).length;
            if (decimalCount > 1) {
                this.value = this.value.replace(/\.+$/, '');
            }

            // 限制小数点后两位
            const parts = this.value.split('.');
            if (parts.length > 1 && parts[1].length > 2) {
                this.value = parts[0] + '.' + parts[1].substring(0, 2);
            }
        });
    }

    // 初始化支付方式选择器
    if (paymentMethodSelector) {
        paymentMethodSelector.addEventListener('click', function() {
            // 如果没有账户，跳转到账户管理页面
            if (this.getAttribute('data-tab')) {
                window.location.href = '/index/security/' + this.getAttribute('data-tab');
                return;
            }

            // 如果有账户，显示/隐藏账户下拉菜单
            if (accountDropdown) {
                accountDropdown.style.display = accountDropdown.style.display === 'none' ? 'block' : 'none';
            }
        });
    }

    // 初始化账户选项点击事件
    if (accountDropdown) {
        const accountOptions = accountDropdown.querySelectorAll('.account-option');
        accountOptions.forEach(option => {
            option.addEventListener('click', function() {
                const accountId = this.getAttribute('data-id');
                const accountType = this.getAttribute('data-type');
                const accountDetails = this.getAttribute('data-details');

                // 更新选中的账户ID
                document.getElementById('selected-account-id').value = accountId;

                // 更新支付方式显示
                const paymentLogo = paymentMethodSelector.querySelector('.payment-logo img');
                const paymentName = paymentMethodSelector.querySelector('.payment-name');
                const paymentDetails = paymentMethodSelector.querySelector('.payment-details');

                if (paymentLogo) {
                    paymentLogo.src = `/assets/img/frontend/payment/${accountType}.png`;
                    paymentLogo.alt = accountType.toUpperCase();
                }

                if (paymentName) {
                    paymentName.textContent = accountType.toUpperCase();
                }

                if (paymentDetails && accountDetails) {
                    paymentDetails.textContent = accountDetails;
                }

                // 隐藏下拉菜单
                accountDropdown.style.display = 'none';
            });
        });
    }
    // 移除可能存在的旧事件监听器
    if (submitBtn) {
        const newSubmitBtn = submitBtn.cloneNode(true);
        submitBtn.parentNode.replaceChild(newSubmitBtn, submitBtn);
        submitBtn = newSubmitBtn;
    }

    // 添加内联点击处理程序
    if (submitBtn) {
        submitBtn.onclick = function() {
            if (!withdrawPinInput) {
                console.error('PIN输入框未初始化');
                return;
            }

            const amount = amountInput ? amountInput.value : '';
            const accountId = document.getElementById('selected-account-id') ? document.getElementById('selected-account-id').value : '';
            const pin = withdrawPinInput.getValue();

            // 验证输入
            if (!amount || parseFloat(amount) <= 0) {
                console.log(window.__('Please enter a valid amount') || '请输入有效金额');
                return;
            }

            // 获取最低和最高提款金额限制
            const minAmount = parseFloat(amountInput.getAttribute('min') || 100);
            const maxAmount = parseFloat(amountInput.getAttribute('max') || 50000);
            const amountValue = parseFloat(amount);

            // 验证最低提款金额
            if (amountValue < minAmount) {
                console.log(window.__('Minimum withdrawal amount is %s', minAmount) || `最低提款金额为 ${minAmount}`);
                return;
            }

            // 验证最高提款金额
            if (amountValue > maxAmount) {
                console.log(window.__('Maximum withdrawal amount is %s', maxAmount) || `最高提款金额为 ${maxAmount}`);
                return;
            }

            if (!accountId) {
                console.log(window.__('Please select a withdrawal account') || 'Por favor, selecione uma conta para saque');
                return;
            }

            if (pin.length !== 6) {
                console.log(window.__('Please enter a 6-digit withdrawal PIN') || '请输入6位提款密码');
                return;
            }

            // 提交提款请求
            submitWithdraw(amount, pin, accountId);
        };
    } else {
        console.error('未找到提交按钮');
    }

    // 提交提款请求
    function submitWithdraw(amount, pin, accountId) {
        console.log('提交提款请求');

        // 显示加载状态
        submitBtn.textContent = 'Processando...';
        submitBtn.style.opacity = '0.7';
        submitBtn.style.pointerEvents = 'none';

        // 发送AJAX请求
        fetch('/index/security/submitWithdraw', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: new URLSearchParams({
                amount: amount,
                pin: pin,
                account_id: accountId
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('提款请求响应:', data);

            // 恢复按钮状态
            submitBtn.textContent = window.__('Confirm');
            submitBtn.style.opacity = '1';
            submitBtn.style.pointerEvents = 'auto';

            if (data.code === 1) {
                // 提款成功
                showToast(data.msg || window.__('Solicitação de saque enviada com sucesso'));

                // 清空表单
                if (amountInput) amountInput.value = '';
                if (withdrawPinInput) withdrawPinInput.clear();

                // 可以添加跳转到记录页面的代码
                setTimeout(() => {
                    window.location.href = '/index/security/withdrawal_records';
                }, 2000);
            } else {
                // 提款失败
                showToast(data.msg || window.__('Falha ao enviar solicitação de saque'));
            }
        })
        .catch(error => {
            console.error('提款请求错误:', error);

            // 恢复按钮状态
            submitBtn.textContent = window.__('Confirm');
            submitBtn.style.opacity = '1';
            submitBtn.style.pointerEvents = 'auto';

            showToast('Falha na conexão');
        });
    }

    // 显示Toast提示
    function showToast(message, type = 'info') {
        console.log('显示Toast:', message, type);

        if (window.Toast && typeof window.Toast.show === 'function') {
            switch(type) {
                case 'success':
                    window.Toast.success(message);
                    break;
                case 'error':
                    window.Toast.error(message);
                    break;
                case 'warning':
                    window.Toast.warning(message);
                    break;
                default:
                    window.Toast.info(message);
            }
        } else {
            console.warn('Toast对象不可用');
        }
    }
}
