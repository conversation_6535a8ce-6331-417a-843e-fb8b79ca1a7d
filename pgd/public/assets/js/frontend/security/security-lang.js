/**
 * 安全中心语言包处理
 * 这个文件用于集中管理安全中心相关页面的语言翻译
 */

// 安全中心语言对象
// var SecurityLang = {};

// 初始化安全中心语言包
// function initSecurityLang() {
//     // 从服务器获取安全中心语言包
//     fetch('/index/security/getLang', {
//         method: 'GET',
//         headers: {
//             'X-Requested-With': 'XMLHttpRequest'
//         }
//     })
//     .then(response => response.json())
//     .then(data => {
//         if (data && data.code === 1) {
//             // 将语言包数据存储到SecurityLang对象中
//             SecurityLang = data.data || {};
//             console.log('安全中心语言包加载成功');

//             // 触发语言包加载完成事件
//             document.dispatchEvent(new CustomEvent('securityLangLoaded'));
//         } else {
//             console.error('加载安全中心语言包失败:', data.msg || '未知错误');
//         }
//     })
//     .catch(error => {
//         console.error('获取安全中心语言包时发生错误:', error);
//     });
// }

// 获取语言变量
// function __(key, ...args) {
//     // 首先尝试从SecurityLang获取
//     if (SecurityLang && SecurityLang[key]) {
//         let text = SecurityLang[key];

//         // 处理参数替换
//         if (args && args.length > 0) {
//             args.forEach((arg, index) => {
//                 text = text.replace(new RegExp(`%s`, 'g'), arg);
//             });
//         }

//         return text;
//     }

//     // 然后尝试从Fast.lang获取
//     if (typeof Fast !== 'undefined' && typeof Fast.lang === 'function') {
//         return Fast.lang(key, ...args);
//     }

//     // 如果全局__函数可用，使用它
//     if (typeof window.__ === 'function' && window.__ !== __) {
//         return window.__(key, ...args);
//     }

//     // 最后返回原始键名
//     return key;
// }

// 覆盖全局__函数
// window.__ = __;

// 在文档加载完成后初始化语言包
document.addEventListener('DOMContentLoaded', function() {
    // initSecurityLang();
});
