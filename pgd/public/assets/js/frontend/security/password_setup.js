/**
 * 设置提款密码页面JavaScript
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('密码设置页面加载完成');

    // 引入通用 PIN 输入框组件
    loadScript('/assets/js/frontend/common/pin-input.js', function() {
        console.log('PIN 输入框组件加载完成');
        // 初始化新密码输入框
        const newPinInput = initPinInput({
            gridSelector: '#newPinGrid',
            inputId: 'newPinInput',
            autoFocus: true, // 确保自动获取焦点
            onComplete: function() {
                // 自动跳到确认密码输入框
                confirmPinInput.focus();
            }
        });

        // 初始化确认密码输入框
        const confirmPinInput = initPinInput({
            gridSelector: '#confirmPinGrid',
            inputId: 'confirmPinInput',
            autoFocus: false
        });

        // 初始化提交按钮
        initSubmitButton(newPinInput, confirmPinInput);
    });
});

/**
 * 加载脚本辅助函数
 */
function loadScript(url, callback) {
    // 检查脚本是否已加载
    const existingScript = document.querySelector(`script[src="${url}"]`);
    if (existingScript) {
        callback();
        return;
    }

    // 创建新脚本元素
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    script.onload = callback;
    document.head.appendChild(script);
}

/**
 * 初始化提交按钮
 */
function initSubmitButton(newPinInput, confirmPinInput) {
    const setupBtn = document.getElementById('setupPasswordBtn');
    if (!setupBtn) return;

    setupBtn.addEventListener('click', function() {
        const newPin = newPinInput.getValue();
        const confirmPin = confirmPinInput.getValue();

        // 验证输入
        if (newPin.length < 6) {
            showToast(window.__('please_enter_6_digit_password'));
            newPinInput.focus();
            return;
        }

        if (confirmPin.length < 6) {
            showToast(window.__('please_confirm_password'));
            confirmPinInput.focus();
            return;
        }

        if (newPin !== confirmPin) {
            showToast(window.__('passwords_do_not_match'));
            confirmPinInput.clear();
            confirmPinInput.focus();
            return;
        }

        // 显示处理中
        showToast(window.__('processing'));

        // 禁用提交按钮，防止重复提交
        setupBtn.style.opacity = '0.7';
        setupBtn.style.pointerEvents = 'none';

        // 发送AJAX请求设置密码
        fetch('/index/security/setupWithdrawPassword', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: new URLSearchParams({
                password: newPin
            })
        })
        .then(response => response.json())
        .then(data => {
            // 重新启用提交按钮
            setupBtn.style.opacity = '1';
            setupBtn.style.pointerEvents = 'auto';

            if (data.code === 1) {
                // 设置成功
                showToast(data.msg || window.__('success'));

                // 跳转到提款页面
                setTimeout(() => {
                    window.location.href = '/index/security/index';
                }, 1500);
            } else {
                // 设置失败
                showToast(data.msg || window.__('error'));
            }
        })
        .catch(error => {
            console.error('设置密码失败:', error);

            // 重新启用提交按钮
            setupBtn.style.opacity = '1';
            setupBtn.style.pointerEvents = 'auto';

            showToast(window.__('error'));
        });
    });
}

/**
 * 显示Toast提示
 */
function showToast(message, type = 'info') {
    if (window.Toast && typeof window.Toast.show === 'function') {
        switch(type) {
            case 'success':
                window.Toast.success(message);
                break;
            case 'error':
                window.Toast.error(message);
                break;
            case 'warning':
                window.Toast.warning(message);
                break;
            default:
                window.Toast.info(message);
        }
    } else {
        console.warn('Toast对象不可用');
    }
}
