/**
 * 安全中心页面专用JavaScript
 */
document.addEventListener('DOMContentLoaded', function() {
    // 设置全局货币符号
    window.currency = window.currency || document.querySelector('meta[name="currency-symbol"]')?.content || 'R$';

    // 检查当前页面是否为提款页面
    const isWithdrawPage = window.location.pathname.includes('/security/index') ||
                          window.location.pathname.includes('/security/withdraw') ||
                          window.location.search.includes('tab=withdraw');

    // 检查当前页面是否为密码设置页面
    const isPasswordSetupPage = window.location.pathname.includes('/security/password_setup');


    // 只在非密码设置页面初始化PIN码输入
    if (!isPasswordSetupPage) {
        console.log('非密码设置页面，初始化通用PIN输入');
        initPinInput();
    } else {
        console.log('密码设置页面，跳过通用PIN输入初始化');
    }

    // 如果不是提款页面且不是密码设置页面，才初始化提交按钮
    if (!isWithdrawPage && !isPasswordSetupPage) {
        console.log('非提款页面且非密码设置页面，初始化提交按钮');
        // 提交按钮处理
        initSubmitButton();
    } else {
        console.log('提款页面或密码设置页面，跳过提交按钮初始化');
    }

    // 初始化余额刷新功能
    initBalanceRefresh();

    // 初始化金额输入字段
    initAmountInput();

    // 初始化标签页显示
    initTabDisplay();

    // 初始化支付方式选择器
    initPaymentMethodSelector();
});

/**
 * 初始化标签页显示
 * 根据URL或者active_tab参数来显示正确的标签内容
 */
function initTabDisplay() {
    // 检查当前页面的active_tab参数
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');

    // 检查URL路径中是否包含某个标签的关键词
    const path = window.location.pathname;
    let activeTab = '';

    if (tabParam) {
        activeTab = tabParam;
    } else if (path.includes('/withdrawal_records')) {
        activeTab = 'withdrawal_records';
    } else if (path.includes('/account_management')) {
        activeTab = 'account_management';
    } else if (path.includes('/withdraw')) {
        activeTab = 'withdraw';
    } else {
        // 查找DOM中被标记为active的标签
        const activeTabItem = document.querySelector('.tab-item.active');
        if (activeTabItem) {
            activeTab = activeTabItem.getAttribute('data-tab');
        } else {
            activeTab = 'withdraw'; // 默认标签
        }
    }

    // 如果没有找到对应的标签或没有指定标签，则使用默认逻辑
    // 隐藏所有标签内容
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.style.display = 'none';
        content.classList.remove('active');
    });

    // 显示目标标签内容
    const targetContent = document.getElementById(activeTab + '-content');
    if (targetContent) {
        targetContent.style.display = 'block';
        targetContent.classList.add('active');

        // 处理余额区域的显示状态
        const balanceSection = document.querySelector('.balance-section');
        if (balanceSection) {
            balanceSection.style.display = 'block';
        }

        // 手动触发标签切换事件
        const tabChangedEvent = new CustomEvent('tabChanged', {
            detail: { tabId: activeTab }
        });
        document.dispatchEvent(tabChangedEvent);
    }
}

/**
 * 初始化金额输入字段，确保不自动填充不记录值
 */
function initAmountInput() {
    const amountInput = document.querySelector('.amount-input-field');
    if (!amountInput) return;

    // 设置默认值为0
    amountInput.value = '0';

    // 确保只能输入数字和小数点
    amountInput.addEventListener('input', function() {
        // 替换非数字和小数点的字符
        this.value = this.value.replace(/[^0-9.]/g, '');

        // 确保只有一个小数点
        const decimalPoints = this.value.match(/\./g);
        if (decimalPoints && decimalPoints.length > 1) {
            this.value = this.value.substring(0, this.value.lastIndexOf('.'));
        }

        // 限制小数点后两位
        if (this.value.includes('.')) {
            const parts = this.value.split('.');
            if (parts[1].length > 2) {
                this.value = parts[0] + '.' + parts[1].substring(0, 2);
            }
        }

        // 如果清空了输入，默认设为0
        if (this.value === '') {
            this.value = '0';
        }
    });

    // 当获得焦点时，如果值为0，则清空
    amountInput.addEventListener('focus', function() {
        if (this.value === '0') {
            this.value = '';
        }
    });

    // 当失去焦点时，如果为空，则设为0
    amountInput.addEventListener('blur', function() {
        if (this.value === '') {
            this.value = '0';
        }
    });

    // 禁止粘贴操作
    amountInput.addEventListener('paste', function(e) {
        e.preventDefault();
    });

    // 防止自动填充
    amountInput.setAttribute('autocomplete', 'off');
    amountInput.setAttribute('autocorrect', 'off');
    amountInput.setAttribute('autocapitalize', 'off');
    amountInput.setAttribute('spellcheck', 'false');
}

/**
 * 初始化PIN码输入功能
 * 使用通用的 pin-input.js 组件
 */
function initPinInput() {
    // 加载通用PIN输入框组件
    loadScript('/assets/js/frontend/common/pin-input.js', function() {
        console.log('PIN输入组件加载完成');

        // 初始化提款页面的PIN输入框
        initWithdrawPinInput();
    });
}

/**
 * 初始化提款页面的PIN输入框
 */
function initWithdrawPinInput() {
    // 检查当前页面是否为提款页面
    const isWithdrawPage = window.location.pathname.includes('/security/index') ||
                          window.location.search.includes('tab=withdraw');

    if (!isWithdrawPage) {
        console.log('非提款页面，跳过提款PIN输入框初始化');
        return;
    }

    console.log('初始化提款PIN输入框');

    // 获取PIN网格元素
    const pinGrid = document.getElementById('withdrawPinGrid');
    if (!pinGrid) {
        console.error('未找到PIN网格元素: #withdrawPinGrid');
        return;
    }

    // 初始化PIN输入框
    try {
        window.withdrawPinInput = initPinInput({
            gridSelector: '#withdrawPinGrid',
            inputId: 'withdrawPinInput',
            maxLength: 6,
            autoFocus: true
        });
        console.log('PIN输入框初始化成功');

        // 初始化提交按钮
        initWithdrawSubmitButton();
    } catch (error) {
        console.error('PIN输入框初始化失败:', error);
    }
}

/**
 * 初始化提款提交按钮
 */
function initWithdrawSubmitButton() {
    const submitBtn = document.getElementById('withdrawSubmitBtn') || document.querySelector('.submit-btn');
    if (!submitBtn) {
        return;
    }
    // 移除可能存在的旧事件监听器
    const newSubmitBtn = submitBtn.cloneNode(true);
    submitBtn.parentNode.replaceChild(newSubmitBtn, submitBtn);

    // 添加点击事件处理程序
    newSubmitBtn.addEventListener('click', function() {

        if (!window.withdrawPinInput) {
            showToast('Erro do sistema, por favor atualize a página e tente novamente');
            return;
        }

        const amountInput = document.querySelector('.amount-input-field');
        const amount = amountInput ? amountInput.value : '';
        const accountId = document.getElementById('selected-account-id') ? document.getElementById('selected-account-id').value : '';
        const pin = window.withdrawPinInput.getValue();
        // 验证输入
        if (!amount || parseFloat(amount) <= 0) {
            showToast('Por favor, insira um valor válido');
            return;
        }

        if (!accountId) {
            // 确保使用正确的翻译函数和键名
            showToast('Por favor, selecione uma conta para saque');
            return;
        }

        if (pin.length !== 6) {
            showToast('Por favor, digite uma senha de 6 dígitos para saque');
            return;
        }

        // 提交提款请求
        submitWithdraw(amount, pin, accountId, newSubmitBtn, amountInput);
    });
}

/**
 * 提交提款请求
 */
function submitWithdraw(amount, pin, accountId, submitBtn, amountInput) {
    // 首先检查打码任务是否完成
    
    // 显示加载状态
    submitBtn.textContent = __('Processing...');
    submitBtn.style.opacity = '0.7';
    submitBtn.style.pointerEvents = 'none';

    // 发送AJAX请求
    fetch('/index/security/submitWithdraw', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            amount: amount,
            pin: pin,
            account_id: accountId
        })
    })
    .then(response => response.json())
    .then(data => {

        // 恢复按钮状态
        submitBtn.textContent = __('confirmar');
        submitBtn.style.opacity = '1';
        submitBtn.style.pointerEvents = 'auto';

        if (data.code === 1) {
            // 提款成功
            showToast(data.msg || __('Solicitação de saque enviada com sucessoy'), 'success');

            // 清空表单
            if (amountInput) amountInput.value = '';
            if (window.withdrawPinInput) window.withdrawPinInput.clear();

            // 可以添加跳转到记录页面的代码
            setTimeout(() => {
                window.location.href = '/index/security/withdrawal_records';
            }, 2000);
        } else {
            // 提款失败
            showToast(data.msg || __('Falha ao enviar solicitação de saque'), 'error');
        }
    })
    .catch(error => {
        console.error('提款请求错误:', error);

        // 恢复按钮状态
        submitBtn.textContent = __('confirmar');
        submitBtn.style.opacity = '1';
        submitBtn.style.pointerEvents = 'auto';

        showToast(__('Connection failed'), 'error');
    });
}

/**
 * 加载脚本辅助函数
 * @param {string} url - 脚本URL
 * @param {Function} callback - 加载完成后的回调函数
 */
function loadScript(url, callback) {
    // 检查脚本是否已加载
    const existingScript = document.querySelector(`script[src="${url}"]`);
    if (existingScript) {
        callback();
        return;
    }

    // 创建新脚本元素
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = url;
    script.onload = callback;
    document.head.appendChild(script);
}

/**
 * 初始化余额刷新功能
 */
function initBalanceRefresh() {
    const refreshBtn = document.getElementById('refreshBalanceBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            // 添加loading类，触发旋转动画
            this.classList.add('loading');

            // 显示加载中提示
            Toast.info(__('loading'));

            // 发送刷新余额请求
            fetch('/index/index/refreshBalance', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                setTimeout(() => {
                    // 移除loading类，停止旋转
                    refreshBtn.classList.remove('loading');

                    if (data.code === 1) {
                        // 更新余额显示
                        const balanceValue = document.querySelector('.balance-value');
                        if (balanceValue) {
                            balanceValue.textContent = window.currency + ' ' + (data.data.balance || '0.00');
                        }

                        // 更新投注要求显示
                        const betRequirementValue = document.querySelector('.requirement-value');
                        if (betRequirementValue && data.data.bet_requirement !== undefined) {
                            betRequirementValue.textContent = window.currency + ' ' + (data.data.bet_requirement || '0.00');
                        }

                        Toast.success(__('success'));
                    } else {
                        Toast.error(data.msg || __('error'));
                    }
                }, 500);
            })
            .catch(error => {
                console.error('刷新余额失败:', error);
                refreshBtn.classList.remove('loading');
                Toast.error(__('error'));
            });
        });
    }
}

/**
 * 初始化提交按钮
 */
function initSubmitButton() {
    // 检查当前页面是否为提款页面
    const isWithdrawPage = window.location.pathname.includes('/security/index') ||
                          window.location.pathname.includes('/security/withdraw') ||
                          window.location.search.includes('tab=withdraw');

    // 检查当前页面是否为密码设置页面
    const isPasswordSetupPage = window.location.pathname.includes('/security/password_setup');

    // 如果是提款页面或密码设置页面，不初始化提交按钮
    if (isWithdrawPage || isPasswordSetupPage) {
        console.log('提款页面或密码设置页面，跳过提交按钮初始化');
        return;
    }
}

/**
 * 显示提示信息
 * @param {string} message - 提示信息内容
 * @param {string} type - 提示类型 (success, error, info)
 */
function showToast(message, type = 'error') {
    // 如果存在Toast对象，使用Toast对象
    if (window.Toast && typeof window.Toast.show === 'function') {
        switch(type) {
            case 'success':
                window.Toast.success(message);
                break;
            case 'error':
                window.Toast.error(message);
                break;
            case 'warning':
                window.Toast.warning(message);
                break;
            default:
                window.Toast.info(message);
        }
    } else {
        // 如果没有，使用简单的alert
        console.warn('Toast对象不可用');
        console.log(message);
    }
}

/**
 * 初始化支付方式选择器
 * 处理支付方式的选择和标签切换
 */
function initPaymentMethodSelector() {
    const paymentMethodSelector = document.getElementById('payment-method-selector');
    const selectedAccountId = document.getElementById('selected-account-id');

    if (!paymentMethodSelector) return;

    // 点击支付方式选择器
    paymentMethodSelector.addEventListener('click', function() {
        // 直接检查页面中是否已经有账户信息
        // 如果 selected-account-id 元素存在且有值，说明用户已有账户
        if (selectedAccountId && selectedAccountId.value) {
            // 有账户，不做任何操作
            console.log('用户已有账户，不跳转');
        } else {
            // 无账户，显示提示信息
            Toast.info('Você ainda não tem uma conta vinculada, adicione uma conta primeiro');

            // 延迟跳转到账户管理页面
            setTimeout(function() {
                window.location.href = '/index/security/account_management';
            }, 1500);
        }
    });
}

// 使用FastAdmin标准语言方案
// 定义一个辅助函数来获取语言变量
function __(key, ...args) {
    // 如果Fast.lang可用，使用它
    if (typeof Fast !== 'undefined' && typeof Fast.lang === 'function') {
        return Fast.lang(key, ...args);
    }
    // 如果全局__函数可用，使用它
    else if (typeof window.__ === 'function') {
        return window.__(key, ...args);
    }
    // 否则返回原始键名
    return key;
}
