/**
 * 提款记录页面JavaScript
 */
document.addEventListener('DOMContentLoaded', function() {
    // 设置日期筛选下拉菜单
    setupDateFilter();

    // 初始化复制功能
    initClipboard();

    // 确保滚动容器正确显示滚动条
    setupScrollableContent();
});

/**
 * 初始化日期筛选下拉功能
 */
function setupDateFilter() {
    const btn = document.getElementById('dateFilterBtn');
    const menu = document.getElementById('dateFilterMenu');

    if (!btn || !menu) return;

    // 点击按钮显示/隐藏下拉菜单
    btn.addEventListener('click', function() {
        const isOpen = menu.classList.contains('show');

        if (isOpen) {
            menu.classList.remove('show');
            btn.classList.remove('active');
        } else {
            menu.classList.add('show');
            btn.classList.add('active');
        }
    });

    // 点击选项时应用筛选并加载数据
    menu.querySelectorAll('.filter-option').forEach(option => {
        option.addEventListener('click', function() {
            const dateValue = this.getAttribute('data-value');

            // 先关闭下拉菜单
            menu.classList.remove('show');
            btn.classList.remove('active');

            // 更新筛选文本显示
            const filterText = this.textContent;
            btn.querySelector('.filter-text').textContent = filterText;

            // 更新所有选项的激活状态
            menu.querySelectorAll('.filter-option').forEach(opt => {
                opt.classList.remove('active');
            });
            this.classList.add('active');

            // 显示加载状态
            const recordsContainer = document.querySelector('.withdrawal-records');
            if (recordsContainer) {
                recordsContainer.innerHTML = `<div class="loading-container"><div class="loading-spinner"></div><div class="loading-text">${window.__('loading')}</div></div>`;
            }

            // 使用fetch请求获取新的记录数据
            fetchRecordsData(dateValue);

            // 更新URL参数而不刷新页面
            const url = new URL(window.location);
            url.searchParams.set('date_range', dateValue);
            window.history.pushState({}, '', url);
        });
    });

    // 点击页面其他地方关闭下拉菜单
    document.addEventListener('click', function(e) {
        if (!btn.contains(e.target) && !menu.contains(e.target)) {
            menu.classList.remove('show');
            btn.classList.remove('active');
        }
    });
}

/**
 * 获取记录数据的函数
 */
function fetchRecordsData(dateRange) {
    fetch(`/index/security/getWithdrawRecords?date_range=${dateRange}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            // 更新总金额
            const totalAmountElement = document.querySelector('.total-withdrawal-amount');
            if (totalAmountElement && data.data.totalAmount) {
                totalAmountElement.textContent = window.currency + ' ' + data.data.totalAmount;
            }

            // 更新记录列表
            updateRecordsList(data.data.records || []);
        } else {
            // 显示错误消息
            const recordsContainer = document.querySelector('.withdrawal-records');
            if (recordsContainer) {
                recordsContainer.innerHTML = `<div class="error-message">${data.msg || window.__('error')}</div>`;
            }
        }
    })
    .catch(error => {
        console.error('获取记录失败:', error);
        // 显示错误消息
        const recordsContainer = document.querySelector('.withdrawal-records');
        if (recordsContainer) {
            recordsContainer.innerHTML = `<div class="error-message">${window.__('error')}</div>`;
        }
    });
}

/**
 * 更新记录列表的函数
 */
function updateRecordsList(records) {
    const recordsContainer = document.querySelector('.withdrawal-records');
    if (!recordsContainer) return;

    if (records.length === 0) {
        // 无记录状态
        recordsContainer.innerHTML = `
            <div class="no-records-container">
                <div class="empty-state">
                    <img src="/assets/img/frontend/common/empty.png" alt="No Records" class="empty-icon">
                    <div class="no-records-message">'Nenhum registro de saque encontrado'</div>
                </div>
            </div>
        `;
        return;
    }

    // 有记录，生成记录列表
    let recordsHTML = '';
    records.forEach(record => {
        const iconUrl = "/assets/img/frontend/common/ipx.png";
        // 使用JavaScript模板字符串语法（这里是正确的，因为这是在JS中动态生成HTML）
        recordsHTML += `
            <div class="history-item">
                <div class="history-item-row">
                    <div class="history-item-left">
                         <img src="${iconUrl}" class="channel-icon">
                        <span class="channel-name">${record.channel_code || 'PIX'}</span>
                    </div>
                    <div class="history-item-right">
                        <span class="deposit-amount">${window.currency} ${record.amount}</span>
                    </div>
                </div>
                <div class="history-item-row">
                    <div class="history-item-left">
                        <span class="deposit-time">${record.create_time}</span>
                    </div>
                    <div class="history-item-middle">
                        <div class="order-container">
                            <span class="order-no">${record.third_order_no || record.id}</span>
                            <button data-clipboard-text="${record.third_order_no || record.id}" title="copy No."><i class="fa fa-copy"></i></button>
                        </div>
                    </div>
                    <div class="history-item-right">
                        <span class="deposit-status status-${record.status}">${record.status_text}</span>
                    </div>
                </div>
            </div>
        `;
    });

    recordsContainer.innerHTML = recordsHTML;

    // 重新初始化复制功能
    initClipboard();
}

/**
 * 初始化复制功能
 */
function initClipboard() {
    const copyButtons = document.querySelectorAll('.copy-btn');

    copyButtons.forEach(btn => {
        // 清除所有旧的点击事件监听器
        const newBtn = btn.cloneNode(true);
        btn.parentNode.replaceChild(newBtn, btn);

        // 添加新的事件监听器
        newBtn.addEventListener('click', function() {
            const text = this.getAttribute('data-clipboard-text');
            console.log('复制文本:', text);

            if (!text) {
                console.error('没有找到要复制的文本');
                showToast(window.__('Copy failed'));
                return;
            }

            // 添加视觉反馈
            const icon = this.querySelector('i');
            if (icon) {
                // 临时改变图标为成功图标
                const originalClass = icon.className;
                icon.className = 'fa fa-check';

                // 1秒后恢复原图标
                setTimeout(() => {
                    icon.className = originalClass;
                }, 1000);
            }

            // 使用现代的 Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text)
                    .then(() => {
                        // 复制成功
                        console.log('复制成功 (Clipboard API)');
                        showToast(window.__('Copied to clipboard'));
                    })
                    .catch(err => {
                        console.error('复制失败 (Clipboard API):', err);
                        // 如果 Clipboard API 失败，回退到旧方法
                        fallbackCopy(text);
                    });
            } else {
                console.log('Clipboard API 不可用，使用回退方法');
                // 回退到旧方法
                fallbackCopy(text);
            }
        });
    });

    // 回退复制方法
    function fallbackCopy(text) {
        console.log('使用回退复制方法');
        // 创建临时输入框
        const tempInput = document.createElement('textarea');
        tempInput.value = text;
        tempInput.style.position = 'fixed';
        tempInput.style.left = '0';
        tempInput.style.top = '0';
        tempInput.style.opacity = '0';
        tempInput.style.pointerEvents = 'none';
        document.body.appendChild(tempInput);

        // 选择并复制文本
        tempInput.focus();
        tempInput.select();

        let success = false;
        try {
            // 尝试使用 document.execCommand('copy')
            success = document.execCommand('copy');
            console.log('execCommand 复制结果:', success);
        } catch (err) {
            console.error('execCommand 复制失败:', err);
            success = false;
        }

        // 移除临时输入框
        document.body.removeChild(tempInput);

        // 显示结果
        if (success) {
            showToast(window.__('Copied to clipboard'));
        } else {
            showToast(window.__('Copy failed'));
        }
    }
}

/**
 * 显示Toast提示
 */
function showToast(message, type = 'info') {
    if (window.Toast && typeof window.Toast.show === 'function') {
        switch(type) {
            case 'success':
                window.Toast.success(message);
                break;
            case 'error':
                window.Toast.error(message);
                break;
            case 'warning':
                window.Toast.warning(message);
                break;
            default:
                window.Toast.info(message);
        }
    } else {
        console.warn('Toast对象不可用');
    }
}

/**
 * 设置滚动容器
 */
function setupScrollableContent() {
    // 获取滚动容器
    const scrollableContent = document.getElementById('withdrawal-scrollable-content');
    if (!scrollableContent) return;

    // 确保滚动容器有足够的高度
    function adjustScrollableContentHeight() {
        const windowHeight = window.innerHeight;
        const headerHeight = document.querySelector('.header')?.offsetHeight || 0;
        const tabsHeight = document.querySelector('.tab-header')?.offsetHeight || 0;

        // 设置滚动容器的高度，留出一些底部空间
        const scrollableHeight = windowHeight - headerHeight - tabsHeight - 20;
        scrollableContent.style.maxHeight = scrollableHeight + 'px';

        // 添加一些测试内容，确保有足够的内容触发滚动
        // 这只是为了测试，实际使用时可以移除
        const recordsContainer = document.querySelector('.withdrawal-records');
        if (recordsContainer && recordsContainer.children.length < 3) {
            // 如果记录很少，添加一些底部空间确保滚动效果明显
            recordsContainer.style.paddingBottom = '50px';
        }
    }

    // 初始调整
    adjustScrollableContentHeight();

    // 窗口大小改变时重新调整
    window.addEventListener('resize', adjustScrollableContentHeight);
}
