/**
 * 公告弹窗JavaScript
 * 简化版 - 因为现在只在需要显示时才加载此JS
 */

// 全局公告弹窗API
window.noticePopup = {
    // 显示弹窗
    show: function() {
        // 使用jQuery显示弹窗
        $("#noticePopup").show();
        console.log('公告弹窗已显示');
    },
    
    // 隐藏弹窗
    hide: function() {
        // 使用jQuery隐藏弹窗
        $("#noticePopup").hide();
        console.log('公告弹窗已隐藏');
        
        // 直接使用jQuery发送Ajax请求
        $.ajax({
            url: '/index/index/markNoticePopupClosed',
            type: 'GET',
            cache: false,
            dataType: 'json',
            success: function(response) {
                console.log('关闭弹窗请求成功:', response);
            },
            error: function(xhr, status, error) {
                console.error('关闭弹窗请求失败:', status, error);
            }
        });
    }
};

// 使用jQuery的文档就绪函数
$(function() {
    console.log('公告弹窗初始化中...');
    
    // 防止点击弹窗内容区域时关闭弹窗
    $('.notice-popup-content').on('click', function(e) {
        e.stopPropagation();
    });
    
    // 点击弹窗背景关闭弹窗
    $('#noticePopup').on('click', function() {
        window.noticePopup.hide();
    });
    
    // 点击关闭按钮关闭弹窗
    $('#closeNotice').on('click', function(e) {
        e.preventDefault();
        console.log('关闭按钮被点击');
        window.noticePopup.hide();
    });
    
    // 标签页切换
    $('.tab-option').on('click', function() {
        // 移除所有标签的active类
        $('.tab-option').removeClass('active');
        $(this).addClass('active');
        
        // 切换内容显示
        var tabId = $(this).data('tab') + '-tab';
        $('.content-tab').removeClass('active');
        $('#' + tabId).addClass('active');
    });
    
    // 自动显示弹窗
    setTimeout(function() {
        window.noticePopup.show();
    }, 1000);
    
    console.log('公告弹窗初始化完成');
}); 