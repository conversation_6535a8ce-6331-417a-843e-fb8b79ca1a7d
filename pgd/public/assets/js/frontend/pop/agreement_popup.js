/**
 * 用户协议弹窗JavaScript
 */

// 创建全局用户协议弹窗控制API
window.agreementPopup = {
    show: function() {
        var popup = document.getElementById('agreementPopup');
        if (popup) {
            popup.classList.add('active');
            
            
            // 防止点击事件穿透到背景
            popup.onclick = function(event) {
                event.stopPropagation();
            };
        }
    },
    hide: function() {
        var popup = document.getElementById('agreementPopup');
        if (popup) {
            popup.classList.remove('active');
            
        }
    }
};

// 初始化弹窗功能
function initAgreementPopup() {
    
    
    // 关闭按钮事件
    var closeBtn = document.getElementById('agreementClose');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            window.agreementPopup.hide();
        });
    }
    
    // "已阅读并理解"按钮事件
    var acceptBtn = document.getElementById('agreementAcceptBtn');
    if (acceptBtn) {
        acceptBtn.addEventListener('click', function() {
            window.agreementPopup.hide();
        });
    }
    
    // 获取弹窗内容区域，阻止冒泡
    var popupContent = document.querySelector('.agreement-popup-content');
    if (popupContent) {
        popupContent.addEventListener('click', function(event) {
            event.stopPropagation();
        });
    }
}

// 当DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initAgreementPopup);
} else {
    // 如果DOM已经加载完成，立即初始化
    initAgreementPopup();
} 