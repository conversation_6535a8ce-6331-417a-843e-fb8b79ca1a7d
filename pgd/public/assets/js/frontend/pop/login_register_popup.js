/**
 * 登录/注册弹窗 JavaScript
 * 提供登录和注册表单的功能和验证
 */

// 切换密码可见性函数 - 移到全局作用域
function togglePasswordVisibility(inputId) {
    const passwordInput = document.getElementById(inputId);
    if (passwordInput) {
        const type = passwordInput.type === 'password' ? 'text' : 'password';
        passwordInput.type = type;
    }
}

// 打开用户协议弹窗 - 移到全局作用域
function openAgreement() {
    
    // 直接使用全局agreementPopup API来显示用户协议
    if (typeof window.agreementPopup !== 'undefined' && typeof window.agreementPopup.show === 'function') {
        window.agreementPopup.show();
    } else {
        console.error("找不到用户协议弹窗API，请确保agreement_popup.html已被包含在页面中");
        // 尝试强制引入用户协议弹窗
        var agreementElement = document.getElementById('agreementPopup');
        if (!agreementElement) {
            
            // 在当前页面上显示一个临时提示
            // 使用Toast对象
            if (window.Toast && typeof window.Toast.info === 'function') {
                window.Toast.info("Carregando termos de uso...");
            } else {
                console.warn("Toast对象不可用");
            }
            // 刷新页面，让服务器重新加载包含协议弹窗的页面
            window.location.reload();
        }
    }
}

// 切换标签函数 - 移到全局作用域
function switchAuthTab(tab, isclick=false) {
    // 移除所有标签的激活状态
    document.querySelectorAll('.auth-tab').forEach(function(tabElement) {
        tabElement.classList.remove('active');
    });

    // 移除所有表单的激活状态
    document.querySelectorAll('.auth-form').forEach(function(form) {
        form.classList.remove('active');
    });

    // 激活选定的标签和表单
    const tabElement = document.querySelector(`.auth-tab[data-tab="${tab}"]`);
    const formElement = document.getElementById(`${tab}Form`);

    if (tabElement && formElement) {
        tabElement.classList.add('active');
        formElement.classList.add('active');

        // 清空表单字段
        if (tab === 'register') {
            // 清空注册表单
            const registerForm = document.getElementById('register-form');
            if (registerForm) {
                registerForm.reset();
                // 强制清空值
                if (document.getElementById('register-username')) {
                    document.getElementById('register-username').value = '';
                    // 移除autofill样式
                    document.getElementById('register-username').classList.remove('auto-filled');
                }
                if (document.getElementById('register-password')) {
                    document.getElementById('register-password').value = '';
                    document.getElementById('register-password').classList.remove('auto-filled');
                }
                if (document.getElementById('register-mobile')) {
                    document.getElementById('register-mobile').value = '';
                }

                // 重置密码强度指示器
                document.querySelectorAll('.strength-meter-bar').forEach(bar => {
                    bar.classList.remove('active');
                });

                // 隐藏所有错误信息
                document.querySelectorAll('#registerForm .error-message').forEach(error => {
                    error.style.display = 'none';
                });
                document.querySelectorAll('#registerForm .form-group').forEach(group => {
                    group.classList.remove('has-error');
                });
            }
            window.handleInviteCodeGlobal();
            if(isclick){
                window.handleInviteCode();
            }
        } else if (tab === 'login') {
            // 清空登录表单
            const loginForm = document.getElementById('login-form');
            if (loginForm) {
                loginForm.reset();
                // 强制清空值
                if (document.getElementById('login-phone')) {
                    document.getElementById('login-phone').value = '';
                    document.getElementById('login-phone').classList.remove('auto-filled');
                }
                if (document.getElementById('login-password')) {
                    document.getElementById('login-password').value = '';
                    document.getElementById('login-password').classList.remove('auto-filled');
                }

                // 隐藏所有错误信息
                document.querySelectorAll('#loginForm .error-message').forEach(error => {
                    error.style.display = 'none';
                });
                document.querySelectorAll('#loginForm .form-group').forEach(group => {
                    group.classList.remove('has-error');
                });
            }
        }
    } else {
        console.error(`未找到标签或表单元素: ${tab}`);
    }
}

// 确保全局API可用，以防脚本加载延迟
if (typeof window.authPopup === 'undefined') {
    window.authPopup = {
        show: function(tab) {
            
            // 当真正的脚本加载完成后，它会检查DOM中是否有弹窗元素

            // 延迟加载检查
            setTimeout(function() {
                if (typeof window._realAuthPopup !== 'undefined') {
                    window._realAuthPopup.show(tab);
                } else {
                    console.error('登录/注册弹窗脚本未正确加载');
                    // Toast.error(__('login_unavailable'));
                }
            }, 500);
        },
        hide: function() {
            

            // 延迟加载检查
            setTimeout(function() {
                if (typeof window._realAuthPopup !== 'undefined') {
                    window._realAuthPopup.hide();
                }
            }, 100);
        }
    };
}

// 在全局范围内定义handleInviteCodeGlobal函数
window.handleInviteCodeGlobal = function() {
    // 获取URL中的参数
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        var results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }

    // 检查URL中使用的是哪个参数
    function checkParameterInHI() {
        // 检查ic参数
        const icRegex = new RegExp('[\\?&]ic=([^&#]*)');
        const icResults = icRegex.exec(location.search);

        if(icResults !== null) {
            return 'ic';
        }

        // 检查id参数
        const idRegex = new RegExp('[\\?&]id=([^&#]*)');
        const idResults = idRegex.exec(location.search);

        if(idResults !== null) {
            return 'id';
        }

        // 检查ch参数
        const chRegex = new RegExp('[\\?&]ch=([^&#]*)');
        const chResults = chRegex.exec(location.search);

        if(chResults !== null) {
            return 'ch';
        }

        // 从localStorage获取之前保存的参数类型
        var savedParamType = localStorage.getItem('inviteCodeParamType');
        if (savedParamType) {
            return savedParamType;
        }

        // 都不存在
        return null;
    }

    // 检查参数类型
    var paramType = checkParameterInHI();

    // 如果是ic参数，隐藏邀请码输入框
    if (paramType === 'ic') {
        // 处理register-invite-code元素
        var registerInviteCode = document.getElementById('register-invite-code');
        if (registerInviteCode) {
            var formGroup = registerInviteCode.closest('.form-group');
            if (formGroup) {
                formGroup.style.display = 'none';
            }
        }

        // 处理invite_code元素
        var inviteCodeElement = document.getElementById('invite_code');
        if (inviteCodeElement) {
            inviteCodeElement.style.display = 'none';
        }

        // 使用setTimeout确保DOM已加载
        setTimeout(function() {
            // 再次尝试处理register-invite-code元素
            var registerInviteCode = document.getElementById('register-invite-code');
            if (registerInviteCode) {
                var formGroup = registerInviteCode.closest('.form-group');
                if (formGroup) {
                    formGroup.style.display = 'none';
                }
            }

            // 再次尝试处理invite_code元素
            var inviteCodeElement = document.getElementById('invite_code');
            if (inviteCodeElement) {
                inviteCodeElement.style.display = 'none';
            } else {
            }
        }, 500);
    }

    // 检查URL中是否有邀请码参数
    // let inviteCode = getUrlParameter('id');
    // if (inviteCode) {
    //     // localStorage.setItem('inviteCode', inviteCode);
    //     // localStorage.setItem('inviteCodeParamType', paramType); // 保存参数类型
    // } 
};

// 立即执行一次
window.handleInviteCodeGlobal();

// 在DOM内容加载完成后再次执行
document.addEventListener('DOMContentLoaded', function() {
    window.handleInviteCodeGlobal();
});

// 在页面完全加载后再次执行
window.addEventListener('load', function() {
    window.handleInviteCodeGlobal();
});

// 立即执行函数，防止污染全局命名空间
(function() {
    // 显示弹窗函数
    function showAuthPopup(tab = 'register') {
        // 获取弹窗元素
        var popup = document.getElementById('authPopup');
        if (!popup) {
            console.error('找不到authPopup元素!');
            return;
        }

        // 显示弹窗
        popup.classList.add('active');
        // 清空表单字段并重置表单状态
        const registerForm = document.getElementById('register-form');
        if (registerForm) {
            registerForm.reset();

            // 强制清空值并移除autofill样式
            if (document.getElementById('register-username')) {
                document.getElementById('register-username').value = '';
                document.getElementById('register-username').classList.remove('auto-filled');
                // 触发输入事件以强制更新值
                const event = new Event('input', { bubbles: true });
                document.getElementById('register-username').dispatchEvent(event);
            }
            if (document.getElementById('register-password')) {
                document.getElementById('register-password').value = '';
                document.getElementById('register-password').classList.remove('auto-filled');
                const event = new Event('input', { bubbles: true });
                document.getElementById('register-password').dispatchEvent(event);
            }
            if (document.getElementById('register-mobile')) {
                document.getElementById('register-mobile').value = '';
            }

            // 重置密码强度指示器
            document.querySelectorAll('.strength-meter-bar').forEach(bar => {
                bar.classList.remove('active');
            });

            // 隐藏所有错误信息
            document.querySelectorAll('#registerForm .error-message').forEach(error => {
                error.style.display = 'none';
            });
            document.querySelectorAll('#registerForm .form-group').forEach(group => {
                group.classList.remove('has-error');
            });
        }

        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.reset();

            // 强制清空值并移除autofill样式
            if (document.getElementById('login-phone')) {
                document.getElementById('login-phone').value = '';
                document.getElementById('login-phone').classList.remove('auto-filled');
                const event = new Event('input', { bubbles: true });
                document.getElementById('login-phone').dispatchEvent(event);
            }
            if (document.getElementById('login-password')) {
                document.getElementById('login-password').value = '';
                document.getElementById('login-password').classList.remove('auto-filled');
                const event = new Event('input', { bubbles: true });
                document.getElementById('login-password').dispatchEvent(event);
            }

            // 隐藏所有错误信息
            document.querySelectorAll('#loginForm .error-message').forEach(error => {
                error.style.display = 'none';
            });
            document.querySelectorAll('#loginForm .form-group').forEach(group => {
                group.classList.remove('has-error');
            });
        }

        // 激活指定标签
        switchAuthTab(tab);

        // 处理邀请码填充
        try {
            handleInviteCode();
        } catch (error) {
            console.error('showAuthPopup中调用handleInviteCode时出错:', error);
            // 如果出错，延迟再试一次
            setTimeout(function() {
                try {
                    handleInviteCode();
                } catch (retryError) {
                    console.error('showAuthPopup中再次调用handleInviteCode时仍然出错:', retryError);
                }
            }, 500);
        }
    }

    // 隐藏弹窗函数
    function hideAuthPopup() {
        // 获取弹窗元素
        var popup = document.getElementById('authPopup');
        if (!popup) {
            console.error('找不到authPopup元素!');
            return;
        }

        // 隐藏弹窗
        popup.classList.remove('active');
    }
    function checkParameter() {
        // 获取URL中的查询字符串
        const queryString = window.location.search;

        // 创建URLSearchParams对象
        const urlParams = new URLSearchParams(queryString);

        // 检查是否存在ic参数
        if(urlParams.has('ic')) {
            return 'ic';
        }

        // 检查是否存在id参数
        if(urlParams.has('id')) {
            return 'id';
        }

        // 从localStorage获取之前保存的参数类型
        var savedParamType = localStorage.getItem('inviteCodeParamType');
        if (savedParamType) {
            return savedParamType;
        }
        return null;
    }
     // 获取URL中的参数
     function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        var results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }
    // 处理邀请码自动填充
    function handleInviteCode() {
        // 检查参数类型
        var paramType = checkParameter();

        // 尝试获取不同的邀请码参数（ic, id）
        // let inviteCode = getUrlParameter('id');

        // 处理邀请码输入框
        var inviteCodeInput = document.getElementById('register-invite-code');

        // 处理邀请码字段组
        var inviteCodeGroup = inviteCodeInput ? inviteCodeInput.closest('.form-group') : null;
        var isHideInviteCode = paramType != null;
        // 如果是ic参数，隐藏邀请码输入框
        if (isHideInviteCode) {
            // 处理register-invite-code元素的form-group
            if (inviteCodeGroup) {
                inviteCodeGroup.style.display = 'none';
            }

            // 同时处理invite_code元素（如果存在）
            var inviteCodeElement = document.getElementById('invite_code');
            if (inviteCodeElement) {
                inviteCodeElement.style.display = 'none';
            }

            // 调用全局函数确保处理
            // if (typeof window.handleInviteCodeGlobal === 'function') {
            //     window.handleInviteCodeGlobal();
            // }
        } else {
            // 确保邀请码字段组可见（如果不是ic参数）
            if (inviteCodeGroup) {
                inviteCodeGroup.style.display = '';
            }
        }

        // if (inviteCode) {
        //     if (inviteCodeInput) {
        //         inviteCodeInput.value = inviteCode;
        //         // 添加事件，防止用户清空邀请码
        //         inviteCodeInput.addEventListener('input', function() {
        //             if (!this.value) {
        //                 this.value = inviteCode;
        //             }
        //         });
        //     } else {
        //         console.error('找不到邀请码输入框元素!');
        //         // 如果元素不存在，可能是因为DOM还未完全加载，设置一个延迟再试
        //         // setTimeout(function() {
        //         //     var delayedInput = document.getElementById('register-invite-code');
        //         //     // 如果是ic参数，隐藏邀请码输入框
        //         //     if (paramType === 'ic') {
        //         //         var delayedGroup = delayedInput.closest('.form-group');
        //         //         if (delayedGroup) {
        //         //             delayedGroup.style.display = 'none';
        //         //         }
        //         //     }
        //         // }, 500);
        //     }
        // } else {

        //     // setTimeout(function() {
        //     //     var delayedInput = document.getElementById('register-invite-code');
        //     //     // 如果是ic参数，隐藏邀请码输入框
        //     //     if (paramType === 'ic') {
        //     //         var delayedGroup = delayedInput.closest('.form-group');
        //     //         if (delayedGroup) {
        //     //             delayedGroup.style.display = 'none';
        //     //         }
        //     //     }
        //     // }, 500);
        // }
    }

    // 验证用户名：4-16个字符，只支持英文字母和数字
    function validateUsername(username) {
        const pattern = /^[a-zA-Z0-9]{4,16}$/;
        return pattern.test(username);
    }

    // 验证密码：6-16个字符，支持英文字母、数字和符号
    function validatePassword(password) {
        const pattern = /^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{6,16}$/;
        return pattern.test(password);
    }

    // 验证巴西手机号
    function validateBrazilianMobile(mobile) {
        // 验证巴西手机号：两位区号+9位手机号，可能以9开头
        // 巴西手机号格式: (XX) XXXXX-XXXX 或 XX9XXXXXXXX
        mobile = mobile.replace(/\D/g, ''); // 移除非数字字符
        if (mobile.length === 10 || mobile.length === 11) {
            // 10位数字(不含9) 或 11位数字(含9)
            return true;
        }
        return false;
    }

    // 显示错误信息
    function showError(inputId, errorId) {
        const inputElement = document.getElementById(inputId);
        const errorElement = document.getElementById(errorId);

        if (!inputElement || !errorElement) {
            console.error(`未找到元素: ${inputId} 或 ${errorId}`);
            return;
        }

        const formGroup = inputElement.closest('.form-group');
        if (formGroup) {
            formGroup.classList.add('has-error');
            errorElement.style.display = 'flex';
        }
    }

    // 隐藏错误信息
    function hideError(inputId, errorId) {
        const inputElement = document.getElementById(inputId);
        const errorElement = document.getElementById(errorId);

        if (!inputElement || !errorElement) {
            console.error(`未找到元素: ${inputId} 或 ${errorId}`);
            return;
        }

        const formGroup = inputElement.closest('.form-group');
        if (formGroup) {
            formGroup.classList.remove('has-error');
            errorElement.style.display = 'none';
        }
    }

    // 初始化函数
    function initAuthPopup() {
        // 获取弹窗元素
        var popup = document.getElementById('authPopup');
        if (!popup) {
            console.error('找不到弹窗元素!');
            return;
        }

        // 关闭按钮事件
        var closeBtn = document.getElementById('closeAuthPopup');
        if (!closeBtn) {
            console.error('找不到关闭按钮!');
        } else {
            closeBtn.addEventListener('click', function() {
                hideAuthPopup();
            });
        }

        // 标签切换事件
        document.querySelectorAll('.auth-tab').forEach(function(tab) {
            tab.addEventListener('click', function() {
                switchAuthTab(this.dataset.tab, true);
            });
        });

        // 添加密码强度检测
        const passwordInput = document.getElementById('register-password');
        if (passwordInput) {
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strengthBars = document.querySelectorAll('.strength-meter-bar');
                let strength = 0;

                // 长度检查
                if(password.length >= 6) strength += 1;
                // 字母检查
                if(/[a-zA-Z]/.test(password)) strength += 1;
                // 数字检查
                if(/\d/.test(password)) strength += 1;
                // 特殊字符检查
                if(/[^a-zA-Z0-9]/.test(password)) strength += 1;

                // 更新强度指示器
                strengthBars.forEach((bar, index) => {
                    if(index < strength) {
                        bar.classList.add('active');
                    } else {
                        bar.classList.remove('active');
                    }
                });
            });
        }

        // 注册表单验证
        const registerForm = document.getElementById('register-form');
        if (registerForm) {
            registerForm.addEventListener('submit', function(e) {
                e.preventDefault();
                let isValid = true;

                // 验证用户名
                const username = document.getElementById('register-username').value;
                if (!validateUsername(username)) {
                    // 使用Toast对象
                    if (window.Toast && typeof window.Toast.error === 'function') {
                        window.Toast.error('Nome de usuário inválido. Use 4-16 caracteres alfanuméricos.');
                    } else {
                        console.warn("Nome de usuário inválido. Use 4-16 caracteres alfanuméricos.'");
                    }
                    showError('register-username', 'username-error');
                    isValid = false;
                } else {
                    hideError('register-username', 'username-error');
                }

                // 验证手机号
                const mobile = document.getElementById('register-mobile').value;
                if (!validateBrazilianMobile(mobile)) {
                    // 使用Toast对象
                    if (window.Toast && typeof window.Toast.error === 'function') {
                        window.Toast.error('Número de celular incorreto ou inexistente');
                    } else {
                        console.warn("Número de celular incorreto ou inexistente");
                    }
                    showError('register-mobile', 'mobile-error');
                    isValid = false;
                } else {
                    hideError('register-mobile', 'mobile-error');
                }

                // 验证密码
                const password = document.getElementById('register-password').value;
                if (!validatePassword(password)) {
                    // 使用Toast对象
                    if (window.Toast && typeof window.Toast.error === 'function') {
                        window.Toast.error('Senha inválida. Use 6-16 caracteres alfanuméricos ou símbolos.');
                    } else {
                        console.warn("Senha inválida. Use 6-16 caracteres alfanuméricos ou símbolos.");
                    }
                    showError('register-password', 'password-error');
                    isValid = false;
                } else {
                    hideError('register-password', 'password-error');
                }

                // 验证协议勾选
                const agree = document.getElementById('register-agree').checked;
                if (!agree) {
                    document.getElementById('agree-error').style.display = 'flex';
                    // 使用Toast对象
                    if (window.Toast && typeof window.Toast.error === 'function') {
                        window.Toast.error('Por favor, aceite os termos e condições');
                    } else {
                        console.warn("Por favor, aceite os termos e condições");
                    }
                    isValid = false;
                } else {
                    document.getElementById('agree-error').style.display = 'none';
                }

                // 验证邀请码
                const inviteCode = document.getElementById('register-invite-code').value;
                if (inviteCode) {
                    hideError('register-invite-code', 'invite-code-error');
                } 

                if (isValid) {

                    // 获取CSRF令牌
                    const token = document.querySelector('meta[name="__token__"]') ?
                                 document.querySelector('meta[name="__token__"]').content : '';
                    // 调用后端API进行注册
                    fetch('/index/index/playerRegister', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: new URLSearchParams({
                            username: username,
                            password: password,
                            mobile: mobile,
                            invite_code: inviteCode,
                            // ic_id:icId,
                            agree: agree ? 1 : 0,
                            __token__: token,
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 1) {
                            // 注册成功
                            if (window.Toast && typeof window.Toast.success === 'function') {
                                window.Toast.success(data.msg);
                            } else {
                                console.warn(data.msg);
                            }

                            // 自动登录
                            fetch('/index/index/playerLogin', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/x-www-form-urlencoded',
                                    'X-Requested-With': 'XMLHttpRequest'
                                },
                                body: new URLSearchParams({
                                    phone_number: mobile,
                                    password: password
                                })
                            })
                            .then(() => {
                                hideAuthPopup();
                                // 刷新页面显示登录状态
                                setTimeout(() => {
                                    window.location.reload();
                                }, 500);

                                // updateLoginStatusUI();
                                // refreshUserSessionData();
                            });
                        } else {
                            // 注册失败
                            console.error('注册失败:', data.msg);

                            // 显示具体错误信息
                            if (data.msg) {
                                // 直接显示后端返回的错误信息
                                if (window.Toast && typeof window.Toast.error === 'function') {
                                    window.Toast.error(data.msg);
                                } else {
                                    console.warn(data.msg);
                                }

                                // 根据错误信息类型显示在对应字段
                                if (data.msg.includes('username') || data.msg.includes('用户名')) {
                                    showError('register-username', 'username-error');
                                } else if (data.msg.includes('mobile') || data.msg.includes('手机')) {
                                    showError('register-mobile', 'mobile-error');
                                } 
                                // else if (data.msg.includes('invite') || data.msg.includes('邀请码')) {
                                //     showError('register-invite-code', 'invite-code-error');
                                // }
                            } else {
                                // 显示通用错误信息
                                if (window.Toast && typeof window.Toast.error === 'function') {
                                    window.Toast.error('Falha no registro, verifique suas informações!');
                                } else {
                                    console.warn("Falha no registro, verifique suas informações!");
                                }
                            }
                        }
                    })
                    .catch(error => {
                        console.error('注册请求出错:', error);
                        if (window.Toast && typeof window.Toast.error === 'function') {
                            window.Toast.error('Falha na solicitação de registro, tente novamente mais tarde');
                        } else {
                            console.warn('Falha na solicitação de registro, tente novamente mais tarde');
                        }
                    });
                } else {
                }
            });
        }

        // 登录表单验证
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                let isValid = true;

                // 验证手机号
                const phone_number = document.getElementById('login-phone').value;
                if (!phone_number) {
                    // 使用Toast对象
                    if (window.Toast && typeof window.Toast.error === 'function') {
                        window.Toast.error('Por favor, insira o número de celular');
                    } else {
                        console.warn('Por favor, insira o número de celular');
                    }
                    // showError('login-phone', 'login-phone-error');
                    isValid = false;
                } else{
                    hideError('login-phone', 'login-phone-error');
                }

                // 验证密码
                const password = document.getElementById('login-password').value;
                if (!password) {
                    // 使用Toast对象
                    if (window.Toast && typeof window.Toast.error === 'function') {
                        window.Toast.error('Por favor, insira a senha');
                    } else {
                        console.warn('Por favor, insira a senha');
                    }
                    showError('login-password', 'login-password-error');
                    isValid = false;
                } else {
                    hideError('login-password', 'login-password-error');
                }

                if (isValid) {
                    // TODO: 这里添加实际提交登录表单的代码

                    // 获取CSRF令牌
                    const token = document.querySelector('meta[name="__token__"]') ?
                                 document.querySelector('meta[name="__token__"]').content : '';

                    // 提交登录请求
                    fetch('/index/index/playerLogin', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: new URLSearchParams({
                            phone_number: phone_number,
                            password: password,
                            __token__: token // 添加CSRF令牌
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 1) {
                            hideAuthPopup();
                            // 刷新页面显示登录状态
                            setTimeout(() => {
                                window.location.reload();
                            }, 500);
                            // 登录成功
                            // updateLoginStatusUI();
                            // refreshUserSessionData();
                        } else {
                            // 登录失败
                            console.error('登录失败:', data.msg);
                            if (window.Toast && typeof window.Toast.error === 'function') {
                                window.Toast.error(data.msg);
                            } else {
                                console.warn(data.msg);
                            }
                        }
                    })
                    .catch(error => {
                        console.error('登录请求出错:', error);
                        if (window.Toast && typeof window.Toast.error === 'function') {
                            window.Toast.error('Login request failed, please try again later');
                        } else {
                            console.warn('Login request failed, please try again later');
                        }
                    });
                } else {
                    
                }
            });
        }

        // 输入框实时验证
        const usernameInput = document.getElementById('register-username');
        if (usernameInput) {
            usernameInput.addEventListener('blur', function() {
                const username = this.value;
                if (username && !validateUsername(username)) {
                    showError('register-username', 'username-error');
                } else {
                    hideError('register-username', 'username-error');
                }
            });
        }

        const passwordInput2 = document.getElementById('register-password');
        if (passwordInput2) {
            passwordInput2.addEventListener('blur', function() {
                const password = this.value;
                if (password && !validatePassword(password)) {
                    showError('register-password', 'password-error');
                } else {
                    hideError('register-password', 'password-error');
                }
            });
        }

        // 邀请码输入框失去焦点时验证
        const inviteCodeInput = document.getElementById('register-invite-code');
        if (inviteCodeInput) {
            inviteCodeInput.addEventListener('blur', function() {
                const inviteCode = this.value;
                if (!inviteCode) {
                    showError('register-invite-code', 'invite-code-error');
                } else {
                    hideError('register-invite-code', 'invite-code-error');
                }
            });
        }
    }

    // 创建真正的API对象
    var realAuthPopup = {
        show: showAuthPopup,
        hide: hideAuthPopup,
        init: initAuthPopup
    };

    /**
     * 更新前端登录状态UI
     */
    function updateLoginStatusUI() {
        // 隐藏登录/注册按钮
        const loggedOutElements = document.querySelectorAll('.logged-out');
        loggedOutElements.forEach(el => el.style.display = 'none');
        
        // 显示已登录元素（包括余额信息）
        const loggedInElements = document.querySelectorAll('.logged-in');
        loggedInElements.forEach(el => el.style.display = 'flex');
        
        // 关闭登录弹窗
        if (window.authPopup) {
            window.authPopup.hide();
        }
    }

    /**
     * 刷新用户会话数据和余额
     */
    function refreshUserSessionData() {
        // 发送请求到服务器检查登录状态并获取最新数据
        fetch('/index/index/checkLoginStatus', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 1 && data.data.isLoggedIn) {
                // 更新余额显示
                const balanceElements = document.querySelectorAll('.balance-amount');
                balanceElements.forEach(el => {
                    el.textContent = data.data.balance || '0.00';
                });
                
                // 更新用户名显示
                const usernameElements = document.querySelectorAll('.username-display');
                if (usernameElements.length > 0 && data.data.username) {
                    usernameElements.forEach(el => {
                        el.textContent = data.data.username;
                    });
                }
            }
        })
        .catch(error => {
            console.error('检查登录状态出错:', error);
        });
    }

    // 暴露公共API
    window.authPopup = {
        show: function(tab) {
            showAuthPopup(tab);
        },
        hide: function() {
            hideAuthPopup();
        },
        init: initAuthPopup
    };

    // 保存真正的API，以防临时API需要使用
    window._realAuthPopup = realAuthPopup;

    // 暴露打开用户协议弹窗函数
    window.openAgreement = openAgreement;

    // 暴露切换标签函数给全局
    window.switchAuthTab = switchAuthTab;

    window.handleInviteCode = handleInviteCode;

    // 文档加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAuthPopup);
    } else {
        // 如果DOM已经加载完成，立即初始化
        initAuthPopup();
    }

    // 检查脚本是否正确加载
    document.addEventListener('DOMContentLoaded', function() {
        // 确保邀请码处理函数被调用
        try {
            handleInviteCode();
        } catch (error) {
            console.error('调用handleInviteCode时出错:', error);
            // 如果出错，延迟再试一次
            setTimeout(function() {
                try {
                    handleInviteCode();
                } catch (retryError) {
                    console.error('再次调用handleInviteCode时仍然出错:', retryError);
                }
            }, 500);
        }

        // 确保在页面完全加载后也执行一次
        window.addEventListener('load', function() {
            try {
                handleInviteCode();
            } catch (error) {
                console.error('load事件中调用handleInviteCode时出错:', error);
            }
        });
    });
})();
