// 活动页面通用JS

document.addEventListener('DOMContentLoaded', function() {
    // 动态设置活动卡片图片高度
    initEventCardImages();
    
    // 初始化标签切换功能（如果没有在页面中直接定义）
    if (typeof initTabsWithLoginCheck !== 'function') {
        initTabs();
    }
});

/**
 * 初始化活动卡片图片
 */
function initEventCardImages() {
    // 获取所有活动卡片图片容器
    const eventImages = document.querySelectorAll('.event-image');
    
    // 为每个图片容器设置高度
    eventImages.forEach(function(imageContainer) {
        // 获取背景图片URL
        const bgImageUrl = getComputedStyle(imageContainer).backgroundImage.replace(/url\(['"]?(.*?)['"]?\)/i, '$1');
        
        if (bgImageUrl && bgImageUrl !== 'none') {
            // 创建一个临时图片对象来获取图片的原始尺寸
            const img = new Image();
            img.src = bgImageUrl;
            
            img.onload = function() {
                // 获取图片的原始宽高比
                const aspectRatio = this.height / this.width;
                
                // 获取容器的当前宽度
                const containerWidth = imageContainer.offsetWidth;
                
                // 根据宽高比计算高度
                const containerHeight = containerWidth * aspectRatio;
                
                // 设置容器高度
                imageContainer.style.paddingBottom = (aspectRatio * 100) + '%';
            };
        }
    });
}

/**
 * 初始化标签切换功能（不包含登录检查）
 */
function initTabs() {
    const tabs = document.querySelectorAll('.horizontal-tabs .tab');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');
            
            // 如果是当前活动标签，不做任何操作
            if (this.classList.contains('active')) {
                return;
            }
            
            // 跳转到相应页面
            const tabMapping = {
                'eventos': '/index/event/index',
                'missao': '/index/event/mission',
                'vip': '/index/event/vip',
                'rebate': '/index/event/rebate',
                'pendente': '/index/event/pendente',
                'history': '/index/event/history'
            };
            
            if (tabMapping[tabId]) {
                window.location.href = tabMapping[tabId];
            }
        });
    });
}

/**
 * 切换到指定标签（供外部调用）
 */
function switchTab(tabId) {
    const tabMapping = {
        'eventos': '/index/event/index',
        'missao': '/index/event/mission',
        'vip': '/index/event/vip',
        'rebate': '/index/event/rebate',
        'pendente': '/index/event/pendente',
        'history': '/index/event/history'
    };
    
    if (tabMapping[tabId]) {
        window.location.href = tabMapping[tabId];
    }
}
