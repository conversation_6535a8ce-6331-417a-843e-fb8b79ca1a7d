/**
 * 侧边菜单功能
 * 处理侧边菜单的展开、收起以及交互
 */
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const menuBtn = document.querySelector('.menu-btn');
    const sideMenu = document.querySelector('.side-menu');
    const overlay = document.querySelector('.side-menu-overlay');
    
    // 音乐播放器相关元素
    const miniPlayer = document.querySelector('.mini-player');
    const musicPlayerModal = document.querySelector('.music-player-modal');
    
    /**
     * 初始化侧边菜单事件监听
     */
    function initSideMenu() {
        // 菜单按钮点击事件
        if (menuBtn) {
            menuBtn.addEventListener('click', toggleSideMenu);
        }
        
        // 遮罩层点击事件 - 关闭菜单，但保持音乐播放器状态
        if (overlay) {
            overlay.addEventListener('click', function(e) {
                // 如果音乐播放器模态框是打开的，点击遮罩层不关闭侧边菜单
                if (musicPlayerModal && musicPlayerModal.style.display === 'block') {
                    e.stopPropagation();
                    return;
                }
                closeSideMenu();
            });
        }
        
        // 如果URL里有特定参数，自动打开侧边菜单
        if (getUrlParameter('menu') === 'open') {
            openSideMenu();
        }

        // 初始化音乐播放器相关事件
        initMusicPlayerEvents();
    }
    
    /**
     * 初始化音乐播放器相关事件
     */
    function initMusicPlayerEvents() {
        // 点击全屏按钮时，即使关闭侧边菜单也保持音乐播放
        const fullscreenBtn = document.querySelector('.mini-player-fullscreen');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡到侧边菜单
                
                // 打开全屏音乐播放器
                if (musicPlayerModal) {
                    musicPlayerModal.style.display = 'block';
                    document.body.style.overflow = 'hidden'; // 禁止滚动
                }
            });
        }
        
        // 关闭全屏音乐播放器按钮
        const closeModalBtn = document.querySelector('.close-music-modal');
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', function() {
                if (musicPlayerModal) {
                    musicPlayerModal.style.display = 'none';
                    
                    // 如果侧边菜单已关闭，恢复滚动
                    if (!sideMenu.classList.contains('active')) {
                        document.body.style.overflow = '';
                    }
                }
            });
        }
    }
    
    /**
     * 切换侧边菜单状态
     */
    function toggleSideMenu() {
        sideMenu.classList.toggle('active');
        overlay.classList.toggle('active');
        
        // 添加禁止滚动
        if (sideMenu.classList.contains('active')) {
            document.body.style.overflow = 'hidden';
        } else {
            // 只有当音乐播放器模态框也关闭时，才恢复滚动
            if (!musicPlayerModal || musicPlayerModal.style.display !== 'block') {
                document.body.style.overflow = '';
            }
        }
    }
    
    /**
     * 打开侧边菜单
     */
    function openSideMenu() {
        sideMenu.classList.add('active');
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    /**
     * 关闭侧边菜单
     */
    function closeSideMenu() {
        sideMenu.classList.remove('active');
        
        // 只有当音乐播放器模态框未打开时，才移除遮罩层和恢复滚动
        if (!musicPlayerModal || musicPlayerModal.style.display !== 'block') {
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }
    }
    
    /**
     * 从URL获取参数值
     * @param {string} name - 参数名
     * @returns {string|null} 参数值或null
     */
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        const results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }
    
    // 初始化侧边菜单
    initSideMenu();
    
    // 暴露公共方法
    window.SideMenu = {
        open: openSideMenu,
        close: closeSideMenu,
        toggle: toggleSideMenu
    };
}); 