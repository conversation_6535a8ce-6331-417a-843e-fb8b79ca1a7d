// 音乐播放器模块
(function() {
  // 加载外部歌曲列表
  // 确保在此脚本前已加载songs.js

  // 播放器状态
  const playerState = {
    playlist: [], // 播放列表
    currentIndex: 0, // 当前播放歌曲索引
    isPlaying: false, // 是否正在播放
    isShuffle: false, // 是否随机播放
    isRepeat: false, // 是否循环播放
    volume: 0.7, // 音量
    duration: 0, // 当前歌曲总时长
    currentTime: 0 // 当前播放时间
  };

  // DOM 元素
  let audioElement, miniPlayerEl, fullPlayerEl;
  let miniPlayerPlayBtn, fullPlayerPlayBtn;
  let progressBarEl, progressHandleEl, progressBarFillEl;
  let volumeBarEl, volumeHandleEl, volumeBarFillEl;
  let currentTimeEl, durationEl;
  let currentSongTitleEl, currentSongArtistEl;
  let songTitleEl, songCountEl;
  let playerArtworkEl, miniArtworkEl;
  let playlistEl;

  // 初始化播放器
  function initPlayer() {
    // 创建音频元素
    if (!audioElement) {
      audioElement = new Audio();

      // 音频事件监听
      audioElement.addEventListener('timeupdate', updateProgress);
      audioElement.addEventListener('ended', handleSongEnd);
      audioElement.addEventListener('loadedmetadata', () => {
        playerState.duration = audioElement.duration;
        updateDurationDisplay();
      });
      audioElement.addEventListener('error', (e) => {
        console.error('音频加载错误:', e);
        showToast('歌曲加载失败，请稍后再试');
      });
    }

    // 获取DOM元素
    miniPlayerEl = document.querySelector('.mini-player');
    fullPlayerEl = document.querySelector('.music-player-modal');

    if (!miniPlayerEl || !fullPlayerEl) {
      console.error('找不到播放器元素，请确保HTML结构正确');
      return;
    }

    // 获取播放控制相关元素
    miniPlayerPlayBtn = miniPlayerEl.querySelector('.mini-player-play');
    fullPlayerPlayBtn = fullPlayerEl.querySelector('.player-play-pause');

    // 进度条元素
    progressBarEl = fullPlayerEl.querySelector('.progress-bar');
    progressHandleEl = fullPlayerEl.querySelector('.progress-handle');
    progressBarFillEl = fullPlayerEl.querySelector('.progress-bar-fill');

    // 音量控制元素
    volumeBarEl = fullPlayerEl.querySelector('.volume-bar');
    volumeHandleEl = fullPlayerEl.querySelector('.volume-handle');
    volumeBarFillEl = fullPlayerEl.querySelector('.volume-bar-fill');

    // 时间显示元素
    currentTimeEl = fullPlayerEl.querySelector('.current-time');
    durationEl = fullPlayerEl.querySelector('.duration');

    // 歌曲信息元素
    currentSongTitleEl = fullPlayerEl.querySelector('.current-song-title');
    currentSongArtistEl = fullPlayerEl.querySelector('.current-song-artist');
    songTitleEl = miniPlayerEl.querySelector('.song-title');
    songCountEl = miniPlayerEl.querySelector('.song-count');

    // 封面元素
    playerArtworkEl = fullPlayerEl.querySelector('.player-artwork img');
    miniArtworkEl = miniPlayerEl.querySelector('.mini-player-artwork img');

    // 播放列表元素
    playlistEl = fullPlayerEl.querySelector('.player-playlist');

    // 绑定播放器事件
    bindEvents();

    // 加载播放列表
    loadPlaylist();
  }

  // 绑定播放器事件
  function bindEvents() {
    // 在绑定事件前确保元素存在
    if (!miniPlayerEl || !fullPlayerEl) {
      console.error('播放器元素不存在，无法绑定事件');
      return;
    }

    // 迷你播放器控制 - 获取元素并检查是否存在
    const miniPlay = miniPlayerEl.querySelector('.mini-player-play');
    const miniPrev = miniPlayerEl.querySelector('.mini-player-prev');
    const miniNext = miniPlayerEl.querySelector('.mini-player-next');
    const miniFullscreen = miniPlayerEl.querySelector('.mini-player-fullscreen');
    const miniInfo = miniPlayerEl.querySelector('.mini-player-info');

    // 只有在元素存在时才添加事件监听器
    if (miniPlay) miniPlay.addEventListener('click', togglePlay);
    if (miniPrev) miniPrev.addEventListener('click', playPrevious);
    if (miniNext) miniNext.addEventListener('click', playNext);
    if (miniFullscreen) miniFullscreen.addEventListener('click', openFullPlayer);
    if (miniInfo) miniInfo.addEventListener('click', openFullPlayer);

    // 全屏播放器控制 - 获取元素并检查是否存在
    const fullPlay = fullPlayerEl.querySelector('.player-play-pause');
    const fullPrev = fullPlayerEl.querySelector('.player-prev');
    const fullNext = fullPlayerEl.querySelector('.player-next');
    const closeBtn = fullPlayerEl.querySelector('.close-music-modal');
    const shuffleBtn = fullPlayerEl.querySelector('.player-shuffle');
    const repeatBtn = fullPlayerEl.querySelector('.player-repeat');

    // 只有在元素存在时才添加事件监听器
    if (fullPlay) fullPlay.addEventListener('click', togglePlay);
    if (fullPrev) fullPrev.addEventListener('click', playPrevious);
    if (fullNext) fullNext.addEventListener('click', playNext);
    if (closeBtn) closeBtn.addEventListener('click', closeFullPlayer);
    if (shuffleBtn) shuffleBtn.addEventListener('click', toggleShuffle);
    if (repeatBtn) repeatBtn.addEventListener('click', toggleRepeat);

    // 进度条控制
    if (progressBarEl) progressBarEl.addEventListener('click', seek);

    // 音量控制
    if (volumeBarEl) volumeBarEl.addEventListener('click', setVolume);

    // 拖动进度和音量
    if (progressHandleEl && volumeHandleEl) {
      setupDragHandlers();
    } else {
      
    }
  }

  // 设置拖动处理
  function setupDragHandlers() {
    // 在设置拖动处理前确保元素存在
    if (!progressHandleEl || !progressBarEl || !volumeHandleEl || !volumeBarEl) {
      console.error('进度条或音量控制元素不存在，无法设置拖动处理');
      return;
    }

    // 进度条拖动
    let isDraggingProgress = false;

    progressHandleEl.addEventListener('mousedown', () => {
      isDraggingProgress = true;
      document.body.style.userSelect = 'none';
    });

    document.addEventListener('mousemove', (e) => {
      if (isDraggingProgress) {
        const rect = progressBarEl.getBoundingClientRect();
        let percent = (e.clientX - rect.left) / rect.width;
        percent = Math.max(0, Math.min(1, percent));

        updateProgressUI(percent);

        // 更新播放时间
        playerState.currentTime = percent * playerState.duration;
        updateTimeDisplay();
      }
    });

    document.addEventListener('mouseup', () => {
      if (isDraggingProgress) {
        isDraggingProgress = false;
        document.body.style.userSelect = '';

        // 设置音频播放位置
        audioElement.currentTime = playerState.currentTime;
      }
    });

    // 音量拖动
    let isDraggingVolume = false;

    volumeHandleEl.addEventListener('mousedown', () => {
      isDraggingVolume = true;
      document.body.style.userSelect = 'none';
    });

    document.addEventListener('mousemove', (e) => {
      if (isDraggingVolume) {
        const rect = volumeBarEl.getBoundingClientRect();
        let percent = (e.clientX - rect.left) / rect.width;
        percent = Math.max(0, Math.min(1, percent));

        updateVolumeUI(percent);

        // 设置音量
        playerState.volume = percent;
        audioElement.volume = percent;
      }
    });

    document.addEventListener('mouseup', () => {
      if (isDraggingVolume) {
        isDraggingVolume = false;
        document.body.style.userSelect = '';
      }
    });
  }

  // 加载播放列表
  function loadPlaylist() {
    // 从外部导入的songs.js中获取播放列表
    if (window.songsList && Array.isArray(window.songsList)) {
      playerState.playlist = window.songsList;
    } else {
      console.error('无法加载歌曲列表，请确保songs.js已正确加载');
      // 防止出错，提供默认歌曲
      playerState.playlist = [{
        id: 1,
        title: '加载失败',
        artist: '未知艺术家',
        cover: '/assets/img/frontend/common/music/default.jpg',
        url: '',
        duration: '0:00'
      }];
    }

    // 更新迷你播放器信息
    updateMiniPlayerInfo();

    // 渲染播放列表
    renderPlaylist();

    // 如果有歌曲，设置第一首
    if (playerState.playlist.length > 0) {
      setCurrentSong(0);
    }
  }

  // 渲染播放列表
  function renderPlaylist() {
    if (!playlistEl) return;

    playlistEl.innerHTML = '';

    playerState.playlist.forEach((song, index) => {
      const songItemEl = document.createElement('div');
      songItemEl.className = `song-item ${index === playerState.currentIndex ? 'active' : ''}`;
      songItemEl.dataset.index = index;

      songItemEl.innerHTML = `
        <div class="song-item-number">${index + 1}</div>
        <div class="song-item-info">
          <div class="song-item-title">${song.title}</div>
          <div class="song-item-artist">${song.artist}</div>
        </div>
        <div class="song-item-duration">${song.duration}</div>
      `;

      songItemEl.addEventListener('click', () => {
        if (index !== playerState.currentIndex) {
          setCurrentSong(index);
          if (playerState.isPlaying) {
            playAudio();
          }
        }
      });

      playlistEl.appendChild(songItemEl);
    });
  }

  // 设置当前歌曲
  function setCurrentSong(index) {
    if (index < 0 || index >= playerState.playlist.length) return;

    playerState.currentIndex = index;
    const song = playerState.playlist[index];

    // 更新音频源
    if (audioElement.src !== song.url) {
      audioElement.src = song.url;
      audioElement.load();
    }

    // 更新全屏播放器信息
    if (currentSongTitleEl) currentSongTitleEl.textContent = song.title;
    if (currentSongArtistEl) currentSongArtistEl.textContent = song.artist;
    if (playerArtworkEl) playerArtworkEl.src = song.cover;

    // 更新迷你播放器信息
    if (songTitleEl) songTitleEl.textContent = song.title;
    if (songCountEl) songCountEl.textContent = `${index + 1}/${playerState.playlist.length}`;
    if (miniArtworkEl) miniArtworkEl.src = song.cover;

    // 重置进度
    playerState.currentTime = 0;
    updateProgressUI(0);
    updateTimeDisplay();

    // 更新播放列表高亮
    updatePlaylistActiveItem();
  }

  // 更新播放列表活动项
  function updatePlaylistActiveItem() {
    if (!playlistEl) return;

    const songItems = playlistEl.querySelectorAll('.song-item');
    songItems.forEach((item, index) => {
      if (index === playerState.currentIndex) {
        item.classList.add('active');
      } else {
        item.classList.remove('active');
      }
    });
  }

  // 更新迷你播放器信息
  function updateMiniPlayerInfo() {
    if (playerState.playlist.length === 0) return;

    if (songCountEl) {
      songCountEl.textContent = `${playerState.currentIndex + 1}/${playerState.playlist.length}`;
    }
  }

  // 播放音频
  function playAudio() {
    if (!audioElement.src) return;

    audioElement.play().then(() => {
      playerState.isPlaying = true;
      updatePlayButtons();

      // 更新迷你播放器状态
      if (miniPlayerEl) {
        miniPlayerEl.classList.add('playing');
      }
    }).catch(error => {
      console.error('播放失败:', error);
      showToast('播放失败，请稍后再试');
    });
  }

  // 暂停音频
  function pauseAudio() {
    audioElement.pause();
    playerState.isPlaying = false;
    updatePlayButtons();

    // 更新迷你播放器状态
    if (miniPlayerEl) {
      miniPlayerEl.classList.remove('playing');
    }
  }

  // 切换播放/暂停
  function togglePlay() {
    if (playerState.isPlaying) {
      pauseAudio();
    } else {
      playAudio();
    }
  }

  // 播放下一首
  function playNext() {
    let nextIndex;

    if (playerState.isShuffle) {
      // 随机播放逻辑
      nextIndex = Math.floor(Math.random() * playerState.playlist.length);
      // 避免随机到当前歌曲
      if (nextIndex === playerState.currentIndex && playerState.playlist.length > 1) {
        nextIndex = (nextIndex + 1) % playerState.playlist.length;
      }
    } else {
      // 顺序播放
      nextIndex = (playerState.currentIndex + 1) % playerState.playlist.length;
    }

    setCurrentSong(nextIndex);

    if (playerState.isPlaying) {
      playAudio();
    }
  }

  // 播放上一首
  function playPrevious() {
    let prevIndex;

    if (playerState.isShuffle) {
      // 随机播放逻辑
      prevIndex = Math.floor(Math.random() * playerState.playlist.length);
      // 避免随机到当前歌曲
      if (prevIndex === playerState.currentIndex && playerState.playlist.length > 1) {
        prevIndex = (prevIndex - 1 + playerState.playlist.length) % playerState.playlist.length;
      }
    } else {
      // 顺序播放
      prevIndex = (playerState.currentIndex - 1 + playerState.playlist.length) % playerState.playlist.length;
    }

    setCurrentSong(prevIndex);

    if (playerState.isPlaying) {
      playAudio();
    }
  }

  // 处理歌曲结束
  function handleSongEnd() {
    if (playerState.isRepeat) {
      // 单曲循环
      audioElement.currentTime = 0;
      playAudio();
    } else {
      // 播放下一首
      playNext();
    }
  }

  // 切换随机播放
  function toggleShuffle() {
    playerState.isShuffle = !playerState.isShuffle;
    const shuffleBtn = fullPlayerEl.querySelector('.player-shuffle');

    if (playerState.isShuffle) {
      shuffleBtn.classList.add('active');
    } else {
      shuffleBtn.classList.remove('active');
    }
  }

  // 切换循环播放
  function toggleRepeat() {
    playerState.isRepeat = !playerState.isRepeat;
    const repeatBtn = fullPlayerEl.querySelector('.player-repeat');

    if (playerState.isRepeat) {
      repeatBtn.classList.add('active');
    } else {
      repeatBtn.classList.remove('active');
    }
  }

  // 更新播放按钮状态
  function updatePlayButtons() {
    if (miniPlayerPlayBtn) {
      miniPlayerPlayBtn.innerHTML = playerState.isPlaying
        ? '<i class="fa fa-pause"></i>'
        : '<i class="fa fa-play"></i>';
    }

    if (fullPlayerPlayBtn) {
      fullPlayerPlayBtn.innerHTML = playerState.isPlaying
        ? '<i class="fa fa-pause"></i>'
        : '<i class="fa fa-play"></i>';
    }
  }

  // 更新进度显示
  function updateProgress() {
    if (!audioElement.duration) return;

    playerState.currentTime = audioElement.currentTime;
    const percent = playerState.currentTime / playerState.duration;

    updateProgressUI(percent);
    updateTimeDisplay();
  }

  // 更新进度条UI
  function updateProgressUI(percent) {
    if (progressBarFillEl) {
      progressBarFillEl.style.width = `${percent * 100}%`;
    }

    if (progressHandleEl) {
      progressHandleEl.style.left = `${percent * 100}%`;
    }
  }

  // 更新音量UI
  function updateVolumeUI(percent) {
    if (volumeBarFillEl) {
      volumeBarFillEl.style.width = `${percent * 100}%`;
    }

    if (volumeHandleEl) {
      volumeHandleEl.style.left = `${percent * 100}%`;
    }
  }

  // 更新时间显示
  function updateTimeDisplay() {
    if (currentTimeEl) {
      currentTimeEl.textContent = formatTime(playerState.currentTime);
    }
  }

  // 更新总时长显示
  function updateDurationDisplay() {
    if (durationEl) {
      durationEl.textContent = formatTime(playerState.duration);
    }
  }

  // 格式化时间
  function formatTime(seconds) {
    if (isNaN(seconds) || !isFinite(seconds)) return '0:00';

    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  }

  // 设置播放位置
  function seek(e) {
    const rect = progressBarEl.getBoundingClientRect();
    const clickPosition = e.clientX - rect.left;
    const percent = clickPosition / rect.width;

    // 更新进度条UI
    updateProgressUI(percent);

    // 设置播放位置
    playerState.currentTime = percent * playerState.duration;
    audioElement.currentTime = playerState.currentTime;

    // 更新时间显示
    updateTimeDisplay();
  }

  // 设置音量
  function setVolume(e) {
    const rect = volumeBarEl.getBoundingClientRect();
    const clickPosition = e.clientX - rect.left;
    const percent = clickPosition / rect.width;

    // 限制在0-1范围内
    playerState.volume = Math.max(0, Math.min(1, percent));

    // 更新音量UI
    updateVolumeUI(playerState.volume);

    // 设置音频音量
    audioElement.volume = playerState.volume;
  }

  // 打开全屏播放器
  function openFullPlayer() {
    if (fullPlayerEl) {
      fullPlayerEl.style.display = 'block';
      document.body.style.overflow = 'hidden'; // 防止背景滚动
    }
  }

  // 关闭全屏播放器
  function closeFullPlayer() {
    if (fullPlayerEl) {
      fullPlayerEl.style.display = 'none';
      document.body.style.overflow = ''; // 恢复背景滚动
    }
  }

  // 使用全局Toast对象的兼容函数
  function showToast(message, type = 'info') {
    if (window.Toast && typeof window.Toast.show === 'function') {
      switch(type) {
        case 'success':
          window.Toast.success(message);
          break;
        case 'error':
          window.Toast.error(message);
          break;
        case 'warning':
          window.Toast.warning(message);
          break;
        default:
          window.Toast.info(message);
      }
    } else {
      console.warn(message);
    }
  }

  // 初始化音乐播放器
  window.addEventListener('DOMContentLoaded', () => {
    // 检查是否应该跳过初始化(某些页面如搜索页面可能没有播放器元素)
    if (window.overrideMusicPlayer === true) {
      
      return;
    }

    // 检查必要元素是否存在
    const miniPlayerCheck = document.querySelector('.mini-player');
    if (!miniPlayerCheck) {
      
      return;
    }

    // 检查是否已经初始化过
    if (window.musicPlayerInitialized === true) {
      
      return;
    }

    // 标记为已初始化
    window.musicPlayerInitialized = true;

    // 延迟初始化以确保DOM已加载
    setTimeout(initPlayer, 100);
  });

  // 导出公共API
  window.MusicPlayer = {
    // 公共方法
    init: initPlayer,
    play: playAudio,
    pause: pauseAudio,
    next: playNext,
    previous: playPrevious,
    togglePlay: togglePlay,
    openFullPlayer: openFullPlayer,
    closeFullPlayer: closeFullPlayer,

    // 获取当前状态
    isPlaying: () => playerState.isPlaying,
    getCurrentSong: () => playerState.playlist[playerState.currentIndex] || null,
    getPlaylist: () => [...playerState.playlist]
  };
})();
