document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已经初始化过音乐播放器
    if (window.musicPlayerInitialized === true) {
        
        return;
    }

    // 音乐播放器元素
    const miniPlayer = document.querySelector('.mini-player');
    const musicPlayerModal = document.querySelector('.music-player-modal');
    const closePlayerBtn = document.querySelector('.close-music-modal');

    // 获取正确的播放按钮
    const playBtn = document.querySelector('.mini-player-play');
    const prevBtn = document.querySelector('.mini-player-prev');
    const nextBtn = document.querySelector('.mini-player-next');
    const fullscreenBtn = document.querySelector('.mini-player-fullscreen');

    // 歌曲列表
    const songItems = document.querySelectorAll('.song-item');

    // 进度条元素
    const progressBar = document.querySelector('.progress-bar');
    const progressBarFill = document.querySelector('.progress-bar-fill');
    const progressHandle = document.querySelector('.progress-handle');
    const currentTimeEl = document.querySelector('.current-time');
    const durationEl = document.querySelector('.duration');

    // 音量控制
    const volumeBar = document.querySelector('.volume-bar');
    const volumeBarFill = document.querySelector('.volume-bar-fill');
    const volumeHandle = document.querySelector('.volume-handle');
    const volumeIcon = document.querySelector('.volume-icon i');

    // 音频对象
    let audio = new Audio();

    // 播放状态
    let isPlaying = false;
    let isLooping = false;
    let currentSongIndex = 0;
    let songs = [];

    // 从songsList加载歌曲
    function loadSongList() {
        if (window.songsList && Array.isArray(window.songsList)) {
            songs = window.songsList;
            
            // 检查歌曲对象结构
            
        } else {
            console.error("无法加载歌曲列表，检查songs.js是否正确加载");
            // 提供默认歌曲数据以防止错误
            songs = [
                {
                    id: 1,
                    title: 'Faded',
                    artist: 'Alan Walker',
                    url: '/assets/audio/faded.mp3',
                    duration: '3:32'
                }
            ];
        }

        // 初始加载第一首歌
        if (songs.length > 0) {
            loadSong(0);
            // 强制更新UI，防止song-title未显示
            updatePlayerUI(0);
        }
    }

    // 更新播放器UI
    function updatePlayerUI(index) {
        if (!songs[index]) return;

        const song = songs[index];
        

        // 更新迷你播放器
        const songTitleEl = document.querySelector('.mini-player .song-title');
        const songCountEl = document.querySelector('.mini-player .song-count');

        if (songTitleEl) {
            songTitleEl.textContent = song.title;
            
        } else {
            console.error("找不到song-title元素");
        }

        if (songCountEl) {
            songCountEl.textContent = `${index + 1}/${songs.length}`;
        }
    }

    // 打开音乐播放器
    function openMusicPlayer() {
        if (!musicPlayerModal) return;

        musicPlayerModal.style.display = 'block';
        document.body.classList.add('modal-open');
    }

    // 关闭音乐播放器
    function closeMusicPlayer() {
        if (!musicPlayerModal) return;

        musicPlayerModal.style.display = 'none';
        document.body.classList.remove('modal-open');
    }

    // 播放/暂停切换
    function togglePlay() {
        try {
            if (isPlaying) {
                // 从播放状态切换到暂停状态
                
                audio.pause();
                isPlaying = false;
                miniPlayer.classList.remove('playing');
            } else {
                // 从暂停状态切换到播放状态
                
                audio.play().then(() => {
                    
                    isPlaying = true;
                    miniPlayer.classList.add('playing');
                }).catch(error => {
                    console.error('播放失败:', error);
                });
            }
        } catch (error) {
            console.error("togglePlay错误:", error);
        }
    }

    // 循环模式切换
    function toggleLoop() {
        isLooping = !isLooping;
        audio.loop = isLooping;
    }

    // 播放上一首
    function playPrevious() {
        currentSongIndex = (currentSongIndex - 1 + songs.length) % songs.length;
        loadSong(currentSongIndex);
    }

    // 播放下一首
    function playNext() {
        currentSongIndex = (currentSongIndex + 1) % songs.length;
        loadSong(currentSongIndex);
    }

    // 加载歌曲
    function loadSong(index) {
        if (!songs[index]) return;

        const song = songs[index];
        audio.src = song.url;

        // 更新UI
        updatePlayerUI(index);

        // 更新全屏播放器UI
        const currentSongTitleEl = document.querySelector('.current-song-title');
        const currentSongArtistEl = document.querySelector('.current-song-artist');

        if (currentSongTitleEl) currentSongTitleEl.textContent = song.title;
        if (currentSongArtistEl) currentSongArtistEl.textContent = song.artist;
        if (durationEl) durationEl.textContent = song.duration;

        // 重置进度条
        if (progressBarFill) progressBarFill.style.width = '0%';
        if (progressHandle) progressHandle.style.left = '0%';
        if (currentTimeEl) currentTimeEl.textContent = '00:00';

        // 更新活跃歌曲
        songItems.forEach((item, i) => {
            if (i === index) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });

        // 如果之前在播放，则自动播放新加载的歌曲
        if (isPlaying) {
            audio.play().then(() => {
                isPlaying = true;
                miniPlayer.classList.add('playing');
            }).catch(error => {
                console.error('播放失败:', error);
                isPlaying = false;
                miniPlayer.classList.remove('playing');
            });
        }
    }

    // 更新进度条
    function updateProgress() {
        if (audio.duration) {
            const progress = (audio.currentTime / audio.duration) * 100;
            progressBarFill.style.width = `${progress}%`;
            progressHandle.style.left = `${progress}%`;

            // 更新当前时间显示
            const minutes = Math.floor(audio.currentTime / 60);
            const seconds = Math.floor(audio.currentTime % 60);
            currentTimeEl.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    // 设置进度条位置
    function setProgress(e) {
        const width = progressBar.clientWidth;
        const clickX = e.offsetX;
        const duration = audio.duration;

        audio.currentTime = (clickX / width) * duration;
    }

    // 设置音量
    function setVolume(e) {
        const width = volumeBar.clientWidth;
        const clickX = e.offsetX;
        const volume = clickX / width;

        // 设置音量 (0-1范围)
        audio.volume = Math.max(0, Math.min(1, volume));

        // 更新音量滑块UI
        volumeBarFill.style.width = `${volume * 100}%`;
        volumeHandle.style.left = `${volume * 100}%`;

        // 更新音量图标
        updateVolumeIcon(volume);
    }

    // 更新音量图标
    function updateVolumeIcon(volume) {
        volumeIcon.className = '';
        if (volume === 0) {
            volumeIcon.classList.add('fa', 'fa-volume-off');
        } else if (volume < 0.5) {
            volumeIcon.classList.add('fa', 'fa-volume-down');
        } else {
            volumeIcon.classList.add('fa', 'fa-volume-up');
        }
    }

    // 切换标签
    function switchTab(tabName) {
        tabs.forEach(tab => {
            if (tab.getAttribute('data-tab') === tabName) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });

        // 这里可以添加加载不同标签内容的逻辑
    }

    // 事件监听
    if (fullscreenBtn) {
        fullscreenBtn.addEventListener('click', openMusicPlayer);
    }

    if (closePlayerBtn) {
        closePlayerBtn.addEventListener('click', closeMusicPlayer);
    }

    // 点击播放/暂停按钮
    if (playBtn) {
        playBtn.addEventListener('click', togglePlay);
    }

    // 播放上一首
    if (prevBtn) {
        prevBtn.addEventListener('click', playPrevious);
    }

    // 播放下一首
    if (nextBtn) {
        nextBtn.addEventListener('click', playNext);
    }

    // 更新进度
    audio.addEventListener('timeupdate', updateProgress);

    // 点击进度条更新进度
    if (progressBar) {
        progressBar.addEventListener('click', setProgress);
    }

    // 音量控制
    if (volumeBar) {
        volumeBar.addEventListener('click', setVolume);
    }

    // 音频结束时的处理
    audio.addEventListener('ended', function() {
        if (!isLooping) {
            playNext();
        }
    });

    // 标签切换
    const tabs = document.querySelectorAll('[data-tab]');
    if (tabs && tabs.length > 0) {
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const tabName = this.getAttribute('data-tab');
                switchTab(tabName);
            });
        });
    }

    // 点击歌曲项播放
    songItems.forEach((item, index) => {
        item.addEventListener('click', function() {
            currentSongIndex = index;
            loadSong(index);
            isPlaying = true;
            audio.play();
            updatePlayButtons();
        });
    });

    // 更新播放按钮状态
    function updatePlayButtons() {
        if (isPlaying) {
            miniPlayer.classList.add('playing');
        } else {
            miniPlayer.classList.remove('playing');
        }
    }

    // 初始音量设置 (75%)
    audio.volume = 0.75;
    volumeBarFill.style.width = '75%';
    volumeHandle.style.left = '75%';

    // 初始化
    loadSongList();

    // 确保播放按钮样式正确显示
    miniPlayer.classList.remove('playing');

    // 点击迷你播放器信息区域也可打开全屏模式
    const miniPlayerInfo = document.querySelector('.mini-player-info');
    if (miniPlayerInfo) {
        miniPlayerInfo.addEventListener('click', openMusicPlayer);
    }

    // 标记为已初始化
    window.musicPlayerInitialized = true;
    
});
