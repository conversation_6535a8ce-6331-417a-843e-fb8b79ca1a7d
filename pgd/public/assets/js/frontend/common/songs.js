/**
 * 音乐播放列表数据
 * 从 public/assets/audio 目录自动生成
 */

const songs = [
    {
        id: 1,
        title: 'Faded',
        artist: '<PERSON>',
        cover: '/assets/img/frontend/common/music/cover1.jpg',
        url: '/assets/audio/faded.mp3',
        duration: '3:32'
    },
    {
        id: 2,
        title: 'See You Again',
        artist: '<PERSON><PERSON> ft. Charlie Puth',
        cover: '/assets/img/frontend/common/music/cover2.jpg',
        url: '/assets/audio/see_you_again.mp3',
        duration: '3:58'
    },
    {
        id: 3,
        title: 'The Nights',
        artist: 'Avicii',
        cover: '/assets/img/frontend/common/music/cover3.jpg',
        url: '/assets/audio/the_nights.mp3',
        duration: '2:56'
    },
    {
        id: 4,
        title: 'Without You',
        artist: 'A<PERSON>ii',
        cover: '/assets/img/frontend/common/music/cover4.jpg',
        url: '/assets/audio/without_you.mp3',
        duration: '3:02'
    }
];

// 导出歌曲列表，供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = songs;
} else {
    // 浏览器环境中作为全局变量
    window.songsList = songs;
} 