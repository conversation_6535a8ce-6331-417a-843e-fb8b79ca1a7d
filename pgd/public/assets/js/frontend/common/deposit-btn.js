/**
 * 充值按钮功能
 */
(function() {
    'use strict';

    // 页面加载完成后执行初始化
    document.addEventListener('DOMContentLoaded', init);

    /**
     * 初始化充值按钮功能
     */
    function init() {
        // 绑定充值按钮点击事件
        const depositBtns = document.querySelectorAll('.deposit-btn');
        depositBtns.forEach(btn => {
            btn.addEventListener('click', handleDepositClick);
        });

        // 绑定下拉按钮点击事件
        const dropdownToggles = document.querySelectorAll('.deposit-dropdown-toggle');
        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', toggleDropdown);
        });

        // 点击外部区域关闭下拉菜单
        document.addEventListener('click', closeDropdownOnOutsideClick);

        // 确保下拉菜单项的功能正常
        initDropdownItems();

        // 添加CSS样式确保下拉菜单正确显示
        const styleEl = document.createElement('style');
        styleEl.textContent = `
            .dropdown-content.show {
                display: block !important;
                z-index: 999999 !important;
            }
            .deposit-btn-wrapper {
                overflow: visible !important;
            }
        `;
        document.head.appendChild(styleEl);
    }

    /**
     * 处理充值按钮点击事件
     * @param {Event} e - 点击事件
     */
    function handleDepositClick(e) {
        e.preventDefault();
        console.log('充值按钮被点击');

        // 直接打开充值弹窗
        openDepositPopup();
    }

    /**
     * 切换下拉菜单的显示状态
     * @param {Event} e - 点击事件
     */
    function toggleDropdown(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('下拉按钮被点击');
        const wrapper = e.currentTarget.closest('.deposit-btn-wrapper');
        const dropdown = wrapper.querySelector('.dropdown-content');

        if (!dropdown) {
            console.error('找不到.dropdown-content元素');
            return;
        }

        // 关闭所有其他下拉菜单
        document.querySelectorAll('.dropdown-content.show').forEach(menu => {
            if (menu !== dropdown) {
                menu.classList.remove('show');
            }
        });

        // 切换当前菜单的显示状态
        dropdown.classList.toggle('show');
    }

    /**
     * 点击外部区域时关闭下拉菜单
     * @param {Event} e - 点击事件
     */
    function closeDropdownOnOutsideClick(e) {
        if (!e.target.closest('.deposit-btn-wrapper')) {
            document.querySelectorAll('.dropdown-content.show').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    }

    /**
     * 初始化下拉菜单项的点击事件
     */
    function initDropdownItems() {
        const items = document.querySelectorAll('.deposit-dropdown-item');
        items.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const action = this.getAttribute('data-action');

                // 检查用户登录状态（除了登录页面）
                if (!isUserLoggedIn() && action !== 'login') {
                    showLoginPopup();
                    return;
                }

                // 根据不同的操作执行不同的功能
                switch(action) {
                    case 'deposit':
                        openDepositPopup();
                        break;
                    default:
                        break;
                }

                // 关闭下拉菜单
                const dropdown = this.closest('.dropdown-content');
                if (dropdown) {
                    dropdown.classList.remove('show');
                }
            });
        });
    }

    /**
     * 检查用户是否已登录
     * @returns {boolean} 用户登录状态
     */
    function isUserLoggedIn() {
        // 这里假设页面上有一个全局变量或者其他方式检测登录状态
        // 实际实现需要根据项目的用户管理方式来定
        if (typeof window.isLoggedIn !== 'undefined') {
            return window.isLoggedIn;
        }

        // 默认实现：检查是否有用户ID cookie或localStorage
        return !!localStorage.getItem('userId') ||
               !!document.cookie.split(';').some(c => c.trim().startsWith('userId='));
    }

    /**
     * 显示登录弹窗
     */
    function showLoginPopup() {
        try {
            // 假设网站使用了全局的 window.authPopup.show 方法来显示登录弹窗
            if (window.authPopup && typeof window.authPopup.show === 'function') {
                window.authPopup.show('login');
            } else {
                // 如果没有定义登录弹窗，则重定向到登录页面
                window.location.href = '/index/user/login';
            }
        } catch (error) {
            console.error('无法显示登录弹窗:', error);
            // 回退方案，重定向到登录页面
            window.location.href = '/index/user/login';
        }
    }

    /**
     * 打开充值弹窗
     */
    function openDepositPopup() {
        // 列出所有弹窗相关元素
        // const popupElements = document.querySelectorAll('[class*="popup"], [class*="deposit"], [id*="popup"], [id*="deposit"]');

        // 方法1: 使用全局depositPopup对象
        // if (window.depositPopup && typeof window.depositPopup.show === 'function') {
        //     try {
        //         window.depositPopup.show();
        //         return;
        //     } catch (e) {
        //         console.error('调用window.depositPopup.show()失败:', e);
        //     }
        // }

        // 方法2: 查找ID为depositPopupOverlay的元素
        // const popupOverlay = document.getElementById('depositPopupOverlay');
        // if (popupOverlay) {
        //     popupOverlay.style.cssText = 'display:block !important; opacity:1 !important; visibility:visible !important;';
        //     document.body.classList.add('popup-open', 'no-scroll');
        //     return;
        // }

        // 方法3: 直接找.deposit-popup (取消display:none)
        // const depositPopup = document.querySelector('.deposit-popup');
        // if (depositPopup) {
        //     // 强制显示
        //     depositPopup.style.cssText = 'display:block !important; opacity:1 !important; visibility:visible !important; z-index:999999 !important;';

        //     // 检查父元素是否隐藏
        //     let parent = depositPopup.parentElement;
        //     while (parent && parent !== document.body) {
        //         if (getComputedStyle(parent).display === 'none') {
        //             console.log('修复隐藏的父元素:', parent);
        //             parent.style.display = 'block';
        //         }
        //         parent = parent.parentElement;
        //     }

        //     // 添加body类
        //     document.body.classList.add('popup-open', 'no-scroll');

        //     // 检查是否有遮罩层
        //     const overlay = depositPopup.querySelector('.deposit-popup-overlay') || document.querySelector('.deposit-popup-overlay');
        //     if (overlay) {
        //         overlay.style.cssText = 'display:block !important; opacity:1 !important;';
        //     }

        //     console.log('已显示充值弹窗');
        //     return;
        // }

        // 方法4: 查找其他可能的弹窗元素
        // console.log('尝试查找其他弹窗元素...');
        // const selectors = [
        //     '.popup', '.modal', '.deposit-popup-container', '.deposit-container',
        //     '[id*="deposit"]', '[class*="deposit-container"]'
        // ];

        // for (const selector of selectors) {
        //     const elements = document.querySelectorAll(selector);
        //     if (elements.length > 0) {
        //         console.log(`✅ 找到${elements.length}个元素匹配: ${selector}`);
        //         // 显示第一个找到的元素
        //         const element = elements[0];
        //         element.style.cssText = 'display:block !important; opacity:1 !important; visibility:visible !important; z-index:999999 !important;';
        //         document.body.classList.add('popup-open');
        //         console.log('已显示元素:', element);
        //         return;
        //     }
        // }

        // 方法5: 尝试jQuery
        // if (typeof $ !== 'undefined') {
        //     const jQuerySelectors = ['.deposit-popup', '#depositPopupOverlay', '.popup', '[class*="deposit"]'];
        //     for (const selector of jQuerySelectors) {
        //         const $element = $(selector);
        //         if ($element.length) {
        //             console.log(`✅ 通过jQuery找到: ${selector}`);
        //             $element.show();
        //             $('body').addClass('popup-open');
        //             return;
        //         }
        //     }
        // }

        // 最后尝试: 调用可能的其他全局对象方法
        // const possibleGlobalObjects = ['popup', 'modal', 'depositPopup', 'rechargePopup', 'showDepositPopup'];
        // for (const objName of possibleGlobalObjects) {
        //     if (window[objName] && typeof window[objName] === 'function') {
        //         console.log(`✅ 调用window.${objName}()`);
        //         try {
        //             window[objName]();
        //             return;
        //         } catch (e) {
        //             console.error(`调用window.${objName}()失败:`, e);
        //         }
        //     } else if (window[objName] && typeof window[objName].show === 'function') {
        //         console.log(`✅ 调用window.${objName}.show()`);
        //         try {
        //             window[objName].show();
        //             return;
        //         } catch (e) {
        //             console.error(`调用window.${objName}.show()失败:`, e);
        //         }
        //     }
        // }

        // 如果所有方法都失败，创建一个备用弹窗
        // console.error('❌ 所有方法都失败，无法显示充值弹窗');

        // 显示备用弹窗，告知用户刷新页面
        // const fallbackPopup = document.createElement('div');
        // fallbackPopup.className = 'fallback-deposit-popup';
        // fallbackPopup.style.cssText = 'position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.7); z-index:999999; display:flex; align-items:center; justify-content:center;';
        // fallbackPopup.innerHTML = `
        //     <div style="background:white; padding:20px; border-radius:10px; max-width:90%; width:400px; text-align:center;">
        //         <h3 style="margin-top:0;">充值功能暂时不可用</h3>
        //         <p>请尝试刷新页面或稍后再试。</p>
        //         <button onclick="this.parentElement.parentElement.remove();" style="padding:8px 15px; background:#f5a623; color:white; border:none; border-radius:4px; cursor:pointer;">关闭</button>
        //     </div>
        // `;
        // document.body.appendChild(fallbackPopup);
    }



    // 暴露给全局以便外部调用
    window.depositBtn = {
        openDepositPopup: openDepositPopup,
        toggleDropdown: toggleDropdown
    };

    // 兼容旧版本的函数名
    window.openChargePopup = openDepositPopup;

    // 兼容旧版本的全局对象
    window.chargeBtn = window.depositBtn;

    // 兼容旧版本的充值弹窗对象
    if (!window.chargePopup && window.depositPopup) {
        window.chargePopup = window.depositPopup;
    }

})();