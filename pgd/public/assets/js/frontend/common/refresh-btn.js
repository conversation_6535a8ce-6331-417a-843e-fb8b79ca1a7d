/**
 * 通用刷新按钮功能
 * 用于刷新用户余额
 */

// 确保在DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('刷新按钮脚本已加载');
    
    // 初始化刷新按钮功能
    initRefreshButton();
});

/**
 * 初始化刷新按钮功能
 */
function initRefreshButton() {
    // 查找所有刷新按钮
    const refreshButtons = document.querySelectorAll('.refresh-btn');
    
    if (refreshButtons.length > 0) {
        console.log('找到刷新按钮:', refreshButtons.length, '个');
        
        // 为每个刷新按钮添加点击事件
        refreshButtons.forEach(function(btn) {
            btn.addEventListener('click', function() {
                console.log('刷新按钮被点击');
                
                // 添加旋转动画效果
                this.style.transition = 'transform 0.5s';
                this.style.transform = 'rotate(360deg)';
                
                // 请求最新的用户余额
                fetch('/index/index/refreshBalance', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    setTimeout(() => {
                        // 恢复按钮状态
                        this.style.transform = 'rotate(0deg)';
                        
                        if (data.code === 1) {
                            // 更新所有余额显示
                            const balanceElements = document.querySelectorAll('.balance-amount, .balance-value');
                            balanceElements.forEach(el => {
                                // 检查是否需要添加货币符号
                                const currencySymbol = window.currency || '';
                                
                                // 如果元素已包含货币符号，则不添加
                                if (el.textContent.includes(currencySymbol)) {
                                    el.textContent = currencySymbol + ' ' + (data.data.balance || '0.00');
                                } else {
                                    el.textContent = data.data.balance || '0.00';
                                }
                            });
                            
                            // 如果存在，也更新投注要求显示
                            const betRequirementElements = document.querySelectorAll('.requirement-value');
                            if (data.data.bet_requirement !== undefined && betRequirementElements.length > 0) {
                                betRequirementElements.forEach(el => {
                                    // 检查是否需要添加货币符号
                                    const currencySymbol = window.currency || '';
                                    
                                    // 如果元素已包含货币符号，则不添加
                                    if (el.textContent.includes(currencySymbol)) {
                                        el.textContent = currencySymbol + ' ' + (data.data.bet_requirement || '0.00');
                                    } else {
                                        el.textContent = data.data.bet_requirement || '0.00';
                                    }
                                });
                            }
                            
                            // 显示成功消息
                            if (window.Toast && typeof window.Toast.success === 'function') {
                                window.Toast.success(data.msg || 'Saldo atualizado');
                            }
                        } else {
                            console.error('刷新余额失败:', data.msg);
                            
                            // 显示错误消息
                            if (window.Toast && typeof window.Toast.error === 'function') {
                                window.Toast.error(data.msg || 'Falha ao atualizar o saldo');
                            }
                        }
                    }, 500);
                })
                .catch(error => {
                    console.error('刷新余额出错:', error);
                    
                    // 恢复按钮状态
                    this.style.transform = 'rotate(0deg)';
                    
                    // 显示错误消息
                    if (window.Toast && typeof window.Toast.error === 'function') {
                        window.Toast.error('Erro de rede, tente novamente mais tarde');
                    }
                });
            });
        });
    } else {
        console.log('未找到刷新按钮');
    }
}

// 暴露全局函数，以便其他脚本可以调用
window.refreshBalance = function() {
    // 模拟点击第一个刷新按钮
    const refreshBtn = document.querySelector('.refresh-btn');
    if (refreshBtn) {
        refreshBtn.click();
    } else {
        console.error('未找到刷新按钮，无法刷新余额');
        
        // 直接发起请求
        fetch('/index/index/refreshBalance', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                // 更新所有余额显示
                const balanceElements = document.querySelectorAll('.balance-amount, .balance-value');
                balanceElements.forEach(el => {
                    el.textContent = data.data.balance || '0.00';
                });
                
                // 显示成功消息
                if (window.Toast && typeof window.Toast.success === 'function') {
                    window.Toast.success(data.msg || 'Saldo atualizado');
                }
            } else {
                console.error('刷新余额失败:', data.msg);
                
                // 显示错误消息
                if (window.Toast && typeof window.Toast.error === 'function') {
                    window.Toast.error(data.msg || 'Falha ao atualizar o saldo');
                }
            }
        })
        .catch(error => {
            console.error('刷新余额出错:', error);
            
            // 显示错误消息
            if (window.Toast && typeof window.Toast.error === 'function') {
                window.Toast.error('Erro de rede, tente novamente mais tarde');
            }
        });
    }
};
