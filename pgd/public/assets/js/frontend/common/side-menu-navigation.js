/**
 * 侧边菜单导航功能
 * 处理侧边菜单中各个按钮的点击事件
 */
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有导航按钮的点击事件
    initNavigationEvents();

    /**
     * 初始化所有导航按钮的点击事件
     */
    function initNavigationEvents() {
        // 获取所有需要处理的导航按钮
        const navButtons = document.querySelectorAll('.side-menu-card[data-href]');

        // 为每个按钮添加点击事件
        navButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault(); // 阻止默认的链接行为

                const href = this.getAttribute('data-href');
                const target = this.getAttribute('data-target');

                // 如果有目标属性且为_blank，则在新窗口打开
                if (target === '_blank') {
                    window.open(href, '_blank');
                } else {
                    // 关闭侧边菜单
                    if (typeof window.SideMenu !== 'undefined') {
                        window.SideMenu.close();
                    }

                    // 延迟导航，让侧边菜单有时间关闭
                    setTimeout(() => {
                        window.location.href = href;
                    }, 300);
                }
            });
        });

        // 处理游戏分类导航
        handleGameCategoryNavigation();

        // 处理社交媒体导航
        handleSocialMediaNavigation();

        // 处理活动和返利导航
        handleEventsNavigation();

        // 处理所有其他按钮
        handleAllButtons();
    }

    /**
     * 处理所有按钮，确保每个按钮都有事件监听
     */
    function handleAllButtons() {
        // 获取所有侧边菜单卡片
        const allCards = document.querySelectorAll('.side-menu-card');

        // 为每个卡片添加点击事件（如果尚未添加）
        allCards.forEach(card => {
            // 检查是否已经有点击事件（通过data属性判断）
            const hasCategory = card.hasAttribute('data-category');
            const hasSocial = card.hasAttribute('data-social');
            const hasEvent = card.hasAttribute('data-event');
            const hasHref = card.hasAttribute('data-href');

            // 如果没有任何导航相关的属性，添加默认点击事件
            if (!hasCategory && !hasSocial && !hasEvent && !hasHref) {
                card.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 获取卡片文本作为标识
                    const cardText = card.querySelector('.side-menu-card-text')?.textContent.trim() || '';
                    console.log(`点击了未配置的按钮: ${cardText}`);

                    // 关闭侧边菜单
                    if (typeof window.SideMenu !== 'undefined') {
                        window.SideMenu.close();
                    }

                    // 这里可以根据卡片的类或其他属性决定跳转地址
                    // 默认跳转到首页
                    setTimeout(() => {
                        // 预留跳转地址，根据实际需求修改
                        window.location.href = ``;
                    }, 300);
                });
            }
        });
    }

    /**
     * 处理游戏分类导航
     */
    function handleGameCategoryNavigation() {
        // 获取所有游戏分类按钮
        const categoryButtons = document.querySelectorAll('.side-menu-card[data-category]');

        // 为每个分类按钮添加点击事件
        categoryButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const category = this.getAttribute('data-category');
                console.log(`点击了游戏分类: ${category}`);

                // 关闭侧边菜单
                if (typeof window.SideMenu !== 'undefined') {
                    window.SideMenu.close();
                }

                // 根据分类决定跳转地址
                let url = '';
                switch (category) {
                    case 'popular':
                        url = `/index/subgame/index?category=slots&hcategory=popular`;
                        break;
                    case 'slots':
                        url = `/index/subgame/index?category=slots`;
                        break;
                    case 'recent':
                        url = `/index/subgame/index?category=slots&hcategory=recente`;
                        break;
                    case 'favorite':
                        url = `/index/subgame/index?category=slots&hcategory=favoritos`;
                        break;
                }

                // 延迟导航，让侧边菜单有时间关闭
                setTimeout(() => {
                    window.location.href = url;
                }, 300);
            });
        });
    }

    /**
     * 处理社交媒体导航
     */
    function handleSocialMediaNavigation() {
        // 获取所有社交媒体按钮
        const socialButtons = document.querySelectorAll('.side-menu-card[data-social]');

        // 为每个社交媒体按钮添加点击事件
        socialButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const social = this.getAttribute('data-social');
                // 根据社交媒体类型决定跳转地址
                let url = '';
                
                // 使用全局变量中的链接（如果存在）
                if (window.socialMediaLinks && social in window.socialMediaLinks) {
                    url = window.socialMediaLinks[social];
                } else {
                    // 作为后备，使用默认链接
                    switch (social) {
                        case 'instagram':
                            url = 'https://www.instagram.com/lotoland_group/';
                            break;
                        case 'telegram':
                            url = 'https://t.me/+_E2zemghlelkYjQ1';
                            break;
                        default:
                            url = 'https://www.instagram.com/';
                            break;
                    }
                }

                // 在新窗口打开社交媒体链接
                window.open(url, '_blank');
            });
        });
    }

    /**
     * 处理活动和返利导航
     */
    function handleEventsNavigation() {
        // 获取所有活动按钮
        const eventButtons = document.querySelectorAll('.side-menu-card[data-event]');

        // 为每个活动按钮添加点击事件
        eventButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const eventType = this.getAttribute('data-event');
                // 关闭侧边菜单
                if (typeof window.SideMenu !== 'undefined') {
                    window.SideMenu.close();
                }

                // 根据活动类型决定跳转地址
                let url = '';
                switch (eventType) {
                    case 'events':
                        url = `index/event/index`;
                        break;
                    case 'rebate':
                        url = `index/event/rebate`;
                        break;
                    case 'pendente':
                        url = `index/event/pendente`;
                        break;
                    case 'history':
                        url = `index/event/history`;
                        break;
                    case 'vip':
                        url = `index/event/vip`;
                        break;
                }

                // 延迟导航，让侧边菜单有时间关闭
                setTimeout(() => {
                    window.location.href = url;
                }, 300);
            });
        });
    }
});
