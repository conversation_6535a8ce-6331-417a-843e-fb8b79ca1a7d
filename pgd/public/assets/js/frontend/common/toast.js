/**
 * 通用Toast弹窗组件
 * 使用方法:
 * Toast.show('复制成功'); // 默认成功提示
 * Toast.success('操作成功'); // 成功提示
 * Toast.error('发生错误'); // 错误提示
 * Toast.warning('警告信息'); // 警告提示
 * Toast.info('提示信息'); // 信息提示
 */

(function() {
    // 创建Toast元素
    function createToastElement() {
        // 如果已存在则不重复创建
        if (document.getElementById('customToast')) {
            return;
        }
        
        // 创建Toast元素
        const toast = document.createElement('div');
        toast.id = 'customToast';
        toast.className = 'custom-toast';
        
        // 添加图标
        const icon = document.createElement('div');
        icon.className = 'toast-icon';
        icon.innerHTML = '✓';
        toast.appendChild(icon);
        
        // 添加消息文本
        const message = document.createElement('div');
        message.className = 'toast-message';
        toast.appendChild(message);
        
        // 添加到body最前面，确保在最顶层
        if (document.body.firstChild) {
            document.body.insertBefore(toast, document.body.firstChild);
        } else {
        document.body.appendChild(toast);
        }
    }
    
    // 确保DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createToastElement);
    } else {
        createToastElement();
    }
    
    // 定义Toast对象
    window.Toast = {
        /**
         * 显示Toast提示
         * @param {string} message 提示消息
         * @param {string} type 提示类型: success, error, warning, info
         * @param {number} duration 持续时间(毫秒)
         */
        show: function(message, type = 'success', duration = 2000) {
            // 如果元素不存在，先创建
            if (!document.getElementById('customToast')) {
                createToastElement();
            }
            
            const toast = document.getElementById('customToast');
            const toastMessage = toast.querySelector('.toast-message');
            const toastIcon = toast.querySelector('.toast-icon');
            
            // 设置消息
            toastMessage.textContent = message;
            
            // 设置图标
            switch(type) {
                case 'success':
                    toastIcon.innerHTML = '✓';
                    break;
                case 'error':
                    toastIcon.innerHTML = '✕';
                    break;
                case 'warning':
                    toastIcon.innerHTML = '!';
                    break;
                case 'info':
                    toastIcon.innerHTML = 'i';
                    break;
                default:
                    toastIcon.innerHTML = '✓';
            }
            
            // 重置所有类型的类名
            toast.className = 'custom-toast';
            toast.classList.add(type);
            
            // 显示Toast
            toast.classList.add('show');
            
            // 设置自动隐藏
            if (window.toastTimeout) {
                clearTimeout(window.toastTimeout);
            }
            
            window.toastTimeout = setTimeout(function() {
                toast.classList.remove('show');
            }, duration);
        },
        
        // 简便方法
        success: function(message, duration) {
            this.show(message, 'success', duration);
        },
        
        error: function(message, duration) {
            this.show(message, 'error', duration);
        },
        
        warning: function(message, duration) {
            this.show(message, 'warning', duration);
        },
        
        info: function(message, duration) {
            this.show(message, 'info', duration);
        }
    };
})(); 