/**
 * 通用 PIN 输入框组件
 *
 * 使用方法:
 * 1. 在 HTML 中添加 pin-grid 和 pin-box 元素
 * 2. 引入 pin-input.css 和 pin-input.js
 * 3. 调用 initPinInput 函数初始化 PIN 输入框
 *
 * 示例:
 * <div class="pin-grid" id="myPinGrid">
 *     <div class="pin-box"></div>
 *     <div class="pin-box"></div>
 *     <div class="pin-box"></div>
 *     <div class="pin-box"></div>
 *     <div class="pin-box"></div>
 *     <div class="pin-box"></div>
 * </div>
 *
 * <script>
 *     document.addEventListener('DOMContentLoaded', function() {
 *         initPinInput({
 *             gridSelector: '#myPinGrid',
 *             onComplete: function(value) {
 *                 console.log('PIN 输入完成:', value);
 *             }
 *         });
 *     });
 * </script>
 */

/**
 * 初始化 PIN 输入框
 * @param {Object} options - 配置选项
 * @param {string} options.gridSelector - PIN 网格容器的选择器
 * @param {string} [options.inputId] - 隐藏输入框的 ID，默认为随机生成
 * @param {number} [options.maxLength=6] - PIN 码最大长度
 * @param {boolean} [options.autoFocus=true] - 是否自动聚焦
 * @param {Function} [options.onComplete] - PIN 输入完成时的回调函数
 * @param {Function} [options.onChange] - PIN 输入变化时的回调函数
 * @param {boolean} [options.showEye=false] - 是否显示密码可见性切换按钮
 * @returns {Object} - 包含 getValue 和 setValue 方法的对象
 */
function initPinInput(options) {
    // 默认选项
    const defaults = {
        gridSelector: '.pin-grid',
        inputId: 'pin-input-' + Math.random().toString(36).substring(2, 9),
        maxLength: 6,
        autoFocus: true,
        onComplete: null,
        onChange: null,
        showEye: false
    };

    // 合并选项
    const settings = Object.assign({}, defaults, options);

    // 获取 PIN 网格容器
    const pinGrid = document.querySelector(settings.gridSelector);

    if (!pinGrid) {
        console.error('PIN 网格容器未找到:', settings.gridSelector);
        return null;
    }

    // 检查是否已存在隐藏输入框
    let hiddenInput = pinGrid.querySelector('.pin-input-hidden');

    // 如果不存在，则创建新的隐藏输入框
    if (!hiddenInput) {
        hiddenInput = document.createElement('input');
        hiddenInput.type = 'password';
        hiddenInput.className = 'pin-input-hidden';
        hiddenInput.id = settings.inputId;
        hiddenInput.maxLength = settings.maxLength;
        hiddenInput.autocomplete = 'off';
        hiddenInput.autocorrect = 'off';
        hiddenInput.autocapitalize = 'off';
        hiddenInput.spellcheck = false;
        hiddenInput.setAttribute('inputmode', 'numeric'); // 确保在移动设备上显示数字键盘
        pinGrid.appendChild(hiddenInput);
    } else {
        // 如果已存在，更新其属性
        hiddenInput.id = settings.inputId;
        hiddenInput.maxLength = settings.maxLength;
    }

    // 确保隐藏输入框不可见
    hiddenInput.style.position = 'absolute';
    hiddenInput.style.opacity = '0';
    hiddenInput.style.pointerEvents = 'auto';
    hiddenInput.style.zIndex = '10';
    hiddenInput.style.width = '100%';  // 确保宽度覆盖整个区域
    hiddenInput.style.height = '100%'; // 确保高度覆盖整个区域
    hiddenInput.style.top = '0';       // 定位到顶部
    hiddenInput.style.left = '0';      // 定位到左侧

    // 获取所有 PIN 盒子
    const pinBoxes = pinGrid.querySelectorAll('.pin-box');
    if (!pinBoxes.length) {
        console.error('PIN 盒子未找到');
        return null;
    }

    // 输入框获取焦点并处理输入
    function focusHiddenInput() {
        // 确保输入框可以接收输入
        hiddenInput.style.pointerEvents = 'auto';

        // 尝试聚焦
        try {
            hiddenInput.focus();

            // 在移动设备上，可能需要额外的处理
            setTimeout(() => {
                // 再次尝试聚焦
                hiddenInput.focus();

                // 如果是iOS设备，可能需要点击事件
                // if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
                //     hiddenInput.click();
                // }
            }, 100);
        } catch (e) {
            console.error('聚焦隐藏输入框失败:', e);
        }

        // 立即更新高亮状态
        updatePinBoxHighlight(hiddenInput.value, pinBoxes);
    }

    // 点击任意 PIN 盒子时聚焦隐藏输入框
    pinBoxes.forEach(box => {
        box.addEventListener('click', focusHiddenInput);
        // 添加触摸事件支持
        box.addEventListener('touchstart', function(e) {
            e.preventDefault(); // 防止默认行为
            focusHiddenInput();
        });
    });

    // 初始清空所有 PIN 盒子
    pinBoxes.forEach(box => {
        box.textContent = '';
    });

    // 添加焦点事件处理器，实现高亮效果
    hiddenInput.addEventListener('focus', function() {
        // 更新高亮状态
        updatePinBoxHighlight(this.value, pinBoxes);
    });

    // 添加失焦事件处理器，移除高亮效果
    hiddenInput.addEventListener('blur', function() {
        // 移除所有 PIN 盒子的高亮
        pinBoxes.forEach(box => {
            box.classList.remove('pin-box-highlight');
        });
    });

    // 辅助函数：更新 PIN 盒子高亮状态
    function updatePinBoxHighlight(value, boxes) {
        // 移除所有高亮
        boxes.forEach(box => {
            box.classList.remove('pin-box-highlight');
        });

        // 如果输入已完成，高亮最后一个盒子
        if (value.length >= boxes.length) {
            boxes[boxes.length - 1].classList.add('pin-box-highlight');
        }
        // 如果输入未完成，高亮下一个待输入的盒子
        else if (value.length < boxes.length) {
            boxes[value.length].classList.add('pin-box-highlight');
        }
    }

    // 处理 PIN 输入 - 只允许数字
    hiddenInput.addEventListener('input', function() {
        // 只保留数字
        this.value = this.value.replace(/\D/g, '');
        const value = this.value;

        // 清空所有 PIN 盒子
        pinBoxes.forEach(box => {
            box.textContent = '';
        });

        // 依次填充 PIN 盒子
        for (let i = 0; i < value.length; i++) {
            if (i < pinBoxes.length) {
                // 使用 • 代替实际输入的数字，保护隐私
                pinBoxes[i].textContent = '•';
            }
        }

        // 更新高亮状态
        updatePinBoxHighlight(value, pinBoxes);

        // 调用 onChange 回调
        if (typeof settings.onChange === 'function') {
            settings.onChange(value);
        }

        // 如果输入完成，调用 onComplete 回调
        if (value.length === settings.maxLength && typeof settings.onComplete === 'function') {
            settings.onComplete(value);
        }
    });

    // 禁止粘贴操作
    hiddenInput.addEventListener('paste', function(e) {
        e.preventDefault();
    });

    // 禁止拖放操作
    hiddenInput.addEventListener('drop', function(e) {
        e.preventDefault();
    });

    // 监听键盘事件，支持退格键删除
    hiddenInput.addEventListener('keydown', function(e) {
        if (e.key === 'Backspace') {
            // 已经在 input 事件中处理了值的更新，这里只需保证一些体验处理
            setTimeout(() => {
                // 让输入框始终保持焦点
                hiddenInput.focus();
            }, 10);
        }
    });

    // 创建一个防止失焦的机制
    document.addEventListener('click', function(e) {
        // 如果点击的不是 PIN 相关元素，并且当前正在输入 PIN
        if (!pinGrid.contains(e.target) && hiddenInput.value.length > 0 && hiddenInput.value.length < settings.maxLength) {
            // 防止点击其他区域而失去焦点
            setTimeout(focusHiddenInput, 100);
        }
    });

    // 如果需要显示密码可见性切换按钮
    if (settings.showEye) {
        // 查找或创建密码可见性切换按钮
        let eyeIcon = pinGrid.querySelector('.pin-icon');
        if (!eyeIcon) {
            eyeIcon = document.createElement('img');
            eyeIcon.className = 'pin-icon';
            eyeIcon.src = '/assets/img/frontend/icons/eye-closed.png';
            eyeIcon.alt = 'Hide';
            pinGrid.parentNode.insertBefore(eyeIcon, pinGrid);
        }

        let passwordVisible = false;

        eyeIcon.addEventListener('click', function() {
            passwordVisible = !passwordVisible;

            // 更换图标
            this.src = passwordVisible
                ? '/assets/img/frontend/icons/eye-open.png'
                : '/assets/img/frontend/icons/eye-closed.png';

            // 当前输入值
            const value = hiddenInput.value;

            // 清空所有 PIN 盒子
            pinBoxes.forEach(box => {
                box.textContent = '';
            });

            // 根据可见性状态填充 PIN 盒子
            for (let i = 0; i < value.length; i++) {
                if (i < pinBoxes.length) {
                    // 可见时显示数字，不可见时显示点
                    pinBoxes[i].textContent = passwordVisible ? value[i] : '•';
                }
            }
        });
    }

    // 如果需要自动聚焦
    if (settings.autoFocus) {
        // 使用多个延迟尝试聚焦，确保在各种情况下都能正常工作
        // setTimeout(focusHiddenInput, 100);
        // setTimeout(focusHiddenInput, 500);
        // setTimeout(focusHiddenInput, 1000);

        // 添加点击事件监听器到文档，确保用户交互后能聚焦
        document.addEventListener('click', function documentClickHandler() {
            focusHiddenInput();
            // 移除事件监听器，避免重复
            document.removeEventListener('click', documentClickHandler);
        }, { once: true });
    }

    // 点击整个 PIN 网格时聚焦隐藏输入框
    pinGrid.addEventListener('click', focusHiddenInput);
    // 添加触摸事件支持
    pinGrid.addEventListener('touchstart', function(e) {
        e.preventDefault(); // 防止默认行为
        focusHiddenInput();
    });
    // 返回 API
    return {
        /**
         * 获取当前 PIN 值
         * @returns {string} - 当前 PIN 值
         */
        getValue: function() {
            return hiddenInput.value;
        },

        /**
         * 设置 PIN 值
         * @param {string} value - 要设置的 PIN 值
         */
        setValue: function(value) {
            // 只保留数字
            value = value.replace(/\D/g, '');

            // 限制长度
            if (value.length > settings.maxLength) {
                value = value.substring(0, settings.maxLength);
            }

            // 设置值
            hiddenInput.value = value;

            // 清空所有 PIN 盒子
            pinBoxes.forEach(box => {
                box.textContent = '';
            });

            // 依次填充 PIN 盒子
            for (let i = 0; i < value.length; i++) {
                if (i < pinBoxes.length) {
                    // 使用 • 代替实际输入的数字，保护隐私
                    pinBoxes[i].textContent = '•';
                }
            }

            // 更新高亮状态
            updatePinBoxHighlight(value, pinBoxes);

            // 调用 onChange 回调
            if (typeof settings.onChange === 'function') {
                settings.onChange(value);
            }

            // 如果输入完成，调用 onComplete 回调
            if (value.length === settings.maxLength && typeof settings.onComplete === 'function') {
                settings.onComplete(value);
            }
        },

        /**
         * 清空 PIN 值
         */
        clear: function() {
            this.setValue('');
        },

        /**
         * 聚焦到 PIN 输入框
         */
        focus: function() {
            focusHiddenInput();
        },

        /**
         * 获取隐藏输入框元素
         * @returns {HTMLElement} - 隐藏输入框元素
         */
        getInputElement: function() {
            return hiddenInput;
        }
    };
}
