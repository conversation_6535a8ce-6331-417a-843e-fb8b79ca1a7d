/**
 * 活动模块通用JavaScript
 * 适用于所有event页面
 */

// 全局函数：切换到指定的标签页
function switchTab(tabName) {
    // 标签页对应控制器方法的映射
    const tabRoutes = {
        'eventos': 'index',
        'missao': 'mission',
        'vip': 'vip',
        'rebate': 'rebate',
        'pendente': 'pendente',
        'history': 'history'
    };

    // 获取对应的方法名
    const method = tabRoutes[tabName] || 'index';

    // 构建URL并跳转
    const url = `/index/event/${method}`;
    window.location.href = url;
}

document.addEventListener('DOMContentLoaded', function() {
    // FastAdmin已经提供了全局的__()翻译函数，不需要自定义实现

    // 初始化页面
    function initPage() {
        // 初始化选项卡切换
        initTabs();
    }

    // 初始化选项卡切换功能
    function initTabs() {
        const tabs = document.querySelectorAll('.tab');

        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const tabName = this.getAttribute('data-tab');
                switchTab(tabName);
            });
        });
    }

    // 初始化页面
    initPage();
});