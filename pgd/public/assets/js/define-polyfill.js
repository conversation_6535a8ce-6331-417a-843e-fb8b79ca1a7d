/**
 * AMD模块定义的简单polyfill
 * 用于替代require.js，处理fast.js的依赖
 */
(function(window) {
    // 存储已加载的模块
    var modules = {};
    
    // 创建模拟layer对象
    var mockLayer = {
        load: function() { return 1; },
        close: function() {},
        open: function() {},
        config: function(options) { return this; },
        getFrameIndex: function() { return 0; },
        getChildFrame: function() { return $('body'); },
        style: function() {},
        setTop: function() {},
        zIndex: 19891014
    };
    
    // 简单实现define函数
    window.define = function(deps, callback) {
        // 简单处理，直接执行回调函数并传入jQuery
        if (typeof callback === 'function') {
            // 模拟常用依赖
            var dependencies = {
                'jquery': window.jQuery,
                'bootstrap': window.jQuery,
                'toastr': window.toastr || { 
                    success: console.log, 
                    error: console.error,
                    options: {}
                },
                'layer': window.layer || mockLayer,
                'lang': window.Lang || {}
            };
            
            // 准备依赖参数
            var args = [];
            if (Array.isArray(deps)) {
                for (var i = 0; i < deps.length; i++) {
                    args.push(dependencies[deps[i].toLowerCase()] || null);
                }
            }
            
            // 执行回调并存储结果
            var result = callback.apply(window, args);
            modules.fast = result;
            window.Fast = result;
            return result;
        }
    };
    
    // 简单实现require函数
    window.require = function(deps, callback) {
        if (typeof callback === 'function') {
            callback.apply(window, [window.Fast]);
        }
        return window.Fast;
    };
    
    // 将Layer暴露到全局
    window.Layer = mockLayer;
})(window); 