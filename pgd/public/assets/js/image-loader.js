/**
 * 图片懒加载工具 - 简化版
 * 用于提高页面加载性能
 */

var ImageLoader = {
    /**
     * 初始化懒加载
     * @param {string} selector - 含data-src属性的图片选择器
     */
    initLazyLoad: function(selector) {
        selector = selector || 'img[data-src]';
        
        // 懒加载实现
        const lazyLoad = function() {
            const lazyImages = document.querySelectorAll(selector);
            
            lazyImages.forEach(function(lazyImage) {
                if (lazyImage.getBoundingClientRect().top <= window.innerHeight && 
                    lazyImage.getBoundingClientRect().bottom >= 0 && 
                    getComputedStyle(lazyImage).display !== "none") {
                    
                    const src = lazyImage.getAttribute('data-src');
                    if (src) {
                        lazyImage.src = src;
                        lazyImage.classList.add('loaded');
                        lazyImage.removeAttribute('data-src');
                    }
                }
            });
        };
        
        // 页面加载完成后执行一次
        lazyLoad();
        
        // 监听滚动和调整大小事件
        window.addEventListener('scroll', lazyLoad);
        window.addEventListener('resize', lazyLoad);
        window.addEventListener('orientationChange', lazyLoad);
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    ImageLoader.initLazyLoad();
    
    // 添加简单的加载样式
    const style = document.createElement('style');
    style.textContent = `
        img[data-src] {
            opacity: 0;
            transition: opacity 0.3s;
        }
        img.loaded {
            opacity: 1;
        }
    `;
    document.head.appendChild(style);
}); 