/**
 * 页面通用工具类
 * 整合常用功能，减少代码重复
 */

var PageUtils = {
    /**
     * 初始化标签页导航
     * @param {string} containerSelector - 标签容器选择器
     * @param {Function} callback - 点击标签时的回调函数(可选)
     */
    initTabNavigation: function(containerSelector, callback) {
        const tabItems = document.querySelectorAll(containerSelector + ' .tab-item');
        
        tabItems.forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有标签的active类
                tabItems.forEach(t => t.classList.remove('active'));
                // 给当前点击的标签添加active类
                this.classList.add('active');
                
                // 如果提供了回调函数，则执行
                if (typeof callback === 'function') {
                    callback(this);
                }
            });
        });
    },
    
    /**
     * 初始化返回按钮
     * @param {string} selector - 返回按钮选择器
     * @param {string} targetUrl - 返回目标URL (可选，默认为上一页)
     */
    initBackButton: function(selector, targetUrl) {
        const backBtn = document.querySelector(selector);
        if (backBtn) {
            backBtn.addEventListener('click', function() {
                if (targetUrl) {
                    window.location.href = targetUrl;
                } else {
                    window.history.back();
                }
            });
        }
    },
    
    /**
     * 复制文本到剪贴板
     * @param {string} text - 要复制的文本
     * @param {Function} successCallback - 复制成功后的回调函数
     * @param {Function} errorCallback - 复制失败后的回调函数
     */
    copyToClipboard: function(text, successCallback, errorCallback) {
        navigator.clipboard.writeText(text)
            .then(() => {
                if (typeof successCallback === 'function') {
                    successCallback();
                }
            })
            .catch(err => {
                console.error('复制文本失败: ', err);
                if (typeof errorCallback === 'function') {
                    errorCallback(err);
                }
            });
    },
    
    /**
     * 显示通知消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success/error/info)
     * @param {number} duration - 显示时长(毫秒)
     */
    showNotification: function(message, type, duration) {
        // 设置默认值
        type = type || 'info';
        duration = duration || 3000;
        
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = 'notification notification-' + type;
        notification.textContent = message;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // 自动关闭
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, duration);
    },
    
    /**
     * 表单验证
     * @param {string} formSelector - 表单选择器
     * @param {Object} rules - 验证规则
     * @param {Function} submitCallback - 验证通过后的回调函数
     */
    validateForm: function(formSelector, rules, submitCallback) {
        const form = document.querySelector(formSelector);
        if (!form) return;
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            let isValid = true;
            let firstInvalidField = null;
            
            // 清除所有错误提示
            form.querySelectorAll('.error-message').forEach(el => el.remove());
            
            // 验证每个字段
            for (const field in rules) {
                const input = form.querySelector('[name="' + field + '"]');
                if (!input) continue;
                
                const value = input.value.trim();
                const fieldRules = rules[field];
                
                // 验证字段
                for (const rule in fieldRules) {
                    let errorMessage = null;
                    
                    switch(rule) {
                        case 'required':
                            if (fieldRules[rule] && value === '') {
                                errorMessage = '此字段为必填项';
                            }
                            break;
                        case 'minLength':
                            if (value.length < fieldRules[rule]) {
                                errorMessage = '最少需要 ' + fieldRules[rule] + ' 个字符';
                            }
                            break;
                        case 'maxLength':
                            if (value.length > fieldRules[rule]) {
                                errorMessage = '最多允许 ' + fieldRules[rule] + ' 个字符';
                            }
                            break;
                        case 'pattern':
                            if (!new RegExp(fieldRules[rule]).test(value)) {
                                errorMessage = '格式不正确';
                            }
                            break;
                        case 'custom':
                            if (typeof fieldRules[rule] === 'function') {
                                errorMessage = fieldRules[rule](value);
                            }
                            break;
                    }
                    
                    // 如果有错误，显示错误信息
                    if (errorMessage) {
                        isValid = false;
                        if (!firstInvalidField) {
                            firstInvalidField = input;
                        }
                        
                        const errorElement = document.createElement('div');
                        errorElement.className = 'error-message';
                        errorElement.textContent = errorMessage;
                        input.parentNode.appendChild(errorElement);
                        
                        break; // 一个字段只显示一个错误
                    }
                }
            }
            
            // 如果验证通过，调用回调函数
            if (isValid && typeof submitCallback === 'function') {
                submitCallback(form);
            } else if (firstInvalidField) {
                firstInvalidField.focus();
            }
        });
    },
    
    /**
     * 动态加载内容
     * @param {string} url - 请求URL
     * @param {string} containerSelector - 容器选择器
     * @param {Function} successCallback - 成功回调函数(可选)
     * @param {boolean} showLoader - 是否显示加载动画
     */
    loadContent: function(url, containerSelector, successCallback, showLoader) {
        const container = document.querySelector(containerSelector);
        if (!container) return;
        
        // 显示加载动画
        if (showLoader !== false) {
            container.innerHTML = '<div class="loader"></div>';
        }
        
        // 发送请求
        fetch(url)
            .then(response => response.text())
            .then(html => {
                container.innerHTML = html;
                if (typeof successCallback === 'function') {
                    successCallback(html);
                }
            })
            .catch(error => {
                console.error('加载内容失败:', error);
                container.innerHTML = '<div class="error-message">加载失败，请稍后重试</div>';
            });
    }
};

// 添加通用样式
const style = document.createElement('style');
style.textContent = `
.notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-20px);
    padding: 10px 20px;
    border-radius: 5px;
    color: white;
    font-weight: bold;
    z-index: 9999;
    opacity: 0;
    transition: all 0.3s ease;
}
.notification.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}
.notification-success {
    background-color: #4CAF50;
}
.notification-error {
    background-color: #F44336;
}
.notification-info {
    background-color: #2196F3;
}
.error-message {
    color: #F44336;
    font-size: 12px;
    margin-top: 5px;
}
.loader {
    border: 3px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top: 3px solid var(--secondary-color);
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
`;
document.head.appendChild(style); 