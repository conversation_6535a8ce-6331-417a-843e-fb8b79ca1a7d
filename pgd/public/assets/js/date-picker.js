/**
 * 自定义日期选择器
 *
 * 使用方法：
 * 1. 在HTML中引入date_picker.html组件
 * 2. 在页面中添加一个触发元素，例如：<div class="date-picker" id="datePicker">01/01/2023 - 01/01/2023</div>
 * 3. 初始化日期选择器：
 *    var datePicker = new DatePicker({
 *        triggerElement: '#datePicker',  // 触发元素选择器
 *        onDateRangeChange: function(startDate, endDate) {  // 日期范围变化回调
 *            console.log('日期范围变化:', startDate, endDate);
 *        }
 *    });
 */

class DatePicker {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     * @param {string} options.triggerElement 触发元素选择器
     * @param {Function} options.onDateRangeChange 日期范围变化回调
     */
    constructor(options) {
        this.options = Object.assign({
            triggerElement: '#datePicker',
            onDateRangeChange: null
        }, options);

        // 日期选择器相关元素
        this.triggerElement = document.querySelector(this.options.triggerElement);
        this.datePickerModal = document.getElementById('datePickerModal');
        this.cancelDateBtn = document.getElementById('cancelDateBtn');
        this.confirmDateBtn = document.getElementById('confirmDateBtn');

        // 日期滚动区域
        this.startYearScroll = document.getElementById('startYearScroll');
        this.startMonthScroll = document.getElementById('startMonthScroll');
        this.startDayScroll = document.getElementById('startDayScroll');
        this.endYearScroll = document.getElementById('endYearScroll');
        this.endMonthScroll = document.getElementById('endMonthScroll');
        this.endDayScroll = document.getElementById('endDayScroll');

        // 当前选中的日期
        this.selectedStartDate = new Date();
        this.selectedEndDate = new Date();

        // 初始化
        this.init();
    }

    /**
     * 初始化日期选择器
     */
    init() {
        // 初始化日期选择器
        this.initDatePicker();

        // 绑定事件
        this.bindEvents();

        // 更新显示的日期范围
        this.updateDateRangeDisplay();

        // 确保所有选中的项目都居中显示
        this.centerSelectedItems();
    }

    /**
     * 初始化日期选择器
     */
    initDatePicker() {
        // 生成年份选项（从当前年份-2年到当前年份，共3年）
        const currentYear = new Date().getFullYear();
        // 只显示3年：前年、去年和今年
        for (let year = currentYear - 2; year <= currentYear; year++) {
            // 开始日期年份
            const startYearItem = document.createElement('div');
            startYearItem.className = 'date-item';
            startYearItem.textContent = year;
            startYearItem.dataset.value = year;
            this.startYearScroll.appendChild(startYearItem);

            // 结束日期年份
            const endYearItem = document.createElement('div');
            endYearItem.className = 'date-item';
            endYearItem.textContent = year;
            endYearItem.dataset.value = year;
            this.endYearScroll.appendChild(endYearItem);

            // 如果是当前年份，设为选中状态
            if (year === currentYear) {
                startYearItem.classList.add('selected');
                endYearItem.classList.add('selected');
            }

            // 添加点击事件
            startYearItem.addEventListener('click', () => {
                this.selectItem(this.startYearScroll, startYearItem);
                this.updateStartDate();
            });

            endYearItem.addEventListener('click', () => {
                this.selectItem(this.endYearScroll, endYearItem);
                this.updateEndDate();
            });
        }

        // 生成月份选项（1-12月）
        for (let month = 1; month <= 12; month++) {
            // 开始日期月份
            const startMonthItem = document.createElement('div');
            startMonthItem.className = 'date-item';
            startMonthItem.textContent = month < 10 ? '0' + month : month;
            startMonthItem.dataset.value = month;
            this.startMonthScroll.appendChild(startMonthItem);

            // 结束日期月份
            const endMonthItem = document.createElement('div');
            endMonthItem.className = 'date-item';
            endMonthItem.textContent = month < 10 ? '0' + month : month;
            endMonthItem.dataset.value = month;
            this.endMonthScroll.appendChild(endMonthItem);

            // 如果是当前月份，设为选中状态
            if (month === this.selectedStartDate.getMonth() + 1) {
                startMonthItem.classList.add('selected');
            }

            if (month === this.selectedEndDate.getMonth() + 1) {
                endMonthItem.classList.add('selected');
            }

            // 添加点击事件
            startMonthItem.addEventListener('click', () => {
                this.selectItem(this.startMonthScroll, startMonthItem);
                this.updateStartDays();
                this.updateStartDate();
            });

            endMonthItem.addEventListener('click', () => {
                this.selectItem(this.endMonthScroll, endMonthItem);
                this.updateEndDays();
                this.updateEndDate();
            });
        }

        // 初始化日期选项
        this.updateStartDays();
        this.updateEndDays();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 打开日期选择器
        if (this.triggerElement) {
            this.triggerElement.addEventListener('click', () => {
                this.datePickerModal.style.display = 'flex';

                // 确保在打开日期选择器时，所有选中的项目都居中显示
                // 使用setTimeout确保DOM已经完全渲染
                setTimeout(() => this.centerSelectedItems(), 50);
            });
        }

        // 取消按钮
        if (this.cancelDateBtn) {
            this.cancelDateBtn.addEventListener('click', () => {
                this.datePickerModal.style.display = 'none';
            });
        }

        // 确认按钮
        if (this.confirmDateBtn) {
            this.confirmDateBtn.addEventListener('click', () => {
                // 检查开始日期是否大于结束日期
                if (this.selectedStartDate > this.selectedEndDate) {
                    if (typeof Toast !== 'undefined') {
                        Toast.error(Config.lang.date_start_error || 'Start date cannot be later than end date');
                    } else {
                        console.log(Config.lang.date_start_error || 'Start date cannot be later than end date');
                    }
                    return;
                }

                this.datePickerModal.style.display = 'none';

                // 调用回调函数
                if (typeof this.options.onDateRangeChange === 'function') {
                    this.options.onDateRangeChange(this.selectedStartDate, this.selectedEndDate);
                }

                // 不再显示处理中提示，由调用者处理
            });
        }
    }

    /**
     * 更新开始日期的天数选项
     */
    updateStartDays() {
        // 清空现有的天数选项
        this.startDayScroll.innerHTML = '';

        // 获取选中的年份和月份
        const selectedYear = parseInt(this.startYearScroll.querySelector('.selected').dataset.value);
        const selectedMonth = parseInt(this.startMonthScroll.querySelector('.selected').dataset.value);

        // 计算该月的天数
        const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate();

        // 生成天数选项
        for (let day = 1; day <= daysInMonth; day++) {
            const dayItem = document.createElement('div');
            dayItem.className = 'date-item';
            dayItem.textContent = day < 10 ? '0' + day : day;
            dayItem.dataset.value = day;
            this.startDayScroll.appendChild(dayItem);

            // 如果是当前选中的日期，设为选中状态
            if (day === this.selectedStartDate.getDate() &&
                selectedMonth === this.selectedStartDate.getMonth() + 1 &&
                selectedYear === this.selectedStartDate.getFullYear()) {
                dayItem.classList.add('selected');
            }

            // 添加点击事件
            dayItem.addEventListener('click', () => {
                this.selectItem(this.startDayScroll, dayItem);
                this.updateStartDate();
            });
        }

        // 如果没有选中的日期，选中第一个
        if (!this.startDayScroll.querySelector('.selected')) {
            const firstDay = this.startDayScroll.querySelector('.date-item');
            if (firstDay) {
                firstDay.classList.add('selected');
            }
        }
    }

    /**
     * 更新结束日期的天数选项
     */
    updateEndDays() {
        // 清空现有的天数选项
        this.endDayScroll.innerHTML = '';

        // 获取选中的年份和月份
        const selectedYear = parseInt(this.endYearScroll.querySelector('.selected').dataset.value);
        const selectedMonth = parseInt(this.endMonthScroll.querySelector('.selected').dataset.value);

        // 计算该月的天数
        const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate();

        // 生成天数选项
        for (let day = 1; day <= daysInMonth; day++) {
            const dayItem = document.createElement('div');
            dayItem.className = 'date-item';
            dayItem.textContent = day < 10 ? '0' + day : day;
            dayItem.dataset.value = day;
            this.endDayScroll.appendChild(dayItem);

            // 如果是当前选中的日期，设为选中状态
            if (day === this.selectedEndDate.getDate() &&
                selectedMonth === this.selectedEndDate.getMonth() + 1 &&
                selectedYear === this.selectedEndDate.getFullYear()) {
                dayItem.classList.add('selected');
            }

            // 添加点击事件
            dayItem.addEventListener('click', () => {
                this.selectItem(this.endDayScroll, dayItem);
                this.updateEndDate();
            });
        }

        // 如果没有选中的日期，选中第一个
        if (!this.endDayScroll.querySelector('.selected')) {
            const firstDay = this.endDayScroll.querySelector('.date-item');
            if (firstDay) {
                firstDay.classList.add('selected');
            }
        }
    }

    /**
     * 选择一个项目
     * @param {HTMLElement} container 容器元素
     * @param {HTMLElement} item 选中的项目元素
     */
    selectItem(container, item) {
        // 移除所有选中状态
        container.querySelectorAll('.date-item').forEach(function(el) {
            el.classList.remove('selected');
        });

        // 添加选中状态
        item.classList.add('selected');

        // 计算需要滚动的位置，使选中项居中显示
        const containerHeight = container.clientHeight;
        const itemHeight = item.clientHeight;

        // 计算项目在容器中的位置
        const itemIndex = Array.from(container.children).indexOf(item);

        // 计算滚动位置：项目的位置 - 容器高度的一半 + 项目高度的一半
        const scrollTop = (itemIndex * itemHeight) - (containerHeight / 2) + (itemHeight / 2);

        // 使用平滑滚动
        container.scrollTo({
            top: Math.max(0, scrollTop), // 确保不会滚动到负值
            behavior: 'smooth'
        });
    }

    /**
     * 更新开始日期
     */
    updateStartDate() {
        const year = parseInt(this.startYearScroll.querySelector('.selected').dataset.value);
        const month = parseInt(this.startMonthScroll.querySelector('.selected').dataset.value) - 1; // JavaScript月份从0开始
        const day = parseInt(this.startDayScroll.querySelector('.selected').dataset.value);

        this.selectedStartDate = new Date(year, month, day);
        this.updateDateRangeDisplay();
    }

    /**
     * 更新结束日期
     */
    updateEndDate() {
        const year = parseInt(this.endYearScroll.querySelector('.selected').dataset.value);
        const month = parseInt(this.endMonthScroll.querySelector('.selected').dataset.value) - 1; // JavaScript月份从0开始
        const day = parseInt(this.endDayScroll.querySelector('.selected').dataset.value);

        this.selectedEndDate = new Date(year, month, day);
        this.updateDateRangeDisplay();
    }

    /**
     * 更新日期范围显示
     */
    updateDateRangeDisplay() {
        if (!this.triggerElement) return;

        const formatDate = function(date) {
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        };

        this.triggerElement.textContent = `${formatDate(this.selectedStartDate)} - ${formatDate(this.selectedEndDate)}`;
    }

    /**
     * 确保所有选中的项目都居中显示
     */
    centerSelectedItems() {
        // 获取所有滚动容器
        const scrollContainers = [
            this.startYearScroll, this.startMonthScroll, this.startDayScroll,
            this.endYearScroll, this.endMonthScroll, this.endDayScroll
        ];

        // 对每个容器进行处理
        scrollContainers.forEach(container => {
            if (!container) return;

            const selectedItem = container.querySelector('.selected');
            if (!selectedItem) return;

            // 计算需要滚动的位置，使选中项居中显示
            const containerHeight = container.clientHeight;
            const itemHeight = selectedItem.clientHeight;

            // 计算项目在容器中的位置
            const itemIndex = Array.from(container.children).indexOf(selectedItem);

            // 计算滚动位置：项目的位置 - 容器高度的一半 + 项目高度的一半
            const scrollTop = (itemIndex * itemHeight) - (containerHeight / 2) + (itemHeight / 2);

            // 立即滚动到计算的位置（不使用平滑滚动，避免初始化时的动画）
            container.scrollTop = Math.max(0, scrollTop);
        });
    }

    /**
     * 设置日期范围
     * @param {Date} startDate 开始日期
     * @param {Date} endDate 结束日期
     */
    setDateRange(startDate, endDate) {
        this.selectedStartDate = startDate || new Date();
        this.selectedEndDate = endDate || new Date();

        // 更新显示
        this.updateDateRangeDisplay();

        // 重新初始化日期选择器
        this.startYearScroll.innerHTML = '';
        this.startMonthScroll.innerHTML = '';
        this.startDayScroll.innerHTML = '';
        this.endYearScroll.innerHTML = '';
        this.endMonthScroll.innerHTML = '';
        this.endDayScroll.innerHTML = '';

        this.initDatePicker();
        this.centerSelectedItems();
    }

    /**
     * 获取当前选中的日期范围
     * @returns {Object} 包含开始日期和结束日期的对象
     */
    getDateRange() {
        return {
            startDate: this.selectedStartDate,
            endDate: this.selectedEndDate
        };
    }
}

// 如果在全局作用域中，将DatePicker暴露出去
if (typeof window !== 'undefined') {
    window.DatePicker = DatePicker;
}
