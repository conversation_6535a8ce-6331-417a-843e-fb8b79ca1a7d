define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'channels/channels/index' + location.search,
                    add_url: 'channels/channels/add',
                    edit_url: 'channels/channels/edit',
                    del_url: 'channels/channels/del',
                    multi_url: 'channels/channels/multi',
                    import_url: 'channels/channels/import',
                    table: 'channels',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                commonSearch: false, // 关闭搜索栏
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), formatter: function(value, row, index) {
                            // 检查admin_id是否存在，如果不存在则不显示可点击样式
                            if (row.admin_id && row.admin_id > 0) {
                                return '<a href="javascript:;" class="channel-name" style="color:#0d6efd;" data-id="' + row.id + '">' + value + '</a>';
                            } else {
                                return row.name || value || '未设置'; // 如果没有昵称，则显示name或默认文本
                            }
                        }},
                        {field: 'admin_username', title: __('Admin username')},
                        {field: 'total_credit_limit', title: __('Credit limit')},
                        {field: 'deposit_fee_rate', title: __('Deposit fee rate')},
                        {field: 'withdraw_fee_rate', title: __('Withdraw fee rate')},
                        {field: 'api_fee_rate', title: __('Api fee rate')},
                        {field: 'allow_withdrawal', title: __('Allow withdrawal'), formatter: Table.api.formatter.toggle, searchList: {1: __('是'), 0: __('否')}},
                        {field: 'allow_hidden_player', title: __('Allow hidden player'), formatter: Table.api.formatter.toggle, searchList: {1: __('是'), 0: __('否')}},
                        {field: 'allow_change_player_credit', title: __('Allow Change Player Credit'), formatter: Table.api.formatter.toggle, searchList: {1: __('是'), 0: __('否')}},
                        {field: 'allow_treasure_box', title: __('Allow treasure box'), formatter: Table.api.formatter.toggle, searchList: {1: __('是'), 0: __('否')}},
                        {field: 'is_active', title: __('Is active'), formatter: Table.api.formatter.toggle, searchList: {1: __('启用'), 0: __('未启用')}},
                        {field: 'created_at', title: __('Created at'), formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, fixed: 'right', events: Table.api.events.operate, formatter: Table.api.formatter.operate, buttons: [
                            {name: __('Channel Config'), text: __('Channel Config'), icon: 'fa fa-cog', classname: 'btn btn-xs btn-info btn-config'},
                            {name: __('Edit Credit'), text: __('Edit Credit'), icon: 'fa fa-money', classname: 'btn btn-xs btn-success btn-edit'},
                            {name: __('Edit'), text: __('Edit'), icon: 'fa fa-pencil', classname: 'btn btn-xs btn-primary btn-editone', url: 'channels/channels/edit', extend: 'data-toggle="tooltip"'},
                            {name: __('Unbind Google'), text: __('Unbind Google'), icon: 'fa fa-google', classname: 'btn btn-xs btn-warning btn-unbind-google', visible: function(row) {
                                return row.two_factor_key && row.two_factor_key !== '';
                            }}
                        ]}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 添加渠道名称点击事件
            $(document).on('click', '.channel-name', function() {
                var id = $(this).data('id');
                Fast.api.ajax({
                    url: 'channels/channels/login',
                    data: {ids: id}
                }, function (data) {
                    if (data.url) {
                        // 在新窗口中打开渠道后台
                        var win = window.open(data.url, '_blank');
                        // 如果浏览器阻止了弹出窗口，提供一个链接
                        if (!win || win.closed || typeof win.closed == 'undefined') {
                            Layer.alert(__('Browser blocked popup window. Please click <a href="' + data.url + '" target="_blank">here</a> to open channel dashboard.'), {
                                title: __('Open channel dashboard'),
                                btn: [__('OK')],
                                icon: 0,
                                closeBtn: 0
                            });
                        }
                    }
                    return false; // 阻止默认的成功回调
                });
            });

            // 绑定按钮点击事件
            $(document).on('click', '.btn-config', function() {
                var index = $(this).data('row-index');
                var row = table.bootstrapTable('getData')[index];
                var configDialog = parent.Layer.open({
                    title: __('Channel Config'),
                    type: 1,
                    area: ['800px', '600px'],
                    maxmin: true,
                    content: '<div class="form-horizontal" style="padding: 20px;">' +
                        '<div class="form-group">' +
                            '<label class="control-label col-xs-12 col-sm-3">' + __('Channel Name') + ':</label>' +
                            '<div class="col-xs-12 col-sm-8">' +
                                '<p class="form-control-static">' + (row.name || __('Not set')) + '</p>' +
                            '</div>' +
                        '</div>' +
                        // 添加更多配置项
                    '</div>',
                    btn: [__('Confirm'), __('Cancel')],
                    yes: function (index, layero) {
                        parent.Layer.close(index);
                    }
                });
            });

            // 修改额度按钮点击事件 - 使用HTML视图表单
            $(document).on('click', '.btn-edit', function() {
                var index = $(this).data('row-index');
                var row = table.bootstrapTable('getData')[index];

                // 打开修改额度页面
                Fast.api.open('channels/channels/updateLimit/ids/' + row.id, __('Update Credit Limit'), {
                    callback: function(data) {
                        table.bootstrapTable('refresh');
                    }
                });
            });

            // 添加解绑谷歌按钮点击事件
            $(document).on('click', '.btn-unbind-google', function(e) {
                e.stopPropagation();

                // 直接从行数据获取ID
                var index = $(this).closest('tr').data('index');
                var row = table.bootstrapTable('getData')[index];

                if (!row || !row.id) {
                    Layer.msg(__('Cannot get channel ID'), {icon: 2, time: 1500});
                    return;
                }

                var id = row.id;

                Layer.confirm(__('Confirm unbind Google verification'), {icon: 3, title: __('Tip')}, function (index) {
                    Fast.api.ajax({
                        url: 'channels/channels/unbindGoogle',
                        data: {id: id}
                    }, function (data, ret) {
                        Layer.close(index);
                        table.bootstrapTable('refresh');
                        return false;
                    }, function(data, ret) {
                        return false;
                    });
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"), null, function(success, error){
                    // 添加自定义验证
                    if ($("input[name='row[name]']").length > 0) {
                        var name = $("input[name='row[name]']").val();

                        if (!name.trim()) {
                            Layer.msg(__('Channel name cannot be empty'), {icon: 2, time: 1500});
                            return false;
                        }
                    }
                    return true;
                });
            }
        }
    };
    return Controller;
});
