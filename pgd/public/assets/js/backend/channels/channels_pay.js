define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'channels/channels_pay/index' + location.search,
                    // 移除 add, edit, del 方法
                    // add_url: 'channels/channels_pay/add',
                    // edit_url: 'channels/channels_pay/edit',
                    // del_url: 'channels/channels_pay/del',
                    // multi_url: 'channels/channels_pay/multi',
                    table: 'channels_pay',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                searchFormVisible: true,
                search: false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'channel_id', title: __('Channel id')},
                        {field: 'channel_name', title: __('Channel name')},
                        {field: 'amount_change', title: __('Amount change'), operate:'BETWEEN'},
                        {field: 'status', title: __('Status'), searchList: {
                            'pending': __('Pending'),
                            'approved': __('Approved'),
                            'rejected': __('Rejected')
                        }, formatter: Table.api.formatter.status, operate: 'SELECT'},
                        {field: 'operator', title: __('Operator')},
                        {field: 'auditor', title: __('Auditor')},
                        {field: 'create_time', title: __('Create time'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 绑定事件
            Table.api.bindevent(table);
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
