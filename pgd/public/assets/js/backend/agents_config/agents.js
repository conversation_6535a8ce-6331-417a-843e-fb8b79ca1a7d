define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'agents_config/agents/index' + location.search,
                    add_url: 'agents_config/agents/add',
                    edit_url: 'agents_config/agents/edit',
                    multi_url: 'agents_config/agents/multi',
                    import_url: 'agents_config/agents/import',
                    table: 'agents'
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                commonSearch: true, // 开启搜索栏
                searchFormVisible: true, // 默认展开
                showToggle: true,
                showColumns: true,
                showExport: true,
                 commonSearch: true, // 开启搜索栏
                searchFormVisible: true, // 默认展开
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: '='},
                        {field: 'agent_name', title: __('Agent name'), operate: 'LIKE', formatter: function(value, row, index) {
                            return '<a href="javascript:;" class="btn-agent-login" data-id="' + row.id + '" title="' + __('Login as this agent') + '">' + value + '</a>';
                        }},
                        {field: 'channel_name', title: __('Channel name'), operate: 'LIKE'},
                        {field: 'username', title: __('Username'), operate: 'LIKE'},
                        {field: 'game_credit', title: __('Game credits'), operate: false},
                        {field: 'used_game_credit', title: __('Used game credits'), operate: false},
                        {field: 'withdraw_credit', title: __('Withdraw credits'), operate: false},
                        {field: 'used_withdraw_credit', title: __('Used withdraw credits'), operate: false},
                        {field: 'withdraw_permission', title: __('Withdraw permission'), formatter: Table.api.formatter.toggle, operate: false},
                        {field: 'can_change_password', title: __('Can change password'), formatter: Table.api.formatter.toggle, operate: false},
                        {field: 'agent_type', title: __('Agent level'), operate: false},
                        {field: 'promotion_link', title: __('Promotion link'), operate: false, formatter: function(value, row, index) {
                            return '<input type="text" class="form-control input-sm" value="' + value + '" readonly style="width:100%;" onclick="this.select();">';
                        }},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate, buttons: [
                            {name: 'edit', icon: 'fa fa-pencil', title: __('Edit'), extend: 'data-toggle="tooltip"', classname: 'btn btn-xs btn-success btn-editone'},
                            {name: 'month_bank', icon: 'fa fa-bar-chart', title: __('Monthly Report'), extend: 'data-toggle="tooltip"', classname: 'btn btn-xs btn-info btn-month-bank'}
                        ]}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定业务员登录点击事件
            $(document).on('click', '.btn-agent-login', function () {
                var id = $(this).data('id');
                Fast.api.ajax({
                    url: 'agents_config/agents/login',
                    data: {ids: id}
                }, function (data) {
                    if (data.url) {
                        // 在新窗口中打开业务员后台
                        var win = window.open(data.url, '_blank');
                        // 如果浏览器阻止了弹出窗口，提供一个链接
                        if (!win || win.closed || typeof win.closed == 'undefined') {
                            Layer.alert(__('Browser blocked popup window. Please click <a href="' + data.url + '" target="_blank">here</a> to open agent dashboard.'), {
                                title: __('Open agent dashboard'),
                                btn: [__('OK')],
                                icon: 0,
                                closeBtn: 0
                            });
                        }
                    }
                    return false; // 阻止默认的成功回调
                });
            });

            // 绑定每月财报按钮点击事件
            $(document).on('click', '.btn-month-bank', function () {
                var row = Table.api.getrowbyindex(table, $(this).data("row-index"));
                var agentId = row.id;

                // 打开弹窗显示业务员的每月财报
                Fast.api.open('mine/month_bank/index?agent_id=' + agentId, __('Monthly Report') + ' - ' + row.agent_name, {
                    area: ['80%', '90%'],
                    callback: function (data) {
                        // 关闭弹窗后可能需要刷新表格
                        table.bootstrapTable('refresh');
                    }
                });
                return false;
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
