define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {
    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'agents_config/agents_reports/index' + location.search,
                    table: 'daily_statistics',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                search: false,
                searchFormVisible: true,
                showToggle: false,
                showColumns: false,
                showExport: false,
                commonSearch: true,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'stat_date', title: __('Report_date'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'agent_id', title: __('Agent_id'), operate: '='},
                        {field: 'new_active_users_text', title: __('New_active_users'), operate: false},
                        {field: 'first_deposit_players', title: __('First_deposit_users'), operate: false},
                        {field: 'deposit_info_text', title: __('Deposit_info'), operate: false},
                        {field: 'withdraw_info_text', title: __('Withdraw_info'), operate: false},
                        {field: 'total_deposit', title: __('Total_deposit'), operate: 'BETWEEN'},
                        {field: 'total_withdraw', title: __('Total_withdraw'), operate: 'BETWEEN'}
                    ]
                ]
            });

            // 绑定事件
            Table.api.bindevent(table);
        },

        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
