define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'payment/deposit_orders/index' + location.search,
                    // add_url: 'payment/deposit_orders/add',
                    // edit_url: 'payment/deposit_orders/edit',
                    // del_url: 'payment/deposit_orders/del',
                    multi_url: 'payment/deposit_orders/multi',
                    import_url: 'payment/deposit_orders/import',
                    table: 'deposit_orders',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                commonSearch: true, // 开启搜索栏
                searchFormVisible: true, // 默认展开
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'payment_status', title: __('Payment_status'), searchList: {0: __('Unpaid'), 1: __('Paid'), 2: __('Payment failed')}, formatter: Table.api.formatter.status, custom: {0: 'warning', 1: 'success', 2: 'danger'}},
                        {field: 'player_id', title: __('Player_id'), formatter: function(value, row, index) {
                            // 使玩家ID可点击，唤起玩家信息小窗
                            return '<a href="javascript:;" class="btn-player-details" data-id="' + value + '">' + value + '</a>';
                        }},
                        {field: 'referrer_id', title: __('Referrer_id'), formatter: function(value, row, index) {
                            // 如果有推荐人ID，使其可点击
                            if (value && value > 0) {
                                return '<a href="javascript:;" class="btn-player-details" data-id="' + value + '">' + value + '</a>';
                            }
                            return value || '-';
                        }},
                        {field: 'player_tag_name', title: __('Player_tag_name'), operate: false},
                        {field: 'tag_id', title: __('Player_tag_name'), searchList: function (column) {
                            return Template('playertagtemplate', {});
                        }, visible: false, operate: '='},
                        {field: 'agent_name', title: __('Agent_id') + '(' + __('Agent_name') + ')', operate: false},
                        {field: 'channel_name', title: __('Channel_id') + '(' + __('Channel_name') + ')', operate: false},
                        {field: 'agent_id', title: __('Agent_id'), searchList: function (column) {
                            return Template('agenttemplate', {});
                        }, visible: false},
                        {field: 'channel_id', title: __('Channel_id'), visible: false, operate: '='},
                        {field: 'payment_channel_name', title: __('Channel_name'), operate: false},
                        {field: 'channel_code', title: __('Channel_code'), operate: 'LIKE', visible: false},
                        {field: 'amount', title: __('Amount'), operate:'BETWEEN'},
                        {field: 'gift_amount', title: __('Gift_amount'), operate:'BETWEEN'},
                        {field: 'service_fee', title: __('Service_fee'), operate:'BETWEEN', visible: false},
                        {field: 'third_order_no', title: __('Third_order_no'), operate: 'LIKE'},
                        {field: 'is_manual_fill', title: __('Is_manual_fill'), searchList: {1: __('Yes'), 0: __('No')}, formatter: Table.api.formatter.status, custom: {1: 'success', 0: 'gray'}},
                        {field: 'remark', title: __('Remark'), operate: 'LIKE'},
                        {field: 'created_at', title: __('Created_at'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'paid_at', title: __('Paid_at'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, buttons: [
                            {
                                name: 'manual_fill',
                                text: __('Manual fill'),
                                title: __('Manual fill'),
                                classname: 'btn btn-xs btn-success btn-ajax',
                                icon: 'fa fa-check',
                                url: 'payment/deposit_orders/manual_fill',
                                confirm: __('Confirm manual fill?'),
                                success: function (data, ret) {
                                    Layer.alert(ret.msg || __('Manual fill successful'));
                                    $(".btn-refresh").trigger("click");
                                },
                                error: function (data, ret) {
                                    Layer.alert(ret.msg || __('Operation failed'));
                                    return false;
                                },
                                visible: function (row) {
                                    return row.payment_status == 0 && row.is_manual_fill == 0; // 只有未支付且未补单的订单才显示补单按钮
                                },
                                extend: 'data-params=\'{\'ids\':\'__id__\'}\''
                            },
                            {
                                name: 'query_status',
                                text: __('Query status'),
                                title: __('Query payment status'),
                                classname: 'btn btn-xs btn-info btn-ajax',
                                icon: 'fa fa-search',
                                url: 'payment/deposit_orders/query_status',
                                success: function (data, ret) {
                                    Layer.alert(ret.msg || __('Query success, payment status: pending'));
                                    $(".btn-refresh").trigger("click");
                                },
                                error: function (data, ret) {
                                    Layer.alert(ret.msg || __('Operation failed'));
                                    return false;
                                },

                                extend: 'data-params=\'{\'ids\':\'__id__\'}\''
                            }
                        ], formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定玩家ID点击事件
            $(document).on('click', '.btn-player-details', function () {
                var id = $(this).data('id');
                // 打开弹窗显示玩家详情，包含多个标签页
                Fast.api.open('players/players/player_detail?ids=' + id, id + '玩家信息', {
                    area: ['80%', '90%'],
                    callback: function (data) {
                        // 关闭弹窗后可能需要刷新表格
                        table.bootstrapTable('refresh');
                    }
                });
                return false;
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
