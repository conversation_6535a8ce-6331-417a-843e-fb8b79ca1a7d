define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'payment/payment_plans/index' + location.search,
                    add_url: 'payment/payment_plans/add',
                    edit_url: 'payment/payment_plans/edit',
                    del_url: 'payment/payment_plans/del',
                    multi_url: 'payment/payment_plans/multi',
                    import_url: 'payment/payment_plans/import',
                    table: 'payment_plans',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                commonSearch: true,
                searchFormVisible: false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'amount', title: __('Amount'), operate:'BETWEEN'},
                        {field: 'gift_amount', title: __('Gift_amount'), operate:'BETWEEN'},
                        {field: 'is_active', title: __('Is_active'), searchList: {1: __('Yes'), 0: __('No')}, formatter: Table.api.formatter.toggle},
                        {field: 'is_hot', title: __('Is_hot'), searchList: {1: __('Yes'), 0: __('No')}, formatter: Table.api.formatter.toggle},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
