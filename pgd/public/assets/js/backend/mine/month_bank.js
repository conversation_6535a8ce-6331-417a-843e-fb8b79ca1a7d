define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'bootstrap-datetimepicker'], function ($, undefined, Backend, Table, Form) {
    var Controller = {
        index: function () {
            // 初始化月份选择器
            $('#month').datetimepicker({
                format: 'YYYY-MM',
                locale: 'zh-cn',
                defaultDate: new Date()
            });

            // 获取URL中的agent_id参数
            var agentId = Fast.api.query('agent_id');

            // 加载数据函数
            function loadData() {
                var month = $('#month').val();
                var data = {month: month};

                // 如果有agent_id参数，则添加到请求中
                if (agentId) {
                    data.agent_id = agentId;
                }

                $.ajax({
                    url: 'mine/month_bank/index',
                    type: 'GET',
                    data: data,
                    dataType: 'json',
                    success: function (response) {
                        var data = response.data;
                        console.log('接收到的数据:', data); // 添加日志以便调试

                        // 更新显示数据，确保将字符串转换为数字
                        $('#total_deposit').text(parseFloat(data.total_deposit || 0).toFixed(2));
                        $('#total_withdraw').text(parseFloat(data.total_withdraw || 0).toFixed(2));
                        $('#deposit_fee').text(parseFloat(data.deposit_fee || 0).toFixed(2));
                        $('#withdraw_fee').text(parseFloat(data.withdraw_fee || 0).toFixed(2));
                        $('#api_fee').text(parseFloat(data.api_fee || 0).toFixed(2));
                        $('#total_profit').text(parseFloat(data.total_profit || 0).toFixed(2));

                        // 如果有汇率，重新计算转换后的利润
                        var rate = $('#rate').val();
                        if (rate) {
                            var profit = parseFloat(data.total_profit || 0);
                            var rateValue = parseFloat(rate);
                            if (!isNaN(rateValue) && rateValue > 0) {
                                var converted = profit / rateValue;
                                $('#converted_profit').text(converted.toFixed(2));
                            }
                        }
                    }
                });
            }

            // 搜索按钮点击事件
            $('#search').on('click', function () {
                loadData();
            });

            // 重新计算按钮点击事件
            $('#calculate').on('click', function () {
                var profit = parseFloat($('#total_profit').text().replace(/,/g, '') || 0);
                var rate = parseFloat($('#rate').val() || 0);
                if (!isNaN(rate) && rate > 0) {
                    var converted = profit / rate;
                    $('#converted_profit').text(converted.toFixed(2));
                } else {
                    Toastr.error('请输入有效的汇率');
                }
            });

            // 初始加载数据
            loadData();
        }
    };
    return Controller;
});