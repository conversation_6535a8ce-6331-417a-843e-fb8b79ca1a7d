define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {
    var Controller = {
        index: function () {
            // 加载链接函数
            function loadLink() {
                $.ajax({
                    url: 'mine/agents_link/index',
                    type: 'GET',
                    dataType: 'json',
                    success: function (response) {
                        $('#promotion_link').val(response.link);
                    }
                });
            }

            // 复制按钮点击事件
            $('#copy').on('click', function () {
                var link = $('#promotion_link').val();
                var tempInput = $('<input>');
                $('body').append(tempInput);
                tempInput.val(link).select();
                document.execCommand('copy');
                tempInput.remove();
                Layer.msg('复制成功');
            });

            // 隐藏重置按钮，因为后端已移除reset方法
            $('#reset').hide();

            // 初始加载链接
            loadLink();
        }
    };
    return Controller;
});