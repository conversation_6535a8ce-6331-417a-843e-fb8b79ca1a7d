define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'bootstrap-datetimepicker', 'moment'], function ($, undefined, Backend, Table, Form, undefined, moment) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'activity/rebate_config/index',
                    add_url: 'activity/rebate_config/add',
                    edit_url: 'activity/rebate_config/edit',
                    del_url: 'activity/rebate_config/del',
                    multi_url: 'activity/rebate_config/multi',
                    table: 'rebate_config',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'activity_name', title: __('Activity_name')},
                        {field: 'channel_text', title: __('Channel_id')},
                        {field: 'level1_first_deposit_rate', title: __('Level1_first_deposit_rate')},
                        {field: 'level1_first_deposit_max', title: __('Level1_first_deposit_max')},
                        {field: 'level2_first_deposit_rate', title: __('Level2_first_deposit_rate')},
                        {field: 'level2_first_deposit_max', title: __('Level2_first_deposit_max')},
                        {field: 'min_deposit_amount', title: __('Min_deposit_amount')},
                        {field: 'start_date', title: __('Start_date')},
                        {field: 'end_date', title: __('End_date')},
                        {field: 'is_active', title: __('Is_active'), searchList: {"1":__('Yes'),"0":__('No')}, formatter: Table.api.formatter.status},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            // 获取渠道列表
            $.ajax({
                url: 'activity/rebate_config/get_channel_list',
                type: 'GET',
                dataType: 'json',
                success: function (response) {
                    console.log('Received channel response:', response);
                    var select = $("#c-channel_id");
                    select.empty();
                    
                    if (response.code === 1 && Array.isArray(response.data)) {
                        response.data.forEach(function(item) {
                            console.log('Adding option:', item.value, item.text);
                            select.append(new Option(item.text, item.value));
                        });
                        
                        // 初始化 bootstrap-select
                        if (typeof $.fn.selectpicker === 'function') {
                            select.selectpicker({
                                width: '100%',
                                liveSearch: true,
                                size: 10,
                                style: 'btn-default',
                                title: '请选择渠道'
                            });
                            select.selectpicker('refresh');
                        } else {
                            console.warn('Bootstrap-select not loaded, falling back to native select');
                            select.addClass('form-control');
                        }
                        
                        // 触发 change 事件以更新表单状态
                        select.trigger('change');
                    } else {
                        console.error('Invalid channel data received:', response);
                        Layer.msg('获取渠道列表失败，请刷新重试');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error fetching channels:', error);
                    Layer.msg('获取渠道列表失败，请刷新页面重试');
                }
            });

            // 初始化日期时间选择器
            $('.datetimepicker').datetimepicker({
                format: 'YYYY-MM-DD HH:mm:ss',
                icons: {
                    time: 'fa fa-clock-o',
                    date: 'fa fa-calendar',
                    up: 'fa fa-chevron-up',
                    down: 'fa fa-chevron-down',
                    previous: 'fa fa-chevron-left',
                    next: 'fa fa-chevron-right',
                    today: 'fa fa-history',
                    clear: 'fa fa-trash',
                    close: 'fa fa-remove'
                },
                showTodayButton: true,
                showClose: true,
                useCurrent: false
            }).on('dp.change', function (e) {
                var date = e.date ? e.date.format('YYYY-MM-DD HH:mm:ss') : '';
                console.log('Date changed:', date);
                $(this).val(date);
                validateDates();
            });

            // 设置默认日期
            var now = new Date();
            var tomorrow = new Date(now.getTime() + (24 * 60 * 60 * 1000));
            $('#c-start_date').val(formatDate(now));
            $('#c-end_date').val(formatDate(tomorrow));

            var form = $("form[role=form]");

            // 格式化日期函数
            function formatDate(date) {
                var year = date.getFullYear();
                var month = (date.getMonth() + 1).toString().padStart(2, '0');
                var day = date.getDate().toString().padStart(2, '0');
                var hours = date.getHours().toString().padStart(2, '0');
                var minutes = date.getMinutes().toString().padStart(2, '0');
                var seconds = date.getSeconds().toString().padStart(2, '0');
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            }

            // 日期验证函数
            function validateDates() {
                var startDate = $("#c-start_date").val();
                var endDate = $("#c-end_date").val();
                
                if (!startDate || !endDate) {
                    return true; // 如果有一个日期未填，暂时返回 true
                }

                var start = new Date(startDate);
                var end = new Date(endDate);
                
                if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                    Layer.msg('日期格式不正确，请使用正确的日期格式');
                    return false;
                }

                if (start > end) {
                    Layer.msg('结束日期必须大于等于开始日期');
                    return false;
                }

                return true;
            }

            // 数值验证函数
            function validateNumbers() {
                var numberFields = {
                    'level1_first_deposit_rate': '一级用户首充返利率',
                    'level1_first_deposit_max': '一级用户首充返现最大金额',
                    'level2_first_deposit_rate': '二级用户首充返利率',
                    'level2_first_deposit_max': '二级用户首充返现最大金额',
                    'min_deposit_amount': '最低充值金额'
                };

                for (var field in numberFields) {
                    var input = $("#c-" + field);
                    var value = input.val();
                    
                    if (!value && value !== '0') {
                        Layer.msg(numberFields[field] + '不能为空');
                        input.focus();
                        return false;
                    }

                    value = parseFloat(value);
                    if (isNaN(value) || value < 0) {
                        Layer.msg(numberFields[field] + '必须是大于等于0的数字');
                        input.focus();
                        return false;
                    }

                    if (field.includes('rate') && value > 100) {
                        Layer.msg(numberFields[field] + '不能大于100');
                        input.focus();
                        return false;
                    }
                }
                return true;
            }

            // 表单提交前的完整验证
            function validateForm() {
                // 验证活动名称
                var activityName = $("#c-activity_name").val();
                if (!activityName || !activityName.trim()) {
                    Layer.msg('请输入活动名称');
                    $("#c-activity_name").focus();
                    return false;
                }

                // 验证渠道选择
                var channelId = $("#c-channel_id").val();
                if (!channelId) {
                    Layer.msg('请选择渠道');
                    return false;
                }

                // 验证日期
                if (!validateDates()) {
                    return false;
                }

                // 验证数值字段
                if (!validateNumbers()) {
                    return false;
                }

                return true;
            }

            // 绑定表单验证
            Form.api.bindevent(form, function(data, ret) {
                console.log('Form submit data:', data);
                
                // 提交前验证
                if (!validateForm()) {
                    return false;
                }

                if (ret.code === 1) {
                    Layer.msg(ret.msg || '保存成功', {icon: 1});
                    Layer.closeAll();
                    return true;
                } else {
                    Layer.msg(ret.msg || '保存失败，请重试', {icon: 2});
                    return false;
                }
            }, function(data, ret) {
                console.error('Form submit error:', ret);
                Layer.msg(ret.msg || '保存失败，请检查表单数据是否正确', {icon: 2});
                return false;
            });

            // 为数值输入框添加实时验证
            $('input[type="number"]').on('input', function() {
                var field = $(this).attr('id').replace('c-', '');
                var value = parseFloat($(this).val());
                
                if (this.value === '') {
                    return; // 允许清空输入框
                }
                
                if (isNaN(value) || value < 0) {
                    Layer.msg('请输入大于等于0的数字');
                    $(this).val('');
                    return;
                }
                
                if (field.includes('rate') && value > 100) {
                    Layer.msg('返利率不能大于100');
                    $(this).val('100');
                    return;
                }
            });

            // 添加日期输入框的焦点事件
            $('.datetimepicker').on('focus', function() {
                $(this).datetimepicker('show');
            });
        },
        edit: function () {
            Controller.add();
        }
    };
    return Controller;
}); 