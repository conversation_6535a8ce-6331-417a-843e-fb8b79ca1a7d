define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'game/vip_config/index' + location.search,
                    add_url: 'game/vip_config/add',
                    edit_url: 'game/vip_config/edit',
                    del_url: 'game/vip_config/del',
                    multi_url: 'game/vip_config/multi',
                    import_url: 'game/vip_config/import',
                    table: 'vip_config',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'level',
                sortName: 'level',
                fixedColumns: true,
                fixedRightNumber: 1,
                commonSearch: true,
                searchFormVisible: true,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'level', title: __('Level')},
                        {field: 'turnover_requirement', title: __('Turnover_requirement'), operate:'BETWEEN'},
                        {field: 'deposit_requirement', title: __('Deposit_requirement'), operate:'BETWEEN'},
                        {field: 'daily_max_withdraw_amount', title: __('Daily_max_withdraw_amount'), operate:'BETWEEN'},
                        {field: 'daily_withdrawal_times', title: __('Daily_withdrawal_times')},
                        {field: 'max_withdraw_amount', title: __('Max_withdraw_amount'), operate:'BETWEEN'},
                        {field: 'single_withdraw_limit', title: __('Single_withdraw_limit'), operate:'BETWEEN'},
                        {field: 'daily_deposit_rebate_rate', title: __('Daily_deposit_rebate_rate'), operate:'BETWEEN'},
                        {field: 'level_up_bonus', title: __('Level_up_bonus'), operate:'BETWEEN'},
                        {field: 'daily_bonus', title: __('Daily_bonus'), operate:'BETWEEN'},
                        {field: 'weekly_bonus', title: __('Weekly_bonus'), operate:'BETWEEN'},
                        {field: 'monthly_bonus', title: __('Monthly_bonus'), operate:'BETWEEN'},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
