define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'game/game_providers/index' + location.search,
                    multi_url: 'game/game_providers/multi',
                    table: 'game_providers',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                commonSearch: true, // 开启搜索栏
                searchFormVisible: true, // 默认展开
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'sort', title: __('Sort'), operate: 'BETWEEN', sortable: true},
                        {field: 'remark', title: __('Remark'), operate: 'LIKE'},
                        {field: 'min_amount', title: __('Min_amount'), operate:'BETWEEN'},
                        {field: 'is_active', title: __('Is_active'), formatter: Table.api.formatter.toggle, searchList: {1: __('启用'), 0: __('未启用')}},
                        {field: 'created_at', title: __('Created_at'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updated_at', title: __('Updated_at'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
