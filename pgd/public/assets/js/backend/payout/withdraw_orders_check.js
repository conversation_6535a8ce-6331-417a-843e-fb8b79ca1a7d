define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'payout/withdraw_orders_check/index' + location.search,
                    approve_url: 'payout/withdraw_orders_check/approve',
                    reject_url: 'payout/withdraw_orders_check/reject',
                    table: 'withdraw_orders',
                }
            });

            // 存储业务员是否有提现权限的状态
            var hasWithdrawPermission = true;

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                sortOrder: 'desc',
                fixedColumns: true,
                fixedRightNumber: 1,
                commonSearch: true, // 开启搜索栏
                searchFormVisible: true, // 默认展开
                // 在请求成功后获取权限状态
                responseHandler: function (res) {
                    if (res && res.hasWithdrawPermission !== undefined) {
                        hasWithdrawPermission = res.hasWithdrawPermission;
                    }
                    return res;
                },
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'player_id', title: __('Player ID'), formatter: function(value, row, index) {
                            // 使玩家ID可点击，唤起玩家信息小窗
                            return '<a href="javascript:;" class="btn-player-details" data-id="' + value + '">' + value + '</a>';
                        }},
                        {field: 'referrer_id', title: __('Referrer ID'), formatter: function(value, row, index) {
                            // 如果有推荐人ID，使其可点击
                            if (value && value > 0) {
                                return '<a href="javascript:;" class="btn-player-details" data-id="' + value + '">' + value + '</a>';
                            }
                            return value || '-';
                        }},
                        {field: 'player_tag_name', title: __('Player Tag Name'), operate: false},
                        {field: 'tag_id', title: __('Player Tag Name'), searchList: function (column) {
                            return Template('playertagtemplate', {});
                        }, visible: false, operate: '='},
                        {field: 'agent_name', title: __('Agent ID') + '(' + __('Agent Name') + ')', operate: false},
                        {field: 'channel_name', title: __('Channel ID') + '(' + __('Channel Name') + ')', operate: false},
                        {field: 'agent_id', title: __('Agent ID'), searchList: function (column) {
                            return Template('agenttemplate', {});
                        }, visible: false},
                        {field: 'channel_id', title: __('Channel ID'), visible: false, operate: '='},
                        {field: 'channel_code', title: __('Channel Code'), operate: 'LIKE'},
                        {field: 'amount', title: __('Amount'), operate:'BETWEEN'},
                        {field: 'service_fee', title: __('Service Fee'), operate:'BETWEEN'},
                        {field: 'balance', title: __('Balance'), operate: false},
                        {field: 'total_deposit', title: __('Total Deposit'), operate: false},
                        {field: 'total_withdraw', title: __('Total Withdraw'), operate: false},
                        {field: 'audit_status', title: __('Audit Status'), searchList: {
                            0: __('Status pending'),
                            1: __('Status waiting review')
                        }, formatter: function(value, row, index) {
                            var statusMap = {
                                0: __('Status pending'),
                                1: __('Status waiting review'),
                                2: __('Status approved'),
                                3: __('Status processing'),
                                4: __('Status completed'),
                                5: __('Status rejected refund'),
                                6: __('Status failed refund'),
                                7: __('Status rejected confiscate'),
                                8: __('Status virtual')
                            };
                            return statusMap[value] !== undefined ? statusMap[value] : value;
                        }},
                        {field: 'withdraw_account_id', title: __('Withdraw Account ID')},
                        {field: 'remark', title: __('Order Remark'), operate: 'LIKE'},
                        {field: 'player_remark', title: __('Player Remark'), operate: 'LIKE'},
                        {field: 'created_at', title: __('Created At'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table,
                            buttons: [
                                {
                                    name: 'approve',
                                    text: __('Pass'),
                                    title: __('Pass'),
                                    classname: 'btn btn-xs btn-success btn-ajax',
                                    icon: 'fa fa-check',
                                    url: 'payout/withdraw_orders_check/approve',
                                    confirm: __('Confirm approve this order?'),
                                    success: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        $(".btn-refresh").trigger("click");
                                    },
                                    error: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible: function (row) {
                                        return hasWithdrawPermission;
                                    },
                                    extend: 'data-params=\'{"ids":"__id__"}\''
                                },
                                {
                                    name: 'virtual',
                                    text: __('Virtual'),
                                    title: __('Virtual payment'),
                                    classname: 'btn btn-xs btn-info btn-ajax',
                                    icon: 'fa fa-magic',
                                    url: 'payout/withdraw_orders_check/virtual',
                                    confirm: __('Confirm mark this order as virtual payment?'),
                                    success: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        $(".btn-refresh").trigger("click");
                                    },
                                    error: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible: function (row) {
                                        return hasWithdrawPermission;
                                    },
                                    extend: 'data-params=\'{"ids":"__id__"}\''
                                },
                                {
                                    name: 'reject',
                                    text: __('Reject'),
                                    title: __('Reject and refund coins'),
                                    classname: 'btn btn-xs btn-warning btn-ajax',
                                    icon: 'fa fa-times',
                                    url: 'payout/withdraw_orders_check/reject',
                                    confirm: __('Confirm reject this order and refund coins?'),
                                    success: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        $(".btn-refresh").trigger("click");
                                    },
                                    error: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible: function (row) {
                                        return hasWithdrawPermission;
                                    },
                                    extend: 'data-params=\'{"ids":"__id__"}\''
                                },
                                {
                                    name: 'confiscate',
                                    text: __('Confiscate'),
                                    title: __('Reject and confiscate coins'),
                                    classname: 'btn btn-xs btn-danger btn-ajax',
                                    icon: 'fa fa-ban',
                                    url: 'payout/withdraw_orders_check/confiscate',
                                    confirm: __('Confirm reject this order and confiscate coins?'),
                                    success: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        $(".btn-refresh").trigger("click");
                                    },
                                    error: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible: function (row) {
                                        return hasWithdrawPermission;
                                    },
                                    extend: 'data-params=\'{"ids":"__id__"}\''
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定玩家ID点击事件
            $(document).on('click', '.btn-player-details', function () {
                var id = $(this).data('id');
                // 打开弹窗显示玩家详情，包含多个标签页
                Fast.api.open('players/players/player_detail?ids=' + id, id + '玩家信息', {
                    area: ['80%', '90%'],
                    callback: function (data) {
                        // 关闭弹窗后可能需要刷新表格
                        table.bootstrapTable('refresh');
                    }
                });
                return false;
            });

            // 一键虚拟支付按钮事件
            $(document).on("click", ".btn-virtual-all", function () {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Layer.alert(__('请至少选择一行'));
                    return false;
                }

                Layer.confirm(
                    __('确定要将这 %s 个订单标记为虚拟支付吗?', ids.length),
                    {icon: 3, title: __('警告'), offset: 0, shadeClose: true},
                    function (index) {
                        Fast.api.ajax({
                            url: "payout/withdraw_orders_check/virtual",
                            data: {ids: ids}
                        }, function (data, ret) {
                            Layer.alert(ret.msg);
                            Layer.close(index);
                            table.bootstrapTable('refresh');
                            return false;
                        });
                    }
                );
            });

            // 移除原有的批量审核通过和拒绝按钮事件
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
