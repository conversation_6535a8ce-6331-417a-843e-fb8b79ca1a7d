define(['jquery', 'bootstrap', 'table'], function ($, undefined, Table) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'payout/invitation_rewards/index' + location.search,
                    table: 'treasure_box_records',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                sortOrder: 'desc',
                fixedColumns: true,
                fixedRightNumber: 1,
                commonSearch: true, // 开启搜索栏
                searchFormVisible: true, // 默认展开
                search: false, // 禁用默认搜索
                columns: [
                    [
                        {field: 'id', title: __('Id')}, // 显示treasure_box_records表的id字段
                        {field: 'player_id', title: __('Player Id')},
                        {field: 'player_channel_name', title: __('Channel Id'), operate: false}, // 显示玩家所属渠道
                        {field: 'player_agent_name', title: __('Agent Id'), operate: false}, // 显示玩家所属业务员
                        {field: 'config_id', title: __('Config Id')},
                        {field: 'config_invite_count', title: __('Config Invite Count'), operate: false}, // 显示配置中的邀请人数要求
                        {field: 'valid_invited_count', title: __('Valid Invited Count'), operate: false}, // 显示有效邀请人数（充值超过10的不重复下线）
                        {field: 'reward_amount', title: __('Reward Amount')},
                        {field: 'is_claimed', title: __('Is Claimed'), formatter: Table.api.formatter.status, searchList: {1: __('Yes'), 0: __('No')}},
                        {field: 'claimed_at', title: __('Claimed At'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'created_at', title: __('Created At'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        }
    };
    return Controller;
});
