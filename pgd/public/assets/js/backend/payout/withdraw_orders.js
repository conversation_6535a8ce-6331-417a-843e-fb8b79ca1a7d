define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'payout/withdraw_orders/index' + location.search,
                    table: 'withdraw_orders',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                commonSearch: true, // 开启搜索栏
                searchFormVisible: true, // 默认展开
                search: false, // 禁用默认搜索
                showExport: false, // 禁用导出按钮
                showToggle: false, // 禁用切换按钮
                showColumns: false, // 禁用列选择按钮
                showRefresh: true, // 只保留刷新按钮
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'player_id', title: __('Player ID'), formatter: function(value, row, index) {
                            // 使玩家ID可点击，唤起玩家信息小窗
                            return '<a href="javascript:;" class="btn-player-details" data-id="' + value + '">' + value + '</a>';
                        }},
                        {field: 'referrer_id', title: __('Referrer ID'), formatter: function(value, row, index) {
                            // 如果有推荐人ID，使其可点击
                            if (value && value > 0) {
                                return '<a href="javascript:;" class="btn-player-details" data-id="' + value + '">' + value + '</a>';
                            }
                            return value || '-';
                        }},
                        {field: 'player_tag_name', title: __('Player Tag Name'), operate: false},
                        {field: 'tag_id', title: __('Player Tag Name'), searchList: function (column) {
                            return Template('playertagtemplate', {});
                        }, visible: false, operate: '='},
                        {field: 'agent_name', title: __('Agent ID') + '(' + __('Agent Name') + ')', operate: false},
                        {field: 'channel_name', title: __('Channel ID') + '(' + __('Channel Name') + ')', operate: false},
                        {field: 'agent_id', title: __('Agent ID'), searchList: function (column) {
                            return Template('agenttemplate', {});
                        }, visible: false},
                        {field: 'channel_id', title: __('Channel ID'), visible: false, operate: '='},
                        {field: 'channel_code', title: __('Channel Code'), operate: 'LIKE'},
                        {field: 'amount', title: __('Amount'), operate:'BETWEEN'},
                        {field: 'service_fee', title: __('Service Fee'), operate:'BETWEEN'},
                        {field: 'total_deposit', title: __('Total Deposit'), operate:'BETWEEN'},
                        {field: 'total_withdraw', title: __('Total Withdraw'), operate:'BETWEEN'},
                        {field: 'audit_status', title: __('Audit Status'), searchList: {
                            2: __('Status approved'),
                            3: __('Status processing'),
                            4: __('Status completed'),
                            5: __('Status rejected refund'),
                            6: __('Status failed refund'),
                            7: __('Status rejected confiscate'),
                            8: __('Status virtual')
                        }, formatter: function(value, row, index) {
                            var statusMap = {
                                0: __('Status pending'),
                                1: __('Status waiting review'),
                                2: __('Status approved'),
                                3: __('Status processing'),
                                4: __('Status completed'),
                                5: __('Status rejected refund'),
                                6: __('Status failed refund'),
                                7: __('Status rejected confiscate'),
                                8: __('Status virtual')
                            };
                            return statusMap[value] !== undefined ? statusMap[value] : value;
                        }},
                        {field: 'withdraw_account_id', title: __('Withdraw Account ID')},
                        {field: 'remark', title: __('Order remark'), operate: 'LIKE'},
                        {field: 'player_remark', title: __('Player remark'), operate: 'LIKE'},
                        {field: 'operator_name', title: __('Operator Name'), operate: 'LIKE'},
                        {field: 'operator_type', title: __('Operator Type'), searchList: {0: __('管理员'), 1: __('渠道'), 2: __('业务员')}},
                        {field: 'third_order_no', title: __('Third Order No'), operate: 'LIKE'},
                        {field: 'created_at', title: __('Created At'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime, visible:false},
                        {field: 'processed_at', title: __('Processed At'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'paid_at', title: __('Paid At'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table,
                            buttons: [
                                {
                                    name: 'query_status',
                                    text: __('Query status'),
                                    title: __('Query payment status'),
                                    classname: 'btn btn-xs btn-info btn-ajax',
                                    icon: 'fa fa-search',
                                    url: 'payout/withdraw_orders/query_status',
                                    success: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        $(".btn-refresh").trigger("click");
                                    },
                                    error: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible: function (row) {
                                        return row.audit_status >= 2; // 已审核及以上状态才显示
                                    },
                                    extend: 'data-params=\'{"ids":"__id__"}\''
                                },
                                {
                                    name: 'view_proof',
                                    text: __('View Proof'),
                                    title: __('View Payment Proof'),
                                    classname: 'btn btn-xs btn-success btn-ajax',
                                    icon: 'fa fa-file-image-o',
                                    url: 'payout/withdraw_orders/view_proof',
                                    success: function (data, ret) {
                                        if (ret.data && ret.data.url) {
                                            Layer.open({
                                                type: 2,
                                                title: __('View Payment Proof'),
                                                shadeClose: true,
                                                shade: 0.8,
                                                area: ['500px', '800px'],
                                                content: ret.data.url
                                            });
                                        } else {
                                            Layer.alert(ret.msg);
                                        }
                                        return false;
                                    },
                                    error: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible: function (row) {
                                        // 只有状态为已审核(2)、三方处理中(3)或已完成(4)的订单才显示凭证按钮
                                        return row.audit_status == 2 || row.audit_status == 3 || row.audit_status == 4;
                                    },
                                    extend: 'data-params=\'{"ids":"__id__"}\''
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定玩家ID点击事件
            $(document).on('click', '.btn-player-details', function () {
                var id = $(this).data('id');
                // 打开弹窗显示玩家详情，包含多个标签页
                Fast.api.open('players/players/player_detail?ids=' + id, id + '玩家信息', {
                    area: ['80%', '90%'],
                    callback: function (data) {
                        // 关闭弹窗后可能需要刷新表格
                        table.bootstrapTable('refresh');
                    }
                });
                return false;
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
