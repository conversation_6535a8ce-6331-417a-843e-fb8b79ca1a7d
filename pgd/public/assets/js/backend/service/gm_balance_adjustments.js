define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'service/gm_balance_adjustments/index' + location.search,
                    add_url: '', // 禁用添加
                    edit_url: '', // 禁用编辑
                    del_url: '', // 禁用删除
                    multi_url: '',
                    import_url: '',
                    table: 'gm_balance_adjustments',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                commonSearch: true, // 开启搜索栏
                searchFormVisible: true, // 默认展开
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: false},
                        {field: 'player_id', title: __('Player_id'), operate: '='},
                        {field: 'player_phone_number', title: __('Phone_number'), operate: false}, // 显示玩家手机号
                        {field: 'player_channel_name', title: __('Channel_id'), operate: false}, // 显示玩家所属渠道
                        {field: 'player_agent_name', title: __('Agent_id'), operate: false}, // 显示玩家所属业务员
                        {field: 'adjustment_type', title: __('Adjustment_type'), searchList: {"1":__('Balance increase'),"2":__('Balance decrease')}, formatter: Table.api.formatter.normal},
                        {field: 'amount', title: __('Amount'), operate:'BETWEEN'},
                        {field: 'operator_type', title: __('Operator_type'), searchList: {"0":__('Administrator'),"1":__('Channel'),"2":__('Agent')}, formatter: Table.api.formatter.normal},
                        {field: 'operator_name', title: __('Operator_name'), operate: 'LIKE'},
                        {field: 'remark', title: __('Remark'), operate: 'LIKE'},
                        {field: 'created_at', title: __('Created_at'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'updated_at', title: __('Updated_at'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'status', title: __('Status'), searchList: {"0":__('Pending review'),"1":__('Approved'),"2":__('Rejected')}, formatter: Table.api.formatter.status},
                        {field: 'operate', title: __('Operate'), table: table,
                            buttons: [
                                {
                                    name: 'approve',
                                    text: __('Approve'),
                                    icon: 'fa fa-check',
                                    classname: 'btn btn-xs btn-success btn-ajax',
                                    url: 'service/gm_balance_adjustments/approve',
                                    confirm: __('Are you sure you want to approve this record?'),
                                    success: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        table.bootstrapTable('refresh');
                                    },
                                    error: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible: function (row) {
                                        // 只有未审核的记录才显示同意按钮
                                        // 并且只有 id 为 1、2、4 的角色组才能看到
                                        var allowedGroups = [1, 2, 4]; // 管理员组、平台管理员、渠道
                                        var hasPermission = false;

                                        if (Config.admin && Config.admin.group_ids) {
                                            for (var i = 0; i < Config.admin.group_ids.length; i++) {
                                                if (allowedGroups.indexOf(parseInt(Config.admin.group_ids[i])) > -1) {
                                                    hasPermission = true;
                                                    break;
                                                }
                                            }
                                        }

                                        return row.status == 0 && hasPermission;
                                    }
                                },
                                {
                                    name: 'reject',
                                    text: __('Reject'),
                                    icon: 'fa fa-times',
                                    classname: 'btn btn-xs btn-danger btn-ajax',
                                    url: 'service/gm_balance_adjustments/reject',
                                    confirm: __('Are you sure you want to reject this record?'),
                                    success: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        table.bootstrapTable('refresh');
                                    },
                                    error: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        return false;
                                    },
                                    visible: function (row) {
                                        // 只有未审核的记录才显示拒绝按钮
                                        // 并且只有 id 为 1、2、4 的角色组才能看到
                                        var allowedGroups = [1, 2, 4]; // 管理员组、平台管理员、渠道
                                        var hasPermission = false;

                                        if (Config.admin && Config.admin.group_ids) {
                                            for (var i = 0; i < Config.admin.group_ids.length; i++) {
                                                if (allowedGroups.indexOf(parseInt(Config.admin.group_ids[i])) > -1) {
                                                    hasPermission = true;
                                                    break;
                                                }
                                            }
                                        }

                                        return row.status == 0 && hasPermission;
                                    }
                                }
                            ],
                            events: Table.api.events.operate,
                            formatter: function (value, row, index) {
                                // 检查用户是否属于允许的角色组
                                var allowedGroups = [1, 2, 4]; // 管理员组、平台管理员、渠道
                                var hasPermission = false;

                                if (Config.admin && Config.admin.group_ids) {
                                    for (var i = 0; i < Config.admin.group_ids.length; i++) {
                                        if (allowedGroups.indexOf(parseInt(Config.admin.group_ids[i])) > -1) {
                                            hasPermission = true;
                                            break;
                                        }
                                    }
                                }

                                // 如果没有权限，则不显示操作列
                                if (!hasPermission) {
                                    return '';
                                }

                                return Table.api.formatter.operate.call(this, value, row, index);
                            }
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },

        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
