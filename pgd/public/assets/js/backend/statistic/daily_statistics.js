define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'bootstrap-daterangepicker', 'moment'], function ($, undefined, Backend, Table, Form, undefined, Moment) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'statistic/daily_statistics/index' + location.search,
                    table: 'daily_statistics',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'stat_date',
                sortName: 'stat_date',
                sortOrder: 'desc', // 默认按日期降序排列，最新的在前面
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {field: 'stat_date', title: __('Stat_date'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {
                            field: 'total_deposit',
                            title: __('Total_deposit'),
                            operate:'BETWEEN',
                            formatter: function (value, row, index) {
                                // 添加千位分隔符
                                return parseFloat(value).toLocaleString('zh-CN', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                            }
                        },
                        {
                            field: 'total_withdraw',
                            title: __('Total_withdraw'),
                            operate:'BETWEEN',
                            formatter: function (value, row, index) {
                                // 添加千位分隔符
                                return parseFloat(value).toLocaleString('zh-CN', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                            }
                        },
                        {
                            field: 'deposit_withdraw_diff',
                            title: __('Deposit_withdraw_diff'),
                            operate:'BETWEEN',
                            formatter: function (value, row, index) {
                                // 添加千位分隔符
                                var formattedValue = parseFloat(value).toLocaleString('zh-CN', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });

                                if (value > 0) {
                                    // 充值大于提现，平台资金净流入，显示绿色
                                    return '<span style="color: green; font-weight: bold;">+' + formattedValue + '</span>';
                                } else if (value < 0) {
                                    // 提现大于充值，平台资金净流出，显示红色
                                    return '<span style="color: red; font-weight: bold;">' + formattedValue + '</span>';
                                } else {
                                    // 值为0，显示正常颜色
                                    return formattedValue;
                                }
                            }
                        },
                        {
                            field: 'total_bet',
                            title: __('Total_bet'),
                            operate:'BETWEEN',
                            formatter: function (value, row, index) {
                                // 添加千位分隔符
                                return parseFloat(value).toLocaleString('zh-CN', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                            }
                        },
                        {
                            field: 'total_win',
                            title: __('Total_win'),
                            operate:'BETWEEN',
                            formatter: function (value, row, index) {
                                // 添加千位分隔符
                                return parseFloat(value).toLocaleString('zh-CN', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                            }
                        },
                        {
                            field: 'total_game_profit',
                            title: __('Total_game_profit'),
                            operate:'BETWEEN',
                            formatter: function (value, row, index) {
                                // 添加千位分隔符
                                var formattedValue = parseFloat(value).toLocaleString('zh-CN', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });

                                if (value > 0) {
                                    // 平台盈利，显示绿色
                                    return '<span style="color: green; font-weight: bold;">+' + formattedValue + '</span>';
                                } else if (value < 0) {
                                    // 平台亏损，显示红色
                                    return '<span style="color: red; font-weight: bold;">' + formattedValue + '</span>';
                                } else {
                                    // 值为0，显示正常颜色
                                    return formattedValue;
                                }
                            }
                        },
                        {field: 'active_players', title: __('Active_players')},
                        {field: 'new_players', title: __('New_players')},
                        {field: 'first_deposit_players', title: __('First_deposit_players')},
                        {field: 'repeat_deposit_players', title: __('Repeat_deposit_players')},
                        {field: 'first_deposit_amount', title: __('First_deposit_amount'), operate:'BETWEEN'},
                        {field: 'repeat_deposit_amount', title: __('Repeat_deposit_amount'), operate:'BETWEEN'},
                        {field: 'updated_at', title: __('Updated_at')}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 初始化日期范围选择器
            var dateRangePicker = $('#date-range-picker');
            dateRangePicker.daterangepicker({
                autoUpdateInput: false,
                locale: {
                    format: 'YYYY-MM-DD',
                    applyLabel: __('Confirm'),
                    cancelLabel: __('Clear'),
                    fromLabel: __('From'),
                    toLabel: __('To'),
                    customRangeLabel: __('Custom Range'),
                    weekLabel: 'W',
                    daysOfWeek: [__('Su'), __('Mo'), __('Tu'), __('We'), __('Th'), __('Fr'), __('Sa')],
                    monthNames: [__('Jan'), __('Feb'), __('Mar'), __('Apr'), __('May'), __('Jun'), __('Jul'), __('Aug'), __('Sep'), __('Oct'), __('Nov'), __('Dec')],
                    firstDay: 1
                },
                ranges: {
                    [__('Today')]: [Moment(), Moment()],
                    [__('Yesterday')]: [Moment().subtract(1, 'days'), Moment().subtract(1, 'days')],
                    [__('Last 7 Days')]: [Moment().subtract(6, 'days'), Moment()],
                    [__('Last 30 Days')]: [Moment().subtract(29, 'days'), Moment()],
                    [__('This Month')]: [Moment().startOf('month'), Moment().endOf('month')],
                    [__('Last Month')]: [Moment().subtract(1, 'month').startOf('month'), Moment().subtract(1, 'month').endOf('month')]
                }
            });

            // 日期范围选择器事件
            dateRangePicker.on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
            });

            dateRangePicker.on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });

            // 搜索按钮事件
            $('#btn-search').on('click', function() {
                var dateRange = dateRangePicker.val();
                if (dateRange) {
                    var dates = dateRange.split(' - ');
                    table.bootstrapTable('refresh', {
                        query: {
                            'stat_date': dates[0] + ' - ' + dates[1]
                        }
                    });
                } else {
                    Toastr.warning(__('Please select date range'));
                }
            });

            // 重置按钮事件
            $('#btn-reset').on('click', function() {
                dateRangePicker.val('');
                table.bootstrapTable('refresh', {
                    query: {
                        'stat_date': ''
                    }
                });
            });
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
