define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'bootstrap-datetimepicker'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'statistic/game_records/index',
                    export_url: 'statistic/game_records/export',
                    table: 'game_sessions',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'provider_id',
                sortName: 'stat_date',
                sortOrder: 'desc',
                toolbar: '#toolbar',
                showExport: false,
                showToggle: false,
                showColumns: false,
                showRefresh: false,
                commonSearch: false,
                search: false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'stat_date', title: __('Date'), sortable: true},
                        {field: 'provider_id', title: __('Provider_id'), sortable: true},
                        {field: 'provider_name', title: __('Provider_name'), operate: 'LIKE'},
                        {field: 'bet_amount', title: __('Bet_amount'), operate: 'BETWEEN', sortable: true},
                        {field: 'platform_profit', title: __('Platform_profit'), operate: 'BETWEEN', sortable: true, formatter: function(value, row, index) {
                            // 正数显示为绿色，负数显示为红色
                            var color = parseFloat(value) >= 0 ? 'green' : 'red';
                            return '<span style="color: ' + color + '">' + value + '</span>';
                        }},
                        {field: 'kill_rate', title: __('Kill_rate'), operate: 'BETWEEN', sortable: true},
                        {field: 'return_rate', title: __('Return_rate'), operate: 'BETWEEN', sortable: true},
                        {field: 'player_count', title: __('Player_count'), operate: 'BETWEEN', sortable: true},
                        {field: 'game_count', title: __('Game_count'), operate: 'BETWEEN', sortable: true}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 计算统计数据并更新信息卡片
            var calculateStatistics = function(data, depositStats) {
                var totalBetAmount = 0;
                var totalPlatformProfit = 0;

                $.each(data, function(index, row) {
                    totalBetAmount += parseFloat(row.bet_amount || 0);
                    totalPlatformProfit += parseFloat(row.platform_profit || 0);
                });

                // 计算杀率
                var totalKillRate = 0;
                if (totalBetAmount > 0) {
                    totalKillRate = (totalPlatformProfit / totalBetAmount) * 100;
                }

                // 更新流水总计
                $('#total-bet-amount').text(totalBetAmount.toFixed(2));

                // 更新盈亏合计
                $('#total-platform-profit').text(totalPlatformProfit.toFixed(2));

                // 更新杀率
                $('#total-kill-rate').text(totalKillRate.toFixed(2) + '%');

                // 根据盈亏正负值更改颜色
                if (totalPlatformProfit >= 0) {
                    $('#profit-box').removeClass('bg-red').addClass('bg-green');
                } else {
                    $('#profit-box').removeClass('bg-green').addClass('bg-red');
                }

                // 更新充值合计
                var totalDepositAmount = 0;
                if (depositStats) {
                    totalDepositAmount = parseFloat(depositStats.deposit_amount.replace(/,/g, '')) || 0;
                    $('#total-deposit-amount').text(depositStats.deposit_amount);
                    $('#total-player-count').text(depositStats.player_count);

                    // 计算盈亏/充值比例
                    var profitDepositRatio = 0;
                    if (totalDepositAmount > 0) {
                        profitDepositRatio = (totalPlatformProfit / totalDepositAmount) * 100;
                    }

                    // 更新盈亏/充值比例
                    $('#profit-deposit-ratio').text(profitDepositRatio.toFixed(2) + '%');

                    // 根据比例值更改颜色
                    if (profitDepositRatio >= 0) {
                        $('#profit-deposit-ratio-box').removeClass('bg-red').addClass('bg-purple');
                    } else {
                        $('#profit-deposit-ratio-box').removeClass('bg-purple').addClass('bg-red');
                    }
                }

                // 更新日期范围文本
                var startDate = $('#start_date').val();
                var endDate = $('#end_date').val();
                $('#date-range-text').text(startDate + ' 至 ' + endDate);
            };

            // 监听表格加载完成事件
            table.on('load-success.bs.table', function(e, data) {
                calculateStatistics(data.rows, data.deposit_stats);
            });

            // 搜索按钮点击事件
            $(document).on('click', '#search-btn', function () {
                var params = $('#search-form').serializeArray();
                var queryParams = {};
                $.each(params, function(i, field){
                    queryParams[field.name] = field.value;
                });
                table.bootstrapTable('refresh', {query: queryParams});
            });

            // 导出按钮点击事件
            $(document).on('click', '#btn-export', function () {
                var params = $('#search-form').serialize();
                var exportUrl = $.fn.bootstrapTable.defaults.extend.export_url + '?' + params;
                window.location.href = exportUrl;
            });

            // 日期选择器初始化
            $('.datetimepicker').datetimepicker({
                format: 'YYYY-MM-DD',
                icons: {
                    time: 'fa fa-clock-o',
                    date: 'fa fa-calendar',
                    up: 'fa fa-chevron-up',
                    down: 'fa fa-chevron-down',
                    previous: 'fa fa-chevron-left',
                    next: 'fa fa-chevron-right',
                    today: 'fa fa-history',
                    clear: 'fa fa-trash',
                    close: 'fa fa-remove'
                },
                showTodayButton: true,
                showClose: true
            });

            // 初始化日期范围文本
            var startDate = $('#start_date').val();
            var endDate = $('#end_date').val();
            $('#date-range-text').text(startDate + ' 至 ' + endDate);
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
