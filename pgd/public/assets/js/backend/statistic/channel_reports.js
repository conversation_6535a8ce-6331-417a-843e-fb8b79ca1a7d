define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'bootstrap-datetimepicker'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'statistic/channel_reports/index',
                    table: 'channels',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'channel_id',
                sortName: 'channel_id',
                sortOrder: 'asc',
                columns: [
                    [
                        {field: 'channel_id', title: __('Channel_id'), sortable: true},
                        {field: 'channel_name', title: __('Channel_name'), sortable: true},
                        {field: 'total_deposit', title: __('Total_deposit'), operate: false},
                        {field: 'total_withdraw', title: __('Total_withdraw'), operate: false},
                        {field: 'deposit_withdraw_diff', title: __('Deposit_withdraw_diff'), operate: false},
                        {field: 'deposit_fee', title: __('Deposit_fee'), operate: false},
                        {field: 'withdraw_fee', title: __('Withdraw_fee'), operate: false},
                        {field: 'game_loss', title: __('Game_loss'), operate: false},
                        {field: 'api_fee', title: __('Api_fee'), operate: false},
                        {field: 'total_profit', title: __('Total_profit'), operate: false}
                    ]
                ],
                // 禁用默认搜索
                search: false,
                // 启用普通表单搜索
                commonSearch: false,
                // 表格加载完成后执行
                onLoadSuccess: function (data) {
                    // 更新统计卡片
                    updateStatCards(data.rows);
                    // 更新日期范围文本
                    updateDateRangeText();
                }
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 初始化日期选择器
            $('.datetimepicker').datetimepicker({
                format: 'YYYY-MM-DD',
                icons: {
                    time: 'fa fa-clock-o',
                    date: 'fa fa-calendar',
                    up: 'fa fa-chevron-up',
                    down: 'fa fa-chevron-down',
                    previous: 'fa fa-chevron-left',
                    next: 'fa fa-chevron-right',
                    today: 'fa fa-history',
                    clear: 'fa fa-trash',
                    close: 'fa fa-remove'
                },
                showTodayButton: true,
                showClose: true
            });

            // 搜索按钮点击事件
            $("#search-btn").on("click", function () {
                var params = $("#search-form").serializeArray();
                var options = table.bootstrapTable('getOptions');
                options.queryParams = function (params) {
                    var temp = {};
                    $.each($("#search-form").serializeArray(), function (i, field) {
                        temp[field.name] = field.value;
                    });
                    temp.offset = params.offset;
                    temp.limit = params.limit;
                    temp.sort = params.sort;
                    temp.order = params.order;
                    return temp;
                };
                table.bootstrapTable('refresh', {});
                // 更新日期范围文本
                updateDateRangeText();
                return false;
            });

            // 更新统计卡片
            function updateStatCards(rows) {
                var totalDeposit = 0;
                var totalWithdraw = 0;
                var totalDiff = 0;
                var totalProfit = 0;

                $.each(rows, function (i, row) {
                    totalDeposit += parseFloat(row.total_deposit.replace(/,/g, ''));
                    totalWithdraw += parseFloat(row.total_withdraw.replace(/,/g, ''));
                    totalDiff += parseFloat(row.deposit_withdraw_diff.replace(/,/g, ''));
                    totalProfit += parseFloat(row.total_profit.replace(/,/g, ''));
                });

                $("#total-deposit").text(totalDeposit.toFixed(2));
                $("#total-withdraw").text(totalWithdraw.toFixed(2));
                $("#total-diff").text(totalDiff.toFixed(2));
                $("#total-profit").text(totalProfit.toFixed(2));

                // 根据盈亏设置颜色
                if (totalProfit > 0) {
                    $("#profit-box").removeClass("bg-red").addClass("bg-green");
                } else {
                    $("#profit-box").removeClass("bg-green").addClass("bg-red");
                }
            }

            // 更新日期范围文本
            function updateDateRangeText() {
                var startDate = $("#start_date").val();
                var endDate = $("#end_date").val();
                $("#date-range-text").text(startDate + " - " + endDate);
            }

            // 初始化时更新日期范围文本
            updateDateRangeText();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
