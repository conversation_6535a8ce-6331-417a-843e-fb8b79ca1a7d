define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'statistic/agents_month/index' + location.search,
                    table: 'daily_statistics',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'month',
                sortName: 'month',
                sortOrder: 'desc',
                search: false,
                searchFormVisible: true,
                commonSearch: true,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'month', title: __('Month'), sortable: true, operate: 'RANGE', addclass: 'datetimerange', formatter: Table.api.formatter.date},
                        {field: 'agent_id_text', title: __('Agent_id'), sortable: false},
                        {field: 'total_deposit', title: __('Total_deposit'), sortable: true, operate: false, formatter: function(value) {
                            return parseFloat(value).toLocaleString('zh-CN', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            });
                        }},
                        {field: 'total_withdraw', title: __('Total_withdraw'), sortable: true, operate: false, formatter: function(value) {
                            return parseFloat(value).toLocaleString('zh-CN', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            });
                        }},
                        {field: 'deposit_withdraw_diff', title: __('Deposit_withdraw_diff'), operate: false, formatter: function(value) {
                            return parseFloat(value).toLocaleString('zh-CN', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            });
                        }},
                        {field: 'actual_deposit_withdraw_diff', title: __('Actual_deposit_withdraw_diff'), operate: false, formatter: function(value) {
                            return parseFloat(value).toLocaleString('zh-CN', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            });
                        }},
                        {field: 'total_game_profit', title: __('Total_game_profit'), sortable: true, operate: false, formatter: function(value) {
                            return parseFloat(value).toLocaleString('zh-CN', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            });
                        }},
                        {field: 'new_active_users_text', title: __('New_active_users'), operate: false},
                        {field: 'first_repeat_deposit_text', title: __('First_deposit_users'), operate: false},
                        {field: 'deposit_info_text', title: __('Deposit_info'), operate: false},
                        {field: 'withdraw_info_text', title: __('Withdraw_info'), operate: false},
                        {field: 'manual_adjust_text', title: __('Manual_adjust'), operate: false},
                        {field: 'game_info_text', title: __('Game_info'), operate: false}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
