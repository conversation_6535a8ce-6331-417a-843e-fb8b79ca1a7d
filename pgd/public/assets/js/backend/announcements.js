define(['jquery', 'bootstrap', 'summernote', 'backend', 'table', 'form'], function ($, undefined, summernote, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'announcements/index' + location.search,
                    add_url: 'announcements/add',
                    edit_url: 'announcements/edit',
                    del_url: 'announcements/del',
                    multi_url: 'announcements/multi',
                    import_url: 'announcements/import',
                    table: 'announcements',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                commonSearch: true, // 开启搜索栏
                searchFormVisible: true, // 默认展开
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'title', title: __('Title'), operate: 'LIKE'},
                        {field: 'content', title: __('Content'), visible: false, operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'created_at', title: __('Created_at'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'start_time', title: __('Start_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'end_time', title: __('End_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'is_active', title: __('Status'), searchList: {"1":__('Enable'),"0":__('Disable')}, formatter: Table.api.formatter.toggle},
                        {field: 'sort_order', visible: false, title: __('Sort_order')},
                        {field: 'updated_at', visible: false, title: __('Updated_at'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
            $('.summernote').summernote({
                height: 280,
                width: 490,
                lang: 'zh-CN'
            });
        },
        edit: function () {
            Controller.api.bindevent();
            setTimeout(function(){
                $('.summernote').summernote({
                    height: 280,
                    width: 490,
                    lang: 'zh-CN'
                });
            }, 300);
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
