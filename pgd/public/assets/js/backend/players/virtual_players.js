define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'players/virtual_players/index' + location.search,
                    add_url: 'players/virtual_players/add',
                    edit_url: 'players/virtual_players/edit',
                    del_url: 'players/virtual_players/del',
                    multi_url: 'players/virtual_players/multi',
                    import_url: 'players/virtual_players/import',
                    table: 'virtual_players',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                commonSearch: true, // 开启搜索栏
                searchFormVisible: true, // 默认展开
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'channel_id', title: __('Channel_id'), searchList: $.getJSON('channels/channels/getList'), formatter: function(value, row, index){
                            return row.channel ? row.channel_id + '-' + row.channel.name : '';
                        }},
                        {field: 'username', title: __('Username'), operate: 'LIKE', formatter: function(value, row, index){
                            return row.id;
                        }},
                        {field: 'balance', title: __('Balance'), operate:'BETWEEN'},
                        {field: 'is_active', title: __('Status'), searchList: {'1': __('Status 1'), '0': __('Status 0')}, formatter: Table.api.formatter.status},
                        {field: 'join_time', title: __('Join_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'last_login_time', title: __('Last_login_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'login_ip', title: __('Login_ip'), operate: 'LIKE'},

                        {field: 'updated_at', title: __('Updated_at'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, visible: false},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
