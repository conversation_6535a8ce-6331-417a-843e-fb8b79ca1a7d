define(['jquery', 'bootstrap', 'backend', 'form'], function ($, undefined, Backend, Form) {
    var Controller = {
        index: function () {
            // 表单提交处理
            Form.api.bindevent($('#rtp-form'), function(data, ret) {
                if (ret.code === 1) {
                    // 操作成功
                    Layer.alert(ret.msg, {icon: 1}, function(index) {
                        Layer.close(index);
                        var index = parent.Layer.getFrameIndex(window.name);
                        parent.Layer.close(index);
                        parent.$('.btn-refresh').trigger('click');
                    });
                } else {
                    // 操作失败
                    Layer.alert(ret.msg, {icon: 2});
                }
                return false;
            });
        }
    };
    return Controller;
});
