define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 不再隐藏面板标题或调整边距

            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'players/players/index' + location.search,
                    add_url: 'players/players/add',
                    edit_url: 'players/players/edit',
                    del_url: 'players/players/del',
                    multi_url: 'players/players/multi',
                    import_url: 'players/players/import',
                    table: 'players'
                }
            });

            var table = $("#table");

            // 不再需要玩家类型列表

            // 获取统计数据
            var loadStatistics = function() {
                $.ajax({
                    url: 'players/players/getStatistics',
                    type: 'GET',
                    success: function(res) {
                        if (res.code === 1) {
                            var statsHtml = '所剩游戏余额: ' + res.data.game_balance +
                                ' 冻结中金额: ' + res.data.frozen_amount +
                                ' 总充值金额: ' + res.data.total_deposit +
                                ' 总提现金额: ' + res.data.total_withdraw;
                            $('#statistics-info').html(statsHtml);
                        }
                    }
                });
            };

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedNumber: 3,
                fixedRightNumber: 1,
                commonSearch: true, // 开启搜索栏
                searchFormVisible: true, // 默认展开
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('玩家ID'), sortable: true, formatter: function(value, row, index) {
                            return '<a href="javascript:;" class="btn-player-details" data-id="'+value+'" style="color:#3c8dbc;text-decoration:underline;">'+value+'</a>';
                        }},
                        {field: 'referrer_id', title: __('上级ID'), sortable: true, formatter: function(value, row, index) {
                            if (value && value > 0) {
                                return '<a href="javascript:;" class="btn-referrer-details" data-id="'+value+'" style="color:#3c8dbc;text-decoration:underline;">'+value+'</a>';
                            } else {
                                return value || '-';
                            }
                        }},
                        {field: 'tag_name', title: __('Tag name'), width: 90, operate: false, formatter: function(value, row, index) {
                            // 创建下拉选择框
                            var html = '<select class="form-control tag-selector" data-id="' + row.id + '">';
                            // 默认空选项
                            html += '<option value="">' + __('Please select') + '</option>';
                            // 从标签列表中获取选项
                            if (playerTypeList && typeof playerTypeList === 'object') {
                                $.each(playerTypeList, function(id, name) {
                                    if (id != 0) { // 跳过"All"选项
                                        var selected = (row.tag_id == id) ? 'selected' : '';
                                        html += '<option value="' + id + '" ' + selected + '>' + name + '</option>';
                                    }
                                });
                            }
                            html += '</select>';
                            return html;
                        }},
                        {field: 'latest_remark', title: __('Remark'), operate: 'LIKE'},
                        {field: 'tag_id', title: __('Tag id'), searchList: function (column) {
                            return Template('playertagtemplate', {});
                        }, visible: false, operate: '='},
                        {field: 'rtp', title: __('Rtp'), operate: 'BETWEEN'},
                        {field: 'vip_level', title: __('Vip level'), operate: '='},
                        {field: 'phone_number', title: __('Phone number'), operate: 'LIKE'},
                        {field: 'username', title: __('Username'), operate: 'LIKE'},
                        {field: 'channel_name', title: __('Channel id'), operate: false},
                        {field: 'channel_id', title: __('Channel id'), visible: false, operate: '='},
                        {field: 'agent_name', title: __('Agent id'), operate: false},
                        {field: 'agent_id', title: __('Agent id'), visible: false, operate: '='},
                        {field: 'balance', title: __('Balance'), sortable: true, operate: false},
                        {field: 'reward_balance', title: __('Reward balance'), sortable: true, operate: false},
                        {field: 'withdrawing_balance', title: __('Withdrawing balance'), sortable: true, operate: false},
                        {field: 'total_deposit', title: __('Total deposit'), sortable: true, operate: false},
                        {field: 'total_withdraw', title: __('Total withdraw'), sortable: true, operate: false},
                        {field: 'direct_referrals_count', title: __('Direct referrals count'), sortable: true, operate: false},
                        {field: 'direct_deposit_referrals_count', title: __('Direct deposit referrals count'), sortable: true, operate: false},
                        {field: 'team_total_deposit', title: __('Team total deposit'), sortable: true, operate: false},
                        {field: 'team_total_withdraw', title: __('Team total withdraw'), sortable: true, operate: false},
                        {field: 'team_deposit_withdraw_diff', title: __('Team deposit withdraw diff'), sortable: true, operate: false},
                        {field: 'total_bet', title: __('Total bet'), sortable: true, operate: false},
                        {field: 'total_win', title: __('Total win'), sortable: true, operate: false},
                        {field: 'last_login_time', title: __('Last login time'), operate: 'RANGE', addclass: 'datetimerange'},
                        {field: 'last_login_ip', title: __('Last login ip'), operate: 'LIKE'},
                        {field: 'created_at', title: __('Register time'), operate: 'RANGE', addclass: 'datetimerange'},
                        {field: 'register_ip', title: __('Register ip'), operate: 'LIKE'},
                        {field: 'is_game_banned', title: __('Is game banned'), searchList: {"0":__('No'),"1":__('Yes')}, table: table, formatter: Table.api.formatter.toggle},
                        {field: 'no_unbind_subordinate', title: __('No unbind subordinate'), searchList: {"0":__('No'),"1":__('Yes')}, operate: false, table: table, formatter: Table.api.formatter.toggle},
                        {field: 'self_unbind_status', title: __('Self unbind status'), searchList: {"0":__('Self unbind 0'),"1":__('Self unbind 1')}, operate: false, table: table, formatter: Table.api.formatter.toggle},
                        {field: 'team_withdraw_audit', title: __('Team withdraw audit'), searchList: {"0":__('No'),"1":__('Yes')}, operate: false, table: table, formatter: Table.api.formatter.toggle},
                        {field: 'personal_withdraw_audit', title: __('Personal withdraw audit'), searchList: {"0":__('No'),"1":__('Yes')}, operate: false, table: table, formatter: Table.api.formatter.toggle},
                        {field: 'is_active', title: __('Is active'), searchList: {"0":__('No active'),"1":__('Active')}, operate: false, table: table, formatter: Table.api.formatter.toggle},
                        {
                            field: 'operate',
                            title: __('Operate'),
                            table: table,
                            width: 200,
                            formatter: function (value, row, index) {
                                var html = [];
                                // 更多按钮及下拉菜单
                                html.push('<div class="btn-group">');
                                html.push('<button type="button" class="btn btn-xs btn-primary btn-more dropdown-toggle" data-toggle="dropdown">' + __('More') + ' <span class="caret"></span></button>');
                                html.push('<ul class="dropdown-menu">');
                                html.push('<li><a href="javascript:;" class="btn-unbind-withdrawal-account" data-id="' + row.id + '">' + __('Unbind withdrawal account') + '</a></li>');
                                html.push('<li><a href="javascript:;" class="btn-player-remark" data-id="' + row.id + '">' + __('Player remark') + '</a></li>');
                                html.push('<li><a href="javascript:;" class="btn-set-vip-level" data-id="' + row.id + '">' + __('Set vip level') + '</a></li>');
                                html.push('<li><a href="javascript:;" class="btn-betting-setting" data-id="' + row.id + '">' + __('Betting setting') + '</a></li>');
                                html.push('<li><a href="javascript:;" class="btn-direct-login" data-id="' + row.id + '">直接登录</a></li>');
                                html.push('</ul>');
                                html.push('</div>');
                                // 账户余额变更按钮
                                html.push('<a href="javascript:;" class="btn btn-xs btn-success btn-balance-change" data-id="' + row.id + '">账户余额变更</a>');
                                // RTP设置按钮
                                html.push('<a href="javascript:;" class="btn btn-xs btn-info btn-rtp-setting" data-id="' + row.id + '">RTP设置</a>');
                                return html.join('');
                            },
                            events: {
                                'click .btn-balance-change': function (e, value, row, index) {
                                    e.stopPropagation();
                                    Fast.api.open('players/balance_change/index?player_id=' + row.id, '账户余额变更', {area: ['90%', '90%']});
                                },
                                'click .btn-rtp-setting': function (e, value, row, index) {
                                    e.stopPropagation();
                                    Fast.api.open('players/rtp/index?player_id=' + row.id, 'RTP设置', {area: ['600px', '600px']});
                                },
                                'click .btn-unbind-withdrawal-account': function (e, value, row, index) {
                                    e.stopPropagation();
                                    Layer.confirm(__('Are you sure you want to unbind the withdrawal account?'), {icon: 3, title: __('Tip')}, function (index) {
                                        Fast.api.ajax({
                                            url: 'players/players/unbind_withdraw_account',
                                            data: {player_id: row.id}
                                        }, function (data, ret) {
                                            Layer.close(index);
                                            table.bootstrapTable('refresh');
                                            return false;
                                        });
                                    });
                                },
                                'click .btn-modify-withdraw': function (e, value, row, index) {
                                    e.stopPropagation();
                                    Fast.api.open('players/withdraw_limit/index?player_id=' + row.id, '修改提现额度', {area: ['90%', '90%']});
                                },
                                'click .btn-player-remark': function (e, value, row, index) {
                                    e.stopPropagation();
                                    // 创建备注弹窗
                                    Layer.prompt({
                                        formType: 2,
                                        title: __('Add remark'),
                                        placeholder: __('Remark content'),
                                        value: '',
                                        area: ['400px', '200px']
                                    }, function(value, index){
                                        if (!value) {
                                            Layer.msg(__('Remark content cannot be empty'));
                                            return;
                                        }
                                        // 提交备注
                                        Fast.api.ajax({
                                            url: 'players/players/add_remark',
                                            data: {player_id: row.id, content: value}
                                        }, function(data, ret){
                                            Layer.close(index);
                                            Layer.msg(__('Remark added successfully'));
                                            return false;
                                        });
                                    });
                                },

                                'click .btn-set-vip-level': function (e, value, row, index) {
                                    e.stopPropagation();
                                    // 获取VIP等级配置
                                    Fast.api.ajax({
                                        url: 'players/players/get_vip_config',
                                        data: {}
                                    }, function(data) {
                                        // 创建VIP设置弹窗
                                        var vipOptions = '';
                                        $.each(data, function(level, config) {
                                            vipOptions += '<option value="' + level + '"' + (row.vip_level == level ? ' selected' : '') + '>VIP ' + level + '</option>';
                                        });

                                        var content =
                                            '<div class="form-horizontal" style="padding: 20px;">' +
                                                '<div class="form-group">' +
                                                    '<label class="control-label col-xs-12 col-sm-4">' + __('Current VIP Level') + ':</label>' +
                                                    '<div class="col-xs-12 col-sm-8">' +
                                                        '<p class="form-control-static">VIP ' + row.vip_level + '</p>' +
                                                    '</div>' +
                                                '</div>' +
                                                '<div class="form-group">' +
                                                    '<label class="control-label col-xs-12 col-sm-4">' + __('New VIP Level') + ':</label>' +
                                                    '<div class="col-xs-12 col-sm-8">' +
                                                        '<select id="new-vip-level" class="form-control">' + vipOptions + '</select>' +
                                                    '</div>' +
                                                '</div>' +
                                            '</div>';

                                        Layer.open({
                                            type: 1,
                                            title: __('Set VIP Level'),
                                            area: ['400px', '250px'],
                                            content: content,
                                            btn: [__('Submit'), __('Cancel')],
                                            yes: function(index) {
                                                var newVipLevel = $('#new-vip-level').val();
                                                // 提交更新
                                                Fast.api.ajax({
                                                    url: 'players/players/set_vip_level',
                                                    data: {player_id: row.id, vip_level: newVipLevel}
                                                }, function(data) {
                                                    Layer.close(index);
                                                    Layer.msg(__('VIP level updated successfully'));
                                                    table.bootstrapTable('refresh');
                                                    return false;
                                                });
                                            }
                                        });
                                        return false;
                                    });
                                },

                                'click .btn-direct-login': function (e, value, row, index) {
                                    e.stopPropagation();
                                    // 直接发送请求，不显示确认对话框
                                    Fast.api.ajax({
                                        url: 'players/players/direct_login',
                                        data: {player_id: row.id},
                                        type: 'GET'
                                    }, function (data, ret) {
                                        if (ret.data && ret.data.url) {
                                            // 在新窗口打开前台页面
                                            window.open(ret.data.url, '_blank');
                                        }
                                        return false;
                                    });
                                },

                                'click .btn-betting-setting': function (e, value, row, index) {
                                    e.stopPropagation();
                                    Fast.api.open('players/betting_setting/index?player_id=' + row.id, '打码设置', {
                                        area: ['500px', '400px'],
                                        callback: function (data) {
                                            table.bootstrapTable('refresh');
                                        }
                                    });
                                },

                            }
                        }
                    ]
                ]
            });

            // 加载统计信息
            loadStatistics();

            // 每60秒刷新一次统计数据
            setInterval(loadStatistics, 60000);

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 获取玩家标签列表
            var playerTypeList = {};
            $.ajax({
                url: 'players/players/getPlayerTypeList',
                type: 'GET',
                async: false,
                success: function(res) {
                    if (res.code === 1) {
                        playerTypeList = res.data;
                    }
                }
            });

            // 绑定标签选择器变化事件
            $(document).on('change', '.tag-selector', function() {
                var player_id = $(this).data('id');
                var tag_id = $(this).val();

                if (!player_id || !tag_id) {
                    Layer.msg(__('Please select a valid tag'));
                    return;
                }

                var that = this;
                Fast.api.ajax({
                    url: 'players/players/update_tag',
                    data: {player_id: player_id, tag_id: tag_id}
                }, function(data) {
                    Layer.msg(__('Tag updated successfully'));
                    return false;
                }, function(data) {
                    return false;
                });
            });

            // 绑定掉绑设置按钮事件
            $(document).on('click', '.btn-unbind-setting', function () {
                Fast.api.open('players/players/unbind_setting', '全局掉绑设置', {area: ['800px', '600px']});
                return false;
            });

            // 绑定渠道RTP设置按钮事件
            $(document).on('click', '.btn-channel-rtp', function () {
                Fast.api.open('channels/channels/set_rtp', '渠道RTP设置', {area: ['600px', '400px']});
                return false;
            });

            // 绑定玩家ID点击事件
            $(document).on('click', '.btn-player-details', function () {
                var id = $(this).data('id');
                // 打开弹窗显示玩家详情，包含多个标签页
                Fast.api.open('players/players/player_detail?ids=' + id, id + '玩家信息', {
                    area: ['80%', '90%'],
                    callback: function (data) {
                        // 关闭弹窗后可能需要刷新表格
                        table.bootstrapTable('refresh');
                    }
                });
                return false;
            });

            // 绑定上级ID点击事件
            $(document).on('click', '.btn-referrer-details', function () {
                var id = $(this).data('id');
                // 打开弹窗显示玩家详情，包含多个标签页
                Fast.api.open('players/players/player_detail?ids=' + id, id + '玩家信息', {
                    area: ['80%', '90%'],
                    callback: function (data) {
                        // 关闭弹窗后可能需要刷新表格
                        table.bootstrapTable('refresh');
                    }
                });
                return false;
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },

        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
