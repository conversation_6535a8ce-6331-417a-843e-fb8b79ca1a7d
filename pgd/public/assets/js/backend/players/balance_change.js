define(['jquery', 'bootstrap', 'backend', 'form'], function ($, undefined, Backend, Form) {
    var Controller = {
        index: function () {
            // 处理表单提交前的数据处理
            $('#balance-change-form').on('submit', function() {
                // 获取金额
                var amount = parseFloat($('#amount').val());

                // 验证金额
                if (!amount || amount === 0) {
                    Layer.alert('请输入有效的变更金额（不能为0）', {icon: 2});
                    return false;
                }

                // 金额的正负直接决定上分或下分，不需要额外处理

                // 变更类型已固定为"GM操作增减金币"，不再需要验证

                // 更新表单中的金额值
                $('#amount').val(amount);

                // 确认操作
                var confirmMsg = amount > 0 ?
                    '确定要增加 ' + Math.abs(amount) + ' 到玩家余额吗？' :
                    '确定要从玩家余额中减少 ' + Math.abs(amount) + ' 吗？';

                return confirm(confirmMsg);
            });

            // 表单提交处理
            Form.api.bindevent($('#balance-change-form'), function(data, ret) {
                if (ret.code === 1) {
                    // 操作成功
                    Layer.alert(ret.msg, {icon: 1}, function(index) {
                        Layer.close(index);
                        var index = parent.Layer.getFrameIndex(window.name);
                        parent.Layer.close(index);
                        parent.$('.btn-refresh').trigger('click');
                    });
                } else {
                    // 操作失败
                    Layer.alert(ret.msg, {icon: 2});
                }
                return false;
            });
        }
    };
    return Controller;
});
