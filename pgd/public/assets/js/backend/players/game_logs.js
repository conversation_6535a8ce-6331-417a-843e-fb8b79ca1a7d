define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'players/game_logs/index' + location.search,
                    table: 'game_records',
                },
                search: false,
                commonSearch: true,
                searchFormVisible: true,
                showExport: false,
                showToggle: false,
                showColumns: false,
                showRefresh: false
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                sortOrder: 'desc',
                toolbar: '#toolbar',
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                showExport: false,
                columns: [
                    [
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'player_id', title: __('Player_id'), operate: '=', formatter: function(value, row, index) {
                            // 使玩家ID可点击，唤起玩家信息小窗
                            return '<a href="javascript:;" class="btn-player-details" data-id="' + value + '">' + value + '</a>';
                        }},
                        {field: 'session_id', title: __('Session_id'), sortable: true},
                        {field: 'game_room.provider_id', title: __('Provider_id'), operate: '='},
                        {field: 'game_room.name', title: __('Game_name'), operate: 'LIKE'},
                        {field: 'bet_id', title: __('Bet_id'), operate: 'LIKE'},
                        {field: 'bet_amount', title: __('Bet_amount'), operate:'BETWEEN'},
                        {field: 'transfer_amount', title: __('Transfer_amount'), operate:'BETWEEN'},
                        {field: 'transfer_amount', title: __('Win_lose'), operate:'BETWEEN', formatter: function(value, row, index) {
                            if (parseFloat(value) > 0) {
                                return '<span style="color: red; font-weight: bold;">' + __('Win') + '</span>';
                            } else if (parseFloat(value) < 0) {
                                return '<span style="color: green; font-weight: bold;">' + __('Lose') + '</span>';
                            } else {
                                return '<span style="color: gray;">' + __('Draw') + '</span>';
                            }
                        }},
                        {field: 'betting_amount', title: __('Betting_amount'), operate:'BETWEEN'},
                        {field: 'tax', title: __('Tax'), operate:'BETWEEN'},
                        {field: 'balance_after_game', title: __('Balance_after_game'), operate:'BETWEEN'},
                        {field: 'is_real', title: __('Is_free'), searchList: {"0":__('No'),"1":__('Yes')}, formatter: function(value, row, index) {
                            return row.is_real_text ? row.is_real_text : value;
                        }},
                        {field: 'created_at', title: __('Created_at'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定玩家ID点击事件
            $(document).on('click', '.btn-player-details', function () {
                var id = $(this).data('id');
                // 打开弹窗显示玩家详情，包含多个标签页
                Fast.api.open('players/players/player_detail?ids=' + id, id + '玩家信息', {
                    area: ['80%', '90%'],
                    callback: function (data) {
                        // 关闭弹窗后可能需要刷新表格
                        table.bootstrapTable('refresh');
                    }
                });
                return false;
            });
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
