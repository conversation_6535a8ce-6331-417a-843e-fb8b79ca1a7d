define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'players/balance_logs/index' + location.search,
                    table: 'player_balance_logs',
                },
                search: false,
                commonSearch: true,
                searchFormVisible: true,
                showExport: false,
                showToggle: false,
                showColumns: false,
                showRefresh: false
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                sortOrder: 'desc',
                toolbar: '#toolbar',
                columns: [
                    [
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'player_id', title: __('Player_id'), operate: '=', formatter: function(value, row, index) {
                            // 使玩家ID可点击，唤起玩家信息小窗
                            return '<a href="javascript:;" class="btn-player-details" data-id="' + value + '">' + value + '</a>';
                        }},
                        {field: 'transaction_type', title: __('Transaction_type'), searchList: $.getJSON('players/balance_logs/transactionTypeList'), formatter: function(value, row, index) {
                            return row.transaction_type_text ? row.transaction_type_text : value;
                        }},
                        {field: 'amount', title: __('Amount'), operate:'BETWEEN', formatter: function(value, row, index) {
                            // 正数显示为绿色，负数显示为红色
                            var color = parseFloat(value) >= 0 ? 'green' : 'red';
                            return '<span style="color: ' + color + '">' + value + '</span>';
                        }},
                        {field: 'balance_before', title: __('Balance_before'), operate:'BETWEEN'},
                        {field: 'balance_after', title: __('Balance_after'), operate:'BETWEEN'},
                        {field: 'balance_type', title: __('Balance_type'), searchList: {"1":__('Account balance'),"2":__('Reward balance')}, formatter: Table.api.formatter.normal},
                        {field: 'created_at', title: __('Created_at'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定玩家ID点击事件
            $(document).on('click', '.btn-player-details', function () {
                var id = $(this).data('id');
                // 打开弹窗显示玩家详情，包含多个标签页
                Fast.api.open('players/players/player_detail?ids=' + id, id + '玩家信息', {
                    area: ['80%', '90%'],
                    callback: function (data) {
                        // 关闭弹窗后可能需要刷新表格
                        table.bootstrapTable('refresh');
                    }
                });
                return false;
            });
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
