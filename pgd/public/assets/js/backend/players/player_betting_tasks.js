define(['jquery', 'bootstrap', 'backend', 'table'], function ($, undefined, Backend, Table) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'players/player_betting_tasks/index' + location.search,
                    table: 'player_betting_tasks',
                },
                search: true,
                commonSearch: true,
                searchFormVisible: true,
                showExport: false,
                showToggle: false,
                showColumns: false,
                showRefresh: true
            });

            var table = $("#table");

            // 初始化表格
            // 添加选择列
            var firstColumns = [{checkbox: true}];

            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                toolbar: '#toolbar',
                pk: 'id',
                sortName: 'id',
                searchFormVisible: true,
                search: true,
                showExport: false,
                showToggle: false,
                showColumns: false,
                showRefresh: true,
                commonSearch: true,
                clickToSelect: true,
                showPaginationSwitch: false,
                columns: [
                    firstColumns.concat([
                        {field: 'id', title: __('Id'), sortable: true, visible: false, operate: false},
                        {field: 'channel_name', title: __('Channel_id') + '(' + __('Channel_name') + ')', operate: false},
                        {field: 'player_id', title: __('Player_id'), searchable: true, operate: '=', formatter: function(value, row, index) {
                            // 使玩家ID可点击，唤起玩家信息小窗
                            return '<a href="javascript:;" class="btn-player-details" data-id="' + value + '">' + value + '</a>';
                        }},
                        {field: 'player_register_time', title: __('Player_register_time'), operate: false, formatter: Table.api.formatter.datetime},
                        {field: 'player_balance', title: __('Player_balance'), operate: false},
                        {field: 'player_total_deposit', title: __('Player_total_deposit'), operate: false},
                        {field: 'player_total_withdraw', title: __('Player_total_withdraw'), operate: false},
                        {field: 'required_betting_amount', title: __('Required_betting_amount'), operate:false},
                        {field: 'completed_betting_amount', title: __('Completed_betting_amount'), operate: false},
                        {field: 'completion_percentage', title: __('Completion_percentage'), operate: false, formatter: function(value, row) {
                            // 确保 value 是数字，并处理整数值
                            if (value !== null && value !== undefined) {
                                // 将 value 转换为数字，然后格式化
                                var numValue = parseFloat(value);
                                return isNaN(numValue) ? '0.00%' : numValue.toFixed(2) + '%';
                            }
                            return '0.00%';
                        }},
                        {field: 'create_time', title: __('Create_time'), operate: false, addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate,
                            buttons: [
                                {
                                    name: 'reset_betting',
                                    text: __('Reset_betting'),
                                    title: __('Reset_betting'),
                                    classname: 'btn btn-xs btn-warning btn-ajax',
                                    icon: 'fa fa-refresh',
                                    url: 'players/player_betting_tasks/reset_betting',
                                    confirm: __('Are you sure you want to reset the betting amount?'),
                                    success: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        table.bootstrapTable('refresh');
                                    },
                                    error: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        return false;
                                    }
                                }
                            ]
                        }
                    ])
                ]
            });

            // 绑定事件
            Table.api.bindevent(table);

            // 批量清空打码量按钮事件
            $(document).on("click", ".btn-batch-reset", function () {
                var ids = Table.api.selectedids(table);
                if (ids.length === 0) {
                    Layer.alert(__('Please select at least one item'));
                    return;
                }

                Layer.confirm(__('Are you sure you want to reset the betting amount of the selected items?'), {icon: 3, title: __('Warning'), offset: 0, shadeClose: true}, function (index) {
                    Layer.close(index);

                    $.ajax({
                        url: 'players/player_betting_tasks/batch_reset',
                        type: 'post',
                        dataType: 'json',
                        data: {
                            params: JSON.stringify({ids: ids})
                        },
                        success: function (data) {
                            if (data.code === 1) {
                                Layer.alert(data.msg);
                                table.bootstrapTable('refresh');
                            } else {
                                Layer.alert(data.msg);
                            }
                        },
                        error: function (xhr) {
                            Layer.alert(__('Network error'));
                        }
                    });
                });
            });

            // 监听表格选择事件，启用/禁用批量按钮
            table.on('check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table', function () {
                var ids = Table.api.selectedids(table);
                $(".btn-batch-reset").toggleClass('disabled', !ids.length);
            });

            // 绑定玩家ID点击事件
            $(document).on('click', '.btn-player-details', function () {
                var id = $(this).data('id');
                // 打开弹窗显示玩家详情，包含多个标签页
                Fast.api.open('players/players/player_detail?ids=' + id, id + '玩家信息', {
                    area: ['80%', '90%'],
                    callback: function (data) {
                        // 关闭弹窗后可能需要刷新表格
                        table.bootstrapTable('refresh');
                    }
                });
                return false;
            });
        }
    };
    return Controller;
});