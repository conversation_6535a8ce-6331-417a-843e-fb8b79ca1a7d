/*
 * Skin: Blue
 * -----------
 */
.skin-blue .main-header .navbar-toggle {
  color: #333;
}
.skin-blue .main-header .navbar-brand {
  color: #333;
  border-right: 1px solid #eee;
}
.skin-blue .main-header .navbar {
  background-color: #fff;
}
.skin-blue .main-header .navbar .nav > li > a {
  color: #444;
}
.skin-blue .main-header .navbar .nav > li > a:hover,
.skin-blue .main-header .navbar .nav > li > a:active,
.skin-blue .main-header .navbar .nav > li > a:focus,
.skin-blue .main-header .navbar .nav .open > a,
.skin-blue .main-header .navbar .nav .open > a:hover,
.skin-blue .main-header .navbar .nav .open > a:focus,
.skin-blue .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.02);
  color: #4e73df;
}
.skin-blue .main-header .navbar .nav-addtabs li > .close-tab {
  color: #4e73df;
}
.skin-blue .main-header .navbar .sidebar-toggle {
  color: #444;
}
.skin-blue .main-header .navbar .sidebar-toggle:hover {
  color: #4e73df;
  background: rgba(0, 0, 0, 0.02);
}
.skin-blue .main-header .navbar > .sidebar-toggle {
  color: #333;
  border-right: 1px solid #eee;
}
.skin-blue .main-header .navbar .navbar-nav > li > a {
  border-right: 1px solid #eee;
}
.skin-blue .main-header .navbar .navbar-custom-menu .navbar-nav > li > a,
.skin-blue .main-header .navbar .navbar-right > li > a {
  border-left: 1px solid #eee;
  border-left: none;
  border-right-width: 0;
}
.skin-blue .main-header > .logo {
  background-color: #4e73df;
  color: #fff;
  border-bottom: 0 solid transparent;
  border-right: 1px solid #4e73df;
  box-shadow: none;
}
.skin-blue .main-header > .logo:hover {
  background-color: #4a70de;
}
@media (max-width: 767px) {
  .skin-blue .main-header > .logo {
    background-color: #fff;
    color: #222;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-blue .main-header > .logo:hover {
    background-color: #fcfcfc;
  }
}
.skin-blue .main-header li.user-header {
  background-color: #4e73df;
}
.skin-blue .main-header .nav-addtabs > li > a,
.skin-blue .main-header .nav-addtabs > li.active > a {
  border-right-color: transparent;
}
.skin-blue .content-header {
  background: transparent;
  box-shadow: none;
}
.skin-blue .wrapper,
.skin-blue .main-sidebar,
.skin-blue .left-side {
  background-color: #605ca8;
}
.skin-blue .user-panel > .info,
.skin-blue .user-panel > .info > a {
  color: #fff;
}
.skin-blue .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-blue .sidebar-menu > li.header {
  color: #a19fcb;
  background: #57539c;
}
.skin-blue .sidebar-menu > li:hover > a,
.skin-blue .sidebar-menu > li.active > a {
  color: #fff;
  background: #5b57a3;
  border-left-color: #fff;
}
.skin-blue .sidebar-menu > li > .treeview-menu {
  background: #555299;
}
.skin-blue .sidebar a {
  color: #c8c5ff;
}
.skin-blue .sidebar a:hover {
  text-decoration: none;
}
.skin-blue .treeview-menu > li > a {
  color: #c8c5ff;
}
.skin-blue .treeview-menu > li.active > a,
.skin-blue .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-blue .sidebar-form {
  border-radius: 3px;
  border: 1px solid #807dba;
  background-color: #807dba;
  margin: 10px 10px;
}
.skin-blue .sidebar-form input[type="text"],
.skin-blue .sidebar-form .btn {
  box-shadow: none;
  background-color: #807dba;
  border: 1px solid transparent;
  height: 35px;
}
.skin-blue .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-blue .sidebar-form input[type="text"]:focus,
.skin-blue .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-blue .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-blue .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-blue .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-blue .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-blue.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
.skin-blue .sidebar-form input[type="text"]::-moz-placeholder {
  color: #fff;
  opacity: 1;
}
.skin-blue .sidebar-form input[type="text"]:-ms-input-placeholder {
  color: #fff;
}
.skin-blue .sidebar-form input[type="text"]::-webkit-input-placeholder {
  color: #fff;
}
.skin-blue .sidebar-form input[type="text"],
.skin-blue .sidebar-form .btn {
  color: #fff;
}
@media (max-width: 767px) {
  .skin-blue.multiplenav .main-header .navbar {
    background-color: #605ca8;
  }
  .skin-blue.multiplenav .main-header .navbar .nav > li > a {
    color: #fff;
  }
  .skin-blue.multiplenav .main-header .navbar .nav > li > a:hover,
  .skin-blue.multiplenav .main-header .navbar .nav > li > a:active,
  .skin-blue.multiplenav .main-header .navbar .nav > li > a:focus,
  .skin-blue.multiplenav .main-header .navbar .nav .open > a,
  .skin-blue.multiplenav .main-header .navbar .nav .open > a:hover,
  .skin-blue.multiplenav .main-header .navbar .nav .open > a:focus,
  .skin-blue.multiplenav .main-header .navbar .nav > .active > a {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
  }
  .skin-blue.multiplenav .main-header .navbar .nav-addtabs li > .close-tab {
    color: #f6f6f6;
  }
  .skin-blue.multiplenav .main-header .navbar .sidebar-toggle {
    color: #fff;
  }
  .skin-blue.multiplenav .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.1);
  }
  .skin-blue.multiplenav .main-header > .logo {
    background-color: #605ca8;
    color: #fff;
    border-bottom: 0 solid transparent;
  }
  .skin-blue.multiplenav .main-header > .logo:hover {
    background-color: #5d59a6;
  }
  .skin-blue.multiplenav .sidebar .mobilenav a.btn-app {
    background: #807dba;
    color: #fff;
  }
  .skin-blue.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #fff;
    color: #807dba;
  }
}
/*
 * Skin: Blue
 * ----------
 */
.skin-blue-light .main-header .navbar {
  background-color: #4e73df;
}
.skin-blue-light .main-header .navbar .nav > li > a {
  color: #fff;
}
.skin-blue-light .main-header .navbar .nav > li > a:hover,
.skin-blue-light .main-header .navbar .nav > li > a:active,
.skin-blue-light .main-header .navbar .nav > li > a:focus,
.skin-blue-light .main-header .navbar .nav .open > a,
.skin-blue-light .main-header .navbar .nav .open > a:hover,
.skin-blue-light .main-header .navbar .nav .open > a:focus,
.skin-blue-light .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.05);
  color: #f6f6f6;
}
.skin-blue-light .main-header .navbar .nav-addtabs li > .close-tab {
  color: #f6f6f6;
}
.skin-blue-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-blue-light .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.05);
}
.skin-blue-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-blue-light .main-header .navbar .sidebar-toggle:hover {
  background-color: #3862db;
}
@media (max-width: 767px) {
  .skin-blue-light .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .skin-blue-light .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .skin-blue-light .main-header .navbar .dropdown-menu li a:hover {
    background: #3862db;
  }
}
.skin-blue-light .main-header .logo {
  background-color: #4e73df;
  color: #fff;
  border-bottom: 0 solid transparent;
}
.skin-blue-light .main-header .logo:hover {
  background-color: #4a70de;
}
.skin-blue-light .main-header li.user-header {
  background-color: #4e73df;
}
.skin-blue-light .content-header {
  background: transparent;
}
.skin-blue-light .wrapper,
.skin-blue-light .main-sidebar,
.skin-blue-light .left-side {
  background-color: #f9fafc;
}
.skin-blue-light .content-wrapper,
.skin-blue-light .main-footer {
  border-left: 1px solid #d2d6de;
}
.skin-blue-light .user-panel > .info,
.skin-blue-light .user-panel > .info > a {
  color: #444;
}
.skin-blue-light .sidebar-menu > li {
  -webkit-transition: border-left-color 0.3s ease;
  -o-transition: border-left-color 0.3s ease;
  transition: border-left-color 0.3s ease;
}
.skin-blue-light .sidebar-menu > li.header {
  color: #848484;
  background: #f9fafc;
}
.skin-blue-light .sidebar-menu > li:hover > a,
.skin-blue-light .sidebar-menu > li.active > a {
  color: #000;
  background: #f4f4f5;
  border-left-color: #4e73df;
}
.skin-blue-light .sidebar-menu > li.active {
  border-left-color: #4e73df;
}
.skin-blue-light .sidebar-menu > li > .treeview-menu {
  background: #f4f4f5;
}
.skin-blue-light .sidebar a {
  color: #444;
}
.skin-blue-light .sidebar a:hover {
  text-decoration: none;
}
.skin-blue-light .treeview-menu > li > a {
  color: #777;
}
.skin-blue-light .treeview-menu > li.active > a,
.skin-blue-light .treeview-menu > li > a:hover {
  color: #000;
}
.skin-blue-light .treeview-menu > li.active > a {
  font-weight: 600;
}
.skin-blue-light .sidebar-form {
  border-radius: 3px;
  border: 1px solid #d2d6de;
  margin: 10px 10px;
}
.skin-blue-light .sidebar-form input[type="text"],
.skin-blue-light .sidebar-form .btn {
  box-shadow: none;
  background-color: #fff;
  border: 1px solid transparent;
  height: 35px;
}
.skin-blue-light .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-blue-light .sidebar-form input[type="text"]:focus,
.skin-blue-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-blue-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-blue-light .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
@media (min-width: 768px) {
  .skin-blue-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
    border-left: 1px solid #d2d6de;
  }
}
.skin-blue-light .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-blue-light.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
.skin-blue-light .main-footer {
  border-top-color: #d2d6de;
}
.skin-blue-light .main-sidebar {
  -webkit-box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
  box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
}
.skin-blue-light .content-wrapper,
.skin-blue-light .main-footer {
  border-left: none;
}
@media (max-width: 767px) {
  .skin-blue-light.multiplenav .sidebar .mobilenav a.btn-app {
    background: #eceff3;
    color: #757575;
  }
  .skin-blue-light.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #4e73df;
    color: #fff;
  }
}
/*
 * Skin: Black
 * -----------
 */
.skin-black .main-header {
  -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
}
.skin-black .main-header .navbar-toggle {
  color: #333;
}
.skin-black .main-header .navbar-brand {
  color: #333;
  border-right: 1px solid #eee;
}
.skin-black .main-header .navbar {
  background-color: #fff;
}
.skin-black .main-header .navbar .nav > li > a {
  color: #666;
}
.skin-black .main-header .navbar .nav > li > a:hover,
.skin-black .main-header .navbar .nav > li > a:active,
.skin-black .main-header .navbar .nav > li > a:focus,
.skin-black .main-header .navbar .nav .open > a,
.skin-black .main-header .navbar .nav .open > a:hover,
.skin-black .main-header .navbar .nav .open > a:focus,
.skin-black .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.02);
  color: #444;
}
.skin-black .main-header .navbar .nav-addtabs li > .close-tab {
  color: #444;
}
.skin-black .main-header .navbar .sidebar-toggle {
  color: #666;
}
.skin-black .main-header .navbar .sidebar-toggle:hover {
  color: #444;
  background: rgba(0, 0, 0, 0.02);
}
.skin-black .main-header .navbar > .sidebar-toggle {
  color: #333;
  border-right: 1px solid #eee;
}
.skin-black .main-header .navbar .navbar-nav > li > a {
  border-right: 1px solid #eee;
}
.skin-black .main-header .navbar .navbar-custom-menu .navbar-nav > li > a,
.skin-black .main-header .navbar .navbar-right > li > a {
  border-left: 1px solid #eee;
  border-left: none;
  border-right-width: 0;
}
.skin-black .main-header .logo {
  background-color: #605ca8;
  color: #fff;
  border-bottom: 0 solid transparent;
  border-right: 1px solid #605ca8;
}
.skin-black .main-header .logo:hover {
  background-color: #5d59a6;
}
@media (max-width: 767px) {
  .skin-black .main-header .logo {
    background-color: #fff;
    color: #222;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-black .main-header .logo:hover {
    background-color: #fcfcfc;
  }
}
.skin-black .main-header li.user-header {
  background-color: #222;
}
.skin-black .main-header .nav-addtabs > li > a,
.skin-black .main-header .nav-addtabs > li.active > a {
  border-right-color: transparent;
}
.skin-black .content-header {
  background: transparent;
  box-shadow: none;
}
.skin-black .wrapper,
.skin-black .main-sidebar,
.skin-black .left-side {
  background-color: #605ca8;
}
.skin-black .user-panel > .info,
.skin-black .user-panel > .info > a {
  color: #fff;
}
.skin-black .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-black .sidebar-menu > li.header {
  color: #a19fcb;
  background: #57539c;
}
.skin-black .sidebar-menu > li:hover > a,
.skin-black .sidebar-menu > li.active > a {
  color: #fff;
  background: #5b57a3;
  border-left-color: #fff;
}
.skin-black .sidebar-menu > li > .treeview-menu {
  background: #555299;
}
.skin-black .sidebar a {
  color: #c8c5ff;
}
.skin-black .sidebar a:hover {
  text-decoration: none;
}
.skin-black .treeview-menu > li > a {
  color: #c8c5ff;
}
.skin-black .treeview-menu > li.active > a,
.skin-black .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-black .sidebar-form {
  border-radius: 3px;
  border: 1px solid #807dba;
  background-color: #807dba;
  margin: 10px 10px;
}
.skin-black .sidebar-form input[type="text"],
.skin-black .sidebar-form .btn {
  box-shadow: none;
  background-color: #807dba;
  border: 1px solid transparent;
  height: 35px;
}
.skin-black .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-black .sidebar-form input[type="text"]:focus,
.skin-black .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-black .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-black .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-black.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
@media (max-width: 767px) {
  .skin-black.multiplenav .main-header .navbar {
    background-color: #605ca8;
  }
  .skin-black.multiplenav .main-header .navbar .nav > li > a {
    color: #fff;
  }
  .skin-black.multiplenav .main-header .navbar .nav > li > a:hover,
  .skin-black.multiplenav .main-header .navbar .nav > li > a:active,
  .skin-black.multiplenav .main-header .navbar .nav > li > a:focus,
  .skin-black.multiplenav .main-header .navbar .nav .open > a,
  .skin-black.multiplenav .main-header .navbar .nav .open > a:hover,
  .skin-black.multiplenav .main-header .navbar .nav .open > a:focus,
  .skin-black.multiplenav .main-header .navbar .nav > .active > a {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
  }
  .skin-black.multiplenav .main-header .navbar .nav-addtabs li > .close-tab {
    color: #f6f6f6;
  }
  .skin-black.multiplenav .main-header .navbar .sidebar-toggle {
    color: #fff;
  }
  .skin-black.multiplenav .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.1);
  }
  .skin-black.multiplenav .main-header > .logo {
    background-color: #605ca8;
    color: #fff;
    border-bottom: 0 solid transparent;
  }
  .skin-black.multiplenav .main-header > .logo:hover {
    background-color: #5d59a6;
  }
  .skin-black.multiplenav .sidebar .mobilenav a.btn-app {
    background: #807dba;
    color: #fff;
  }
  .skin-black.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #fff;
    color: #807dba;
  }
}
/*
 * Skin: Black light
 * -----------
 */
.skin-black-light .main-header {
  -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
}
.skin-black-light .main-header .navbar-toggle {
  color: #333;
}
.skin-black-light .main-header .navbar-brand {
  color: #333;
  border-right: 1px solid #eee;
}
.skin-black-light .main-header .navbar {
  background-color: #222d32;
}
.skin-black-light .main-header .navbar .nav > li > a {
  color: #fff;
}
.skin-black-light .main-header .navbar .nav > li > a:hover,
.skin-black-light .main-header .navbar .nav > li > a:active,
.skin-black-light .main-header .navbar .nav > li > a:focus,
.skin-black-light .main-header .navbar .nav .open > a,
.skin-black-light .main-header .navbar .nav .open > a:hover,
.skin-black-light .main-header .navbar .nav .open > a:focus,
.skin-black-light .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.3);
  color: #f6f6f6;
}
.skin-black-light .main-header .navbar .nav-addtabs li > .close-tab {
  color: #f6f6f6;
}
.skin-black-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-black-light .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.3);
}
.skin-black-light .main-header .navbar > .sidebar-toggle {
  color: #333;
  border-right: 1px solid #eee;
}
.skin-black-light .main-header .navbar .navbar-nav > li > a {
  color: #fff;
}
.skin-black-light .main-header .navbar .navbar-custom-menu .navbar-nav > li > a,
.skin-black-light .main-header .navbar .navbar-right > li > a {
  border-left: none;
  border-right-width: 0;
}
.skin-black-light .main-header .logo {
  background-color: #222d32;
  color: #fff;
  border-bottom: 0 solid transparent;
}
.skin-black-light .main-header .logo:hover {
  background-color: #202a2f;
}
@media (max-width: 767px) {
  .skin-black-light .main-header .logo {
    background-color: #222d32;
    color: #fff;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-black-light .main-header .logo:hover {
    background-color: #202a2f;
  }
}
.skin-black-light .main-header li.user-header {
  background-color: #222d32;
}
.skin-black-light .content-header {
  background: transparent;
  box-shadow: none;
}
.skin-black-light .wrapper,
.skin-black-light .main-sidebar,
.skin-black-light .left-side {
  background-color: #f9fafc;
}
.skin-black-light .content-wrapper,
.skin-black-light .main-footer {
  border-left: 1px solid #d2d6de;
}
.skin-black-light .user-panel > .info,
.skin-black-light .user-panel > .info > a {
  color: #444;
}
.skin-black-light .sidebar-menu > li {
  -webkit-transition: border-left-color 0.3s ease;
  -o-transition: border-left-color 0.3s ease;
  transition: border-left-color 0.3s ease;
}
.skin-black-light .sidebar-menu > li.header {
  color: #848484;
  background: #f9fafc;
}
.skin-black-light .sidebar-menu > li:hover > a,
.skin-black-light .sidebar-menu > li.active > a {
  color: #000;
  background: #f4f4f5;
  border-left-color: #222d32;
}
.skin-black-light .sidebar-menu > li.active {
  border-left-color: #222d32;
}
.skin-black-light .sidebar-menu > li > .treeview-menu {
  background: #f4f4f5;
}
.skin-black-light .sidebar a {
  color: #444;
}
.skin-black-light .sidebar a:hover {
  text-decoration: none;
}
.skin-black-light .treeview-menu > li > a {
  color: #777;
}
.skin-black-light .treeview-menu > li.active > a,
.skin-black-light .treeview-menu > li > a:hover {
  color: #000;
}
.skin-black-light .treeview-menu > li.active > a {
  font-weight: 600;
}
.skin-black-light .sidebar-form {
  border-radius: 3px;
  border: 1px solid #d2d6de;
  margin: 10px 10px;
}
.skin-black-light .sidebar-form input[type="text"],
.skin-black-light .sidebar-form .btn {
  box-shadow: none;
  background-color: #fff;
  border: 1px solid transparent;
  height: 35px;
}
.skin-black-light .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-black-light .sidebar-form input[type="text"]:focus,
.skin-black-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-black-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-black-light .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
@media (min-width: 768px) {
  .skin-black-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
    border-left: 1px solid #d2d6de;
  }
}
.skin-black-light .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-black-light.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
.skin-black-light .main-sidebar {
  -webkit-box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
  box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
}
.skin-black-light .content-wrapper,
.skin-black-light .main-footer {
  border-left: none;
}
@media (max-width: 767px) {
  .skin-black-light.multiplenav .sidebar .mobilenav a.btn-app {
    background: #eceff3;
    color: #757575;
  }
  .skin-black-light.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #222d32;
    color: #fff;
  }
}
/*
 * Skin: Green
 * -----------
 */
.skin-green .main-header {
  background-color: #18bc9c;
}
.skin-green .main-header .navbar {
  background-color: #18bc9c;
}
.skin-green .main-header .navbar .nav > li > a {
  color: #fff;
}
.skin-green .main-header .navbar .nav > li > a:hover,
.skin-green .main-header .navbar .nav > li > a:active,
.skin-green .main-header .navbar .nav > li > a:focus,
.skin-green .main-header .navbar .nav .open > a,
.skin-green .main-header .navbar .nav .open > a:hover,
.skin-green .main-header .navbar .nav .open > a:focus,
.skin-green .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.05);
  color: #f6f6f6;
}
.skin-green .main-header .navbar .nav-addtabs li > .close-tab {
  color: #f6f6f6;
}
.skin-green .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-green .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.05);
}
.skin-green .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-green .main-header .navbar .sidebar-toggle:hover {
  background-color: #15a589;
}
@media (max-width: 767px) {
  .skin-green .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .skin-green .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .skin-green .main-header .navbar .dropdown-menu li a:hover {
    background: #15a589;
  }
}
.skin-green .main-header .logo {
  background-color: #15a589;
  color: #fff;
  border-bottom: 0 solid transparent;
}
.skin-green .main-header .logo:hover {
  background-color: #15a185;
}
@media (max-width: 767px) {
  .skin-green .main-header .logo {
    background-color: #18bc9c;
    color: #fff;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-green .main-header .logo:hover {
    background-color: #17b798;
  }
}
.skin-green .main-header li.user-header {
  background-color: #18bc9c;
}
.skin-green .content-header {
  background: transparent;
}
.skin-green .wrapper,
.skin-green .main-sidebar,
.skin-green .left-side {
  background-color: #605ca8;
}
.skin-green .user-panel > .info,
.skin-green .user-panel > .info > a {
  color: #fff;
}
.skin-green .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-green .sidebar-menu > li.header {
  color: #a19fcb;
  background: #57539c;
}
.skin-green .sidebar-menu > li:hover > a,
.skin-green .sidebar-menu > li.active > a {
  color: #fff;
  background: #5b57a3;
  border-left-color: #18bc9c;
}
.skin-green .sidebar-menu > li > .treeview-menu {
  background: #555299;
}
.skin-green .sidebar a {
  color: #c8c5ff;
}
.skin-green .sidebar a:hover {
  text-decoration: none;
}
.skin-green .treeview-menu > li > a {
  color: #c8c5ff;
}
.skin-green .treeview-menu > li.active > a,
.skin-green .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-green .sidebar-form {
  border-radius: 3px;
  border: 1px solid #807dba;
  background-color: #807dba;
  margin: 10px 10px;
}
.skin-green .sidebar-form input[type="text"],
.skin-green .sidebar-form .btn {
  box-shadow: none;
  background-color: #807dba;
  border: 1px solid transparent;
  height: 35px;
}
.skin-green .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-green .sidebar-form input[type="text"]:focus,
.skin-green .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-green .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-green .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-green .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-green .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-green.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
@media (max-width: 767px) {
  .skin-green.multiplenav .sidebar .mobilenav a.btn-app {
    background: #807dba;
    color: #fff;
  }
  .skin-green.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #18bc9c;
    color: #fff;
  }
}
/*
 * Skin: Green
 * -----------
 */
.skin-green-light .main-header {
  background-color: #18bc9c;
}
.skin-green-light .main-header .navbar {
  background-color: #18bc9c;
}
.skin-green-light .main-header .navbar .nav > li > a {
  color: #fff;
}
.skin-green-light .main-header .navbar .nav > li > a:hover,
.skin-green-light .main-header .navbar .nav > li > a:active,
.skin-green-light .main-header .navbar .nav > li > a:focus,
.skin-green-light .main-header .navbar .nav .open > a,
.skin-green-light .main-header .navbar .nav .open > a:hover,
.skin-green-light .main-header .navbar .nav .open > a:focus,
.skin-green-light .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.05);
  color: #f6f6f6;
}
.skin-green-light .main-header .navbar .nav-addtabs li > .close-tab {
  color: #f6f6f6;
}
.skin-green-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-green-light .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.05);
}
.skin-green-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-green-light .main-header .navbar .sidebar-toggle:hover {
  background-color: #15a589;
}
@media (max-width: 767px) {
  .skin-green-light .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .skin-green-light .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .skin-green-light .main-header .navbar .dropdown-menu li a:hover {
    background: #15a589;
  }
}
.skin-green-light .main-header .logo {
  background-color: #18bc9c;
  color: #fff;
  border-bottom: 0 solid transparent;
}
.skin-green-light .main-header .logo:hover {
  background-color: #17b798;
}
.skin-green-light .main-header li.user-header {
  background-color: #18bc9c;
}
.skin-green-light .content-header {
  background: transparent;
}
.skin-green-light .wrapper,
.skin-green-light .main-sidebar,
.skin-green-light .left-side {
  background-color: #f9fafc;
}
.skin-green-light .content-wrapper,
.skin-green-light .main-footer {
  border-left: 1px solid #d2d6de;
}
.skin-green-light .user-panel > .info,
.skin-green-light .user-panel > .info > a {
  color: #444;
}
.skin-green-light .sidebar-menu > li {
  -webkit-transition: border-left-color 0.3s ease;
  -o-transition: border-left-color 0.3s ease;
  transition: border-left-color 0.3s ease;
}
.skin-green-light .sidebar-menu > li.header {
  color: #848484;
  background: #f9fafc;
}
.skin-green-light .sidebar-menu > li:hover > a,
.skin-green-light .sidebar-menu > li.active > a {
  color: #000;
  background: #f4f4f5;
  border-left-color: #18bc9c;
}
.skin-green-light .sidebar-menu > li.active {
  border-left-color: #18bc9c;
}
.skin-green-light .sidebar-menu > li > .treeview-menu {
  background: #f4f4f5;
}
.skin-green-light .sidebar a {
  color: #444;
}
.skin-green-light .sidebar a:hover {
  text-decoration: none;
}
.skin-green-light .treeview-menu > li > a {
  color: #777;
}
.skin-green-light .treeview-menu > li.active > a,
.skin-green-light .treeview-menu > li > a:hover {
  color: #000;
}
.skin-green-light .treeview-menu > li.active > a {
  font-weight: 600;
}
.skin-green-light .sidebar-form {
  border-radius: 3px;
  border: 1px solid #d2d6de;
  margin: 10px 10px;
}
.skin-green-light .sidebar-form input[type="text"],
.skin-green-light .sidebar-form .btn {
  box-shadow: none;
  background-color: #fff;
  border: 1px solid transparent;
  height: 35px;
}
.skin-green-light .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-green-light .sidebar-form input[type="text"]:focus,
.skin-green-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-green-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-green-light .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
@media (min-width: 768px) {
  .skin-green-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
    border-left: 1px solid #d2d6de;
  }
}
.skin-green-light .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-green-light.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
.skin-green-light .main-sidebar {
  -webkit-box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
  box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
}
.skin-green-light .content-wrapper,
.skin-green-light .main-footer {
  border-left: none;
}
@media (max-width: 767px) {
  .skin-green-light.multiplenav .sidebar .mobilenav a.btn-app {
    background: #eceff3;
    color: #757575;
  }
  .skin-green-light.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #18bc9c;
    color: #fff;
  }
}
/*
 * Skin: Red
 * ---------
 */
.skin-red .main-header {
  background-color: #f75444;
}
.skin-red .main-header .navbar {
  background-color: #f75444;
}
.skin-red .main-header .navbar .nav > li > a {
  color: #fff;
}
.skin-red .main-header .navbar .nav > li > a:hover,
.skin-red .main-header .navbar .nav > li > a:active,
.skin-red .main-header .navbar .nav > li > a:focus,
.skin-red .main-header .navbar .nav .open > a,
.skin-red .main-header .navbar .nav .open > a:hover,
.skin-red .main-header .navbar .nav .open > a:focus,
.skin-red .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.05);
  color: #f6f6f6;
}
.skin-red .main-header .navbar .nav-addtabs li > .close-tab {
  color: #f6f6f6;
}
.skin-red .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-red .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.05);
}
.skin-red .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-red .main-header .navbar .sidebar-toggle:hover {
  background-color: #f63e2c;
}
@media (max-width: 767px) {
  .skin-red .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .skin-red .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .skin-red .main-header .navbar .dropdown-menu li a:hover {
    background: #f63e2c;
  }
}
.skin-red .main-header .logo {
  background-color: #f63e2c;
  color: #fff;
  border-bottom: 0 solid transparent;
}
.skin-red .main-header .logo:hover {
  background-color: #f63927;
}
@media (max-width: 767px) {
  .skin-red .main-header .logo {
    background-color: #f75444;
    color: #fff;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-red .main-header .logo:hover {
    background-color: #f7503f;
  }
}
.skin-red .main-header li.user-header {
  background-color: #f75444;
}
.skin-red .content-header {
  background: transparent;
}
.skin-red .wrapper,
.skin-red .main-sidebar,
.skin-red .left-side {
  background-color: #605ca8;
}
.skin-red .user-panel > .info,
.skin-red .user-panel > .info > a {
  color: #fff;
}
.skin-red .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-red .sidebar-menu > li.header {
  color: #a19fcb;
  background: #57539c;
}
.skin-red .sidebar-menu > li:hover > a,
.skin-red .sidebar-menu > li.active > a {
  color: #fff;
  background: #5b57a3;
  border-left-color: #f75444;
}
.skin-red .sidebar-menu > li > .treeview-menu {
  background: #555299;
}
.skin-red .sidebar a {
  color: #c8c5ff;
}
.skin-red .sidebar a:hover {
  text-decoration: none;
}
.skin-red .treeview-menu > li > a {
  color: #c8c5ff;
}
.skin-red .treeview-menu > li.active > a,
.skin-red .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-red .sidebar-form {
  border-radius: 3px;
  border: 1px solid #807dba;
  background-color: #807dba;
  margin: 10px 10px;
}
.skin-red .sidebar-form input[type="text"],
.skin-red .sidebar-form .btn {
  box-shadow: none;
  background-color: #807dba;
  border: 1px solid transparent;
  height: 35px;
}
.skin-red .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-red .sidebar-form input[type="text"]:focus,
.skin-red .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-red .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-red .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-red .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-red .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-red.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
@media (max-width: 767px) {
  .skin-red.multiplenav .sidebar .mobilenav a.btn-app {
    background: #807dba;
    color: #fff;
  }
  .skin-red.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #f75444;
    color: #fff;
  }
}
/*
 * Skin: Red
 * ---------
 */
.skin-red-light .main-header {
  background-color: #f75444;
}
.skin-red-light .main-header .navbar {
  background-color: #f75444;
}
.skin-red-light .main-header .navbar .nav > li > a {
  color: #fff;
}
.skin-red-light .main-header .navbar .nav > li > a:hover,
.skin-red-light .main-header .navbar .nav > li > a:active,
.skin-red-light .main-header .navbar .nav > li > a:focus,
.skin-red-light .main-header .navbar .nav .open > a,
.skin-red-light .main-header .navbar .nav .open > a:hover,
.skin-red-light .main-header .navbar .nav .open > a:focus,
.skin-red-light .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.05);
  color: #f6f6f6;
}
.skin-red-light .main-header .navbar .nav-addtabs li > .close-tab {
  color: #f6f6f6;
}
.skin-red-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-red-light .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.05);
}
.skin-red-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-red-light .main-header .navbar .sidebar-toggle:hover {
  background-color: #f63e2c;
}
@media (max-width: 767px) {
  .skin-red-light .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .skin-red-light .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .skin-red-light .main-header .navbar .dropdown-menu li a:hover {
    background: #f63e2c;
  }
}
.skin-red-light .main-header .logo {
  background-color: #f75444;
  color: #fff;
  border-bottom: 0 solid transparent;
}
.skin-red-light .main-header .logo:hover {
  background-color: #f7503f;
}
.skin-red-light .main-header li.user-header {
  background-color: #f75444;
}
.skin-red-light .content-header {
  background: transparent;
}
.skin-red-light .wrapper,
.skin-red-light .main-sidebar,
.skin-red-light .left-side {
  background-color: #f9fafc;
}
.skin-red-light .content-wrapper,
.skin-red-light .main-footer {
  border-left: 1px solid #d2d6de;
}
.skin-red-light .user-panel > .info,
.skin-red-light .user-panel > .info > a {
  color: #444;
}
.skin-red-light .sidebar-menu > li {
  -webkit-transition: border-left-color 0.3s ease;
  -o-transition: border-left-color 0.3s ease;
  transition: border-left-color 0.3s ease;
}
.skin-red-light .sidebar-menu > li.header {
  color: #848484;
  background: #f9fafc;
}
.skin-red-light .sidebar-menu > li:hover > a,
.skin-red-light .sidebar-menu > li.active > a {
  color: #000;
  background: #f4f4f5;
  border-left-color: #f75444;
}
.skin-red-light .sidebar-menu > li.active {
  border-left-color: #f75444;
}
.skin-red-light .sidebar-menu > li > .treeview-menu {
  background: #f4f4f5;
}
.skin-red-light .sidebar a {
  color: #444;
}
.skin-red-light .sidebar a:hover {
  text-decoration: none;
}
.skin-red-light .treeview-menu > li > a {
  color: #777;
}
.skin-red-light .treeview-menu > li.active > a,
.skin-red-light .treeview-menu > li > a:hover {
  color: #000;
}
.skin-red-light .treeview-menu > li.active > a {
  font-weight: 600;
}
.skin-red-light .sidebar-form {
  border-radius: 3px;
  border: 1px solid #d2d6de;
  margin: 10px 10px;
}
.skin-red-light .sidebar-form input[type="text"],
.skin-red-light .sidebar-form .btn {
  box-shadow: none;
  background-color: #fff;
  border: 1px solid transparent;
  height: 35px;
}
.skin-red-light .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-red-light .sidebar-form input[type="text"]:focus,
.skin-red-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-red-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-red-light .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
@media (min-width: 768px) {
  .skin-red-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
    border-left: 1px solid #d2d6de;
  }
}
.skin-red-light .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-red-light.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
.skin-red-light .main-sidebar {
  -webkit-box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
  box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
}
.skin-red-light .content-wrapper,
.skin-red-light .main-footer {
  border-left: none;
}
@media (max-width: 767px) {
  .skin-red-light.multiplenav .sidebar .mobilenav a.btn-app {
    background: #eceff3;
    color: #757575;
  }
  .skin-red-light.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #f75444;
    color: #fff;
  }
}
/*
 * Skin: Yellow
 * ------------
 */
.skin-yellow .main-header {
  background-color: #f39c12;
}
.skin-yellow .main-header .navbar {
  background-color: #f39c12;
}
.skin-yellow .main-header .navbar .nav > li > a {
  color: #fff;
}
.skin-yellow .main-header .navbar .nav > li > a:hover,
.skin-yellow .main-header .navbar .nav > li > a:active,
.skin-yellow .main-header .navbar .nav > li > a:focus,
.skin-yellow .main-header .navbar .nav .open > a,
.skin-yellow .main-header .navbar .nav .open > a:hover,
.skin-yellow .main-header .navbar .nav .open > a:focus,
.skin-yellow .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.05);
  color: #f6f6f6;
}
.skin-yellow .main-header .navbar .nav-addtabs li > .close-tab {
  color: #f6f6f6;
}
.skin-yellow .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-yellow .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.05);
}
.skin-yellow .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-yellow .main-header .navbar .sidebar-toggle:hover {
  background-color: #e08e0b;
}
@media (max-width: 767px) {
  .skin-yellow .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .skin-yellow .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .skin-yellow .main-header .navbar .dropdown-menu li a:hover {
    background: #e08e0b;
  }
}
.skin-yellow .main-header .logo {
  background-color: #e08e0b;
  color: #fff;
  border-bottom: 0 solid transparent;
}
.skin-yellow .main-header .logo:hover {
  background-color: #db8b0b;
}
@media (max-width: 767px) {
  .skin-yellow .main-header .logo {
    background-color: #f39c12;
    color: #fff;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-yellow .main-header .logo:hover {
    background-color: #f39a0d;
  }
}
.skin-yellow .main-header li.user-header {
  background-color: #f39c12;
}
.skin-yellow .content-header {
  background: transparent;
}
.skin-yellow .wrapper,
.skin-yellow .main-sidebar,
.skin-yellow .left-side {
  background-color: #605ca8;
}
.skin-yellow .user-panel > .info,
.skin-yellow .user-panel > .info > a {
  color: #fff;
}
.skin-yellow .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-yellow .sidebar-menu > li.header {
  color: #a19fcb;
  background: #57539c;
}
.skin-yellow .sidebar-menu > li:hover > a,
.skin-yellow .sidebar-menu > li.active > a {
  color: #fff;
  background: #5b57a3;
  border-left-color: #f39c12;
}
.skin-yellow .sidebar-menu > li > .treeview-menu {
  background: #555299;
}
.skin-yellow .sidebar a {
  color: #c8c5ff;
}
.skin-yellow .sidebar a:hover {
  text-decoration: none;
}
.skin-yellow .treeview-menu > li > a {
  color: #c8c5ff;
}
.skin-yellow .treeview-menu > li.active > a,
.skin-yellow .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-yellow .sidebar-form {
  border-radius: 3px;
  border: 1px solid #807dba;
  background-color: #807dba;
  margin: 10px 10px;
}
.skin-yellow .sidebar-form input[type="text"],
.skin-yellow .sidebar-form .btn {
  box-shadow: none;
  background-color: #807dba;
  border: 1px solid transparent;
  height: 35px;
}
.skin-yellow .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-yellow .sidebar-form input[type="text"]:focus,
.skin-yellow .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-yellow .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-yellow .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-yellow .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-yellow .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-yellow.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
@media (max-width: 767px) {
  .skin-yellow.multiplenav .sidebar .mobilenav a.btn-app {
    background: #807dba;
    color: #fff;
  }
  .skin-yellow.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #f39c12;
    color: #fff;
  }
}
/*
 * Skin: Yellow light
 * ------------
 */
.skin-yellow-light .main-header {
  background-color: #f39c12;
}
.skin-yellow-light .main-header .navbar {
  background-color: #f39c12;
}
.skin-yellow-light .main-header .navbar .nav > li > a {
  color: #fff;
}
.skin-yellow-light .main-header .navbar .nav > li > a:hover,
.skin-yellow-light .main-header .navbar .nav > li > a:active,
.skin-yellow-light .main-header .navbar .nav > li > a:focus,
.skin-yellow-light .main-header .navbar .nav .open > a,
.skin-yellow-light .main-header .navbar .nav .open > a:hover,
.skin-yellow-light .main-header .navbar .nav .open > a:focus,
.skin-yellow-light .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.05);
  color: #f6f6f6;
}
.skin-yellow-light .main-header .navbar .nav-addtabs li > .close-tab {
  color: #f6f6f6;
}
.skin-yellow-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-yellow-light .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.05);
}
.skin-yellow-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-yellow-light .main-header .navbar .sidebar-toggle:hover {
  background-color: #e08e0b;
}
@media (max-width: 767px) {
  .skin-yellow-light .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .skin-yellow-light .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .skin-yellow-light .main-header .navbar .dropdown-menu li a:hover {
    background: #e08e0b;
  }
}
.skin-yellow-light .main-header .logo {
  background-color: #f39c12;
  color: #fff;
  border-bottom: 0 solid transparent;
}
.skin-yellow-light .main-header .logo:hover {
  background-color: #f39a0d;
}
.skin-yellow-light .main-header li.user-header {
  background-color: #f39c12;
}
.skin-yellow-light .content-header {
  background: transparent;
}
.skin-yellow-light .wrapper,
.skin-yellow-light .main-sidebar,
.skin-yellow-light .left-side {
  background-color: #f9fafc;
}
.skin-yellow-light .content-wrapper,
.skin-yellow-light .main-footer {
  border-left: 1px solid #d2d6de;
}
.skin-yellow-light .user-panel > .info,
.skin-yellow-light .user-panel > .info > a {
  color: #444;
}
.skin-yellow-light .sidebar-menu > li {
  -webkit-transition: border-left-color 0.3s ease;
  -o-transition: border-left-color 0.3s ease;
  transition: border-left-color 0.3s ease;
}
.skin-yellow-light .sidebar-menu > li.header {
  color: #848484;
  background: #f9fafc;
}
.skin-yellow-light .sidebar-menu > li:hover > a,
.skin-yellow-light .sidebar-menu > li.active > a {
  color: #000;
  background: #f4f4f5;
  border-left-color: #f39c12;
}
.skin-yellow-light .sidebar-menu > li.active {
  border-left-color: #f39c12;
}
.skin-yellow-light .sidebar-menu > li > .treeview-menu {
  background: #f4f4f5;
}
.skin-yellow-light .sidebar a {
  color: #444;
}
.skin-yellow-light .sidebar a:hover {
  text-decoration: none;
}
.skin-yellow-light .treeview-menu > li > a {
  color: #777;
}
.skin-yellow-light .treeview-menu > li.active > a,
.skin-yellow-light .treeview-menu > li > a:hover {
  color: #000;
}
.skin-yellow-light .treeview-menu > li.active > a {
  font-weight: 600;
}
.skin-yellow-light .sidebar-form {
  border-radius: 3px;
  border: 1px solid #d2d6de;
  margin: 10px 10px;
}
.skin-yellow-light .sidebar-form input[type="text"],
.skin-yellow-light .sidebar-form .btn {
  box-shadow: none;
  background-color: #fff;
  border: 1px solid transparent;
  height: 35px;
}
.skin-yellow-light .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-yellow-light .sidebar-form input[type="text"]:focus,
.skin-yellow-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-yellow-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-yellow-light .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
@media (min-width: 768px) {
  .skin-yellow-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
    border-left: 1px solid #d2d6de;
  }
}
.skin-yellow-light .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-yellow-light.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
.skin-yellow-light .main-sidebar {
  -webkit-box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
  box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
}
.skin-yellow-light .content-wrapper,
.skin-yellow-light .main-footer {
  border-left: none;
}
@media (max-width: 767px) {
  .skin-yellow-light.multiplenav .sidebar .mobilenav a.btn-app {
    background: #eceff3;
    color: #757575;
  }
  .skin-yellow-light.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #f39c12;
    color: #fff;
  }
}
/*
 * Skin: Purple
 * ------------
 */
.skin-purple .main-header .navbar {
  background-color: #fff;
}
.skin-purple .main-header .navbar .nav > li > a {
  color: #444;
}
.skin-purple .main-header .navbar .nav > li > a:hover,
.skin-purple .main-header .navbar .nav > li > a:active,
.skin-purple .main-header .navbar .nav > li > a:focus,
.skin-purple .main-header .navbar .nav .open > a,
.skin-purple .main-header .navbar .nav .open > a:hover,
.skin-purple .main-header .navbar .nav .open > a:focus,
.skin-purple .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.02);
  color: #605ca8;
}
.skin-purple .main-header .navbar .nav-addtabs li > .close-tab {
  color: #605ca8;
}
.skin-purple .main-header .navbar .sidebar-toggle {
  color: #444;
}
.skin-purple .main-header .navbar .sidebar-toggle:hover {
  color: #605ca8;
  background: rgba(0, 0, 0, 0.02);
}
@media (max-width: 767px) {
  .skin-purple .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .skin-purple .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .skin-purple .main-header .navbar .dropdown-menu li a:hover {
    background: #555299;
  }
}
.skin-purple .main-header > .logo {
  background-color: #605ca8;
  color: #fff;
  border-bottom: 0 solid transparent;
  border-right: 1px solid #605ca8;
  box-shadow: none;
}
.skin-purple .main-header > .logo:hover {
  background-color: #5d59a6;
}
@media (max-width: 767px) {
  .skin-purple .main-header > .logo {
    background-color: #fff;
    color: #222;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-purple .main-header > .logo:hover {
    background-color: #fcfcfc;
  }
}
.skin-purple .main-header li.user-header {
  background-color: #605ca8;
}
.skin-purple .main-header .nav-addtabs > li > a,
.skin-purple .main-header .nav-addtabs > li.active > a {
  border-right-color: transparent;
}
.skin-purple .content-header {
  background: transparent;
}
.skin-purple .wrapper,
.skin-purple .main-sidebar,
.skin-purple .left-side {
  background-color: #605ca8;
}
.skin-purple .user-panel > .info,
.skin-purple .user-panel > .info > a {
  color: #fff;
}
.skin-purple .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-purple .sidebar-menu > li.header {
  color: #a19fcb;
  background: #57539c;
}
.skin-purple .sidebar-menu > li:hover > a,
.skin-purple .sidebar-menu > li.active > a {
  color: #fff;
  background: #5b57a3;
  border-left-color: #fff;
}
.skin-purple .sidebar-menu > li > .treeview-menu {
  background: #555299;
}
.skin-purple .sidebar a {
  color: #c8c5ff;
}
.skin-purple .sidebar a:hover {
  text-decoration: none;
}
.skin-purple .treeview-menu > li > a {
  color: #c8c5ff;
}
.skin-purple .treeview-menu > li.active > a,
.skin-purple .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-purple .sidebar-form {
  border-radius: 3px;
  border: 1px solid #807dba;
  background-color: #807dba;
  margin: 10px 10px;
}
.skin-purple .sidebar-form input[type="text"],
.skin-purple .sidebar-form .btn {
  box-shadow: none;
  background-color: #807dba;
  border: 1px solid transparent;
  height: 35px;
}
.skin-purple .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-purple .sidebar-form input[type="text"]:focus,
.skin-purple .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-purple .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-purple .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-purple .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-purple .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-purple.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
.skin-purple .sidebar-form input[type="text"]::-moz-placeholder {
  color: #fff;
  opacity: 1;
}
.skin-purple .sidebar-form input[type="text"]:-ms-input-placeholder {
  color: #fff;
}
.skin-purple .sidebar-form input[type="text"]::-webkit-input-placeholder {
  color: #fff;
}
.skin-purple .sidebar-form input[type="text"],
.skin-purple .sidebar-form .btn {
  color: #fff;
}
@media (max-width: 767px) {
  .skin-purple.multiplenav .main-header .navbar {
    background-color: #605ca8;
  }
  .skin-purple.multiplenav .main-header .navbar .nav > li > a {
    color: #fff;
  }
  .skin-purple.multiplenav .main-header .navbar .nav > li > a:hover,
  .skin-purple.multiplenav .main-header .navbar .nav > li > a:active,
  .skin-purple.multiplenav .main-header .navbar .nav > li > a:focus,
  .skin-purple.multiplenav .main-header .navbar .nav .open > a,
  .skin-purple.multiplenav .main-header .navbar .nav .open > a:hover,
  .skin-purple.multiplenav .main-header .navbar .nav .open > a:focus,
  .skin-purple.multiplenav .main-header .navbar .nav > .active > a {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
  }
  .skin-purple.multiplenav .main-header .navbar .nav-addtabs li > .close-tab {
    color: #f6f6f6;
  }
  .skin-purple.multiplenav .main-header .navbar .sidebar-toggle {
    color: #fff;
  }
  .skin-purple.multiplenav .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.1);
  }
  .skin-purple.multiplenav .main-header > .logo {
    background-color: #605ca8;
    color: #fff;
    border-bottom: 0 solid transparent;
  }
  .skin-purple.multiplenav .main-header > .logo:hover {
    background-color: #5d59a6;
  }
  .skin-purple.multiplenav .sidebar .mobilenav a.btn-app {
    background: #807dba;
    color: #fff;
  }
  .skin-purple.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #fff;
    color: #807dba;
  }
}
/*
 * Skin: Purple
 * ------------
 */
.skin-purple-light .main-header {
  background-color: #605ca8;
}
.skin-purple-light .main-header .navbar {
  background-color: #605ca8;
}
.skin-purple-light .main-header .navbar .nav > li > a {
  color: #fff;
}
.skin-purple-light .main-header .navbar .nav > li > a:hover,
.skin-purple-light .main-header .navbar .nav > li > a:active,
.skin-purple-light .main-header .navbar .nav > li > a:focus,
.skin-purple-light .main-header .navbar .nav .open > a,
.skin-purple-light .main-header .navbar .nav .open > a:hover,
.skin-purple-light .main-header .navbar .nav .open > a:focus,
.skin-purple-light .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.05);
  color: #f6f6f6;
}
.skin-purple-light .main-header .navbar .nav-addtabs li > .close-tab {
  color: #f6f6f6;
}
.skin-purple-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-purple-light .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.05);
}
.skin-purple-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-purple-light .main-header .navbar .sidebar-toggle:hover {
  background-color: #555299;
}
@media (max-width: 767px) {
  .skin-purple-light .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .skin-purple-light .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .skin-purple-light .main-header .navbar .dropdown-menu li a:hover {
    background: #555299;
  }
}
.skin-purple-light .main-header .logo {
  background-color: #605ca8;
  color: #fff;
  border-bottom: 0 solid transparent;
}
.skin-purple-light .main-header .logo:hover {
  background-color: #5d59a6;
}
.skin-purple-light .main-header li.user-header {
  background-color: #605ca8;
}
.skin-purple-light .content-header {
  background: transparent;
}
.skin-purple-light .wrapper,
.skin-purple-light .main-sidebar,
.skin-purple-light .left-side {
  background-color: #f9fafc;
}
.skin-purple-light .content-wrapper,
.skin-purple-light .main-footer {
  border-left: 1px solid #d2d6de;
}
.skin-purple-light .user-panel > .info,
.skin-purple-light .user-panel > .info > a {
  color: #444;
}
.skin-purple-light .sidebar-menu > li {
  -webkit-transition: border-left-color 0.3s ease;
  -o-transition: border-left-color 0.3s ease;
  transition: border-left-color 0.3s ease;
}
.skin-purple-light .sidebar-menu > li.header {
  color: #848484;
  background: #f9fafc;
}
.skin-purple-light .sidebar-menu > li:hover > a,
.skin-purple-light .sidebar-menu > li.active > a {
  color: #000;
  background: #f4f4f5;
  border-left-color: #605ca8;
}
.skin-purple-light .sidebar-menu > li.active {
  border-left-color: #605ca8;
}
.skin-purple-light .sidebar-menu > li > .treeview-menu {
  background: #f4f4f5;
}
.skin-purple-light .sidebar a {
  color: #444;
}
.skin-purple-light .sidebar a:hover {
  text-decoration: none;
}
.skin-purple-light .treeview-menu > li > a {
  color: #777;
}
.skin-purple-light .treeview-menu > li.active > a,
.skin-purple-light .treeview-menu > li > a:hover {
  color: #000;
}
.skin-purple-light .treeview-menu > li.active > a {
  font-weight: 600;
}
.skin-purple-light .sidebar-form {
  border-radius: 3px;
  border: 1px solid #d2d6de;
  margin: 10px 10px;
}
.skin-purple-light .sidebar-form input[type="text"],
.skin-purple-light .sidebar-form .btn {
  box-shadow: none;
  background-color: #fff;
  border: 1px solid transparent;
  height: 35px;
}
.skin-purple-light .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-purple-light .sidebar-form input[type="text"]:focus,
.skin-purple-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-purple-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-purple-light .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
@media (min-width: 768px) {
  .skin-purple-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
    border-left: 1px solid #d2d6de;
  }
}
.skin-purple-light .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-purple-light.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
.skin-purple-light .main-sidebar {
  -webkit-box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
  box-shadow: 7px 0 14px rgba(0, 0, 0, 0.03);
}
.skin-purple-light .content-wrapper,
.skin-purple-light .main-footer {
  border-left: none;
}
@media (max-width: 767px) {
  .skin-purple-light.multiplenav .sidebar .mobilenav a.btn-app {
    background: #eceff3;
    color: #757575;
  }
  .skin-purple-light.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #605ca8;
    color: #fff;
  }
}
/*
 * Skin: Black blue
 * -----------
 */
.skin-black-blue .main-header {
  background: #605ca8;
  -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
}
.skin-black-blue .main-header .navbar {
  background-color: #fff;
}
.skin-black-blue .main-header .navbar .nav > li > a {
  color: #666;
}
.skin-black-blue .main-header .navbar .nav > li > a:hover,
.skin-black-blue .main-header .navbar .nav > li > a:active,
.skin-black-blue .main-header .navbar .nav > li > a:focus,
.skin-black-blue .main-header .navbar .nav .open > a,
.skin-black-blue .main-header .navbar .nav .open > a:hover,
.skin-black-blue .main-header .navbar .nav .open > a:focus,
.skin-black-blue .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.02);
  color: #333;
}
.skin-black-blue .main-header .navbar .nav-addtabs li > .close-tab {
  color: #333;
}
.skin-black-blue .main-header .navbar .sidebar-toggle {
  color: #666;
}
.skin-black-blue .main-header .navbar .sidebar-toggle:hover {
  color: #333;
  background: rgba(0, 0, 0, 0.02);
}
.skin-black-blue .main-header .navbar .navbar-nav > li > a {
  border-right: none;
}
.skin-black-blue .main-header .navbar .navbar-custom-menu .navbar-nav > li > a,
.skin-black-blue .main-header .navbar .navbar-right > li > a {
  border-left: none;
  border-right-width: 0;
}
@media (max-width: 767px) {
  .skin-black-blue .main-header .navbar {
    background-color: #555299;
  }
  .skin-black-blue .main-header .navbar .nav > li > a {
    color: #fff;
  }
  .skin-black-blue .main-header .navbar .nav > li > a:hover,
  .skin-black-blue .main-header .navbar .nav > li > a:active,
  .skin-black-blue .main-header .navbar .nav > li > a:focus,
  .skin-black-blue .main-header .navbar .nav .open > a,
  .skin-black-blue .main-header .navbar .nav .open > a:hover,
  .skin-black-blue .main-header .navbar .nav .open > a:focus,
  .skin-black-blue .main-header .navbar .nav > .active > a {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
  }
  .skin-black-blue .main-header .navbar .nav-addtabs li > .close-tab {
    color: #f6f6f6;
  }
  .skin-black-blue .main-header .navbar .sidebar-toggle {
    color: #fff;
  }
  .skin-black-blue .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.1);
  }
}
.skin-black-blue .main-header .logo {
  background-color: #605ca8;
  color: #fff;
  border-bottom: 0 solid transparent;
  border-right: 1px solid #605ca8;
}
.skin-black-blue .main-header .logo:hover {
  background-color: #5d59a6;
}
@media (max-width: 767px) {
  .skin-black-blue .main-header .logo {
    background-color: #555299;
    color: #fff;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-black-blue .main-header .logo:hover {
    background-color: #545096;
  }
}
.skin-black-blue .main-header li.user-header {
  background-color: #605ca8;
}
.skin-black-blue .main-header .nav-addtabs > li > a,
.skin-black-blue .main-header .nav-addtabs > li.active > a {
  border-right-color: transparent;
}
.skin-black-blue .content-header {
  background: transparent;
  box-shadow: none;
}
.skin-black-blue .wrapper,
.skin-black-blue .main-sidebar,
.skin-black-blue .left-side {
  background-color: #605ca8;
}
.skin-black-blue .user-panel > .info,
.skin-black-blue .user-panel > .info > a {
  color: #fff;
}
.skin-black-blue .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-black-blue .sidebar-menu > li.header {
  color: #a19fcb;
  background: #57539c;
}
.skin-black-blue .sidebar-menu > li:hover > a,
.skin-black-blue .sidebar-menu > li.active > a {
  color: #fff;
  background: #5b57a3;
  border-left-color: #fff;
}
.skin-black-blue .sidebar-menu > li > .treeview-menu {
  background: #555299;
}
.skin-black-blue .sidebar a {
  color: #c8c5ff;
}
.skin-black-blue .sidebar a:hover {
  text-decoration: none;
}
.skin-black-blue .treeview-menu > li > a {
  color: #c8c5ff;
}
.skin-black-blue .treeview-menu > li.active > a,
.skin-black-blue .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-black-blue .sidebar-form {
  border-radius: 3px;
  border: 1px solid #807dba;
  background-color: #807dba;
  margin: 10px 10px;
}
.skin-black-blue .sidebar-form input[type="text"],
.skin-black-blue .sidebar-form .btn {
  box-shadow: none;
  background-color: #807dba;
  border: 1px solid transparent;
  height: 35px;
}
.skin-black-blue .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-black-blue .sidebar-form input[type="text"]:focus,
.skin-black-blue .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-black-blue .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-blue .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-black-blue .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-blue .treeview-menu > li > a {
  padding-left: 18px;
}
.skin-black-blue .treeview-menu > li.active > a {
  background-color: #f5549f;
}
.skin-black-blue .sidebar-menu > li.active > a {
  color: #fff;
  background: #f5549f;
  border-left-color: #f5549f;
}
.skin-black-blue .sidebar-menu > li:hover > a {
  border-left-color: transparent;
}
.skin-black-blue .sidebar-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-blue .sidebar-menu li.treeview.active > a,
.skin-black-blue .sidebar-menu li.treeview.treeview-open > a {
  background-color: #555299;
  border-left-color: #555299;
}
.skin-black-blue .sidebar-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-blue .sidebar-menu .treeview-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-blue .sidebar-menu .treeview-menu .treeview-menu > li > a {
  padding-left: 30px;
}
.skin-black-blue .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-blue.sidebar-collapse .sidebar-menu li:hover > a,
.skin-black-blue.sidebar-collapse .sidebar-menu li.active > a {
  color: #fff;
  background: #f5549f;
}
.skin-black-blue.sidebar-collapse .sidebar-menu .treeview-menu li.active > a {
  color: #fff;
  background: #f5549f;
}
.skin-black-blue.sidebar-collapse .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
@media (max-width: 767px) {
  .skin-black-blue.multiplenav .sidebar .mobilenav a.btn-app {
    background: #807dba;
    color: #fff;
  }
  .skin-black-blue.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #f5549f;
    color: #fff;
  }
}
/*
 * Skin: Black purple
 * -----------
 */
.skin-black-purple .main-header {
  background: #605ca8;
  -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
}
.skin-black-purple .main-header .navbar {
  background-color: #fff;
}
.skin-black-purple .main-header .navbar .nav > li > a {
  color: #666;
}
.skin-black-purple .main-header .navbar .nav > li > a:hover,
.skin-black-purple .main-header .navbar .nav > li > a:active,
.skin-black-purple .main-header .navbar .nav > li > a:focus,
.skin-black-purple .main-header .navbar .nav .open > a,
.skin-black-purple .main-header .navbar .nav .open > a:hover,
.skin-black-purple .main-header .navbar .nav .open > a:focus,
.skin-black-purple .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.02);
  color: #333;
}
.skin-black-purple .main-header .navbar .nav-addtabs li > .close-tab {
  color: #333;
}
.skin-black-purple .main-header .navbar .sidebar-toggle {
  color: #666;
}
.skin-black-purple .main-header .navbar .sidebar-toggle:hover {
  color: #333;
  background: rgba(0, 0, 0, 0.02);
}
.skin-black-purple .main-header .navbar .navbar-nav > li > a {
  border-right: none;
}
.skin-black-purple .main-header .navbar .navbar-custom-menu .navbar-nav > li > a,
.skin-black-purple .main-header .navbar .navbar-right > li > a {
  border-left: none;
  border-right-width: 0;
}
@media (max-width: 767px) {
  .skin-black-purple .main-header .navbar {
    background-color: #555299;
  }
  .skin-black-purple .main-header .navbar .nav > li > a {
    color: #fff;
  }
  .skin-black-purple .main-header .navbar .nav > li > a:hover,
  .skin-black-purple .main-header .navbar .nav > li > a:active,
  .skin-black-purple .main-header .navbar .nav > li > a:focus,
  .skin-black-purple .main-header .navbar .nav .open > a,
  .skin-black-purple .main-header .navbar .nav .open > a:hover,
  .skin-black-purple .main-header .navbar .nav .open > a:focus,
  .skin-black-purple .main-header .navbar .nav > .active > a {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
  }
  .skin-black-purple .main-header .navbar .nav-addtabs li > .close-tab {
    color: #f6f6f6;
  }
  .skin-black-purple .main-header .navbar .sidebar-toggle {
    color: #fff;
  }
  .skin-black-purple .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.1);
  }
}
.skin-black-purple .main-header .logo {
  background-color: #605ca8;
  color: #fff;
  border-bottom: 0 solid transparent;
  border-right: 1px solid #605ca8;
}
.skin-black-purple .main-header .logo:hover {
  background-color: #5d59a6;
}
@media (max-width: 767px) {
  .skin-black-purple .main-header .logo {
    background-color: #555299;
    color: #fff;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-black-purple .main-header .logo:hover {
    background-color: #545096;
  }
}
.skin-black-purple .main-header li.user-header {
  background-color: #605ca8;
}
.skin-black-purple .main-header .nav-addtabs > li > a,
.skin-black-purple .main-header .nav-addtabs > li.active > a {
  border-right-color: transparent;
}
.skin-black-purple .content-header {
  background: transparent;
  box-shadow: none;
}
.skin-black-purple .wrapper,
.skin-black-purple .main-sidebar,
.skin-black-purple .left-side {
  background-color: #605ca8;
}
.skin-black-purple .user-panel > .info,
.skin-black-purple .user-panel > .info > a {
  color: #fff;
}
.skin-black-purple .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-black-purple .sidebar-menu > li.header {
  color: #a19fcb;
  background: #57539c;
}
.skin-black-purple .sidebar-menu > li:hover > a,
.skin-black-purple .sidebar-menu > li.active > a {
  color: #fff;
  background: #5b57a3;
  border-left-color: #fff;
}
.skin-black-purple .sidebar-menu > li > .treeview-menu {
  background: #555299;
}
.skin-black-purple .sidebar a {
  color: #c8c5ff;
}
.skin-black-purple .sidebar a:hover {
  text-decoration: none;
}
.skin-black-purple .treeview-menu > li > a {
  color: #c8c5ff;
}
.skin-black-purple .treeview-menu > li.active > a,
.skin-black-purple .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-black-purple .sidebar-form {
  border-radius: 3px;
  border: 1px solid #807dba;
  background-color: #807dba;
  margin: 10px 10px;
}
.skin-black-purple .sidebar-form input[type="text"],
.skin-black-purple .sidebar-form .btn {
  box-shadow: none;
  background-color: #807dba;
  border: 1px solid transparent;
  height: 35px;
}
.skin-black-purple .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-black-purple .sidebar-form input[type="text"]:focus,
.skin-black-purple .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-black-purple .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-purple .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-black-purple .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-purple .treeview-menu > li > a {
  padding-left: 18px;
}
.skin-black-purple .treeview-menu > li.active > a {
  background-color: #f5549f;
}
.skin-black-purple .sidebar-menu > li.active > a {
  color: #fff;
  background: #f5549f;
  border-left-color: #f5549f;
}
.skin-black-purple .sidebar-menu > li:hover > a {
  border-left-color: transparent;
}
.skin-black-purple .sidebar-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-purple .sidebar-menu li.treeview.active > a,
.skin-black-purple .sidebar-menu li.treeview.treeview-open > a {
  background-color: #555299;
  border-left-color: #555299;
}
.skin-black-purple .sidebar-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-purple .sidebar-menu .treeview-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-purple .sidebar-menu .treeview-menu .treeview-menu > li > a {
  padding-left: 30px;
}
.skin-black-purple .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-purple.sidebar-collapse .sidebar-menu li:hover > a,
.skin-black-purple.sidebar-collapse .sidebar-menu li.active > a {
  color: #fff;
  background: #f5549f;
}
.skin-black-purple.sidebar-collapse .sidebar-menu .treeview-menu li.active > a {
  color: #fff;
  background: #f5549f;
}
.skin-black-purple.sidebar-collapse .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
@media (max-width: 767px) {
  .skin-black-purple.multiplenav .sidebar .mobilenav a.btn-app {
    background: #807dba;
    color: #fff;
  }
  .skin-black-purple.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #f5549f;
    color: #fff;
  }
}
/*
 * Skin: Black green
 * -----------
 */
.skin-black-green .main-header {
  background: #605ca8;
  -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
}
.skin-black-green .main-header .navbar {
  background-color: #fff;
}
.skin-black-green .main-header .navbar .nav > li > a {
  color: #666;
}
.skin-black-green .main-header .navbar .nav > li > a:hover,
.skin-black-green .main-header .navbar .nav > li > a:active,
.skin-black-green .main-header .navbar .nav > li > a:focus,
.skin-black-green .main-header .navbar .nav .open > a,
.skin-black-green .main-header .navbar .nav .open > a:hover,
.skin-black-green .main-header .navbar .nav .open > a:focus,
.skin-black-green .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.02);
  color: #333;
}
.skin-black-green .main-header .navbar .nav-addtabs li > .close-tab {
  color: #333;
}
.skin-black-green .main-header .navbar .sidebar-toggle {
  color: #666;
}
.skin-black-green .main-header .navbar .sidebar-toggle:hover {
  color: #333;
  background: rgba(0, 0, 0, 0.02);
}
.skin-black-green .main-header .navbar .navbar-nav > li > a {
  border-right: none;
}
.skin-black-green .main-header .navbar .navbar-custom-menu .navbar-nav > li > a,
.skin-black-green .main-header .navbar .navbar-right > li > a {
  border-left: none;
  border-right-width: 0;
}
@media (max-width: 767px) {
  .skin-black-green .main-header .navbar {
    background-color: #555299;
  }
  .skin-black-green .main-header .navbar .nav > li > a {
    color: #fff;
  }
  .skin-black-green .main-header .navbar .nav > li > a:hover,
  .skin-black-green .main-header .navbar .nav > li > a:active,
  .skin-black-green .main-header .navbar .nav > li > a:focus,
  .skin-black-green .main-header .navbar .nav .open > a,
  .skin-black-green .main-header .navbar .nav .open > a:hover,
  .skin-black-green .main-header .navbar .nav .open > a:focus,
  .skin-black-green .main-header .navbar .nav > .active > a {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
  }
  .skin-black-green .main-header .navbar .nav-addtabs li > .close-tab {
    color: #f6f6f6;
  }
  .skin-black-green .main-header .navbar .sidebar-toggle {
    color: #fff;
  }
  .skin-black-green .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.1);
  }
}
.skin-black-green .main-header .logo {
  background-color: #605ca8;
  color: #fff;
  border-bottom: 0 solid transparent;
  border-right: 1px solid #605ca8;
}
.skin-black-green .main-header .logo:hover {
  background-color: #5d59a6;
}
@media (max-width: 767px) {
  .skin-black-green .main-header .logo {
    background-color: #555299;
    color: #fff;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-black-green .main-header .logo:hover {
    background-color: #545096;
  }
}
.skin-black-green .main-header li.user-header {
  background-color: #605ca8;
}
.skin-black-green .main-header .nav-addtabs > li > a,
.skin-black-green .main-header .nav-addtabs > li.active > a {
  border-right-color: transparent;
}
.skin-black-green .content-header {
  background: transparent;
  box-shadow: none;
}
.skin-black-green .wrapper,
.skin-black-green .main-sidebar,
.skin-black-green .left-side {
  background-color: #605ca8;
}
.skin-black-green .user-panel > .info,
.skin-black-green .user-panel > .info > a {
  color: #fff;
}
.skin-black-green .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-black-green .sidebar-menu > li.header {
  color: #a19fcb;
  background: #57539c;
}
.skin-black-green .sidebar-menu > li:hover > a,
.skin-black-green .sidebar-menu > li.active > a {
  color: #fff;
  background: #5b57a3;
  border-left-color: #fff;
}
.skin-black-green .sidebar-menu > li > .treeview-menu {
  background: #555299;
}
.skin-black-green .sidebar a {
  color: #c8c5ff;
}
.skin-black-green .sidebar a:hover {
  text-decoration: none;
}
.skin-black-green .treeview-menu > li > a {
  color: #c8c5ff;
}
.skin-black-green .treeview-menu > li.active > a,
.skin-black-green .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-black-green .sidebar-form {
  border-radius: 3px;
  border: 1px solid #807dba;
  background-color: #807dba;
  margin: 10px 10px;
}
.skin-black-green .sidebar-form input[type="text"],
.skin-black-green .sidebar-form .btn {
  box-shadow: none;
  background-color: #807dba;
  border: 1px solid transparent;
  height: 35px;
}
.skin-black-green .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-black-green .sidebar-form input[type="text"]:focus,
.skin-black-green .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-black-green .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-green .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-black-green .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-green .treeview-menu > li > a {
  padding-left: 18px;
}
.skin-black-green .treeview-menu > li.active > a {
  background-color: #f5549f;
}
.skin-black-green .sidebar-menu > li.active > a {
  color: #fff;
  background: #f5549f;
  border-left-color: #f5549f;
}
.skin-black-green .sidebar-menu > li:hover > a {
  border-left-color: transparent;
}
.skin-black-green .sidebar-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-green .sidebar-menu li.treeview.active > a,
.skin-black-green .sidebar-menu li.treeview.treeview-open > a {
  background-color: #555299;
  border-left-color: #555299;
}
.skin-black-green .sidebar-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-green .sidebar-menu .treeview-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-green .sidebar-menu .treeview-menu .treeview-menu > li > a {
  padding-left: 30px;
}
.skin-black-green .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-green.sidebar-collapse .sidebar-menu li:hover > a,
.skin-black-green.sidebar-collapse .sidebar-menu li.active > a {
  color: #fff;
  background: #f5549f;
}
.skin-black-green.sidebar-collapse .sidebar-menu .treeview-menu li.active > a {
  color: #fff;
  background: #f5549f;
}
.skin-black-green.sidebar-collapse .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
@media (max-width: 767px) {
  .skin-black-green.multiplenav .sidebar .mobilenav a.btn-app {
    background: #807dba;
    color: #fff;
  }
  .skin-black-green.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #f5549f;
    color: #fff;
  }
}
/*
 * Skin: Black red
 * -----------
 */
.skin-black-red .main-header {
  background: #605ca8;
  -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
}
.skin-black-red .main-header .navbar {
  background-color: #fff;
}
.skin-black-red .main-header .navbar .nav > li > a {
  color: #666;
}
.skin-black-red .main-header .navbar .nav > li > a:hover,
.skin-black-red .main-header .navbar .nav > li > a:active,
.skin-black-red .main-header .navbar .nav > li > a:focus,
.skin-black-red .main-header .navbar .nav .open > a,
.skin-black-red .main-header .navbar .nav .open > a:hover,
.skin-black-red .main-header .navbar .nav .open > a:focus,
.skin-black-red .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.02);
  color: #333;
}
.skin-black-red .main-header .navbar .nav-addtabs li > .close-tab {
  color: #333;
}
.skin-black-red .main-header .navbar .sidebar-toggle {
  color: #666;
}
.skin-black-red .main-header .navbar .sidebar-toggle:hover {
  color: #333;
  background: rgba(0, 0, 0, 0.02);
}
.skin-black-red .main-header .navbar .navbar-nav > li > a {
  border-right: none;
}
.skin-black-red .main-header .navbar .navbar-custom-menu .navbar-nav > li > a,
.skin-black-red .main-header .navbar .navbar-right > li > a {
  border-left: none;
  border-right-width: 0;
}
@media (max-width: 767px) {
  .skin-black-red .main-header .navbar {
    background-color: #555299;
  }
  .skin-black-red .main-header .navbar .nav > li > a {
    color: #fff;
  }
  .skin-black-red .main-header .navbar .nav > li > a:hover,
  .skin-black-red .main-header .navbar .nav > li > a:active,
  .skin-black-red .main-header .navbar .nav > li > a:focus,
  .skin-black-red .main-header .navbar .nav .open > a,
  .skin-black-red .main-header .navbar .nav .open > a:hover,
  .skin-black-red .main-header .navbar .nav .open > a:focus,
  .skin-black-red .main-header .navbar .nav > .active > a {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
  }
  .skin-black-red .main-header .navbar .nav-addtabs li > .close-tab {
    color: #f6f6f6;
  }
  .skin-black-red .main-header .navbar .sidebar-toggle {
    color: #fff;
  }
  .skin-black-red .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.1);
  }
}
.skin-black-red .main-header .logo {
  background-color: #605ca8;
  color: #fff;
  border-bottom: 0 solid transparent;
  border-right: 1px solid #605ca8;
}
.skin-black-red .main-header .logo:hover {
  background-color: #5d59a6;
}
@media (max-width: 767px) {
  .skin-black-red .main-header .logo {
    background-color: #555299;
    color: #fff;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-black-red .main-header .logo:hover {
    background-color: #545096;
  }
}
.skin-black-red .main-header li.user-header {
  background-color: #605ca8;
}
.skin-black-red .main-header .nav-addtabs > li > a,
.skin-black-red .main-header .nav-addtabs > li.active > a {
  border-right-color: transparent;
}
.skin-black-red .content-header {
  background: transparent;
  box-shadow: none;
}
.skin-black-red .wrapper,
.skin-black-red .main-sidebar,
.skin-black-red .left-side {
  background-color: #605ca8;
}
.skin-black-red .user-panel > .info,
.skin-black-red .user-panel > .info > a {
  color: #fff;
}
.skin-black-red .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-black-red .sidebar-menu > li.header {
  color: #a19fcb;
  background: #57539c;
}
.skin-black-red .sidebar-menu > li:hover > a,
.skin-black-red .sidebar-menu > li.active > a {
  color: #fff;
  background: #5b57a3;
  border-left-color: #fff;
}
.skin-black-red .sidebar-menu > li > .treeview-menu {
  background: #555299;
}
.skin-black-red .sidebar a {
  color: #c8c5ff;
}
.skin-black-red .sidebar a:hover {
  text-decoration: none;
}
.skin-black-red .treeview-menu > li > a {
  color: #c8c5ff;
}
.skin-black-red .treeview-menu > li.active > a,
.skin-black-red .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-black-red .sidebar-form {
  border-radius: 3px;
  border: 1px solid #807dba;
  background-color: #807dba;
  margin: 10px 10px;
}
.skin-black-red .sidebar-form input[type="text"],
.skin-black-red .sidebar-form .btn {
  box-shadow: none;
  background-color: #807dba;
  border: 1px solid transparent;
  height: 35px;
}
.skin-black-red .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-black-red .sidebar-form input[type="text"]:focus,
.skin-black-red .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-black-red .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-red .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-black-red .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-red .treeview-menu > li > a {
  padding-left: 18px;
}
.skin-black-red .treeview-menu > li.active > a {
  background-color: #f5549f;
}
.skin-black-red .sidebar-menu > li.active > a {
  color: #fff;
  background: #f5549f;
  border-left-color: #f5549f;
}
.skin-black-red .sidebar-menu > li:hover > a {
  border-left-color: transparent;
}
.skin-black-red .sidebar-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-red .sidebar-menu li.treeview.active > a,
.skin-black-red .sidebar-menu li.treeview.treeview-open > a {
  background-color: #555299;
  border-left-color: #555299;
}
.skin-black-red .sidebar-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-red .sidebar-menu .treeview-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-red .sidebar-menu .treeview-menu .treeview-menu > li > a {
  padding-left: 30px;
}
.skin-black-red .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-red.sidebar-collapse .sidebar-menu li:hover > a,
.skin-black-red.sidebar-collapse .sidebar-menu li.active > a {
  color: #fff;
  background: #f5549f;
}
.skin-black-red.sidebar-collapse .sidebar-menu .treeview-menu li.active > a {
  color: #fff;
  background: #f5549f;
}
.skin-black-red.sidebar-collapse .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
@media (max-width: 767px) {
  .skin-black-red.multiplenav .sidebar .mobilenav a.btn-app {
    background: #807dba;
    color: #fff;
  }
  .skin-black-red.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #f5549f;
    color: #fff;
  }
}
/*
 * Skin: Black yellow
 * -----------
 */
.skin-black-yellow .main-header {
  background: #605ca8;
  -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
}
.skin-black-yellow .main-header .navbar {
  background-color: #fff;
}
.skin-black-yellow .main-header .navbar .nav > li > a {
  color: #666;
}
.skin-black-yellow .main-header .navbar .nav > li > a:hover,
.skin-black-yellow .main-header .navbar .nav > li > a:active,
.skin-black-yellow .main-header .navbar .nav > li > a:focus,
.skin-black-yellow .main-header .navbar .nav .open > a,
.skin-black-yellow .main-header .navbar .nav .open > a:hover,
.skin-black-yellow .main-header .navbar .nav .open > a:focus,
.skin-black-yellow .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.02);
  color: #333;
}
.skin-black-yellow .main-header .navbar .nav-addtabs li > .close-tab {
  color: #333;
}
.skin-black-yellow .main-header .navbar .sidebar-toggle {
  color: #666;
}
.skin-black-yellow .main-header .navbar .sidebar-toggle:hover {
  color: #333;
  background: rgba(0, 0, 0, 0.02);
}
.skin-black-yellow .main-header .navbar .navbar-nav > li > a {
  border-right: none;
}
.skin-black-yellow .main-header .navbar .navbar-custom-menu .navbar-nav > li > a,
.skin-black-yellow .main-header .navbar .navbar-right > li > a {
  border-left: none;
  border-right-width: 0;
}
@media (max-width: 767px) {
  .skin-black-yellow .main-header .navbar {
    background-color: #555299;
  }
  .skin-black-yellow .main-header .navbar .nav > li > a {
    color: #fff;
  }
  .skin-black-yellow .main-header .navbar .nav > li > a:hover,
  .skin-black-yellow .main-header .navbar .nav > li > a:active,
  .skin-black-yellow .main-header .navbar .nav > li > a:focus,
  .skin-black-yellow .main-header .navbar .nav .open > a,
  .skin-black-yellow .main-header .navbar .nav .open > a:hover,
  .skin-black-yellow .main-header .navbar .nav .open > a:focus,
  .skin-black-yellow .main-header .navbar .nav > .active > a {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
  }
  .skin-black-yellow .main-header .navbar .nav-addtabs li > .close-tab {
    color: #f6f6f6;
  }
  .skin-black-yellow .main-header .navbar .sidebar-toggle {
    color: #fff;
  }
  .skin-black-yellow .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.1);
  }
}
.skin-black-yellow .main-header .logo {
  background-color: #605ca8;
  color: #fff;
  border-bottom: 0 solid transparent;
  border-right: 1px solid #605ca8;
}
.skin-black-yellow .main-header .logo:hover {
  background-color: #5d59a6;
}
@media (max-width: 767px) {
  .skin-black-yellow .main-header .logo {
    background-color: #555299;
    color: #fff;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-black-yellow .main-header .logo:hover {
    background-color: #545096;
  }
}
.skin-black-yellow .main-header li.user-header {
  background-color: #605ca8;
}
.skin-black-yellow .main-header .nav-addtabs > li > a,
.skin-black-yellow .main-header .nav-addtabs > li.active > a {
  border-right-color: transparent;
}
.skin-black-yellow .content-header {
  background: transparent;
  box-shadow: none;
}
.skin-black-yellow .wrapper,
.skin-black-yellow .main-sidebar,
.skin-black-yellow .left-side {
  background-color: #605ca8;
}
.skin-black-yellow .user-panel > .info,
.skin-black-yellow .user-panel > .info > a {
  color: #fff;
}
.skin-black-yellow .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-black-yellow .sidebar-menu > li.header {
  color: #a19fcb;
  background: #57539c;
}
.skin-black-yellow .sidebar-menu > li:hover > a,
.skin-black-yellow .sidebar-menu > li.active > a {
  color: #fff;
  background: #5b57a3;
  border-left-color: #fff;
}
.skin-black-yellow .sidebar-menu > li > .treeview-menu {
  background: #555299;
}
.skin-black-yellow .sidebar a {
  color: #c8c5ff;
}
.skin-black-yellow .sidebar a:hover {
  text-decoration: none;
}
.skin-black-yellow .treeview-menu > li > a {
  color: #c8c5ff;
}
.skin-black-yellow .treeview-menu > li.active > a,
.skin-black-yellow .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-black-yellow .sidebar-form {
  border-radius: 3px;
  border: 1px solid #807dba;
  background-color: #807dba;
  margin: 10px 10px;
}
.skin-black-yellow .sidebar-form input[type="text"],
.skin-black-yellow .sidebar-form .btn {
  box-shadow: none;
  background-color: #807dba;
  border: 1px solid transparent;
  height: 35px;
}
.skin-black-yellow .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-black-yellow .sidebar-form input[type="text"]:focus,
.skin-black-yellow .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-black-yellow .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-yellow .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-black-yellow .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-yellow .treeview-menu > li > a {
  padding-left: 18px;
}
.skin-black-yellow .treeview-menu > li.active > a {
  background-color: #f5549f;
}
.skin-black-yellow .sidebar-menu > li.active > a {
  color: #fff;
  background: #f5549f;
  border-left-color: #f5549f;
}
.skin-black-yellow .sidebar-menu > li:hover > a {
  border-left-color: transparent;
}
.skin-black-yellow .sidebar-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-yellow .sidebar-menu li.treeview.active > a,
.skin-black-yellow .sidebar-menu li.treeview.treeview-open > a {
  background-color: #555299;
  border-left-color: #555299;
}
.skin-black-yellow .sidebar-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-yellow .sidebar-menu .treeview-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-yellow .sidebar-menu .treeview-menu .treeview-menu > li > a {
  padding-left: 30px;
}
.skin-black-yellow .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-yellow.sidebar-collapse .sidebar-menu li:hover > a,
.skin-black-yellow.sidebar-collapse .sidebar-menu li.active > a {
  color: #fff;
  background: #f5549f;
}
.skin-black-yellow.sidebar-collapse .sidebar-menu .treeview-menu li.active > a {
  color: #fff;
  background: #f5549f;
}
.skin-black-yellow.sidebar-collapse .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
@media (max-width: 767px) {
  .skin-black-yellow.multiplenav .sidebar .mobilenav a.btn-app {
    background: #807dba;
    color: #fff;
  }
  .skin-black-yellow.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #f5549f;
    color: #fff;
  }
}
/*
 * Skin: Black pink
 * -----------
 */
.skin-black-pink .main-header {
  background: #605ca8;
  -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
}
.skin-black-pink .main-header .navbar {
  background-color: #fff;
}
.skin-black-pink .main-header .navbar .nav > li > a {
  color: #666;
}
.skin-black-pink .main-header .navbar .nav > li > a:hover,
.skin-black-pink .main-header .navbar .nav > li > a:active,
.skin-black-pink .main-header .navbar .nav > li > a:focus,
.skin-black-pink .main-header .navbar .nav .open > a,
.skin-black-pink .main-header .navbar .nav .open > a:hover,
.skin-black-pink .main-header .navbar .nav .open > a:focus,
.skin-black-pink .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.02);
  color: #333;
}
.skin-black-pink .main-header .navbar .nav-addtabs li > .close-tab {
  color: #333;
}
.skin-black-pink .main-header .navbar .sidebar-toggle {
  color: #666;
}
.skin-black-pink .main-header .navbar .sidebar-toggle:hover {
  color: #333;
  background: rgba(0, 0, 0, 0.02);
}
.skin-black-pink .main-header .navbar .navbar-nav > li > a {
  border-right: none;
}
.skin-black-pink .main-header .navbar .navbar-custom-menu .navbar-nav > li > a,
.skin-black-pink .main-header .navbar .navbar-right > li > a {
  border-left: none;
  border-right-width: 0;
}
@media (max-width: 767px) {
  .skin-black-pink .main-header .navbar {
    background-color: #555299;
  }
  .skin-black-pink .main-header .navbar .nav > li > a {
    color: #fff;
  }
  .skin-black-pink .main-header .navbar .nav > li > a:hover,
  .skin-black-pink .main-header .navbar .nav > li > a:active,
  .skin-black-pink .main-header .navbar .nav > li > a:focus,
  .skin-black-pink .main-header .navbar .nav .open > a,
  .skin-black-pink .main-header .navbar .nav .open > a:hover,
  .skin-black-pink .main-header .navbar .nav .open > a:focus,
  .skin-black-pink .main-header .navbar .nav > .active > a {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
  }
  .skin-black-pink .main-header .navbar .nav-addtabs li > .close-tab {
    color: #f6f6f6;
  }
  .skin-black-pink .main-header .navbar .sidebar-toggle {
    color: #fff;
  }
  .skin-black-pink .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.1);
  }
}
.skin-black-pink .main-header .logo {
  background-color: #605ca8;
  color: #fff;
  border-bottom: 0 solid transparent;
  border-right: 1px solid #605ca8;
}
.skin-black-pink .main-header .logo:hover {
  background-color: #5d59a6;
}
@media (max-width: 767px) {
  .skin-black-pink .main-header .logo {
    background-color: #555299;
    color: #fff;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-black-pink .main-header .logo:hover {
    background-color: #545096;
  }
}
.skin-black-pink .main-header li.user-header {
  background-color: #605ca8;
}
.skin-black-pink .main-header .nav-addtabs > li > a,
.skin-black-pink .main-header .nav-addtabs > li.active > a {
  border-right-color: transparent;
}
.skin-black-pink .content-header {
  background: transparent;
  box-shadow: none;
}
.skin-black-pink .wrapper,
.skin-black-pink .main-sidebar,
.skin-black-pink .left-side {
  background-color: #605ca8;
}
.skin-black-pink .user-panel > .info,
.skin-black-pink .user-panel > .info > a {
  color: #fff;
}
.skin-black-pink .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-black-pink .sidebar-menu > li.header {
  color: #a19fcb;
  background: #57539c;
}
.skin-black-pink .sidebar-menu > li:hover > a,
.skin-black-pink .sidebar-menu > li.active > a {
  color: #fff;
  background: #5b57a3;
  border-left-color: #fff;
}
.skin-black-pink .sidebar-menu > li > .treeview-menu {
  background: #555299;
}
.skin-black-pink .sidebar a {
  color: #c8c5ff;
}
.skin-black-pink .sidebar a:hover {
  text-decoration: none;
}
.skin-black-pink .treeview-menu > li > a {
  color: #c8c5ff;
}
.skin-black-pink .treeview-menu > li.active > a,
.skin-black-pink .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-black-pink .sidebar-form {
  border-radius: 3px;
  border: 1px solid #807dba;
  background-color: #807dba;
  margin: 10px 10px;
}
.skin-black-pink .sidebar-form input[type="text"],
.skin-black-pink .sidebar-form .btn {
  box-shadow: none;
  background-color: #807dba;
  border: 1px solid transparent;
  height: 35px;
}
.skin-black-pink .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-black-pink .sidebar-form input[type="text"]:focus,
.skin-black-pink .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-black-pink .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-pink .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-black-pink .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-pink .treeview-menu > li > a {
  padding-left: 18px;
}
.skin-black-pink .treeview-menu > li.active > a {
  background-color: #f5549f;
}
.skin-black-pink .sidebar-menu > li.active > a {
  color: #fff;
  background: #f5549f;
  border-left-color: #f5549f;
}
.skin-black-pink .sidebar-menu > li:hover > a {
  border-left-color: transparent;
}
.skin-black-pink .sidebar-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-pink .sidebar-menu li.treeview.active > a,
.skin-black-pink .sidebar-menu li.treeview.treeview-open > a {
  background-color: #555299;
  border-left-color: #555299;
}
.skin-black-pink .sidebar-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-pink .sidebar-menu .treeview-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-pink .sidebar-menu .treeview-menu .treeview-menu > li > a {
  padding-left: 30px;
}
.skin-black-pink .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-pink.sidebar-collapse .sidebar-menu li:hover > a,
.skin-black-pink.sidebar-collapse .sidebar-menu li.active > a {
  color: #fff;
  background: #f5549f;
}
.skin-black-pink.sidebar-collapse .sidebar-menu .treeview-menu li.active > a {
  color: #fff;
  background: #f5549f;
}
.skin-black-pink.sidebar-collapse .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
@media (max-width: 767px) {
  .skin-black-pink.multiplenav .sidebar .mobilenav a.btn-app {
    background: #807dba;
    color: #fff;
  }
  .skin-black-pink.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #f5549f;
    color: #fff;
  }
}
/*# sourceMappingURL=_all-skins.css.map */