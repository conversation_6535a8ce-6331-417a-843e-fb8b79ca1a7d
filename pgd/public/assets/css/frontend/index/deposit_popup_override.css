/* 覆盖充值弹窗的背景色 */
.deposit-popup {
    background-color: var(--darker-bg-color) !important;
}

/* 调整弹窗主体布局 */
.deposit-popup-body {
    padding: 16px !important;
    flex: 1 !important;
    overflow-y: auto !important;
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    box-sizing: border-box !important;
    background-color: var(--darker-bg-color) !important;
}

/* 确保payment-section和deposit-section正确显示 */
.payment-section, .deposit-section {
    margin-bottom: 16px !important;
    width: 100% !important;
}

/* 调整弹窗底部固定定位 */
.deposit-popup-footer {
    position: sticky !important;
    bottom: 0 !important;
    width: 100% !important;
    background-color: var(--darker-bg-color) !important;
    padding: 16px !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    z-index: 10 !important;
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1) !important;
}

/* 覆盖payment-item的样式 */
.payment-item {
    background-color: var(--card-bg) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
    padding: 8px 10px !important;
    text-align: left !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    min-height: 50px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: flex-start !important;
    position: relative !important;
    overflow: hidden !important;
}

/* 增强payment-item的选中效果 */
.payment-item.active {
    background-color: rgba(var(--secondary-color-rgb, 255, 215, 0), 0.15) !important;
    border-color: var(--secondary-color) !important;
    box-shadow: 0 0 8px rgba(var(--secondary-color-rgb, 255, 215, 0), 0.5) !important;
}

/* 添加payment-item的悬停效果 */
.payment-item:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
    transform: translateX(2px) !important;
}

/* 添加payment-item的点击效果 */
.payment-item:active {
    transform: translateX(0) !important;
    background-color: rgba(0, 0, 0, 0.3) !important;
}

/* 添加选中标记 */
.payment-item.active::after {
    content: '\f00c' !important; /* Font Awesome的勾选图标 */
    font-family: 'FontAwesome' !important;
    position: absolute !important;
    top: 8px !important;
    right: 8px !important;
    color: var(--secondary-color) !important;
    font-size: 14px !important;
    font-weight: bold !important;
}

/* 覆盖payment-icon的样式 */
.payment-icon {
    width: 30px !important;
    height: 30px !important;
    margin: 0 10px 0 0 !important; /* 右侧添加间距，移除底部间距 */
    display: flex !important;
    justify-content: center !important;
    flex-shrink: 0 !important; /* 防止图标被压缩 */
    object-fit: contain !important; /* 确保图像适应容器 */
}

.payment-item span {
    display: block !important;
    color: #fff !important;
    font-size: 13px !important;
    pointer-events: none !important; /* 确保事件穿透到父元素 */
    flex: 1 !important; /* 占据剩余空间 */
    text-align: left !important; /* 文字左对齐 */
    white-space: nowrap !important; /* 防止文字换行 */
    overflow: hidden !important;
    text-overflow: ellipsis !important; /* 文字过长时显示省略号 */
    padding-right: 20px !important; /* 为选中标记留出空间 */
}

.payment-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important; /* 改为2列，使每个项目有更多水平空间 */
    gap: 10px !important;
    max-height: calc(3 * (10px + 50px)) !important; /* 3行的高度：考虑到新的payment-item高度 */
    overflow-y: auto !important;
    padding-right: 0 !important; /* 移除右侧内边距，因为滚动条已隐藏 */
    width: 100% !important;
    box-sizing: border-box !important;
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
    -webkit-overflow-scrolling: touch !important; /* 在iOS上提供平滑滚动 */
}

/* 调整amount-grid为一行最多4个，并完全展开显示 */
.amount-grid {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important; /* 一行最多4个 */
    gap: 8px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow: visible !important; /* 确保内容完全显示，不滚动 */
    height: auto !important; /* 自动调整高度以适应内容 */
}

/* 调整amount-item的宽高 */
.amount-item {
    background-color: var(--card-bg) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
    padding: 10px !important;
    text-align: center !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: visible !important;
    height: 45px !important; /* 固定高度为45px */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 调整amount-item中的文本样式 */
.amount-item .amount {
    color: #fff !important;
    font-size: 14px !important;
    font-weight: normal !important;
    line-height: 1.2 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 增强的选中效果 */
.amount-item.active {
    background-color: rgba(var(--secondary-color-rgb, 255, 215, 0), 0.15) !important;
    border-color: var(--secondary-color) !important;
    box-shadow: 0 0 8px rgba(var(--secondary-color-rgb, 255, 215, 0), 0.3) !important;
}

/* 添加amount-item的悬停效果 */
.amount-item:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
    transform: translateY(-1px) !important;
}

/* 添加amount-item的点击效果 */
.amount-item:active {
    transform: translateY(1px) !important;
}

/* 响应式设计 */
@media (max-width: 480px) {
    /* 调整payment-item在小屏幕上的样式 */
    .payment-icon {
        width: 24px !important;
        height: 24px !important;
        margin-right: 8px !important; /* 减小右侧间距 */
    }

    .payment-item {
        min-height: 45px !important; /* 在小屏幕上稍微减小高度 */
        padding: 6px 8px !important;
    }

    .payment-item span {
        font-size: 12px !important; /* 减小字体大小 */
    }

    /* 调整amount-grid在小屏幕上的样式 */
    .amount-grid {
        grid-template-columns: repeat(4, 1fr) !important; /* 保持4列 */
        gap: 6px !important; /* 减小间距 */
        overflow: visible !important; /* 确保内容完全显示，不滚动 */
        height: auto !important; /* 自动调整高度以适应内容 */
    }

    /* 调整amount-item在小屏幕上的样式 */
    .amount-item {
        min-height: 45px !important; /* 减小高度 */
        padding: 4px 2px !important;
    }

    .amount-item .amount {
        font-size: 12px !important; /* 减小字体大小 */
    }
}

/* 角标样式 */
.bonus-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--secondary-color) !important;
    color: var(--text-color) !important;
    font-size: 9px;
    font-weight: bold;
    padding: 2px 5px;
    border-radius: 0 8px 0 3px; /* 调整右上角圆角与amount-item一致 */
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
    z-index: 1;
    max-width: 70%;
    width: 35%; 
    height: 35%; 
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1;
    transform: scale(0.95);
    box-sizing: border-box;
}

/* 响应式调整角标 */
@media (max-width: 480px) {
    .bonus-badge {
        font-size: 8px;
        padding: 1px 3px;
    }
}

/* 确认按钮样式 */
.btn-confirm {
    background-color: var(--secondary-color) !important;
    /* color: var(--darker-bg-color) !important; */
    border: none !important;
    border-radius: 5px !important;
    padding: 12px !important;
    font-size: 16px !important;
    font-weight: bold !important;
    cursor: pointer !important;
    width: 100% !important;
    transition: all 0.3s ease !important;
}

.btn-confirm:hover {
    opacity: 0.9 !important;
    transform: translateY(-2px) !important;
}

/* 弹窗标题和按钮颜色 */
.deposit-popup-title,
.history-button {
    color: var(--text-color) !important;
}

.history-button {
    color: var(--secondary-color) !important;
}

/* 弹窗头部背景 */
.deposit-popup-header {
    background-color: var(--darker-bg-color) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}
