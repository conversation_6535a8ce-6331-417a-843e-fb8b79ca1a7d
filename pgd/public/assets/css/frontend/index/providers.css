/* 网站免责声明样式 */
/* .site-disclaimer {
    padding: 15px;
    background-color: var(--darker-bg-color);
    margin-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
} */

.disclaimer-text {
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-color);
    text-align: center;
    margin: 0 auto; /* 水平居中 */
    padding: 0 10px;
    max-width: 90%; /* 限制最大宽度 */
}

/* 游戏提供商部分样式 */
.game-providers-section {
    /* padding: 20px 15px; */
    background-color: var(--darker-bg-color);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.game-providers-section .container {
    max-width: 516px;
    margin: 0 auto;
    overflow: visible;
}

.section-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 25px; /* 增加底部边距 */
    text-align: center;
    padding-top: 10px; /* 增加顶部内边距 */
}

.providers-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.provider-item {
    width: 80px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.provider-logo {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* 支付和监管机构图标部分 */
.payment-license-section {
    margin-top: 30px;
    padding-bottom: 20px;
}

.payment-license-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.payment-license-item {
    width: 80px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payment-license-logo {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* 版权信息 */
.copyright-section {
    text-align: center;
    padding: 15px 0;
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .provider-item, .payment-license-item {
        width: 70px;
        height: 35px;
    }
}
