/* 充值弹窗样式 */
.deposit-popup-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: var(--darker-bg-color) !important;
    z-index: 1000000 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: flex-end !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}

.deposit-popup {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    max-width: 516px !important;
    margin: 0 auto !important;
    height: calc(100vh) !important;
    min-height: 90vh !important;
    overflow-y: hidden !important;
    background-color: var(--darker-bg-color) !important; /* 使用主题变量 */
    display: flex !important;
    flex-direction: column !important;
    z-index: 1000001 !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}

.deposit-popup-container {
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    border-radius: 12px 12px 0 0 !important;
    overflow: hidden !important;
    z-index: 1000002 !important;
    visibility: visible !important;
    opacity: 1 !important;
    background-color: var(--darker-bg-color) !important; /* 使用主题变量 */
}

/* 弹窗头部 */
.deposit-popup-header {
    position: relative;
    padding: 16px;
    /* border-bottom: 1px solid rgba(255, 255, 255, 0.1); */
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--darker-bg-color) !important; /* 使用主题变量 */
}

.deposit-popup-title {
    color: var(--text-color) !important; /* 使用主题变量 */
    font-size: 18px;
    margin: 0;
    text-align: center;
}

.back-button {
    position: absolute;
    left: 16px;
    background: none;
    border: none;
    color: var(--text-color) !important; /* 使用主题变量 */
    font-size: 22px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.history-button {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    color: var(--secondary-color) !important; /* 使用主题变量 */
    font-size: 14px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

/* 弹窗主体 */
.deposit-popup-body {
    padding: 16px;
    flex: 1;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
    height: calc(100% - 56px);
    width: 100%;
    box-sizing: border-box;
    background-color: var(--darker-bg-color) !important; /* 使用主题变量 */
}

.deposit-section {
    margin-bottom: 20px;
    width: 100%;
}

.deposit-section h4 {
    color: var(--text-color) !important; /* 使用主题变量 */
    font-size: 16px;
    margin-bottom: 12px;
    width: 100%;
}

/* 金额网格 */
.amount-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    margin-bottom: 12px;
    max-height: calc(4 * (8px + 36px));
    overflow-y: auto;
    padding-right: 0;
    width: 100%;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.amount-item {
    background-color: var(--card-bg) !important; /* 使用主题变量 */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 8px 4px;
    text-align: center;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    min-height: 50px; /* 减小高度 */
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.95); /* 稍微缩小一点，增加间距感 */
}

.amount-item.active {
    background-color: rgba(var(--secondary-color-rgb, 255, 215, 0), 0.2) !important; /* 使用主题变量 */
    border-color: var(--secondary-color) !important; /* 使用主题变量 */
}

.amount-item .amount {
    display: inline-block;
    color: var(--text-color) !important; /* 使用主题变量 */
    font-size: 14px;
    margin-bottom: 0;
    pointer-events: none; /* 确保事件穿透到父元素 */
    width: 100%;
    text-align: center;
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden;
    text-overflow: ellipsis; /* 超出部分显示省略号 */
}

.amount-item .bonus {
    display: block;
    color: var(--secondary-color) !important; /* 使用主题变量 */
    font-size: 12px;
}

.amount-item .bonus-tag {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--secondary-color) !important; /* 使用主题变量 */
    color: var(--darker-bg-color) !important; /* 使用主题变量 */
    font-size: 10px;
    padding: 2px 4px;
    border-top-right-radius: 8px;
    border-bottom-left-radius: 8px;
}

.min-max-info {
    text-align: center;
    color: var(--text-color) !important; /* 使用主题变量 */
    font-size: 12px;
    margin: 8px 0;
    opacity: 0.7;
}

.custom-amount-input {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

.custom-amount-input input {
    flex: 1;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: var(--card-bg) !important; /* 使用主题变量 */
    color: var(--text-color) !important; /* 使用主题变量 */
}

.custom-amount-input button {
    padding: 10px 16px;
    border-radius: 8px;
    border: none;
    background-color: var(--secondary-color) !important; /* 使用主题变量 */
    color: var(--darker-bg-color) !important; /* 使用主题变量 */
    font-weight: bold;
    cursor: pointer;
}

/* 支付方式网格 */
.payment-section {
    margin-bottom: 20px;
    width: 100%;
}

.payment-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    max-height: calc(3 * (10px + 50px)); /* 3行的高度：考虑到新的payment-item高度 */
    overflow-y: auto;
    padding-right: 0; /* 移除右侧内边距，因为滚动条已隐藏 */
    width: 100%;
    box-sizing: border-box;
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
}

.payment-item {
    background-color: var(--card-bg) !important; /* 使用主题变量 */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 8px 10px;
    text-align: left;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 50px;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
}

.payment-item.active {
    background-color: rgba(var(--secondary-color-rgb, 255, 215, 0), 0.2) !important; /* 使用主题变量 */
    border-color: var(--secondary-color) !important; /* 使用主题变量 */
}

.payment-item span {
    display: block;
    color: var(--text-color) !important; /* 使用主题变量 */
    font-size: 13px;
    pointer-events: none;
    flex: 1;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 汇总部分 */
.summary-section {
    background-color: var(--card-bg) !important; /* 使用主题变量 */
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    width: 100%;
    box-sizing: border-box;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    color: var(--text-color) !important; /* 使用主题变量 */
    align-items: center;
}

.summary-input {
    background-color: var(--darker-bg-color) !important; /* 使用主题变量 */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: var(--text-color) !important; /* 使用主题变量 */
    padding: 5px 8px;
    width: 100px;
    text-align: right;
    font-size: 16px;
}

.summary-input:focus {
    border-color: var(--secondary-color) !important; /* 使用主题变量 */
    outline: none;
    box-shadow: 0 0 5px rgba(var(--secondary-color-rgb, 255, 215, 0), 0.3);
}

.summary-row.total {
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: bold;
}

.offer-bonus-section {
    margin-bottom: 16px;
    width: 100%;
    box-sizing: border-box;
}

.offer-bonus-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-color) !important; /* 使用主题变量 */
    cursor: pointer;
}

.offer-bonus-checkbox input {
    accent-color: var(--secondary-color) !important; /* 使用主题变量 */
}

/* 弹窗底部 */
.deposit-popup-footer {
    padding: 16px 16px 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    width: 100%;
    box-sizing: border-box;
    position: relative;
    margin-bottom: 10px;
    background-color: var(--darker-bg-color) !important; /* 使用主题变量 */
}

.btn-confirm {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    border: none;
    background-color: var(--button-normal) !important; /* 使用主题变量 */
    color: var(--text-color);
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-confirm:hover {
    background-color: var(--button-active) !important; /* 使用主题变量 */
    opacity: 0.9;
}

.btn-confirm:disabled {
    background-color: var(--secondary-color) !important; /* 使用主题变量 */
    cursor: not-allowed;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .deposit-popup {
        width: 95%;
        max-width: none;
    }

    .deposit-popup-footer {
        padding: 16px 12px;
    }
}

@media (max-width: 480px) {
    .deposit-popup {
        width: 100%;
        max-width: 100%;
        max-height: 90vh;
    }

    .deposit-popup-container {
        border-radius: 12px 12px 0 0;
        display: flex;
        flex-direction: column;
    }

    .deposit-popup-body {
        flex: 1;
        overflow-y: hidden;
        display: flex;
        flex-direction: column;
        height: calc(100% - 56px); /* 减去头部高度 */
    }

    /* 调整历史记录列表在小屏幕上的高度 */
    .history-list {
        max-height: calc(100% - 120px); /* 在小屏幕上减少筛选区域的高度 */
    }

    .deposit-popup-footer {
        width: 100%;
        z-index: 2;
        padding: 16px;
        border-top: 1px solid rgba(255, 255, 255, 0.15);
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
    }

    .btn-confirm {
        padding: 15px;
        font-size: 18px;
        border-radius: 6px;
    }

    .amount-grid,
    .payment-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 6px;
        max-height: calc(4 * (6px + 36px)); /* 允许更多行，适应小屏幕 */
        width: 100%;
        padding-right: 0;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }

    /* 确保在小屏幕上也隐藏滚动条 */
    .amount-grid::-webkit-scrollbar,
    .payment-grid::-webkit-scrollbar {
        width: 0;
        height: 0;
        display: none;
    }

    .amount-item {
        padding: 6px 2px;
        transform: scale(0.98);
    }

    .amount-item .amount {
        font-size: 14px;
    }

    .bonus-badge {
        font-size: 10px;
        padding: 1px 3px;
    }

    .payment-icon {
        width: 24px;
        height: 24px;
        margin-right: 8px; /* 减小右侧间距 */
    }

    .history-filter-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .date-filter-dropdown {
        width: 100%;
    }

    .history-total {
        width: 100%;
    }
}

/* 隐藏滚动条但保持可滚动 */
.amount-grid::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
}

.amount-grid::-webkit-scrollbar-track {
    display: none;
}

.amount-grid::-webkit-scrollbar-thumb {
    display: none;
}

.amount-grid::-webkit-scrollbar-thumb:hover {
    display: none;
}

/* 确保在Firefox中也隐藏滚动条 */
.amount-grid {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    -webkit-overflow-scrolling: touch; /* 在iOS上提供平滑滚动 */
}

/* 支付iframe容器样式 */
.payment-iframe-container {
    background-color: var(--darker-bg-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin: 0 auto;
    padding: 15px;
    box-sizing: border-box;
    animation: slideUp 0.3s ease-out;
    position: relative;
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.payment-iframe-close {
    transition: all 0.2s ease;
}

.payment-iframe-close:hover {
    background-color: var(--button-active) !important;
    transform: translateX(-50%) scale(1.05) !important;
}

.payment-iframe-close:active {
    transform: translateX(-50%) scale(0.95) !important;
}

/* 移除支付方式网格滚动条样式，因为我们已经在上面隐藏了滚动条 */

/* 充值历史弹窗样式 */
.history-filter-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.date-filter-dropdown {
    position: relative;
    background-color: var(--darker-bg-color) !important;
    border-radius: 4px;
    padding: 0 5px;
    border: 1px solid rgba(231, 134, 7, 0.15);
    overflow: hidden;
    width: 150px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.date-filter-select {
    appearance: none;
    background: transparent;
    border: none;
    color: #ffffff;
    font-size: 14px;
    padding: 6px 20px 6px 8px;
    width: 100%;
    cursor: pointer;
    outline: none;
}

.date-filter-select:focus {
    color: var(--text-color);
}

.date-filter-select option {
    background-color: var(--darker-bg-color) !important;
    color: #ffffff;
    padding: 10px;
}

.date-filter-select option:hover,
.date-filter-select option:focus,
.date-filter-select option:active {
    background-color: var(--darker-bg-color) !important;
    color: var(--text-color);
}

.dropdown-arrow {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: var(--arrow-color);
    font-size: 10px;
}

.dropdown-arrow:after {
    content: "▼";
}

/* 空状态样式 - 已移至通用样式文件 /assets/css/frontend/common/empty-state.css */
/* 请在页面中使用通用空状态样式 */

.empty-records {
    text-align: center;
    padding: 30px 0;
    color: rgba(255, 255, 255, 0.6);
}

.empty-records-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

/* 历史总金额显示 */
.history-total {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: auto;
}

.total-label {
    color: var(--text-color);
    font-size: 14px;
}

.total-amount {
    color: var(--text-color);
    font-size: 16px;
    font-weight: bold;
}

/* 历史记录列表样式 */
.history-list {
    margin-bottom: 16px;
    max-height: calc(100% - 100px); /* 减去筛选区域和底部边距的高度 */
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    -webkit-overflow-scrolling: touch; /* 在iOS上提供平滑滚动 */
}

/* 隐藏webkit浏览器的滚动条 */
.history-list::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
    background: transparent;
}

/* 确保在所有浏览器中都隐藏滚动条 */
.history-list::-webkit-scrollbar-thumb,
.history-list::-webkit-scrollbar-track {
    display: none;
    background: transparent;
}

/* 统一所有可滚动区域的滚动条隐藏样式 */
.amount-grid, .payment-grid, .history-list, .deposit-popup-body {
    /* 隐藏滚动条但保持可滚动 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    -webkit-overflow-scrolling: touch; /* 在iOS上提供平滑滚动 */
}

.amount-grid::-webkit-scrollbar,
.payment-grid::-webkit-scrollbar,
.history-list::-webkit-scrollbar,
.deposit-popup-body::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
    background: transparent;
}

.history-item {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
}

.history-item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.history-item-row:last-child {
    margin-bottom: 0;
}

.history-item-left {
    display: flex;
    align-items: center;
    flex: 0.8;
    min-width: 80px;
}

.history-item-middle {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1.5;
    position: relative;
    overflow: hidden;
    padding: 0 8px;
}

.history-item-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 0.7;
    min-width: 70px;
}

.channel-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    margin-right: 8px;
    object-fit: contain;
}

.channel-name {
    color: var(--text-color);
    font-size: 14px;
    text-transform: uppercase;
}

.deposit-amount {
    color: var(--text-color);
    font-size: 16px;
    font-weight: bold;
}

.deposit-time {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
}

.order-container {
    display: flex;
    align-items: center;
    max-width: 100%;
    overflow: hidden;
}

.order-no {
    color: var(--text-color);
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    margin-right: 4px;
}

.copy-btn {
    flex-shrink: 0;
    width: 16px;
    height: 16px;
    background: none;
    border: none;
    cursor: pointer;
    position: relative;
    margin-left: 4px;
}

/* 使用CSS绘制复制图标 */
.copy-btn:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 10px;
    height: 10px;
    border: 1px solid var(--gray-blue);
    border-radius: 1px;
}

.copy-btn:after {
    content: "";
    position: absolute;
    top: 3px;
    left: 3px;
    width: 10px;
    height: 10px;
    border: 1px solid var(--text-color);
    border-radius: 1px;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: -1;
}

/* 复制成功提示 */
.copy-btn.copied:before {
    content: "✓";
    color: var(--text-color);
    border: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.deposit-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    text-align: center;
    min-width: 60px;
}

.status-success {
    /* background-color: rgba(10, 192, 117, 0.2); */
    color: var(--text-color);
}

.status-pending {
    background-color: var(--secondary-color);
    color: var(--text-color);
}

.status-failed, .status-error {
    background-color: rgba(255, 59, 48, 0.2);
    color: var(--darker-bg-color);
}

.status-unknown {
    background-color: rgba(158, 158, 158, 0.2);
    color: #9E9E9E;
}

.loading {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.7);
}

.loading-more {
    text-align: center;
    padding: 10px;
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
    background-color: rgba(0, 0, 0, 0.1);
    margin: 5px 0;
    border-radius: 4px;
}
