/* 游戏瀑布流布局样式 */

/* 游戏分类整体容器 */
.game-container {
    padding: 0 15px;
    overflow: visible; /* 取消滚动，让内容自然流动 */
}



/* 游戏分类板块 */
.game-section {
    margin-bottom: 30px;
}

/* 分类标题栏 */
.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 0 5px;
}

.game-category-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--game-category-title-color) !important; /* 添加!important确保优先级 */
    display: flex;
    align-items: center;
}

/* 增加选择器特异性 */
html body .game-category-title {
    color: var(--game-category-title-color) !important;
}

/* 确保子元素也使用正确的颜色 */
.game-category-title * {
    color: var(--game-category-title-color) !important;
}

.category-icon {
    margin-right: 8px;
    font-size: 20px;
}

.view-all {
    color: var(--secondary-color);
    font-size: 14px;
    cursor: pointer;
}

.view-all:hover {
    text-decoration: underline;
}

/* 游戏卡片网格容器 - index页面特定样式 */
.game-section .game-grid {
    padding: 0; /* 移除内边距 */
    margin: 0; /* 移除外边距 */
    gap: 15px; /* 统一使用15px的间距 */
    grid-auto-rows: auto; /* 自动调整行高 */
}

/* 分类底部区域 */
.category-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding: 0 5px;
}

.games-count {
    font-size: 14px;
    color: var(--text-secondary-color, #999);
}

.current-games-count {
    color: var(--gray-blue);
    font-weight: bold;
}

/* 加载更多按钮 */
.load-more-btn {
    background-color: var(--button-normal);
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-align: center;
}

.load-more-btn:hover {
    background-color: var(--secondary-color, --gray-blue);
}

.load-more-btn.loading {
    background-color: var(--button-active, #ccc);
    cursor: wait;
}

/* 无游戏状态提示 */
.no-games, .no-category-games {
    grid-column: 1 / -1;
    padding: 20px;
    text-align: center;
    color: var(--text-secondary-color, #999);
    background-color: var(--card-bg-color, #292929);
    border-radius: 8px;
}

/* 响应式布局 - 保持一致的间距 */
@media (max-width: 768px) {
    .game-section .game-grid {
        gap: 15px; /* 保持统一的间距 */
    }

    .category-header {
        flex-direction: row; /* 保持水平布局 */
        justify-content: space-between; /* 两端对齐 */
        align-items: center; /* 垂直居中 */
    }
}

/* Bgaming游戏特殊样式 - 改为填充整个卡片 */
.game-card[data-id^="GPKBG_"] .game-thumbnail {
    max-width: 100% !important;
    max-height: 100% !important;
    width: 100% !important;
    height: 100% !important;
    margin: 0;
    object-fit: cover !important;
}
