/* 水平标签导航 */
.horizontal-tabs {
    display: flex;
    width: 100%;
    justify-content: space-around;
    padding: 10px;
    background-color: var(--card-bg-color, #292929);
    margin-bottom: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    height: 70px; /* 减少10px高度，原高度为80px */
    align-items: center;
}

/* 标签样式 */
.tab {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 5px 10px;
    border-radius: 5px;
}

.tab:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.tab.active {
    color: var(--secondary-color, --button-active);
}

/* 标签图标样式 */
.tab-icon {
    font-size: 50px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50px; /* 修改为50px，与font-size一致 */
    width: 50px; /* 添加宽度确保图标居中 */
    margin-left: auto; /* 水平居中 */
    margin-right: auto;
}

.tab-icon i {
    font-size: 50px; /* 修改为50px，与父元素一致 */
    color: var(--text-color, #ffffff);
}

.tab.active .tab-icon i {
    color: var(--secondary-color, --text-color);
}

/* 标签文字样式 */
.tab span:not(.tab-icon) {
    font-size: 12px;
    font-weight: 500;
    display: block; /* 确保文字独占一行 */
    text-align: center; /* 文字居中 */
    margin-top: 5px; /* 增加与图标的间距 */
}
