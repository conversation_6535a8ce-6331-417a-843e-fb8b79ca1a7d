/* 充值历史弹窗样式 */
.deposit-history-popup {
    background-color: var(--darker-bg-color) !important;
}

.deposit-history-popup-header {
    background-color: var(--darker-bg-color) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.deposit-history-popup-title {
    color: var(--text-color) !important;
}

/* 日期筛选下拉菜单 */
.date-filter-select {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--secondary-color) !important;
}

/* 总金额显示 */
.total-label {
    color: var(--text-color) !important;
}

.total-amount {
    color: var(--text-color) !important;
    font-weight: bold !important;
}

/* 历史记录项 */
.history-item {
    background-color: var(--card-bg) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.history-item-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.history-item-title {
    color: var(--text-color) !important;
}

.history-item-date {
    color: rgba(255, 255, 255, 0.7) !important;
}

.history-item-amount {
    color: var(--secondary-color) !important;
}

.history-item-status {
    background-color: var(--secondary-color) !important;
    color: var(--darker-bg-color) !important;
}

.history-item-status.pending {
    background-color: #FFA500 !important;
}

.history-item-status.failed {
    background-color: #FF6347 !important;
}

/* 空状态显示 */
.empty-state {
    color: var(--secondary-color) !important;
}

.empty-text {
    color: var(--secondary-color) !important;
}