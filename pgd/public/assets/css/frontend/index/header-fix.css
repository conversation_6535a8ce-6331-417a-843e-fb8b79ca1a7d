/**
 * 特别为修复header宽度问题创建的样式文件
 * 使用最高优先级选择器和!important来覆盖所有其他样式
 */

/* 重要：强制设置header宽度为516px */
html body .header,
html body .fixed-header-section .header,
body .header,
.fixed-header-section .header,
.header {
    width: 100% !important;
    max-width: 516px !important;
    margin-left: auto !important;
    margin-right: auto !important;
    box-sizing: border-box !important;
}

/* 确保fixed-header-section也有正确的宽度 */
html body .fixed-header-section,
body .fixed-header-section,
.fixed-header-section {
    width: 100% !important;
    max-width: 516px !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* 强制设置横向标签宽度 */
html body .horizontal-tabs,
body .horizontal-tabs,
.horizontal-tabs {
    width: 100% !important;
    max-width: 516px !important;
    margin-left: auto !important;
    margin-right: auto !important;
} 