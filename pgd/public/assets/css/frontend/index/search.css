
body {
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--background-color);
  color: var(--text-color);
  overflow: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.search-page {
  max-width: 800px;
  margin: 0 auto;
  padding-bottom: 60px;
}

/* 头部样式 */
.header {
  position: sticky;
  top: 0;
  display: flex;
  align-items: center;
  width: 100%;
  height: 50px;
  background-color: var(--darker-bg-color);
  box-shadow: 0 1px 0 rgba(255,255,255,0.1);
  padding: 0 15px;
  box-sizing: border-box;
  z-index: 100;
  flex-shrink: 0;
  position: relative;
}

.back-btn {
  font-size: 22px;
  margin-right: 15px;
  cursor: pointer;
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
}

.header-title {
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  width: 100%;
}

/* 搜索部分样式 */
.search-section {
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
  flex-shrink: 0;
}

/* 搜索框容器 */
.search-box {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

/* 搜索输入框 */
.search-input {
  width: 100%;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.15);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 0 40px 0 15px;
  color: var(--darker-bg-color);
  font-size: 16px;
}

.search-input::placeholder {
  color: var(--gray-blue);
}

.search-input:focus {
  outline: none;
  background-color: rgba(255, 255, 255, 0.2);
}



/* 搜索图标按钮 - 使用CSS绘制 */
/* 搜索图标 */
.search-icon {
    position: absolute;
    right: 15px;
    color: var(--light-text-color);
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--darker-bg-color);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.search-icon:hover {
    background-color: var(--active-bg-color);
    color: #7d0d1a;
}

/* 搜索历史部分 */
.search-history {
  margin-top: 15px;
  width: 100%;
  display: none;  /* 默认隐藏，有历史记录时显示 */
  box-sizing: border-box;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  width: 100%;
}

.history-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--darker-bg-color);
}

/* 清除所有历史按钮 */
.clear-history {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--darker-bg-color);
  cursor: pointer;
}

/* 历史记录项目容器 */
.history-items {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
}

/* 单个历史记录项 */
.history-item {
  position: relative;
  padding: 6px 25px 6px 12px;
  background-color: var(--secondary-color);
  border-radius: 15px;
  font-size: 13px;
  color: var(--darker-bg-color);
  cursor: pointer;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-item:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* 历史记录项删除按钮 */
.history-item .delete-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-item .delete-btn:before,
.history-item .delete-btn:after {
  content: '';
  position: absolute;
  width: 8px;
  height: 1px;
  background-color: var(--text-color);
}

.history-item .delete-btn:before {
  transform: rotate(45deg);
}

.history-item .delete-btn:after {
  transform: rotate(-45deg);
}

/* 标签页相关样式保留 */
.tabs-container {
  margin: 0 15px 15px;
  flex-shrink: 0;
}

.tabs {
  display: flex;
  background-color: var(--darker-bg-color);
  border-radius: 10px;
  overflow: hidden;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  border-bottom: 2px solid transparent;
  color: var(--stat-label-color);
}

.tab.active {
  color: var(--text-color);
  border-bottom-color: var(--text-color);
  font-weight: 500;
}

/* 标签页内容 */
.tab-contents {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  height: calc(100vh - 185px); /* 减去header、search-section和tabs-container的高度 */
}

/* 隐藏滚动条 */
.tab-contents::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.tab-content {
  display: none;
  padding: 0;
  height: 100%;
}

.tab-content.active {
  display: block;
}

/* 游戏网格 - search页面特定样式 */
.tab-content .game-grid {
    gap: 15px; /* 统一使用15px的间距 */
    padding: 0; /* 移除内边距 */
    margin: 15px; /* 设置外边距 */
    width: calc(100% - 30px); /* 考虑左右边距 */
    box-sizing: border-box;
}

/* 加载更多样式 */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  width: 100%;
}

/* 加载中动画 */
.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border-top-color: var(--text-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 加载中提示 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px;
  color: var(--light-text-color);
}

/* 初始搜索信息 */
.initial-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  color: var(--light-text-color);
}

.search-illustration {
  margin-bottom: 20px;
  color: #ccc;
}

/* 无搜索结果的样式 */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;
  color: var(--light-text-color);
}

.no-results-icon {
  margin-bottom: 15px;
  opacity: 0.5;
}

/* 底部样式 */
.footer {
  width: 100%;
  padding: 15px 0;
  text-align: center;
  color: var(--light-text-color);
  font-size: 12px;
}

/* 响应式样式 */
@media (max-width: 600px) {
  .tab {
    padding: 12px 0;
    font-size: 13px;
  }

  /* 小屏幕仍然保持一行三个游戏和统一间距 */
  .tab-content .game-grid {
    gap: 15px; /* 保持统一的间距 */
    margin: 15px; /* 保持外边距 */
  }
}

@media (max-width: 400px) {
  .history-items {
    gap: 8px;
  }

  .history-item {
    padding: 5px 20px 5px 10px;
    font-size: 12px;
  }

  .history-item .delete-btn {
    width: 12px;
    height: 12px;
    top: 5px;
    right: 5px;
  }

  .history-title, .clear-history {
    font-size: 12px;
  }
}