/**
 * 前端主题配置文件
 * 用于统一管理前端页面的背景色和主要颜色
 * 版本: 1.1.2 - 移动到frontend目录
 * 从原来的theme.css移动到frontend目录下
 */

/* 导入通用变量 */
@import url("variables.css?v=20250413");

/* 全局背景色设置 */
body {
    background-color: var(--outer-bg-color) !important;
    margin: 0;
    padding: 0;
    font-family: 'Arial', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    min-height: 100vh;
}

/* 容器基本样式 */
.container {
    background-color: var(--main-bg-color) !important;
    width: 100% !important;
    max-width: 516px !important;
    margin: 0 auto !important;
    min-height: 100vh !important;
    padding-bottom: 60px !important; /* 确保与bottom-nav的高度完全一致 */
    overflow-x: hidden !important;
    overflow-y: auto !important; /* 允许垂直滚动 */
    border-radius: 0 !important; /* 确保没有圆角 */
    box-shadow: none !important; /* 移除阴影 */
    position: relative !important; /* 确保定位属性一致 */
    top: 0 !important; /* 确保容器从页面顶部开始 */
    left: 0 !important; /* 水平居中时不受影响 */
    right: 0 !important; /* 水平居中时不受影响 */
}

/* 媒体查询 - 确保大屏幕样式一致 */
@media (min-width: 481px) {
    .container, .bottom-nav, .horizontal-tabs {
        border-radius: 0 !important;
        margin: 0 auto !important;
        box-shadow: none !important;
        width: 100% !important;
    }
}

@media (min-width: 516px) {
    .container, .bottom-nav, .horizontal-tabs {
        max-width: 516px !important;
    }
}

/* 头部背景色设置 - 仅设置颜色，移除宽度限制（使用common-components.css中的宽度规则） */
.header, .horizontal-tabs {
    background-color: var(--darker-bg-color);
}

/* 卡片背景色设置 */
.card, .user-info, .balance-card, .jackpot-container, .mission-card {
    background-color: var(--card-bg);
}

/* 其他深色区域背景设置 */
.agent-info-box, .banner-bg, .menu-container {
    background-color: var(--darker-bg-color);
}

/* 按钮和强调色 */
.secondary-color, .balance-amount, .star {
    color: var(--secondary-color);
}

/* 主文本颜色 */
body, p, div, span, h1, h2, h3, h4, h5, h6 {
    color: var(--text-color);
} 