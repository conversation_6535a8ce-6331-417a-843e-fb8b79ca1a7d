/**
 * 通用刷新按钮样式
 * 用于首页和安全中心等页面
 */

/* 首页样式的刷新按钮 */
.refresh-btn {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin-left: 5px;
    width: 20px;
    height: 20px;
    background: none;
    border-radius: 50%;
    position: relative;
    border: 1.5px solid var(--gray-bg);
    transition: transform 0.5s;
}

/* 添加CSS绘制的刷新图标 */
.refresh-btn::before,
.refresh-btn::after {
    content: '';
    position: absolute;
}

/* 画圆弧 */
.refresh-btn::before {
    width: 10px;
    height: 10px;
    border: 1.5px solid var(--text-color);
    border-top-color: transparent;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* 画箭头 */
.refresh-btn::after {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 3px;
    border-color: transparent var(--text-color) var(--text-color) transparent;
    top: 2px;
    right: 6px;
    transform: rotate(-45deg);
}

/* 安全中心样式的刷新按钮 */
.security-style .refresh-btn {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    margin-left: 10px;
    border: none;
}

.security-style .refresh-btn:hover {
    background-color: rgba(0, 0, 0, 0.4);
}

.security-style .refresh-icon {
    width: 16px;
    height: 16px;
    border: 2px solid var(--secondary-color);
    border-radius: 50%;
    position: relative;
}

.security-style .refresh-icon::before {
    content: '';
    position: absolute;
    top: -3px;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 0 6px 6px;
    border-color: transparent transparent var(--secondary-color) transparent;
}

.security-style .refresh-icon::after {
    content: '';
    position: absolute;
    top: 0;
    right: -3px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 6px 0 0 6px;
    border-color: transparent transparent transparent var(--secondary-color);
}

/* 旋转动画 */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.refresh-btn.loading,
.refresh-btn.loading .refresh-icon {
    animation: rotate 1s linear infinite;
}

/* 响应式调整 */
@media (max-width: 480px) {
    .refresh-btn {
        width: 18px;
        height: 18px;
    }
    
    .refresh-btn::before {
        width: 8px;
        height: 8px;
    }
    
    .refresh-btn::after {
        border-width: 2px;
        top: 3px;
        right: 7px;
    }
    
    .security-style .refresh-btn {
        width: 22px;
        height: 22px;
    }
    
    .security-style .refresh-icon {
        width: 14px;
        height: 14px;
    }
}
