/*
 * 侧边菜单样式
 * 包含菜单容器、遮罩层、菜单卡片和链接的样式
 */

/* 左侧菜单样式 */
.side-menu {
    position: absolute;
    top: 0;
    left: 0;
    width: 80%;
    max-width: 280px;
    height: 100%;
    background-color: color-mix(in srgb, var(--darker-bg-color) 25%, transparent); /* 使用变量但添加85%透明度 */
    backdrop-filter: blur(10px); /* Apply blur effect */
    -webkit-backdrop-filter: blur(10px); /* Safari compatibility */
    z-index: 10000; /* 增加 z-index 使其层级最高，位于 bottom-nav 之上 */
    overflow-y: auto;
    transform: translateX(-100%);
    transition: transform 0.3s ease, background-color 0.3s ease; /* Added background transition */
    padding-top: 15px;
}

.side-menu.active {
    transform: translateX(0);
}

/* 遮罩层样式 */
.side-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999; /* 增加 z-index，但保持低于 side-menu */
    display: none;
}

.side-menu-overlay.active {
    display: block;
}

.side-menu-content {
    padding: 15px;
}

/* 菜单卡片组样式 */
.side-menu-group {
    margin-bottom: 20px;
}

.side-menu-group-title {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 10px;
    padding-left: 5px;
    font-weight: 500;
}

.side-menu-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.side-menu-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 10px 12px;
    text-decoration: none;
    transition: all 0.2s ease;
    text-align: left;
    width: 100%;
    box-sizing: border-box;
    height: 44px;
    cursor: pointer; /* 添加指针样式，表明可点击 */
}

/* 图标按钮特殊样式 - 用于 Eventos, Rebate, Pendente, History */
.side-menu-card.icon-only-card {
    justify-content: flex-start;
    padding: 0 15px;
    height: 60px;
    overflow: hidden;
    position: relative;
}

.side-menu-card.icon-only-card .side-menu-card-icon {
    margin: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}

.side-menu-card.icon-only-card .side-menu-card-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.side-menu-card.icon-only-card .side-menu-card-text {
    display: block;
    color: var(--text-color);
    font-weight: bold;
    font-size: 16px;
    position: relative;
    z-index: 1;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.side-menu-card:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.side-menu-card.highlight {
    background: linear-gradient(135deg, var(--secondary-color) 0%, rgba(255, 215, 0, 0.7) 100%);
}

.side-menu-card.highlight:hover {
    background: linear-gradient(135deg, var(--secondary-color) 0%, rgba(255, 215, 0, 0.8) 100%);
}

/* 代理按钮特殊样式 */
.side-menu-card.agent-card {
    justify-content: center;
    padding: 0 15px;
    height: 60px;
    background-color: #4682B4; /* 淡蓝色背景 */
}

.side-menu-card.agent-card:hover {
    background-color: #5F9EA0; /* 鼠标悬停时的颜色 */
}

.side-menu-card.agent-card .side-menu-card-icon {
    margin-right: 10px;
    color: var(--text-color); /* 白色图标 */
}

.side-menu-card.agent-card .side-menu-card-text {
    flex-grow: 0;
    font-weight: 600;
    color: var(--text-color); /* 白色文字 */
}

.side-menu-card-icon {
    width: 24px;
    height: 24px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--secondary-color);
    font-size: 20px;
    flex-shrink: 0;
}

.side-menu-card-icon img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* VIP 卡片特殊样式 */
.side-menu-card.vip-card {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    justify-content: flex-start;
    align-items: center;
    height: 60px;
    padding: 0 15px;
    position: relative;
}

/* 社交媒体卡片特殊样式 */
.side-menu-card.social-card {
    justify-content: center;
}

.side-menu-card.social-card .side-menu-card-icon {
    margin-right: 0;
}

.side-menu-card.highlight .side-menu-card-icon {
    color: #000;
}

.side-menu-card-text {
    font-size: 13px;
    color: var(--text-color);
    line-height: 1.2;
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.side-menu-card.highlight .side-menu-card-text {
    color: #000;
    font-weight: 600;
}

.side-menu-card.social-card .side-menu-card-text {
    display: none;
}

.side-menu-card.vip-card .side-menu-card-text {
    display: block;
    color: var(--text-color);
    font-weight: bold;
    font-size: 18px;
    position: relative;
    z-index: 1;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* 底部链接样式 */
.side-menu-bottom {
    padding-top: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.side-menu-link {
    display: flex;
    align-items: center;
    padding: 12px 10px;
    color: var(--text-color);
    text-decoration: none;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: background-color 0.3s;
}

.side-menu-link:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.side-menu-link-icon {
    margin-right: 12px;
    color: var(--secondary-color);
    font-size: 18px;
    width: 24px;
    text-align: center;
}

/* 响应式调整 */
@media (max-width: 480px) {
    .side-menu {
        width: 85%;
    }
}

/* 暗色主题和浅色主题下的调整 */
/* [data-theme="light"] .side-menu {
    box-shadow: 3px 0 10px rgba(0, 0, 0, 0.1);
} */

[data-theme="light"] .side-menu-group-title {
    color: rgba(0, 0, 0, 0.6);
}

[data-theme="light"] .side-menu-card {
    background-color: rgba(0, 0, 0, 0.03);
}

[data-theme="light"] .side-menu-card:hover {
    background-color: rgba(0, 0, 0, 0.05);
}