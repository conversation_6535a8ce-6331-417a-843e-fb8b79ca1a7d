/* 充值按钮样式 */
.deposit-btn-wrapper {
    background-color: var(--secondary-color);
    position: relative;
    display: flex;
    align-items: center;
    height: 36px;
    padding: 5px 10px;
    border-radius: 4px;
    border: none;
    overflow: visible;
    font-weight: bold;
    cursor: pointer;
    /* background: linear-gradient(to right, #f5a623, #f9cf87); */
    /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
    white-space: nowrap;
    min-width: fit-content;
    text-align: center;
}


.deposit-btn {
    color: var(--text-color);
    border: none;
    height: 100%;
    padding: 0 15px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    background: transparent;
}

.deposit-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 中间分隔线 */
.deposit-divider {
    height: 20px;
    width: 1px;
    background-color: rgba(255, 255, 255, 0.5);
}

.deposit-dropdown-toggle {
    border: none;
    height: 100%;
    width: 30px;
    padding: 0;
    cursor: pointer;
    position: relative;
    background: transparent;
    transition: all 0.2s ease;
}

.deposit-dropdown-toggle:hover {
    background-color: var(--darker-bg-color);
}

.deposit-dropdown-toggle .arrow {
    display: block;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid var(--text-color);
    margin: 0 auto;
}

/* 下拉菜单样式 */
.dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    width: 120px;
    background-color: var(--secondary-color); /* 使用主题变量替代硬编码的颜色 */
    border-radius: 4px;
    box-shadow: 0 2px 8px var(--border-color);
    display: none;
    z-index: 999999;
    margin-top: 5px;
    overflow: hidden;
}

.dropdown-content.show {
    display: block;
    animation: fadeIn 0.2s ease-in-out;
}

.deposit-dropdown-item {
    display: block;
    padding: 10px 15px;
    color: var(--text-color); /* 白色文字 */
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s ease;
}

.deposit-dropdown-item:hover {
    background-color: var(--secondary-color); /* 半透明白色背景 */
    color: var(--text-color); /* 保持悬停时的文字 */
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式调整 */
@media (max-width: 480px) {
    .deposit-btn {
        padding: 0 10px;
        font-size: 12px;
    }
    
    .deposit-dropdown-toggle {
        width: 24px;
    }
    
    .dropdown-content {
        width: 110px;
    }
    
    .deposit-dropdown-item {
        padding: 8px 12px;
        font-size: 12px;
    }
} 
