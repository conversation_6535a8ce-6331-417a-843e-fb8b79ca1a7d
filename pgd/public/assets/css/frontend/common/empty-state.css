/* 空状态通用样式 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    text-align: center;
}

.empty-icon {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    opacity: 0.7;
}

.empty-text {
    color: var(--secondary-color,rgba(255, 255, 255, 0.7));
    font-size: 14px;
}

/* 添加空状态图片样式 */
.empty-state-icon {
    width: 100px;
    height: 100px;
    margin-bottom: 20px;
    opacity: 0.8;
}

/* 空状态状态变化 */
.empty-state.small {
    padding: 40px 20px;
}

.empty-state.small .empty-icon,
.empty-state.small .empty-state-icon {
    width: 60px;
    height: 60px;
}

/* 适配特定背景的空状态 */
.empty-state.light .empty-text {
    color: rgba(0, 0, 0, 0.5);
} 
