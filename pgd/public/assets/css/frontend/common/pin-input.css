/**
 * 通用 PIN 输入框组件样式
 */

/* PIN 网格容器 */
.pin-grid {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    width: 100%;
    box-sizing: border-box;
    position: relative;
}

/* PIN 输入框 */
.pin-box {
    height: 50px;
    background-color: var(--secondary-color, rgba(140, 18, 26, 0.8));
    border-radius: 5px;
    flex: 1;
    margin: 0 2px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    color: var(--text-color, #ffffff);
    border: 1px solid rgba(255, 255, 255, 0.2);
    user-select: none; /* 防止选择文本 */
}

/* 隐藏实际密码输入框 */
.pin-input-hidden {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 10; /* 确保在上层，可以接收输入 */
    font-size: 16px; /* 使用合理的字体大小 */
    -webkit-text-security: disc; /* 将输入的文本显示为点 */
    background: transparent; /* 透明背景 */
    border: none; /* 无边框 */
    outline: none; /* 无轮廓 */
    color: transparent; /* 透明文字颜色 */
}

/* PIN 输入框高亮样式 */
.pin-box-highlight {
    /* 盒子高亮样式 - 使用主题变量 */
    background-color: var(--darker-bg-color, --secondary-color); /* 保持原来的背景色 */
    border: 3px solid var(--secondary-color, --gray-blue) !important; /* 使用主题变量 */
    box-shadow: 0 0 8px rgba(var(--secondary-color-rgb, 255, 215, 0), 0.5) !important; /* 使用主题变量 */
    transition: all 0.2s ease; /* 平滑过渡效果 */
    position: relative; /* 确保定位正确 */
    z-index: 2; /* 确保高亮元素在上层 */
}

/* 适配小屏幕设备 */
@media (max-width: 360px) {
    .pin-grid {
        margin-left: -10px;
        margin-right: -10px;
    }

    .pin-box {
        margin: 0 1px;
    }
}

/* PIN输入组件样式 */
.pin-input-container {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 20px 0;
}

.pin-input {
    width: 45px;
    height: 45px;
    border: 2px solid var(--secondary-color) !important;
    border-radius: 8px;
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
    font-size: 20px;
    text-align: center;
    font-weight: bold;
    outline: none;
}

.pin-input:focus {
    border-color: var(--secondary-color) !important;
    box-shadow: 0 0 8px rgba(var(--secondary-color-rgb, 255, 215, 0), 0.5) !important;
}

/* PIN输入标题 */
.pin-input-title {
    color: var(--text-color) !important;
    text-align: center;
    margin-bottom: 15px;
    font-size: 16px;
}

/* PIN输入错误消息 */
.pin-input-error {
    color: #FF6347 !important;
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
}

/* PIN输入按钮 */
.pin-input-button {
    background-color: var(--secondary-color) !important;
    color: var(--darker-bg-color) !important;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 15px;
    width: 100%;
    max-width: 200px;
}

.pin-input-button:hover {
    opacity: 0.9;
}

.pin-input-button:disabled {
    background-color: #666 !important;
    cursor: not-allowed;
    opacity: 0.7;
}
