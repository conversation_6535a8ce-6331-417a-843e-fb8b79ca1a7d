/* 音乐播放器样式 */
:root {
  --player-primary: #5a67d8;
  --player-secondary: #4c51bf;
  --player-text: #2d3748;
  --player-text-light: #718096;
  --player-bg: #fff;
  --player-bg-secondary: #f7fafc;
  --player-border: #e2e8f0;
  --player-shadow: rgba(0, 0, 0, 0.1);
}

/* 迷你播放器 */
.mini-player {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  width: 280px;
  height: 64px;
  background-color: var(--player-bg);
  border-radius: 12px;
  box-shadow: 0 4px 12px var(--player-shadow);
  display: flex;
  align-items: center;
  padding: 0 12px;
  z-index: 999;
  transition: all 0.3s ease;
  border: 1px solid var(--player-border);
}

.mini-player:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px var(--player-shadow);
}

.mini-player-artwork {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
}

.mini-player-artwork img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.mini-player-info {
  flex: 1;
  overflow: hidden;
  cursor: pointer;
}

.song-title {
  font-weight: 600;
  color: var(--player-text);
  font-size: 0.9rem;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.song-count {
  color: var(--player-text-light);
  font-size: 0.75rem;
  margin: 0;
}

.mini-player-controls {
  display: flex;
  align-items: center;
  margin-left: 8px;
}

.mini-player-prev,
.mini-player-play,
.mini-player-next,
.mini-player-fullscreen {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--player-bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  color: var(--player-text);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--player-border);
}

.mini-player-play {
  width: 36px;
  height: 36px;
  color: var(--player-bg);
  background-color: var(--player-primary);
  border: none;
}

.mini-player-prev:hover,
.mini-player-next:hover,
.mini-player-fullscreen:hover {
  background-color: var(--player-border);
}

.mini-player-play:hover {
  background-color: var(--player-secondary);
}

/* 播放状态显示 */
.mini-player.playing .mini-player-play {
  background-color: var(--player-secondary);
}

/* 全屏播放器模态框 */
.music-player-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: none;
}

.music-player-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 420px;
  background-color: var(--player-bg);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.player-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--player-border);
}

.player-title {
  font-weight: 700;
  font-size: 1.2rem;
  color: var(--player-text);
  margin: 0;
}

.close-music-modal {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--player-bg-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-music-modal:hover {
  background-color: var(--player-border);
}

.player-artwork {
  width: 220px;
  height: 220px;
  margin: 30px auto;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 16px var(--player-shadow);
}

.player-artwork img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.player-info {
  padding: 0 30px 20px;
  text-align: center;
}

.current-song-title {
  font-weight: 700;
  font-size: 1.4rem;
  color: var(--player-text);
  margin: 0 0 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.current-song-artist {
  font-size: 1rem;
  color: var(--player-text-light);
  margin: 0;
}

.player-progress {
  padding: 0 30px;
  margin-bottom: 20px;
}

.progress-time {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: var(--player-text-light);
  margin-bottom: 5px;
}

.progress-bar {
  width: 100%;
  height: 5px;
  background-color: var(--player-bg-secondary);
  border-radius: 3px;
  position: relative;
  cursor: pointer;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--player-primary);
  border-radius: 3px;
  width: 0;
  transition: width 0.1s linear;
}

.progress-handle {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: var(--player-primary);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
  left: 0;
}

.player-main-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 30px;
}

.player-prev,
.player-play-pause,
.player-next {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.player-shuffle,
.player-repeat {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--player-text-light);
  cursor: pointer;
  transition: all 0.2s ease;
}

.player-shuffle.active,
.player-repeat.active {
  color: var(--player-primary);
}

.player-prev:hover,
.player-next:hover,
.player-shuffle:hover,
.player-repeat:hover {
  background-color: var(--player-bg-secondary);
}

.player-play-pause {
  width: 64px;
  height: 64px;
  background-color: var(--player-primary);
  color: var(--text-color);
  margin: 0 20px;
}

.player-play-pause:hover {
  background-color: var(--player-secondary);
}

.player-extra-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px 20px;
}

.volume-control {
  display: flex;
  align-items: center;
  width: 140px;
}

.volume-icon {
  margin-right: 10px;
  color: var(--player-text);
  width: 20px;
}

.volume-bar {
  flex: 1;
  height: 5px;
  background-color: var(--player-bg-secondary);
  border-radius: 3px;
  position: relative;
  cursor: pointer;
}

.volume-bar-fill {
  height: 100%;
  background-color: var(--player-primary);
  border-radius: 3px;
  width: 70%;
  transition: width 0.1s ease;
}

.volume-handle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--player-primary);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 70%;
}

.player-playlist {
  max-height: 300px;
  overflow-y: auto;
  border-top: 1px solid var(--player-border);
}

.song-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.song-item:hover {
  background-color: var(--player-bg-secondary);
}

.song-item.active {
  background-color: var(--player-bg-secondary);
}

.song-item-number {
  width: 24px;
  text-align: center;
  font-size: 0.9rem;
  color: var(--player-text-light);
  margin-right: 12px;
}

.song-item-info {
  flex: 1;
}

.song-item-title {
  font-weight: 600;
  color: var(--player-text);
  font-size: 0.9rem;
  margin: 0 0 2px;
}

.song-item-artist {
  font-size: 0.8rem;
  color: var(--player-text-light);
  margin: 0;
}

.song-item-duration {
  font-size: 0.8rem;
  color: var(--player-text-light);
  margin-left: 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .mini-player {
    width: calc(100% - 2rem);
    left: 1rem;
    right: 1rem;
    border-radius: 16px;
  }
  
  .player-artwork {
    width: 180px;
    height: 180px;
  }
  
  .player-play-pause {
    width: 56px;
    height: 56px;
  }
  
  .player-prev,
  .player-next {
    width: 42px;
    height: 42px;
  }
}

/* 滚动条美化 */
.player-playlist::-webkit-scrollbar {
  width: 6px;
}

.player-playlist::-webkit-scrollbar-track {
  background-color: var(--player-bg-secondary);
}

.player-playlist::-webkit-scrollbar-thumb {
  background-color: var(--player-border);
  border-radius: 3px;
}

.player-playlist::-webkit-scrollbar-thumb:hover {
  background-color: var(--player-text-light);
} 