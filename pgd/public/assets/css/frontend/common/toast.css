/* 通用Toast提示组件样式 */
.custom-toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, 0);
    background-color: rgba(39, 174, 96, 0.95);
    color: var(--text-color);
    padding: 10px 20px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    z-index: 999999; /* 非常高的z-index确保在顶层 */
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    max-width: 90%;
    pointer-events: none; /* 确保Toast不会阻止点击 */
}

.custom-toast.show {
    opacity: 1;
}

.toast-icon {
    background-color: white;
    color: #27AE60;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-weight: bold;
    flex-shrink: 0;
}

.toast-message {
    font-size: 14px;
    word-break: break-word;
}

/* 不同类型的Toast */
.custom-toast.success {
    background-color: rgba(39, 174, 96, 0.95);
}

.custom-toast.success .toast-icon {
    color: #27AE60;
}

.custom-toast.error {
    background-color: rgba(231, 76, 60, 0.95);
}

.custom-toast.error .toast-icon {
    color: #E74C3C;
}

.custom-toast.warning {
    background-color: rgba(243, 156, 18, 0.95);
}

.custom-toast.warning .toast-icon {
    color: #F39C12;
}

.custom-toast.info {
    background-color: rgba(52, 152, 219, 0.95);
}

.custom-toast.info .toast-icon {
    color: #3498DB;
} 