/* 右侧菜单样式 */
.menu-right {
    position: fixed;
    right: 0; /* 直接靠右对齐 */
    bottom: 60px; /* 确保在bottom-nav上方 */
    z-index: 999; /* 确保在bottom-nav上方但不遮挡其他重要元素 */
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 80px;
    transform: translateX(0); /* 重置任何可能的变换 */
}

/* 在大屏幕上，将menu-right放在container的边缘 */
@media (min-width: 516px) {
    .menu-right {
        right: calc((100% - 516px) / 2); /* 与container右边缘对齐 */
    }
}

.menu-right-item {
    margin-bottom: 10px;
    position: relative;
    width: 80px;
    height: 80px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.menu-right-item:hover {
    transform: scale(1.05);
}

.menu-right-item img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    /* border-radius: 10px; */
    /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); */
}

.menu-right-item.rewards {
    height: 120px;
}

/* .menu-right-item.rewards img {
    border-radius: 0;
} */

/* 响应式设计 - 中等屏幕适配 */
@media (max-width: 768px) {
    .menu-right {
        width: 70px; /* 减小宽度 */
    }

    .menu-right-item {
        width: 70px; /* 减小宽度 */
        height: 70px; /* 减小高度 */
        margin-bottom: 8px; /* 减小间距 */
    }

    .menu-right-item.rewards {
        height: 100px; /* 减小高度 */
    }

    .menu-right-text {
        font-size: 11px; /* 减小字体大小 */
    }
}

/* 在小屏幕上做更多适配 */
@media (max-width: 480px) {
    .menu-right {
        bottom: 65px; /* 调整底部位置，确保在bottom-nav上方 */
        width: 60px; /* 进一步减小宽度 */
    }

    .menu-right-item {
        width: 60px; /* 进一步减小宽度 */
        height: 60px; /* 进一步减小高度 */
        margin-bottom: 5px; /* 减小间距 */
    }

    .menu-right-item.rewards {
        height: 85px; /* 进一步减小高度 */
        margin-bottom: 8px; /* 与其他项目保持一定间距 */
    }

    .menu-right-text {
        font-size: 10px; /* 进一步减小字体大小 */
        bottom: -15px; /* 调整底部间距 */
        padding: 1px 0; /* 减小内边距 */
        background-color: rgba(0, 0, 0, 0.6); /* 增加背景不透明度，提高可读性 */
    }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
    .menu-right {
        width: 50px; /* 进一步减小宽度 */
        bottom: 60px; /* 调整底部位置 */
    }

    .menu-right-item {
        width: 50px; /* 进一步减小宽度 */
        height: 50px; /* 进一步减小高度 */
        margin-bottom: 4px; /* 减小间距 */
    }

    .menu-right-item.rewards {
        height: 70px; /* 进一步减小高度 */
        margin-bottom: 6px; /* 减小间距 */
    }

    .menu-right-text {
        font-size: 9px; /* 进一步减小字体大小 */
        bottom: -12px; /* 调整底部间距 */
        padding: 1px 0; /* 减小内边距 */
        background-color: rgba(0, 0, 0, 0.7); /* 进一步增加背景不透明度，提高可读性 */
        letter-spacing: -0.2px; /* 减小字母间距，使文字更紧凑 */
    }
}
