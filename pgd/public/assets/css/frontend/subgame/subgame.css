/**
 * 子游戏页面样式
 */
/* 
:root {
    --primary-color: var(--darker-bg-color);  
    --secondary-color: var(--secondary-color);
    --text-color: var(--text-color);
    --light-text-color: var(--light-text-color);
    --border-color: var(--border-color);
    --shadow-color: var(--shadow-color);
    --background-color: var(--background-color);
    --darker-bg-color: var(--darker-bg-color);
    --active-bg-color: var(--active-bg-color); 
    --active-text-color: var(--active-text-color);
    --card-bg-color: var(--card-bg-color);
} */

html, body {
    font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
    overflow: hidden; /* 禁止滚动 */
    position: fixed; /* 固定位置 */
    width: 100%; /* 占满宽度 */
    height: 100%; /* 占满高度 */
}

.container {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 516px;
    margin: 0 auto;
    height: 100%; /* 使用100%高度而不是100vh */
    overflow: hidden; /* 禁止滚动 */
    position: absolute; /* 绝对定位 */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* 头部样式 */
.header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 50px;
    background-color: var(--darker-bg-color);
    border-bottom: var(--border-color);
    z-index: 10;
}

.back-btn {
    position: absolute;
    left: 10px;
    /* 调整垂直位置，使其在header中居中 */
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    color: var(--text-color);
    cursor: pointer;
    z-index: 10;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-title {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
}

/* 搜索部分样式 */
.search-section {
    width: 100%;
    padding: 15px;
    box-sizing: border-box;
    background-color: var(--background-color);
}

/* 搜索框容器 */
.search-box {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
}

/* 搜索输入框 */
.search-input {
    width: 100%;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 0 50px 0 15px; /* 增加右侧内边距，为搜索图标腾出更多空间 */
    color: var(--darker-bg-color);
    font-size: 16px;
}

.search-input::placeholder {
    color: var(--gray-blue);
}

.search-input:focus {
    outline: none;
    background-color: rgba(255, 255, 255, 0.2);
}

/* 搜索图标 */
.search-icon {
    position: absolute;
    right: 15px;
    color: var(--light-text-color);
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--darker-bg-color);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.search-icon:hover {
    background-color: var(--active-bg-color);
    color: #7d0d1a;
}

/* 分类容器 */
.category-container {
    display: flex;
    flex: 1;
    overflow: hidden; /* 确保容器不滚动 */
    margin-top: 10px; /* 向下移动10px */
    height: calc(100% - 120px); /* 设置固定高度，减去头部和搜索区域的高度 */
    max-height: calc(100% - 120px); /* 确保不超过容器高度 */
    position: relative; /* 相对定位 */
    width: 100%; /* 确保容器占满宽度 */
    box-sizing: border-box; /* 确保内边距不会增加元素的总宽度 */
}

/* 左侧分类列表 */
.category-list {
    width: 120px; /* 增加宽度 */
    margin-left: 10px; /* 向右移动 */
    /* 移除背景颜色 */
    overflow-y: auto; /* 允许垂直滚动 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    height: 100%; /* 使用100%高度填充父容器 */
    box-sizing: border-box; /* 确保内边距不会增加元素的总高度 */
}

.category-list::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 分类项 */
.category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px 5px; /* 减小上下内边距，调低高度 */
    cursor: pointer;
    border-radius: 8px;
    margin: 5px;
    transition: all 0.3s ease;
    /* 添加默认背景色 */
    background-color: var(--secondary-color);
    /* 添加滤镜效果 */
    filter: brightness(0.85);
}

.category-item.active {
    background-color: var(--secondary-color-active);
    /* 移除滤镜效果，让激活状态更明亮 */
    filter: none;
}

.category-icon {
    width: 35px; /* 减小图标尺寸 */
    height: 35px; /* 减小图标尺寸 */
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-icon img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.category-name {
    font-size: 12px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90px;
}

.category-item.active .category-name {
    color: #7d0d1a;
    font-weight: bold; /* 可选：使文字加粗，增强视觉效果 */
}

/* 右侧内容区域 */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 确保容器不滚动 */
    padding-left: 5px; /* 添加左侧内边距，与左侧分类列表保持一定距离 */
    padding-right: 15px; /* 添加右侧内边距，确保内容不会超出容器 */
    box-sizing: border-box; /* 确保内边距不会增加元素的总宽度 */
    height: 100%; /* 使用100%高度填充父容器 */
}

/* 横向分类标签 */
.horizontal-categories {
    display: flex;
    overflow-x: auto;
    padding: 10px 5px;
    /* 移除背景颜色 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.horizontal-categories::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.h-category-item {
    flex: 0 0 auto;
    padding: 8px 15px;
    margin: 0 5px;
    background-color: var(--secondary-color);
    border-radius: 20px;
    font-size: 14px;
    white-space: nowrap;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.h-category-item.active {
    background-color: var(--secondary-color-active);
    color: var(--text-color); /* 修改选中状态的文字颜色为 7d0d1a */
    font-weight: bold; /* 可选：使文字加粗 */
}

/* 游戏卡片网格 - subgame页面特定样式 */
.content-area .game-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 始终保持一行三个卡片 */
    gap: 15px; /* 统一使用15px的间距 */
    padding: 15px; /* 设置内边距 */
    overflow-y: auto; /* 允许垂直滚动 */
    flex: 1;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    width: 100%; /* 确保网格占满容器宽度 */
    box-sizing: border-box; /* 确保内边距不会增加元素的总宽度 */
    height: calc(100% - 100px); /* 减去横向分类标签和分页的高度 */
    max-height: calc(100% - 100px); /* 确保不超过容器高度 */
    grid-auto-rows: auto; /* 允许行高自适应 */
    justify-items: center; /* 水平居中网格项目 */
    align-items: start; /* 从顶部开始对齐项目 */
    grid-row-gap: 15px; /* 减小行间距，防止卡片间隔过大 */
    align-content: flex-start; /* 添加此属性，让内容从顶部开始排列 */
}

/* subgame页面游戏卡片特定样式 */
.content-area .game-card {
    position: relative;
    width: 100%; /* 使用百分比宽度，适应网格布局 */
    max-width: 162px; /* 设置最大宽度为162px */
    min-height: 120px;
    height: auto; /* 高度自适应 */
    aspect-ratio: auto; /* 移除固定宽高比，让高度自适应图片 */
    justify-self: center; /* 在网格中居中显示 */
    margin-bottom: 10px; /* 添加底部外边距，防止卡片叠在一起 */
    padding: 0; /* 移除内边距 */
    box-sizing: border-box; /* 确保内边距不会增加元素的总宽度 */
    display: flex; /* 使用flex布局 */
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
    background-color: transparent; /* 透明背景 */
    border-radius: 8px; /* 圆角 */
    overflow: hidden; /* 确保内容不会溢出卡片 */
}

/* 添加或修改图片相关样式 */
.content-area .game-card .game-image {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
    background-color: transparent; /* 透明背景 */
}

.content-area .game-card .game-icon {
    flex: 1;
    overflow: hidden;
    display: flex;
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
    position: relative;
    height: 100%;
    width: 100%;
    padding: 0;
}

.content-area .game-card .game-thumbnail {
    width: 100% !important;
    height: auto !important; /* 改为auto，让高度根据宽度自适应 */
    object-fit: contain !important; /* 确保图片完整显示 */
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: relative !important;
    z-index: 5 !important;
    max-height: none; /* 移除最大高度限制 */
    margin: 0; /* 移除外边距 */
}

.content-area .game-grid::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 游戏提供商标签 */
.game-provider {
    position: absolute;
    top: 5px;
    left: 5px;
    background-color: rgba(0, 0, 0, 0.6);
    color: var(--text-color);
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 3px;
    z-index: 10;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 0;
}

.page-btn {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--darker-bg-color);
    border-radius: 5px;
    margin: 0 5px;
    cursor: pointer;
}

.page-number {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--darker-bg-color);
    border-radius: 5px;
    margin: 0 5px;
    cursor: pointer;
}

.page-number.active {
    background-color: var(--active-bg-color);
    color: #7d0d1a; /* 修改选中状态的文字颜色为 7d0d1a */
    font-weight: bold; /* 可选：使文字加粗 */
}

/* 省略号样式 */
.page-ellipsis {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 5px;
    font-weight: bold;
    color: var(--gray-blue);
}

/* 空状态 */
.empty-state {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    text-align: center;
}

.empty-icon {
    width: 80px;
    height: 80px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-text {
    color: var(--light-text-color);
    font-size: 14px;
}

/* 响应式布局 */
@media (max-width: 480px) {
    .category-list {
        width: 80px;
    }

    .category-icon {
        font-size: 20px;
    }

    .category-name {
        font-size: 10px;
        max-width: 70px;
    }

    .h-category-item {
        padding: 6px 12px;
        font-size: 12px;
    }

    .content-area {
        padding-right: 10px; /* 在小屏幕上减小右侧内边距 */
    }

    .content-area .game-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr); /* 保持一行三个卡片 */
        gap: 15px; /* 保持统一的间距 */
        padding: 15px; /* 保持统一的内边距 */
        grid-auto-rows: auto; /* 自动调整行高 */
        justify-items: center; /* 水平居中网格项目 */
    }

    .content-area .game-card {
        max-width: 162px; /* 在中等屏幕上减小最大宽度 */
        aspect-ratio: auto; /* 移除固定宽高比，让高度自适应图片 */
        height: auto; /* 高度由宽高比决定 */
        justify-self: center; /* 在网格中居中显示 */
    }
}

/* 更小屏幕的适配 */
@media (max-width: 370px) {
    .category-list {
        width: 100px;
    }

    .content-area {
        padding-left: 3px; /* 在更小屏幕上进一步减小左侧内边距 */
        padding-right: 8px; /* 在更小屏幕上进一步减小右侧内边距 */
    }

    .content-area .game-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr); /* 保持一行三个卡片 */
        gap: 15px; /* 保持统一的间距 */
        grid-auto-rows: auto; /* 自动调整行高 */
        justify-items: center; /* 水平居中网格项目 */
    }

    .content-area .game-card {
        max-width: 120px; /* 在小屏幕上进一步减小最大宽度 */
        aspect-ratio: auto; /* 移除固定宽高比，让高度自适应图片 */
        height: auto; /* 高度由宽高比决定 */
        justify-self: center; /* 在网格中居中显示 */
    }
}
