/* Agent页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

body {
    background-color: var(--outer-bg-color) !important;
    color: var(--text-color);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.container {
    max-width: 480px;
    width: 100%;
    margin: 0 auto;
    background-color: var(--main-bg-color);
}

/* 空内容提示 */
.empty-content {
    display: none;
}

.empty-icon {
    display: none;
}

/* 代理信息 */
.agent-info-box {
    background-color: var(--darker-bg-color);
    display: flex;
    flex-wrap: wrap;
    padding: 15px;
    position: relative;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    margin-top: 3px;
}

.agent-level-badge {
    width: 55px;
    height: 55px;
    margin-right: 15px;
    position: relative;
    flex: 0 0 auto;
}

.badge-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.badge-bg img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.badge-circle {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 18px;
    height: 18px;
    background-color: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--darker-bg-color);
    font-weight: bold;
}

.agent-info-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.bonus-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 12px;
}

.bonus-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
}

.bonus-value {
    font-size: 14px;
    color: var(--secondary-color);
    font-weight: bold;
    margin-left: 5px;
}

.agent-description {
    font-size: 10px;
    line-height: 1.2;
    color: rgba(255, 255, 255, 0.7);
}

.action-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    gap: 10px; /* 添加间距 */
}

.action-btn {
    background-color: var(--button-normal);
    color: var(--text-color);
    border: none;
    border-radius: 5px;
    padding: 10px 15px; /* 增加垂直内边距 */
    height: 40px; /* 固定高度 */
    line-height: 20px; /* 确保文本垂直居中 */
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    text-align: center;
    flex: 1;
    margin: 0;
    display: flex; /* 使用flex布局 */
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
}

.action-btn:hover {
    background-color: var(--button-active);
}

.receive-btn {
    background-color: var(--button-normal);
}

.history-btn {
    background-color: var(--button-normal);
}

/* 响应式调整 */
@media (max-width: 360px) {
    .action-btn {
        font-size: 12px; /* 小屏幕上减小字体 */
        padding: 8px 10px; /* 小屏幕上减小内边距 */
        height: 36px; /* 小屏幕上减小高度 */
    }
}

.agent-mode-row {
    display: flex;
    align-items: center;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.85);
    margin-bottom: 8px;
}

.agent-mode-text {
    margin-right: 8px;
}

.level-difference {
    color: #fff;
    font-weight: bold;
}

/* 社交分享栏 */
.social-bar {
    background-color: var(--darker-bg-color);
    margin-top: 15px;
    padding: 10px 15px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.social-bar-label {
    color: var(--text-color);
    font-size: 14px;
    margin-right: 10px;
}

.social-icons-row {
    display: flex;
    flex: 1;
    justify-content: space-between;
    gap: 5px;
}

.social-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
}

.social-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* 代理信息下方的二维码和邀请码部分 */
.qr-section {
    padding: 15px;
    background-color: var(--darker-bg-color);
    position: relative;
}

.qr-title {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
    text-align: left;
    margin-left: 90px;
}

.qr-code-container {
    float: left;
    width: 75px;
    margin-right: 15px;
    overflow: hidden;
    position: relative;
    margin-bottom: 15px;
}

.qr-code {
    width: 100%;
    height: 75px;
    background-color: #fff;
    overflow: hidden;
}

.qr-code img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.share-qr-btn {
    width: 100%;
    background-color: var(--secondary-color);
    color: var(--text-color);
    text-align: center;
    padding: 3px 0;
    font-size: 10px;
    font-weight: bold;
    cursor: pointer;
    opacity: 1;
}

.qr-info {
    overflow: hidden;
}

.my-link-container {
    margin-bottom: 10px;
}

.link-text {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
    background-color: var(--secondary-color-active);
    padding: 8px 10px;
    border-radius: 3px;
    margin-bottom: 8px;
    position: relative;
    word-break: break-all;
    padding-right: 30px;
}

.copy-link-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    align-items: center;
    background-color: var(--secondary-color-active);
    padding: 8px 10px;
    border-radius: 3px;
}

.info-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.info-value {
    font-size: 12px;
    color: #fff;
    display: flex;
    align-items: center;
}

.code-value {
    color: var(--secondary-color);
    font-weight: bold;
}

.copy-icon {
    display: inline-block;
    background-color: var(--secondary-color);
    color: var(--darker-bg-color);
    width: 18px;
    height: 18px;
    text-align: center;
    line-height: 18px;
    border-radius: 2px;
    font-size: 12px;
    margin-left: 5px;
    font-weight: bold;
    cursor: pointer;
}

.agent-settlement {
    color: var(--text-color);
    font-size: 0.9rem;
    margin-top: 4px;
}

.horizontal-line {
    width: 100%;
    height: 1px;
    background-color: rgba(255,255,255,0.1);
    margin: 15px 0 5px 0;
}

/* 代理网络卡片 */
.agent-network-title {
    padding: 15px 15px 10px;
    display: flex;
    align-items: center;
    color: var(--secondary-color);
}

.agent-network-title img {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    max-width: 100%;
    object-fit: contain;
}

.agent-network-card {
    background-color: rgba(255,255,255,0.1);
    margin: 0 15px 20px;
    border-radius: 8px;
    padding: 15px;
}

.agent-avatar-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.agent-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    border: 2px solid var(--secondary-color);
    flex-shrink: 0;
}

.agent-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.agent-stats {
    flex: 1;
}

.network-header {
    display: none;
}

.performance-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 13px;
}

.performance-label {
    color: rgba(255, 255, 255, 0.8);
}

.performance-value {
    color: #fff;
    font-weight: bold;
}

.total-commission {
    color: var(--text-color);
    font-weight: bold;
}

.commission-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.commission-value {
    margin-left: auto;
    color: var(--text-color);
    font-weight: bold;
}

/* 响应式设计 */
@media (min-width: 481px) {
    /* 大屏幕下样式已在theme.css中设置 */
}

.responsive-img {
    max-width: 100%;
    height: auto;
    display: inline-block;
}

/* 添加可滚动内容区域样式 */
.scrollable-content {
    height: calc(100vh - 110px); /* 减去header和tab-nav的高度 */
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.scrollable-content::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
} 
