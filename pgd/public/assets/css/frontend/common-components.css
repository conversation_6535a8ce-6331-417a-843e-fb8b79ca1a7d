/**
 * 通用组件样式
 * 为减少重复代码，将各页面常用样式整合到此文件
 */

/* 导入充值按钮样式 */
@import url("common/deposit-btn.css");

/* 通用容器样式 */
.container {
    width: 100%;
    max-width: 516px;
    margin: 0 auto;
    height: 100vh; /* 固定高度为视窗高度 */
    position: relative;
    overflow-x: hidden;
    overflow-y: hidden !important; /* 允许垂直滚动 */
    background-color: var(--main-bg-color);
    padding-bottom: 60px; /* 确保与bottom-nav的高度完全一致 */
    border-radius: 0;
    box-shadow: none;
    top: 0; /* 确保容器从页面顶部开始 */
    left: 0; /* 水平居中时不受影响 */
    right: 0; /* 水平居中时不受影响 */
}

/* 通用头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: var(--darker-bg-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    width: 100%;
    max-width: 516px;
    margin: 0 auto;
    box-sizing: border-box;
}

/* 通用返回按钮 */
.back-btn {
    font-size: 24px;
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    padding: 5px;
}

/* 通用标题 */
.header-title {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    color: var(--text-color);
    flex: 1;
}

/* 通用卡片样式 */
.card {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 15px;
    margin: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 通用标签栏样式 */
.tab-nav {
    display: flex;
    position: relative;
    margin-bottom: 1px;
    background-color: var(--darker-bg-color);
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.tab-nav::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.tab-item {
    flex: 1;
    text-align: center;
    padding: 10px 5px;
    font-size: 14px;
    cursor: pointer;
    position: relative;
    white-space: nowrap;
    color: rgba(255, 255, 255, 0.7);
}

.tab-item.active {
    color: var(--secondary-color);
    border-bottom: 3px solid var(--secondary-color);
    z-index: 1;
}

/* 通用表单元素 */
.form-group {
    margin-bottom: 15px;
}

.form-control {
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

/* 通用按钮样式 */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    text-align: center;
    font-weight: bold;
}

.btn-primary {
    background-color: var(--secondary-color);
    color: var(--darker-bg-color);
}

.btn-secondary {
    background-color: rgba(255, 255, 255, 0.2);
    color: var(--text-color);
}

/* 响应式设计通用规则 */
@media (min-width: 481px) {
    .container {
        border-radius: 0;
        margin: 0 auto;
        height: 100vh; /* 保持响应式设计中的高度一致 */
        box-shadow: none;
    }
}

@media (min-width: 516px) {
    .container, .header {
        max-width: 516px;
    }
} 