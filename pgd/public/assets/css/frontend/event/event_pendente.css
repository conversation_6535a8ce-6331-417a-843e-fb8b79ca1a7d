/* 待领取页面专用CSS */

/* 待领取奖励页面专用样式 */
.pending-container {
    padding: 15px;
}

.pending-title {
    font-size: 18px;
    font-weight: bold;
    margin: 15px 0;
    color: var(--primary-color);
}

.pending-reward-item {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

.pending-reward-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.pending-reward-title {
    font-weight: bold;
    font-size: 16px;
}

.pending-reward-amount {
    font-weight: bold;
    color: var(--secondary-color);
}

.pending-reward-description {
    color: var(--text-color-secondary);
    margin-bottom: 15px;
    font-size: 14px;
}

.pending-reward-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pending-reward-expiry {
    font-size: 12px;
    color: var(--text-color-secondary);
}

.pending-reward-claim {
    background-color: var(--secondary-color);
    color: var(--darker-bg-color);
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    font-weight: bold;
    cursor: pointer;
}

.pending-reward-claim:hover {
    background-color: var(--secondary-color-dark);
}

.pending-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.pending-stat-item {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    flex: 1;
    min-width: 100px;
    margin: 0 5px 10px 5px;
    text-align: center;
}

.pending-stat-value {
    font-size: 18px;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 5px;
}

.pending-stat-label {
    font-size: 12px;
    color: var(--text-color-secondary);
} 
