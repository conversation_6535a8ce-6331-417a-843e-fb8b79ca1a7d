/**
 * VIP页面专用样式
 */

/* VIP状态卡片 */
.vip-status-card {
    position: relative;
    width: 100%;
    height: 120px;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    background-color: var(--darker-bg-color);
    /* 移除硬编码的背景色 */
}

.vip-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 15px;
    z-index: 3;
}

.vip-level-container {
    position: relative;
    width: 64px;
    height: 64px;
    margin-right: 15px;
}

.vip-level-circle-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../../../img/frontend/common/vip_bg.png');
    background-size: cover;
    background-position: center;
    border-radius: 50%;
}

.vip-level-circle-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../../../img/frontend/common/vip_bg2.png');
    background-size: cover;
    background-position: center;
    border-radius: 50%;
    z-index: 2;
}

.vip-level-number {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 30px;
    font-weight: bold;
    color: #f7ea94;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
    z-index: 3;
}

.vip-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.vip-info-row {
    display: flex;
    align-items: center;
}

.vip-info-label {
    font-size: 14px;
    color: #fff;
    margin-right: 5px;
}

.vip-info-value {
    font-size: 14px;
    font-weight: bold;
    color: var(--text-color);
}

.vip-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-left: 10px;
}

.vip-btn {
    border: none;
    border-radius: 5px;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    text-align: center;
    white-space: nowrap;
}

.vip-btn.collect-all {
    background-color: #666;
    color: #fff;
}

.vip-btn.history {
    background-color: var(--secondary-color) !important; /* 使用主题变量并添加!important */
    color: var(--text-color);
}

/* VIP等级列表 */
.vip-section-title {
    color: var(--secondary-color);
    font-size: 18px;
    margin-bottom: 15px;
    position: relative;
    padding-left: 15px;
}

.vip-section-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 18px;
    background-color: var(--secondary-color);
    border-radius: 3px;
}

.vip-levels-container {
    margin-bottom: 20px;
}

.vip-levels {
    display: flex;
    overflow-x: auto;
    gap: 15px;
    padding: 5px 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.vip-levels::-webkit-scrollbar {
    display: none;
}

.vip-level-card {
    min-width: 150px;
    background-color: #1E1E2D;
    border-radius: 10px;
    padding: 0;
    overflow: hidden;
    position: relative;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.vip-level-card.active {
    border-color: var(--secondary-color);
}

.vip-level-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: var(--secondary-color);
    color: var(--text-color);
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 10px;
    z-index: 2;
    font-weight: bold;
    transform: rotate(15deg);
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.vip-level-header {
    background-color: var(--darker-bg-color);
    padding: 15px 10px;
    text-align: center;
}

.vip-level-requirement {
    color: var(--text-color);
    font-size: 12px;
    margin-bottom: 10px;
    text-align: center;
}

.vip-level-benefits {
    list-style: none;
    padding: 0;
}

.vip-level-benefits li {
    margin-bottom: 5px;
    color: #FFFFFF;
    font-size: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 5px;
}

.benefit-value {
    color: var(--text-color);
    font-weight: bold;
}

/* VIP特权 */
.vip-privileges {
    margin-bottom: 20px;
}

.vip-privileges-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.vip-privilege-item {
    background-color: #1E1E2D;
    border-radius: 10px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.vip-privilege-icon {
    font-size: 24px;
    min-width: 32px;
    text-align: center;
}

.vip-privilege-name {
    color: #FFFFFF;
    font-size: 14px;
}

/* VIP分类标签 */
.vip-category-tabs {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    background-color: var(--darker-bg-color);
    margin-bottom: 15px;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.vip-category-tabs::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.vip-category-tab {
    flex: 1 0 auto;
    padding: 12px 15px;
    text-align: center;
    color: var(--secondary-color);
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.3s ease;
    font-size: 14px;
    border-bottom: 3px solid transparent;
}

.vip-category-tab.active {
    color: var(--text-color);
    border-bottom: 3px solid var(--secondary-color);
    font-weight: bold;
}

/* VIP分类内容 */
.vip-category-content {
    position: relative;
    margin-bottom: 20px;
    background-color: var(--darker-bg-color) !important; /* 添加!important确保覆盖 */
    border-radius: 0 0 8px 8px;
    overflow: hidden;
    max-height: calc(100vh - 380px);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.vip-category-content::-webkit-scrollbar {
    display: none;
}

.progress-container {
    position: relative;
    width: 100%;
    height: 20px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    overflow: hidden;
    margin: 0 auto; /* 水平居中 */
    max-width: 150px; /* 限制最大宽度 */
    display: flex;
    align-items: center; /* 垂直居中内容 */
    justify-content: center; /* 水平居中内容 */
}

.progress-bar {
    height: 100%;
    background: linear-gradient(to right, var(--button-active), var(--secondary-color));
    border-radius: 5px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

.progress-text {
    font-size: 12px;
    color: var(--secondary-color);
    position: absolute;
    width: 100%;
    text-align: center;
    line-height: 20px;
    z-index: 2;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
    left: 0; /* 确保左对齐从0开始 */
    top: 50%; /* 垂直居中 */
    transform: translateY(-50%); /* 精确垂直居中 */
    display: flex; /* 使用flex布局 */
    justify-content: center; /* 水平居中 */
    align-items: center; /* 垂直居中 */
    height: 100%; /* 占满整个容器高度 */
}

.vip-category-item {
    display: none;
    padding: 15px;
    background-color: var(--darker-bg-color) !important; /* 添加!important确保覆盖 */
    min-height: 200px;
}

.vip-category-item.active {
    display: block;
}

/* VIP表格样式 */
.vip-table-container {
    width: 100%;
    overflow-x: auto;
}

.vip-table {
    width: 100%;
    border-collapse: collapse;
    text-align: center;
    color: #fff;
}

.vip-table th {
    background-color: var(--darker-bg-color) !important; /* 使用主题变量并添加!important */
    color: var(--secondary-color) !important; /* 使用主题变量并添加!important */
    padding: 12px 10px;
    font-weight: bold;
    font-size: 14px;
    text-transform: uppercase;
    border-bottom: 1px solid #333;
}

.vip-table td {
    padding: 10px;
    border-bottom: none;
    font-size: 14px;
    vertical-align: middle;
    text-align: center;
}

.vip-table tr {
    border-bottom: none;
}

.vip-table tr:last-child td {
    border-bottom: none;
}

.vip-table tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.05);
}

.vip-table tr:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 当前VIP等级高亮显示 */
.vip-table tr.current-vip {
    background-color: rgba(var(--secondary-color-rgb, 247, 234, 148), 0.15) !important; /* 使用主题变量并添加!important */
    position: relative;
    border: 2px solid var(--secondary-color) !important; /* 使用主题变量并添加!important */
    box-shadow: 0 0 10px rgba(var(--secondary-color-rgb, 247, 234, 148), 0.3); /* 使用主题变量 */
}

.vip-table tr.current-vip td {
    font-weight: bold; /* 文本加粗 */
    color: var(--secondary-color) !important; /* 使用主题变量并添加!important */
    position: relative;
    z-index: 1;
}

/* 处理hover效果与当前VIP高亮的冲突 */
.vip-table tr.current-vip:hover {
    background-color: rgba(var(--secondary-color-rgb, 247, 234, 148), 0.2) !important; /* 保持一致的高亮色调 */
}

/* 响应式布局 */
@media (max-width: 480px) {
    .vip-content {
        flex-direction: column;
        padding: 10px;
    }
    
    .vip-level-container {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .vip-info {
        margin-bottom: 10px;
        width: 100%;
    }
    
    .vip-actions {
        margin-left: 0;
        width: 100%;
        flex-direction: row;
    }
    
    .vip-btn {
        flex: 1;
    }
    
    .vip-status-card {
        height: auto;
        padding: 15px 0;
    }
    
    .vip-category-tab {
        padding: 10px;
        font-size: 12px;
    }
    
    .vip-table th,
    .vip-table td {
        padding: 8px 6px;
        font-size: 12px;
    }
}

/* 空状态文本颜色 */
.empty-text {
    color: var(--secondary-text-color);
    font-size: 14px;
    text-align: center;
    padding: 20px;
}
