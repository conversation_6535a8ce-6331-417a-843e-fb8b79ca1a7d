/**
 * 活动模块通用样式
 * 适用于所有event页面
 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: var(--outer-bg-color) !important;
    color: var(--text-color);
    font-family: Arial, sans-serif;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}


.horizontal-tabs {
    display: flex;
    overflow-x: auto;
    background-color: var(--darker-bg-color); 
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    white-space: nowrap;
    padding: 0;
    width: 100%;
    justify-content: space-between;
    max-width: 516px;
    margin: 0 auto;
}

.horizontal-tabs::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.tab {
    padding: 15px 0;
    white-space: nowrap;
    cursor: pointer;
    color: var(--text-color);
    opacity: 0.7;
    font-weight: normal;
    font-size: 14px;
    text-align: center;
    min-width: auto; /* 从固定的16.66%改为auto */
    flex: 1;
    position: relative;
    padding-left: 10px; /* 添加左右内边距 */
    padding-right: 10px;
}

.tab.active {
    color: var(--text-color);
    opacity: 1;
    position: relative;
    font-weight: bold;
}

.tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--secondary-color);
}

.main-content {
    min-height: calc(100vh - 50px);
    position: relative;
    padding: 15px;
    background-color: var(--main-bg-color);
    padding-bottom: 70px; /* 确保底部内容不被导航栏遮挡 */
    max-width: 516px;
    margin: 0 auto;
}

/* 活动页面样式 */

/* 左右布局 */
.event-container {
    display: flex;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    padding: 15px;
    gap: 15px;
    flex-direction: row;
}

/* 修改媒体查询，移除小屏幕上的列式布局转换 */
@media (max-width: 768px) {
    .event-container {
        flex-direction: row; /* 保持行布局 */
        gap: 10px; /* 减小间距以适应小屏幕 */
    }
}

.event-sidebar {
    width: 80px; /* 减小宽度以适应小屏幕 */
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 80px;
}

.event-sidebar-btn {
    background-color: var(--secondary-color);
    color: var(--darker-bg-color);
    border: none;
    border-radius: 10px;
    padding: 15px 5px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.7;
    box-shadow: none;
    padding: 10px;
    border-radius: 5px;
}

.event-sidebar-btn.active {
    background-color: var(--button-active);
    opacity: 1;
    box-shadow: 0 0 10px rgba(44, 169, 217, 0.7); /* 使用与--button-active相匹配的蓝色阴影 */
    transform: scale(1.05);
    border: 2px solid #1a8cb6; /* 使用比--button-active稍深的蓝色边框 */
    color: var(--text-color);
}

.event-sidebar-btn:hover {
    opacity: 0.9;
}

.event-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: fit-content; /* 使高度适应内容 */
}

/* 活动卡片 - 简化版本 */
.event-card {
    margin-bottom: 15px;
    cursor: pointer;
    border-radius: 10px;
    overflow: hidden;
    background: transparent;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s;
    width: 100%;
}

.event-card:hover {
    transform: translateY(-5px);
}

/* 图片容器 */
.event-image {
    width: 100%;
    height: 0;
    padding-bottom: 30%; /* 默认16:9比例，作为初始值 */
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

/* 确保event-details不会影响布局 */
.event-details {
    display: none;
}

/* 标签位置调整 */
.event-tag {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--secondary-color);
    color: var(--darker-bg-color);
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
}

.event-tag.new {
    background: var(--success-color, #4CAF50);
    animation: pulse 1.2s infinite;
}

.event-tag.popular {
    background: var(--info-color, #2196F3);
    animation: pulse 2s infinite;
}

.event-tag.vip {
    background: var(--special-color, #9C27B0);
    animation: pulse 1.8s infinite;
}

.event-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    margin-bottom: 5px;
}

.event-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.event-date {
    display: flex;
    align-items: center;
}

.event-date-icon {
    margin-right: 4px;
}

.event-status {
    display: flex;
    align-items: center;
}

.event-status-icon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-active {
    background-color: var(--success-color, #4CAF50);
}

.status-ended {
    background-color: var(--danger-color, #F44336);
}

.status-coming {
    background-color: var(--warning-color, #FFC107);
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 30px 0;
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
}

/* 任务页面样式 */
.mission-container {
    padding: 15px;
}

.mission-item {
    background: var(--secondary-color);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.mission-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.mission-title {
    font-weight: bold;
    font-size: 16px;
    color: var(--text-color);
}

.mission-reward {
    color: var(--text-color);
    font-weight: bold;
}

.mission-description {
    color: #666;
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.4;
}

.mission-progress {
    margin-bottom: 15px;
}

.progress-bar {
    height: 8px;
    background: #E0E0E0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 5px;
}

.progress-fill {
    height: 100%;
    background: #4CAF50;
    border-radius: 4px;
}

/* 进度条百分比样式 */
.progress-0 { width: 0%; }
.progress-5 { width: 5%; }
.progress-10 { width: 10%; }
.progress-15 { width: 15%; }
.progress-20 { width: 20%; }
.progress-25 { width: 25%; }
.progress-30 { width: 30%; }
.progress-35 { width: 35%; }
.progress-40 { width: 40%; }
.progress-45 { width: 45%; }
.progress-50 { width: 50%; }
.progress-55 { width: 55%; }
.progress-60 { width: 60%; }
.progress-65 { width: 65%; }
.progress-70 { width: 70%; }
.progress-75 { width: 75%; }
.progress-80 { width: 80%; }
.progress-85 { width: 85%; }
.progress-90 { width: 90%; }
.progress-95 { width: 95%; }
.progress-100 { width: 100%; }

.progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #888;
}

.mission-button {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 5px;
    background: var(--button-normal);
    color: var(--text-color);
    font-weight: bold;
    cursor: pointer;
}

.mission-button.disabled {
    background: #E0E0E0;
    color: #888;
    cursor: default;
}

.mission-button.completed {
    background: #4CAF50;
    color: var(--text-color);
}

.mission-icon {
    font-size: 20px;
    margin-right: 10px;
}

/* 空状态样式 - 已移至通用样式文件 /assets/css/frontend/common/empty-state.css */
/* 请在页面中使用通用空状态样式 */

/* VIP页面样式 */
.vip-levels {
    display: flex;
    padding: 15px 0;
    gap: 15px;
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.vip-levels::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.vip-level {
    min-width: 200px;
    padding: 15px;
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0.08);
    transition: transform 0.3s;
    position: relative;
}

.vip-level.current {
    border: 2px solid var(--secondary-color);
}

.vip-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--secondary-color);
    color: var(--darker-bg-color);
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
}

.vip-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #ffffff;
}

.vip-requirement {
    color: var(--secondary-color);
    margin-bottom: 15px;
    font-size: 14px;
}

.vip-benefits {
    list-style: none;
}

.vip-benefit {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

.vip-benefit-icon {
    margin-right: 8px;
    font-size: 16px;
    display: inline-block;
    width: 20px;
    text-align: center;
}

@media (max-width: 516px) {
    .header, .horizontal-tabs, .main-content {
        max-width: 100%;
    }
}

@media (min-width: 517px) {
    .header, .horizontal-tabs, .main-content {
        max-width: 516px;
        margin: 0 auto;
    }
}

/* 在小屏幕上调整内容布局 */
@media (max-width: 480px) {
    .event-container {
        padding: 10px; /* 减小内边距 */
    }
    
    .event-sidebar {
        width: 70px; /* 进一步减小侧边栏宽度 */
        min-width: 70px;
    }
    
    .event-sidebar-btn {
        padding: 8px 5px; /* 减小按钮内边距 */
        font-size: 12px; /* 减小字体大小 */
    }
    
    .event-content {
        gap: 10px; /* 减小网格间距 */
    }
} 
