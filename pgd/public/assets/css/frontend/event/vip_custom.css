/* VIP页面自定义样式 - 仅适用于VIP页面 */

/* 禁止容器滚动 */
.container {
    overflow: hidden; /* 禁止容器滚动 */
    height: 100vh; /* 设置高度为视口高度 */
    position: relative; /* 确保绝对定位元素相对于容器定位 */
}

/* 禁止主内容区滚动 */
.main-content {
    overflow: hidden; /* 禁止主内容区滚动 */
    padding-bottom: 75px; /* 增加底部内边距，确保与bottom-nav有足够距离 */
    height: calc(100vh - 50px); /* 减去顶部标签高度 */
    position: relative; /* 设置为相对定位，使内部元素定位正确 */
}

/* VIP内容区域滚动样式 */
.vip-category-content {
    max-height: calc(100vh - 380px); /* 增加减去的高度，确保不会超出bottom-nav */
    overflow-y: auto; /* 只允许此区域垂直滚动 */
    /* 隐藏滚动条 - Firefox */
    scrollbar-width: none;
    /* 隐藏滚动条 - IE/Edge */
    -ms-overflow-style: none;
    /* 确保底部与bottom-nav保持距离 */
    /* margin-bottom: 35px; */
}

/* 隐藏滚动条 - Chrome/Safari/Opera */
.vip-category-content::-webkit-scrollbar {
    display: none;
}

/* VIP类别项样式调整 */
.vip-category-item {
    background-color: var(--darker-bg-color);
    padding: 15px;
    border-radius: 8px;
    color: var(--text-color);
}

/* VIP表格样式调整 */
.vip-table {
    width: 100%;
    border-collapse: collapse;
}

.vip-table th {
    background-color: var(--darker-bg-color);
    color: var(--secondary-color);
    text-align: left;
    padding: 12px 8px;
}

.vip-table td {
    padding: 10px 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 当前VIP等级高亮样式 */
.vip-table tr.current-vip {
    background-color: rgba(var(--secondary-color-rgb, 247, 234, 148), 0.15);
}

.vip-table tr:hover {
    background-color: rgba(var(--secondary-color-rgb, 247, 234, 148), 0.1);
}

/* 进度条样式 */
.progress-container {
    width: 100%;
    height: 20px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    overflow: hidden;
    margin-top: 5px;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(to right, var(--button-active), var(--secondary-color));
    border-radius: 5px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}

.progress-text {
    font-size: 12px;
    color: var(--secondary-color);
    position: absolute;
    width: 100%;
    text-align: center;
    line-height: 20px;
    z-index: 2;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .vip-category-content {
        max-height: calc(100vh - 370px); /* 中等屏幕上稍微调整高度 */
    }
}

@media screen and (max-width: 480px) {
    .vip-category-content {
        max-height: calc(100vh - 350px); /* 移动设备上的高度调整 */
        min-height: 150px; /* 移动设备上的最小高度 */
    }
    
    .vip-table th, 
    .vip-table td {
        padding: 8px 4px;
        font-size: 12px;
    }
    
    /* 移动设备上调整当前VIP等级标识位置 */
    .vip-table tr.current-vip td:first-child::before {
        left: -5px;
    }
} 
