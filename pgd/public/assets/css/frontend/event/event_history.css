/* 历史页面专用CSS */

/* 筛选下拉菜单样式 */
.filter-container {
    display: flex;
    justify-content: space-between;
    position: relative;
    align-items: center;
    padding: 15px;
    background-color: var(--secondary-color);
    box-shadow: 0 1px 2px lch(72.41% 38.2 241.39 / 0.771); /* 添加轻微阴影增加层次感 */
}

.filter-section {
    display: flex;
    gap: 10px;
}

.filter-dropdown {
    position: relative;
    max-width: none;
    width: auto;
    min-width: 100px;
}

.filter-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: auto;
    padding: 8px 12px;
    background-color: var(--darker-bg-color) !important; /* 添加!important确保覆盖 */
    color: var(--text-color) !important; /* 添加!important确保覆盖 */
    border: 1px solid var(--secondary-color) !important; /* 添加!important确保覆盖 */
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
}

.filter-dropdown-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: auto;
    padding: 8px 12px;
    background-color: var(--darker-bg-color) !important; /* 添加!important确保覆盖 */
    color: var(--text-color) !important; /* 添加!important确保覆盖 */
    border: 1px solid var(--secondary-color) !important; /* 添加!important确保覆盖 */
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
}

.filter-btn .arrow {
    margin-left: 8px;
    transition: transform 0.3s;
}

.filter-btn.active {
    background-color: var(--button-active) !important; /* 添加!important确保覆盖 */
    border-color: var(--secondary-color) !important; /* 添加!important确保覆盖 */
}

.filter-btn.active .arrow {
    transform: rotate(180deg);
}

.filter-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: auto;
    min-width: 100%;
    white-space: nowrap;
    background-color: var(--darker-bg-color) !important; /* 添加!important确保覆盖 */
    border: 1px solid var(--secondary-color) !important; /* 添加!important确保覆盖 */
    border-radius: 10px;
    z-index: 100;
    margin-top: 5px;
    display: none;
    overflow: hidden;
}

.filter-menu.show {
    display: block;
}

.filter-option {
    padding: 10px 15px;
    cursor: pointer;
    color: var(--text-color) !important; /* 添加!important确保覆盖 */
    transition: background-color 0.2s;
    white-space: nowrap;
    min-width: max-content;
}

.filter-option:hover {
    background-color: var(--button-active) !important; /* 添加!important确保覆盖 */
}

.filter-option.active {
    background-color: var(--secondary-color) !important; /* 添加!important确保覆盖 */
    color: var(--darker-bg-color) !important; /* 添加!important确保覆盖 */
    font-weight: bold;
}

.bonus-display {
    font-size: 18px;
    font-weight: bold;
}

.bonus-label {
    color: var(--text-color) !important; /* 修改为使用--secondary-color变量 */
    margin-right: 8px;
}

.bonus-value {
    color: var(--text-color);
}

/* 历史记录页面专用样式 */
.history-container {
    padding: 15px;
}

.history-title {
    font-size: 18px;
    font-weight: bold;
    margin: 15px 0;
    color: var(--primary-color);
}

.history-item {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.history-item-title {
    font-weight: bold;
    font-size: 16px;
}

.history-item-amount {
    font-weight: bold;
    color: var(--secondary-color);
}

.history-item-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 10px;
    background-color: #eee;
    color: #666;
}

.history-item-badge.completed {
    background-color: #e6f7e6;
    color: #28a745;
}

.history-item-badge.rejected {
    background-color: #f8d7da;
    color: #dc3545;
}

.history-item-details {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    color: var(--text-color-secondary);
    font-size: 14px;
}

.history-item-date {
    color: var(--text-color-secondary);
    font-size: 12px;
}

.history-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.history-stat-item {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    flex: 1;
    min-width: 100px;
    margin: 0 5px 10px 5px;
    text-align: center;
}

.history-stat-value {
    font-size: 18px;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 5px;
}

.history-stat-label {
    font-size: 12px;
    color: var(--text-color-secondary);
}

/* 加载更多按钮 */
.load-more-btn {
    display: block;
    width: 100%;
    padding: 10px;
    background-color: var(--secondary-color);
    border: none;
    border-radius: 5px;
    text-align: center;
    margin: 20px 0;
    color: var(--darker-bg-color);
    cursor: pointer;
}

.load-more-btn:hover {
    background-color: #ebebeb;
}

/* 空状态消息 */
.empty-message {
    text-align: center;
    padding: 20px;
    color: var(--secondary-color) !important; /* 修改为使用--secondary-color变量 */
} 
