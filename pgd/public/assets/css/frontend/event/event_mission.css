/* 任务页面专用CSS */

/* 任务页面专用样式 */
.mission-container {
    padding: 15px;
}

.mission-item {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.mission-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.mission-title {
    font-weight: bold;
    font-size: 16px;
}

.mission-reward {
    color: var(--secondary-color);
    font-weight: bold;
}

.mission-description {
    color: var(--text-color-secondary);
    margin-bottom: 15px;
    font-size: 14px;
}

.mission-progress {
    margin-bottom: 15px;
}

.progress-bar {
    height: 10px;
    background-color: #eee;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 5px;
}

.progress-fill {
    height: 100%;
    background-color: var(--secondary-color);
}

/* 通过data-progress属性设置进度条宽度 */
.progress-fill[data-progress="0"] { width: 0%; }
.progress-fill[data-progress="5"] { width: 5%; }
.progress-fill[data-progress="10"] { width: 10%; }
.progress-fill[data-progress="15"] { width: 15%; }
.progress-fill[data-progress="20"] { width: 20%; }
.progress-fill[data-progress="25"] { width: 25%; }
.progress-fill[data-progress="30"] { width: 30%; }
.progress-fill[data-progress="35"] { width: 35%; }
.progress-fill[data-progress="40"] { width: 40%; }
.progress-fill[data-progress="45"] { width: 45%; }
.progress-fill[data-progress="50"] { width: 50%; }
.progress-fill[data-progress="55"] { width: 55%; }
.progress-fill[data-progress="60"] { width: 60%; }
.progress-fill[data-progress="65"] { width: 65%; }
.progress-fill[data-progress="70"] { width: 70%; }
.progress-fill[data-progress="75"] { width: 75%; }
.progress-fill[data-progress="80"] { width: 80%; }
.progress-fill[data-progress="85"] { width: 85%; }
.progress-fill[data-progress="90"] { width: 90%; }
.progress-fill[data-progress="95"] { width: 95%; }
.progress-fill[data-progress="100"] { width: 100%; }

.progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--text-color-secondary);
}

.mission-button {
    background-color: var(--secondary-color);
    color: var(--darker-bg-color);
    border: none;
    border-radius: 5px;
    padding: 10px 15px;
    font-weight: bold;
    cursor: pointer;
    width: 100%;
}

.mission-button:hover {
    background-color: var(--secondary-color-dark);
}

.mission-button.disabled {
    background-color: var(--disabled-color, #888);
    cursor: not-allowed;
}

.mission-button.completed {
    background-color: var(--success-color, #4CAF50);
}
