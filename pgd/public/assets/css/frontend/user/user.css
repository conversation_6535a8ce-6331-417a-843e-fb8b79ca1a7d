/* 用户中心页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    background-color: var(--outer-bg-color) !important;
}

/* 确保容器样式与theme.css中定义一致 */
.container {
    background-color: var(--main-bg-color) !important;
    width: 100% !important;
    max-width: 516px !important;
    margin: 0 auto !important;
    min-height: 100vh !important;
    padding-bottom: 150px !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    /* 隐藏滚动条但保留滚动功能 */
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
}

/* 隐藏WebKit浏览器的滚动条 */
.container::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    display: none !important;
}

/* 响应式适配屏幕 */
@media (max-width: 516px) {
    .container {
        width: 100% !important;
    }
}

@media (min-width: 516px) {
    .container {
        max-width: 516px !important;
    }
}

/* 用户中心主要头部区域 */
.main-header {
    background-color: var(--darker-bg-color) !important;
    position: relative;
    padding: 20px 15px;
    overflow: hidden;
}

/* 添加波浪曲线装饰元素 */
.main-header::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 60px;
    background: rgba(255, 255, 255, 0.15);
    top: 20px;
    left: -10%;
    transform: rotate(-5deg) skewX(20deg);
    pointer-events: none;
    z-index: 1;
    border-radius: 100px / 30px;
}

.main-header::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 40px;
    background: rgba(255, 255, 255, 0.12);
    bottom: 30px;
    right: -10%;
    transform: rotate(3deg) skewX(-15deg);
    pointer-events: none;
    z-index: 1;
    border-radius: 100px / 20px;
}

/* 波浪和曲线元素 */
.wave, .curve {
    position: absolute;
    width: 100%;
    background-color: var(--main-bg-color) !important;
}

/* 波浪曲线元素 */
.main-header .wave {
    position: absolute;
    width: 140%;
    height: 100px;
    background: rgba(255, 255, 255, 0.15);
    pointer-events: none;
    border-radius: 100%;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.main-header .wave1 {
    top: -50px;
    left: -20%;
    height: 80px;
    transform: rotate(5deg);
    z-index: 2;
    opacity: 0.6;
}

.main-header .wave2 {
    bottom: -60px;
    right: -20%;
    height: 70px;
    transform: rotate(-3deg);
    z-index: 2;
    opacity: 0.5;
}

/* 弧形曲线元素 */
.main-header .curve {
    position: absolute;
    pointer-events: none;
    z-index: 2;
}

.main-header .curve1 {
    top: 10px;
    right: 20px;
    width: 200px;
    height: 150px;
    border-radius: 50% 50% 0 50%;
    border: 2px solid rgba(255, 255, 255, 0.15);
    transform: rotate(45deg);
    opacity: 0.5;
}

.main-header .curve2 {
    bottom: 20px;
    left: 30px;
    width: 180px;
    height: 120px;
    border-radius: 50% 50% 50% 0;
    border: 2px solid rgba(255, 255, 255, 0.15);
    transform: rotate(-30deg);
    opacity: 0.5;
}

/* 顶部按钮栏 */
.top-buttons {
    position: absolute;
    top: 10px;
    right: 15px;
    display: flex;
    gap: 15px;
    z-index: 10;
}

.top-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.top-button-icon {
    background-color: rgba(0, 0, 0, 0.3);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2px;
}

.top-button-icon i {
    color: var(--button-normal);
    font-size: 16px;
}

.top-button-text {
    font-size: 10px;
    color: var(--text-color);
    text-decoration: none;
}

/* Header */
.header {
    position: relative;
    padding: 15px 10px;
    display: flex;
    align-items: center;
    background-color: var(--darker-bg-color);
}

.back-btn {
    font-size: 24px;
    margin-right: 15px;
    cursor: pointer;
    color: var(--text-color);
}

.header-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--text-color);
}

/* User Info Card */
.user-info {
    background-color: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 20px;
    margin: 15px;
    display: flex;
    align-items: center;
}

.avatar {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: var(--secondary-color);
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    color: var(--darker-bg-color);
}

.user-details {
    flex: 1;
}

.username {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.user-id {
    font-size: 14px;
    color: rgba(255,255,255,0.7);
    margin-bottom: 10px;
}

.vip-badge {
    display: inline-block;
    background-color: var(--secondary-color);
    color: var(--darker-bg-color);
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: bold;
}

/* Font Awesome图标样式 */
.copy-id {
    width: 18px;
    height: 18px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
    cursor: pointer;
    vertical-align: middle;
    transition: transform 0.2s ease;
}

.copy-id:hover {
    transform: scale(1.1);
}

.copy-id i {
    color: var(--text-color);
    font-size: 16px;
}

.refresh-balance {
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
    cursor: pointer;
    vertical-align: middle;
}

.refresh-balance i {
    color: var(--button-normal);
    font-size: 16px;
}

/* Balance Card */
.balance-card {
    background-color: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
    margin: 0 15px 15px;
}

.balance-title {
    font-size: 14px;
    color: rgba(255,255,255,0.7);
    margin-bottom: 5px;
}

.balance-amount {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
}

.balance-actions {
    display: flex;
    justify-content: space-around;
}

.action-btn {
    text-align: center;
}

.action-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: var(--secondary-color);
    color: var(--darker-bg-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    font-size: 20px;
    cursor: pointer;
}

.action-name {
    font-size: 12px;
}

/* Menu Section */
.menu-section {
    background-color: rgba(255,255,255,0.1);
    border-radius: 10px;
    margin: 0 15px 15px;
    overflow: hidden;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    cursor: pointer;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-icon {
    width: 24px;
    height: 24px;
    margin-right: 15px;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-text {
    flex: 1;
    font-size: 15px;
}

.menu-arrow {
    font-size: 18px;
    color: var(--arrow-color);
}

/* 用户信息区域 */
.user-profile {
    padding: 15px 20px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 5;
}

.user-profile-content {
    display: flex;
    align-items: center;
    width: 100%;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    overflow: hidden;
    margin: 40px 15px 0px 0px;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 40px 0px 0px 0px;
}

.user-info-row {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.account-label, .id-label {
    color: var(--text-color);
    font-size: 14px;
}

.account-value, .id-value {
    color: var(--text-color);
    font-weight: bold;
    font-size: 16px;
}

.country-flag {
    width: 20px;
    height: 15px;
    margin-left: 15px;
    margin-right: 8px;
}

.balance-value {
    color: var(--secondary-color) !important;
}

.main-actions {
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
    margin-top: -10px;
    transform: scale(0.9);
    position: relative;
    z-index: 5;
}

.action-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 30px;
    border-radius: 12px;
    text-decoration: none;
    color: var(--text-color) !important;
    transition: background 0.3s;
    min-width: 100px; /* 增加最小宽度 */
    width: 40%; /* 设置百分比宽度 */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    cursor: pointer; /* 添加手型光标 */
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.action-button:hover {
    background: rgba(255, 255, 255, 0.15); /* 鼠标悬停时略亮的背景 */
}

/* Font Awesome提现和充值图标 */
.icon-withdraw, .icon-deposit {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.icon-withdraw i, .icon-deposit i {
    color: var(--text-color); /* 图标 */
    font-size: 24px;
}

.action-button span:not(.icon-withdraw):not(.icon-deposit) {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color) !important;
}

.withdraw-btn {
    background-color: var(--secondary-color) !important;
}

.deposit-btn {
    background-color: var(--secondary-color) !important;
    color: var(--darker-bg-color) !important;
}

/* VIP状态区域 */
.vip-status {
    background-color: var(--secondary-color) !important;
    margin: 0px 15px 0;
    border-radius: 12px;
    padding: 0;
    color: var(--text-color) !important;
    position: relative;
    cursor: pointer;
    margin-top: 20px;
}

.vip-header {
    padding: 15px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    position: relative;
}

.vip-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.vip-level {
    display: flex;
    align-items: center;
    gap: 8px;
}

.level-box {
    width: 48px;
    height: 32px;
    background-color: var(--secondary-color) !important;
    color: var(--text-color) !important;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.vip-level img {
    width: 32px;
    height: 32px;
}

.vip-title {
    font-size: 16px;
    font-weight: bold;
    color: var(--text-color) !important;
}

.vip-next {
    font-size: 14px;
    margin-right: 25px;
    color: var(--text-color) !important;
}

.arrow-right {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border-top: 2px solid var(--white-add);
    border-right: 2px solid var(--white-add);
    transform: translateY(-50%) rotate(45deg);
}

.vip-progress {
    padding: 10px 15px;
}

.progress-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    gap: 15px;
}

.progress-item:last-child {
    margin-bottom: 0;
}

.progress-label {
    display: flex;
    flex-direction: column;
    font-size: 13px;
    white-space: nowrap;
    min-width: 120px;
    color: var(--text-color) !important;
}

.progress-label span {
    color: var(--text-color) !important; /* 确保所有子元素文本也是褐色 */
}

.progress-value {
    font-size: 12px;
    color: var(--text-color) !important;
    opacity: 0.8;
    margin-top: 2px;
}

.progress-bar-container {
    flex: 1;
    margin-right: 15px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--darker-bg-color) !important;
    border-radius: 4px;
    overflow: hidden;
    border-color: var(--gray-blue);
}

.progress-fill {
    height: 80%;
    background-color: var(--white-add) !important;
    border-radius: 4px;
    width: var(--progress-width, 0%);
}

.user-menu-list {
    flex: 1;
    overflow: visible;
    margin-top: 20px;
}

.user-menu-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: var(--secondary-color);
    color: var(--text-color);
    text-decoration: none;
    position: relative;
    transition: background 0.3s;
    height: 55px;
    border-radius: 8px;
    margin-bottom: 6px;
    cursor: pointer;
}

.user-menu-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.user-menu-icon {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-menu-icon i {
    color: var(--text-color);
    font-size: 20px;
}

.user-menu-item span {
    font-size: 14px;
    flex: 1;
}

.user-menu-item::after {
    content: '';
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--white-add);
    border-top: 2px solid var(--white-add);
    transform: rotate(45deg);
    margin-right: 8px;
}

.menu-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.05);
    margin: 4px 0;
}

.language-switch {
    position: fixed;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 10px;
    z-index: 1000;
}

.lang-btn {
    padding: 5px 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.lang-btn[data-lang="${currentLang}"] {
    background: rgba(255, 255, 255, 0.3);
}

.menu-group {
    background-color: var(--secondary-color) !important;
    border-radius: 12px;
    margin: 0 15px 15px;
    padding: 4px;
    /* 确保菜单组内容不会被截断 */
    overflow: visible;
}

/* 最后一个菜单组需要与底部导航保持距离 */
.menu-group:last-child {
    margin-bottom: 150px;
}

/* 移除响应式调整滚动区域高度 */

/* 确保最后一个菜单项（登出按钮）有足够的底部空间 */
.menu-group:last-child .user-menu-item:last-child {
    margin-bottom: 80px;
}
