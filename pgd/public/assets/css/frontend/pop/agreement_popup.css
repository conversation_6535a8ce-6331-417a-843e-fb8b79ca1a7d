/* 用户协议弹窗样式 */

/* 弹窗容器 */
.agreement-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 999999; /* 确保在login_register_popup之上 */
    display: none;
    justify-content: center;
    align-items: center;
}

/* 当显示时的样式 */
.agreement-popup.active {
    display: flex !important;
}

/* 弹窗内容 */
.agreement-popup-content {
    position: relative;
    width: 90%;
    max-width: 400px;
    background-color: var(--secondary-color); /* 红色背景，与登录弹窗样式一致 */
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
}

/* 弹窗标题 */
.agreement-title {
    color: var(--text-color);
    text-align: center;
    padding: 15px 0;
    font-size: 18px;
    font-weight: bold;
    margin: 0;
}

/* 弹窗内容区域 */
.agreement-content {
    padding: 15px 20px;
    max-height: 350px;
    overflow-y: auto;
    color: var(--text-color);
    font-size: 14px;
    line-height: 1.5;
    scrollbar-width: none; /* 隐藏Firefox滚动条 */
    -ms-overflow-style: none; /* 隐藏IE和Edge滚动条 */
}

/* 隐藏Webkit浏览器滚动条 */
.agreement-content::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
}

/* 条款编号样式 */
.agreement-content strong {
    color: var(--text-color);
    font-weight: bold;
}

/* 接受按钮 */
.agreement-accept-btn {
    width: 100%;
    padding: 12px 0;
    margin: 0 auto 20px;
    width: 90%;
    background-color: var(--button-normal); /* 按钮 */
    color: var(--text-color);
    font-weight: bold;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.agreement-accept-btn:hover {
    background-color: #f5c400;
}

/* 关闭按钮 */
.agreement-close {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 30px;
    background-color: var(--text-color);
    border: 3px solid rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    color: #444;
} 