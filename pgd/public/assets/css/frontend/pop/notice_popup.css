/* 公告弹窗样式 */

/* 弹窗容器 */
.notice-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex !important; /* 使用!important确保显示 */
    justify-content: center;
    align-items: center;
    opacity: 1;
    transition: opacity 0.3s ease;
}

/* 隐藏弹窗 */
.notice-popup[style*="display: none"] {
    opacity: 0;
    pointer-events: none;
}

/* 弹窗内容 */
.notice-popup-content {
    position: relative;
    width: 90%;
    max-width: calc(516px * 0.9); /* container的最大宽度(516px)的90% */
    background-color: var(--main-bg-color); /* 使用与container相同的背景色 */
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    display: flex; /* 设置为左右布局 */
    flex-direction: column; /* 改为纵向布局 */
    transform: scale(1);
    transition: transform 0.3s ease;
}

/* 弹窗隐藏时内容区缩小 */
.notice-popup[style*="display: none"] .notice-popup-content {
    transform: scale(0.9);
}

/* 主要内容区容器 */
.notice-content-wrapper {
    display: flex;
    flex-direction: row;
    width: 100%;
}

/* 底部关闭按钮区域 */
.notice-popup-footer {
    width: 100%;
    padding: 15px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #66d2f9; /* 使用与container相同的背景色 */
}

/* 关闭按钮 - 位于底部居中 */
.notice-popup-close {
    width: 120px;
    height: 36px;
    background: transparent; /* 透明背景 */
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 18px;
    color: #fff;
    font-weight: bold;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.notice-popup-close:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.notice-popup-close:active {
    transform: scale(0.95);
}

/* 左侧选项栏 */
.notice-popup-sidebar {
    width: 25%;
    background-color: var(--darker-bg-color); /* 使用与header相同的背景色 */
    padding: 10px 0;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* 选项按钮样式 */
.tab-option {
    padding: 12px 10px;
    color: (--button-normal);
    cursor: pointer;
    font-size: 14px;
    text-align: center;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tab-option.active {
    color: var(--button-active);
    background-color: rgba(0, 0, 0, 0.2);
    border-left: 3px solid var(--secondary-color);
}

.tab-option i {
    margin-right: 5px;
    font-size: 16px;
}

/* 右侧内容区 - 图片叠加显示 */
.notice-popup-main {
    width: 75%;
    height: 360px; /* 适当减小高度以适应移动设备 */
    position: relative;
    overflow: hidden;
}

/* 内容标签页 - 使用绝对定位叠加在一起 */
.content-tab {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.content-tab.active {
    display: block;
    opacity: 1;
    z-index: 2; /* 确保活动标签在最上层 */
}

/* 图片容器占满整个右侧区域 */
.notice-popup-image {
    width: 100%;
    height: 100%;
    position: relative;
}

.notice-popup-image img {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 保持图片比例并填充容器 */
    display: block;
}

/* 响应式调整 */
@media (max-width: 480px) {
    .notice-popup-content {
        width: 95%;
    }
    
    .notice-popup-main {
        height: 300px;
    }
}

/* 添加一个隐藏类用于需要时 */
.hidden {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
} 
