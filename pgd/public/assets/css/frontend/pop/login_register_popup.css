/* 登录/注册弹窗样式 */

/* 弹窗容器 */
.auth-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 99999;  /* 最高层级 */
    display: flex; /* 保持 flex 布局 */
    justify-content: center;
    align-items: center;
    opacity: 0; /* 初始透明 */
    visibility: hidden; /* 初始不可见 */
    transform: scale(0.95); /* 初始稍微缩小 */
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0s linear 0.3s; /* 过渡效果 */
    pointer-events: none; /* 初始状态下不响应交互 */
}

/* 当显示时应用的样式 */
.auth-popup.active {
    opacity: 1; /* 变为不透明 */
    visibility: visible; /* 变为可见 */
    transform: scale(1); /* 恢复正常大小 */
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0s linear 0s; /* 过渡效果 */
    pointer-events: auto; /* 可见时响应交互 */
}

/* 弹窗内容 */
.auth-popup-content {
    position: relative;
    width: 90%;
    max-width: 380px;
    background-color: var(--darker-bg-color); /* 使用主题变量替换硬编码的红色背景 */
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

/* 关闭按钮 */
.auth-popup-close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    border: none;
}

.auth-popup-close i {
    color: #fff;
    font-size: 20px;
}

/* 标签栏 */
.auth-tabs {
    display: flex;
    background-color: rgba(0, 0, 0, 0.2); /* 半透明黑色背景，适应任何主题 */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-tab {
    flex: 1;
    padding: 15px 0;
    text-align: center;
    color: #fff;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    -webkit-tap-highlight-color: transparent; /* 禁用移动端点击高亮 */
}

.auth-tab.active {
    background-color: var(--darker-bg-color); /* 使用主题变量 */
    color: var(--secondary-color); /* 使用主题变量替换金色 */
}

.auth-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background-color: var(--secondary-color); /* 使用主题变量替换金色下划线 */
    border-radius: 3px 3px 0 0;
}

/* 表单容器 */
.auth-form-container {
    /* padding: 20px; */
    position: relative; /* Needed for absolute positioning of forms */
    /* Consider adding min-height if height jumps are an issue */
}

/* 表单部分 - Setup for fade transition */
.auth-form {
    /* display: none; */ /* Replaced by opacity/visibility */
    position: absolute; /* Allow forms to overlap */
    top: 0;
    left: 0;
    width: 100%;
    padding: 20px; /* Add padding here since container padding won't apply directly */
    box-sizing: border-box; /* Ensure padding is included in width */
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0s linear 0.3s; /* Fade out */
}

.auth-form.active {
    /* display: block; */ /* Replaced by opacity/visibility */
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s ease, visibility 0s linear 0s; /* Fade in */
    position: relative; /* Override absolute positioning for the active form to define container height */
}

/* 表单标题 */
.auth-form-title {
    font-size: 18px;
    margin-bottom: 5px;
    color: #fff;
    font-weight: bold;
}

.auth-form-subtitle {
    font-size: 12px;
    color: #ccc;
    margin-bottom: 20px;
}

/* 表单组 */
.form-group {
    margin-bottom: 15px;
    position: relative;
}

.form-group label {
    display: block;
    font-size: 14px;
    color: #fff;
    margin-bottom: 8px;
}

.form-group input {
    width: 100%;
    padding: 10px 15px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    color: #fff;
    font-size: 14px;
}

/* Placeholder 文本颜色 */
.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.5); /* 半透明白色，更易于阅读 */
}

.form-group input:focus {
    border-color: var(--gray-blue);
    outline: none;
}

/* 密码查看按钮 */
.toggle-password {
    position: absolute;
    right: 10px;
    top: 35px;
    color: #ccc;
    cursor: pointer;
    font-size: 18px;
}

/* 强度指示器 */
.strength-meter {
    display: flex;
    margin-top: 10px;
}

.strength-meter-bar {
    height: 5px;
    flex: 1;
    margin-right: 5px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.strength-meter-bar.active {
    background-color: var(--secondary-color); /* 使用主题变量替换金色 */
}

/* 国家代码选择器 */
.country-code-selector {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    overflow: hidden;
    min-width: 80px; /* 确保有足够的最小宽度 */
}

.country-flag {
    padding: 0 8px;
    display: flex;
    align-items: center;
    flex-shrink: 0; /* 防止国旗被压缩 */
}

.country-flag img {
    width: 24px;
    height: auto;
}

.country-code {
    font-weight: bold;
    color: #fff;
    padding-right: 10px;
    white-space: nowrap; /* 防止文本换行 */
    flex-grow: 1; /* 允许文本区域增长 */
    text-align: center; /* 文本居中 */
}

.country-code-selector select {
    background-color: transparent;
    border: none;
    color: #fff;
    padding: 10px 5px;
    width: 100%;
    cursor: pointer;
}

/* 复选框 */
.checkbox-container {
    display: flex;
    align-items: center;
    margin: 20px 0;
}

.checkbox-container input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin-right: 10px;
    accent-color: var(--secondary-color); /* 使用主题变量替换金色 */
}

.checkbox-container label {
    color: #fff;
    font-size: 14px;
}

.contract-link {
    color: var(--secondary-color); /* 使用主题变量替换金色 */
    text-decoration: none;
}
/* 添加忘记密码链接样式 */
.forgot-password {
    color: var(--secondary-color); /* 使用主题变量替换金色 */
    text-decoration: none;
    font-size: 14px;
    float: right;
    margin-top: 5px;
}

/* 添加切换链接样式 */
.auth-switch-link {
    text-align: center;
    margin-top: 20px;
}

.auth-switch-link a {
    color: var(--secondary-color); /* 使用主题变量替换金色 */
    text-decoration: none;
    font-size: 14px;
}

/* 提交按钮 */
.auth-submit-btn {
    width: 100%;
    padding: 12px 0;
    background-color: var(--secondary-color); /* 使用主题变量 */
    color: #fff; /* 修改为白色文字 */
    font-weight: bold;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.auth-submit-btn:hover {
    background-color: var(--button-active); /* 使用主题变量 */
    opacity: 0.9;
}

/* 第三方登录支持 */
.auth-support-text {
    text-align: center;
    margin: 20px 0;
    color: #ccc;
    font-size: 14px;
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 手机号输入框样式 */
.mobile-input-container {
    display: flex;
    align-items: stretch; /* 确保子元素高度一致 */
    gap: 10px; /* 使用gap代替margin，更现代的布局方式 */
}

.country-code-selector {
    width: auto; /* 自动宽度，根据内容调整 */
    min-width: 80px; /* 最小宽度确保显示完整 */
    max-width: 100px; /* 最大宽度限制 */
    flex-shrink: 0; /* 防止被压缩 */
}

.mobile-input {
    flex-grow: 1; /* 允许输入框占据剩余空间 */
    min-width: 0; /* 允许输入框在必要时缩小 */
}

/* 验证错误样式 */
.error-message {
    display: none;
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 5px;
    align-items: center;
}

.error-message img {
    width: 14px;
    height: 14px;
    margin-right: 5px;
}

.error-message::before {
    content: '\26A0'; /* Unicode警告符号 */
    display: inline-block;
    margin-right: 5px;
    font-size: 14px;
}

/* 隐藏图片，使用Unicode符号代替 */
.error-message img {
    display: none;
}

.form-group.has-error input {
    border-color: #ff4d4f;
}

.form-group.has-error .error-message {
    display: flex;
}

/* 响应式调整 */
@media (max-width: 480px) {
    .auth-popup-content {
        width: 95%;
    }
}
