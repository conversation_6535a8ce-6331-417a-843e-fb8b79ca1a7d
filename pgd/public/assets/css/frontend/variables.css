/**
 * 前端通用颜色变量定义
 * 这个文件定义了前端页面使用的所有颜色变量
 * 从原来的custom-variables.css移动到frontend目录下
 */

:root {
    /* 主色调 - 红色主题 */
    --main-bg-color: #eafaff;      /* 主要背景色 */
    --outer-bg-color: #99999950;     /* 外部背景色 */
    --darker-bg-color: #1282ae;    /* 更深的背景色（用于头部等，稍淡于主背景色） */
    
    /* 辅助颜色 */
    --secondary-color: #67d1f9;    /* 次要颜色 */
    --secondary-color-active: #2ca9d9;    /* 次要颜色 激活 */
    --text-color: #ffffff;         /* 文本颜色（白色） */
    --button-active: #2ca9d9;      /* 激活按钮颜色 */
    --button-normal: #67d1f9;      /* 普通按钮颜色 */
    --card-bg: rgba(0, 0, 0, 0.2);  /* 卡片背景色（半透明黑色） */

    /* 新增变量 */
    --gray-blue: #00000033;    /* 次要颜色 激活 */
    --jackpot-colot:#ffdb1e;
    --white-add:#ffffff;  /* 额外的白色 */
    --error-msg-color:#2ca9d9;

    /* 页面特有变量 */
    --primary-color: #efb810;      /* 主要颜色（金色，用于一些页面） */
    --button-bg: #67d1f9;          /* 按钮背景色 */
    --button-text: #ffffff;        /* 按钮文本色 */
    --card-bg-color: rgba(255, 255, 255, 0.1); /* 卡片背景色变体 */
    --completed-bg-color: rgba(76, 175, 80, 0.2); /* 完成状态背景色 */
    --pending-bg-color: rgba(255, 152, 0, 0.2); /* 待处理状态背景色 */

    --notice-content-color: #2b3082; /* 公告内容文字颜色 */
    --game-category-title-color: #2b3082; /* 游戏分类标题颜色 */
    --secondary-text-color: #2b3082; /* 次要文本颜色 */

    --arrow-color:#fff; /* 箭头颜色 */
    --border-color:#1282ae; /* 边框颜色 */

    --stat-label-color:rgba(255, 255, 255, 0.6);
    --empty-text-color:#67d1f9;
} 
