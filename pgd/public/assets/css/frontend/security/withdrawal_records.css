/* 提款记录页面样式 */
.records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 15px;
}

.filter-container {
    margin: 0;
    text-align: left;
}

.filter-dropdown {
    position: relative;
    display: inline-block;
}

.filter-btn {
    display: flex;
    align-items: center;
    background-color: var(--secondary-color);
    border-radius: 5px;
    padding: 8px 12px;
    cursor: pointer;
}

.filter-text {
    color: var(--text-color);
    font-size: 14px;
    margin-right: 5px;
}

.arrow {
    font-size: 10px;
    color: var(--arrow-color);
}

.filter-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: var(--secondary-color);
    border-radius: 0 0 5px 5px;
    box-shadow: 0 5px 10px var(--outer-bg-color);
    z-index: 10;
    display: none;
}

.filter-menu.show {
    display: block;
}

.filter-option {
    padding: 10px 12px;
    color: var(--text-color);
    font-size: 14px;
    cursor: pointer;
    border-bottom: 1px solid var(--card-bg);
}

.filter-option:last-child {
    border-bottom: none;
}

.filter-option:hover, .filter-option.active {
    background-color: var(--secondary-color);
}

.total-section {
    text-align: right;
    margin: 0;
}

.total-label {
    font-size: 12px;
    color: var(--secondary-color);
    margin-bottom: 5px;
}

.total-value {
    font-size: 16px;
    font-weight: 500;
    color: var(--secondary-color);
}

.withdrawal-records {
    padding: 0 15px;
}

.record-item {
    background-color: var(--secondary-color);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
}

.record-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.record-date {
    font-size: 14px;
    color: var(--secondary-color);
}

.record-amount {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
}

.record-status {
    text-align: right;
    font-size: 14px;
    font-weight: 500;
}

.status-pending {
    color: var(--primary-color);
}

.status-processing {
    color: var(--secondary-color-active);
}

.status-completed {
    color: var(--completed-bg-color);
}

.status-rejected, .status-failed {
    color: var(--error-msg-color);
}

.no-records-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
}

.empty-state {
    text-align: center;
}

.empty-icon {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-records-message {
    color: var(--secondary-color);
    font-size: 16px;
}

.loading-container {
    text-align: center;
    padding: 30px;
}

.loading-spinner {
    display: inline-block;
    width: 30px;
    height: 30px;
    border: 3px solid var(--card-bg-color);
    border-radius: 50%;
    border-top-color: var(--text-color);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-text {
    color: var(--secondary-color);
    font-size: 14px;
}

.error-message {
    color: var(--error-msg-color);
    text-align: center;
    padding: 20px;
    font-size: 14px;
}

/* 历史记录项样式 */
.history-item {
    background-color: var(--secondary-color);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
}

.history-item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.history-item-row:last-child {
    margin-bottom: 0;
}

.history-item-left {
    display: flex;
    align-items: center;
}

.channel-icon {
    width: 24px;
    height: 24px;
    margin-right: 10px;
}

.channel-name {
    font-size: 14px;
    color: var(--text-color);
}

.deposit-time {
    font-size: 12px;
    color: var(--stat-label-color);
}

.history-item-middle {
    flex: 1;
    margin: 0 10px;
}

.order-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.order-no {
    font-size: 12px;
    color: var(--stat-label-color);
    margin-right: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.history-item-right {
    text-align: right;
}

.deposit-amount {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
}

.deposit-status {
    font-size: 12px;
    font-weight: 500;
}
