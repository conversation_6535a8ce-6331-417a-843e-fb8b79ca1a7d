/* 添加PIX账户弹窗样式 */
.add-pix-account-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.popup-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
}

.popup-container {
    position: relative; /* 改为相对定位 */
    width: 90%;
    max-width: 400px;
    background-color: var(--darker-bg-color);
    border-radius: 10px;
    overflow: visible; /* 修改为visible以便显示外部的关闭按钮 */
    margin: 0 auto; /* 居中 */
}

.popup-header {
    padding: 20px;
    text-align: center;
}

.popup-title {
    margin: 0;
    color: var(--text-color);
    font-size: 20px;
    font-weight: bold;
}

.popup-content {
    padding: 20px 20px;
    text-align: center; /* 添加文本居中对齐 */
}

.form-group {
    margin-bottom: 15px;
    margin-left: auto;
    margin-right: auto;
    max-width: 100%; /* 限制宽度，使其在居中时看起来更好 */
}

.form-input {
    width: 90%;
    padding: 15px;
    border: none;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 16px;
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* 下拉选择框样式 */
.select-container {
    position: relative;
    margin-left: auto;
    margin-right: auto;
    max-width: 90%;
}

.select-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 90%;
    padding: 15px;
    border: 2px solid var(--gray-blue);
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 16px;
    cursor: pointer;
}

.select-arrow {
    font-size: 20px;
}

.select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: var(--secondary-color);
    border: 2px solid var(--gray-blue);
    border-top: none;
    border-radius: 0 0 5px 5px;
    z-index: 10;
}

.select-option {
    padding: 15px;
    color: var(--text-color);
    cursor: pointer;
}

.select-option:hover, .select-option.selected {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--button-active);
}

.pix-account-hint {
    margin: 20px auto;
    color: #fff;
    font-size: 14px;
    line-height: 1.5;
    text-align: center;
    max-width: 90%;
}

.popup-footer {
    padding: 20px;
    text-align: center;
}

.popup-button {
    width: 90%;
    padding: 15px;
    border: none;
    border-radius: 5px;
    background-color: var(--button-normal);
    color: var(--text-color);
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    margin: 0 auto;
    display: inline-block;
}

.popup-close {
    position: absolute;
    bottom: -60px; /* 调整位置，使其在弹窗下方 */
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3); /* 添加阴影使其更明显 */
}

.popup-close i {
    color: var(--secondary-color);
    font-size: 24px;
}
