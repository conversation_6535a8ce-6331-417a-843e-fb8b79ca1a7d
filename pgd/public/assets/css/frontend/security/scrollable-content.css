/* 滚动内容区域样式 */
.scrollable-content {
    overflow-y: auto;
    max-height: calc(100vh - 150px); /* 调整高度，留出顶部导航和底部空间 */
    padding-bottom: 20px; /* 底部添加一些空间 */
    /* 移除隐藏滚动条的样式 */
    scrollbar-width: thin; /* Firefox */
    -ms-overflow-style: auto; /* IE and Edge */
}

/* 自定义滚动条样式 */
.scrollable-content::-webkit-scrollbar {
    width: 5px; /* 滚动条宽度 */
    display: block; /* 显示滚动条 */
}

.scrollable-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1); /* 滚动条轨道背景 */
    border-radius: 10px;
}

.scrollable-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3); /* 滚动条滑块颜色 */
    border-radius: 10px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5); /* 鼠标悬停时滑块颜色 */
}

/* 确保内容区域有足够的高度触发滚动 */
#withdrawal_records-content {
    min-height: 100%;
    padding-bottom: 20px;
}

/* 确保记录列表有足够的空间 */
.withdrawal-records {
    padding: 0 15px;
    margin-bottom: 20px;
}
