/* Header & Tabs 样式 */
.header {
    position: relative;
    border-bottom: 3px solid #a01520;
}

.back-btn {
    position: absolute;
    left: 10px;
    top: 15px;
    font-size: 20px;
    color: var(--text-color);
    cursor: pointer;
    z-index: 10;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-title {
    text-align: center;
    padding: 15px 0;
    font-size: 16px;
    font-weight: bold;
}

.tab-nav {
    display: flex;
    position: relative;
    margin-bottom: 1px;
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.tab-nav::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.tab-nav::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #a01520;
}

.tab-item {
    flex: 1;
    min-width: 85px;
    text-align: center;
    padding: 10px 5px;
    font-size: 14px;
    cursor: pointer;
    position: relative;
    white-space: nowrap;
}

.tab-item.active {
    border-bottom: 3px solid var(--secondary-color);
    color: var(--secondary-color);
    z-index: 1;
}

/* 滚动内容区域样式 */
.scrollable-content {
    overflow-y: auto;
    max-height: calc(100vh - 120px);
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.scrollable-content::-webkit-scrollbar {
    display: none;
}

/* 响应式样式 */
@media (max-width: 480px) {
    .tab-item {
        min-width: 70px;
        font-size: 12px;
    }
}
