/**
 * 安全中心页面的样式
 */

/* 安全中心页面特定样式 */
.security-container {
    padding: 15px;
    margin-bottom: 70px;
}

/* 安全中心专用样式 */

/* Header样式 */
.header {
    position: relative;
    border-bottom: 3px solid var(--secondary-color) ;
}

.back-btn {
    position: absolute;
    left: 10px;
    top: 15px;
    font-size: 20px;
    color: var(--text-color);
    cursor: pointer;
    z-index: 10;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-title {
    text-align: center;
    padding: 15px 0;
    font-size: 16px;
    font-weight: bold;
}

/* 标签页导航样式 */
.tab-header {
    display: flex;
    align-items: center;
    background-color: var(--darker-bg-color);
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-nav-security {
    display: flex;
    flex: 1;
    justify-content: space-between;
    overflow-x: auto;
    scrollbar-width: none;
}

.tab-nav-security::-webkit-scrollbar {
    display: none;
}

.tab-option {
    padding: 10px 15px;
    font-size: 14px;
    color: var(--text-color);
    white-space: nowrap;
    text-align: center;
    cursor: pointer;
    position: relative;
}

.tab-option.active {
    /* color: var(--secondary-color); */
    font-weight: bold;
}

.tab-option.active::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 3px;
    /* background-color: var(--secondary-color); */
}

.tab-indicator {
    position: absolute;
    bottom: 0;
    height: 3px;
    background-color: var(--secondary-color);
    transition: all 0.3s ease;
}

/* 标签导航样式 */
.tab-nav {
    display: flex;
    position: relative;
    margin-bottom: 1px;
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.tab-nav::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.tab-nav::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    /* background-color: var(--secondary-color); */
}

.tab-item {
    flex: 1;
    min-width: 85px;
    text-align: center;
    padding: 10px 5px;
    font-size: 14px;
    cursor: pointer;
    position: relative;
    white-space: nowrap;
}

.tab-item.active {
    border-bottom: 3px solid var(--secondary-color);
    color: var(--text-color);
    z-index: 1;
}

@media (max-width: 480px) {
    .tab-item {
        min-width: 70px;
        font-size: 12px;
    }
}

/* 内容区域样式 */
.tab-content {
    display: none;
    padding: 20px;
}

.tab-content.active {
    display: block;
}

/* 滚动内容区域样式 - 基础样式，可被特定页面覆盖 */
/* 注意：这些样式可能被 scrollable-content.css 覆盖 */
.scrollable-content {
    overflow-y: auto;
    max-height: calc(100vh - 120px);
}

/* 余额信息样式 */
.balance-section {
    padding: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.balance-info {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
}

.balance-label {
    font-size: 14px;
    color: var(--secondary-color);
    margin-right: 10px;
}

.balance-value {
    font-size: 14px;
    color: var(--secondary-color);
}

/* 添加旋转动画 */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.bet-requirement {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: var(--secondary-color);
    margin-top: 10px;
}

.bet-amount {
    color: var(--secondary-color);
    font-weight: bold;
}

/* 要求标签和值的样式 */
.requirement-label {
    font-size: 14px;
    color: var(--secondary-color);
    margin-right: 10px;
}

.requirement-value {
    font-size: 14px;
    color: var(--secondary-color);
    font-weight: bold;
}

/* 提款表单样式 */
.withdrawal-form {
    margin-top: 20px;
    max-width: 100%;
    box-sizing: border-box;
}

.payment-method {
    background-color: var(--secondary-color);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    box-sizing: border-box;
    cursor: pointer;
    position: relative;
    transition: background-color 0.2s ease;
}

.payment-method:hover {
    background-color:var(--secondary-color);;
}

.payment-logo {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    background-color: #FFF;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.payment-info {
    flex: 1;
    min-width: 0; /* 防止溢出 */
}

.payment-name {
    font-size: 15px;
    font-weight: bold;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.payment-details {
    font-size: 12px;
    color: var(--secondary-color); 
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.payment-dropdown {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.5);
    flex-shrink: 0;
    margin-left: 10px;
}


/* 账户下拉菜单样式 */
.account-dropdown {
    position: absolute;
    top: calc(100% + 5px);
    left: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 100;
    overflow: hidden;
    max-height: 200px;
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.account-dropdown::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.account-option {
    padding: 12px 15px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.account-option:hover {
    background-color: rgba(140, 18, 26, 0.5);
}

.account-option-logo {
    width: 30px;
    height: 30px;
    margin-right: 10px;
    background-color: #FFF;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.account-option-info {
    flex: 1;
    min-width: 0;
}

.account-option-name {
    font-size: 14px;
    font-weight: bold;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.account-option-details {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.amount-input {
    position: relative;
    margin-bottom: 30px;
    width: 100%;
    box-sizing: border-box;
}

.amount-input-field {
    width: 100%;
    padding: 15px;
    background-color: var(--secondary-color);
    border: none;
    border-radius: 10px;
    color: var(--text-color);
    font-size: 16px;
    text-indent: 30px;
    box-sizing: border-box;
    -webkit-appearance: none; /* 防止iOS默认样式 */
    -moz-appearance: none;
    appearance: none;
}

/* 防止自动填充和记录值 */
.amount-input-field[data-independent="true"] {
    background-color: var(--secondary-color) !important; /* 防止自动填充改变背景色 */
}

/* 禁用自动填充的样式覆盖 */
.amount-input-field:-webkit-autofill,
.amount-input-field:-webkit-autofill:hover,
.amount-input-field:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 1000px rgba(0, 0, 0, 0.2) inset !important;
    -webkit-text-fill-color: var(--text-color) !important;
    transition: background-color 5000s ease-in-out 0s;
}

.amount-input-prefix {
    position: absolute;
    left: 15px;
    top: 15px;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    pointer-events: none; /* 确保不会干扰输入 */
}

/* 验证密码区域 - 优化后的样式 */
.pin-verification {
    margin-top: 30px;
    width: 100%;
    box-sizing: border-box;
}

.pin-label {
    font-size: 16px;
    color: var(--secondary-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.pin-icon {
    margin-left: auto;
    width: 20px;
    height: 20px;
    opacity: 0.7;
    cursor: pointer;
}

/* 移除与 pin-input.css 冲突的样式，使用通用组件样式 */

.submit-btn {
    width: 110px;
    padding: 15px;
    background-color: var(--button-normal);
    color: var(--text-color);
    font-size: 16px;
    font-weight: bold;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    margin: 0 auto;
    display: block;
}

/* 提款通知样式 */
.withdrawal-notice {
    margin-bottom: 20px;
    font-size: 14px;
    color: var(--secondary-color);
    line-height: 1.5;
}

.withdrawal-notice-label {
    font-size: 14px;
    color: var(--secondary-color);
    line-height: 1.5;
    margin: 0;
    padding: 0;
}

/* 提款记录页面 */
.date-selector {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 25px;
    display: inline-block;
    overflow: hidden;
    margin-bottom: 20px;
}

.date-option {
    display: inline-block;
    padding: 8px 20px;
    font-size: 14px;
    color: var(--secondary-color);
    cursor: pointer;
}

.date-option.active {
    background-color: var(--secondary-color);
    color: #000;
}

.total-withdrawal {
    float: right;
    text-align: right;
    margin-top: 10px;
    margin-bottom: 20px;
}

.total-label {
    font-size: 14px;
    color: var(--secondary-color);
}

.total-value {
    font-size: 16px;
    font-weight: bold;
    color: var(--secondary-color);
}


.loading-spinner {
    display: inline-block;
    width: 30px;
    height: 30px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--secondary-color);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-text {
    color: var(--secondary-color);
    font-size: 14px;
}

/* 无记录状态 */
.no-records-container {
    text-align: center;
    padding: 30px 0;
}

.no-records-icon {
    margin-bottom: 15px;
}

.no-records-icon img {
    width: 60px;
    height: 60px;
    opacity: 0.5;
}

.no-records-message {
    text-align: center;
    color: var(--secondary-color);
    font-size: 14px;
}

/* 记录列表 */
.withdrawal-records {
    margin-top: 10px;
}


.status-pending {
    background-color: #FFC107;
    color: #000;
}

.status-approved {
    background-color: #4CAF50;
    color: #FFF;
}

.status-rejected {
    background-color: #F44336;
    color: #FFF;
}

/* 账户管理页面 */
.account-section {
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    margin-bottom: 20px;
}

.account-title {
    font-size: 16px;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.account-counter {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
    margin-left: 5px;
}

.account-item {
    padding: 15px;
    background-color: rgba(140, 18, 26, 0.5);
    border-radius: 8px;
    margin-bottom: 15px;
    position: relative;
}

.account-item.selected {
    border: 2px solid var(--secondary-color);
}

.account-selected-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    background-color: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    font-weight: bold;
    font-size: 12px;
}

.account-type {
    font-size: 14px;
    font-weight: bold;
    color: var(--text-color);
    margin-bottom: 5px;
}

.account-number {
    font-size: 16px;
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.empty-records {
    text-align: center;
    padding: 50px 0;
    color: rgba(255, 255, 255, 0.5);
}

.empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    opacity: 0.5;
}

/* 设置提款密码页面样式 */
.setup-withdraw-password {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.setup-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    text-align: center;
}

.setup-description {
    font-size: 14px;
    color: #666;
    margin-bottom: 30px;
    text-align: center;
    line-height: 1.5;
}

.pin-setup-container {
    width: 100%;
    max-width: 320px;
}


/* 日期选择器下拉菜单样式 */
.date-filter-container {
    margin: 15px 0;
    text-align: center;
}

.date-range-select {
    width: 80%;
    max-width: 250px;
    padding: 8px 12px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: rgba(0, 0, 0, 0.2);
    color: #fff;
    font-size: 14px;
    text-align: center;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    cursor: pointer;
}

.date-range-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--gray-blue);
}

.date-range-select option {
    background-color: #1a1a1a;
    color: var(--text-color);
}

/* 筛选下拉菜单样式 */
.filter-dropdown {
    position: relative;
    max-width: none;
    width: auto;
    min-width: 100px;
}

.filter-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: auto;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.2);
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
}

.filter-btn .arrow {
    margin-left: 8px;
    transition: transform 0.3s;
    color: var(--secondary-color);
}

.filter-btn.active {
    background-color: rgba(0, 0, 0, 0.4);
    border-color: var(--secondary-color);
}

.filter-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: auto;
    min-width: 100%;
    white-space: nowrap;
    background-color: rgba(0, 0, 0, 0.8);
    border: 1px solid var(--secondary-color);
    border-radius: 10px;
    z-index: 100;
    margin-top: 5px;
    display: none;
    overflow: hidden;
}

.filter-option {
    padding: 10px 15px;
    cursor: pointer;
    color: var(--secondary-color);
    transition: background-color 0.2s;
    white-space: nowrap;
    min-width: max-content;
}

.filter-option:hover {
    background-color: rgba(0, 0, 0, 0.4);
}

.filter-option.active {
    background-color: rgba(0, 0, 0, 0.6);
    font-weight: bold;
}

/* 加载中动画 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--secondary-color);
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--secondary-color);
}

/* 历史记录列表样式 */
.history-list {
    margin-bottom: 16px;
    max-height: calc(100% - 100px); /* 减去筛选区域和底部边距的高度 */
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    -webkit-overflow-scrolling: touch; /* 在iOS上提供平滑滚动 */
}

/* 隐藏webkit浏览器的滚动条 */
.history-list::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
    background: transparent;
}

/* 确保在所有浏览器中都隐藏滚动条 */
.history-list::-webkit-scrollbar-thumb,
.history-list::-webkit-scrollbar-track {
    display: none;
    background: transparent;
}

.history-item {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
}

.history-item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.history-item-row:last-child {
    margin-bottom: 0;
}

.history-item-left {
    display: flex;
    align-items: center;
    flex: 0.8;
    min-width: 80px;
}

.history-item-middle {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1.5;
    position: relative;
    overflow: hidden;
    padding: 0 8px;
}

.history-item-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 0.7;
    min-width: 70px;
}

.channel-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    margin-right: 8px;
    object-fit: contain;
}

.channel-name {
    color: var(--text-color);
    font-size: 14px;
    text-transform: uppercase;
}

.copy-btn {
    flex-shrink: 0;
    width: 16px;
    height: 16px;
    background: none;
    border: none;
    cursor: pointer;
    position: relative;
    margin-left: 4px;
}

/* 使用CSS绘制复制图标 */
.copy-btn:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 10px;
    height: 10px;
    border: 1px solid var(--text-color);
    border-radius: 1px;
}

.copy-btn:after {
    content: "";
    position: absolute;
    top: 3px;
    left: 3px;
    width: 10px;
    height: 10px;
    border: 1px solid var(--secondary-color);
    border-radius: 1px;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: -1;
}

/* 复制成功提示 */
.copy-btn.copied:before {
    content: "✓";
    color: #0AC075;
    border: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.deposit-amount {
    color: var(--text-color);
    font-size: 16px;
    font-weight: bold;
}

.deposit-time {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
}

.order-container {
    display: flex;
    align-items: center;
    max-width: 100%;
    overflow: hidden;
}

.order-no {
    color: #fff;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    margin-right: 4px;
}

.deposit-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    text-align: center;
    min-width: 60px;
}

.status-success {
    /* background-color: rgba(10, 192, 117, 0.2); */
    color: #fff;
}

.status-pending {
    background-color: var(--secondary-color);
    color: var(--text-color)
}

.status-failed, .status-error {
    background-color: rgba(255, 59, 48, 0.2);
    color: var(--darker-bg-color);;
}

.status-unknown {
    background-color: rgba(158, 158, 158, 0.2);
    color: #9E9E9E;
}

.progress-bar {
    display: flex;
    align-items: center;
    width: 30%;
    height: 8px;
    background: var(--gray-blue) !important;
    border-radius: 4px;
    overflow: hidden;
    border-color: var(--gray-blue);
}

.progress-fill {
    height: 80%;
    background-color: var(--secondary-color) !important;
    border-radius: 4px;
    width: var(--progress-width, 0%);
}
