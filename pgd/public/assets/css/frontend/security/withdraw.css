/* 提款页面样式 */
.withdrawal-form {
    padding: 15px;
}

.payment-method {
    display: flex;
    align-items: center;
    background-color: #7d0d1a;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    position: relative;
    cursor: pointer;
}

.payment-logo {
    margin-right: 15px;
}

.payment-info {
    flex: 1;
}

.payment-name {
    font-size: 16px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 5px;
}

.payment-details {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

.payment-dropdown {
    font-size: 20px;
    color: #fff;
    margin-left: 10px;
}

.account-dropdown {
    background-color: #7d0d1a;
    border-radius: 0 0 10px 10px;
    margin-top: -15px;
    margin-bottom: 15px;
    padding: 10px 15px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.account-option {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
}

.account-option:last-child {
    border-bottom: none;
}

.account-option-logo {
    margin-right: 15px;
}

.account-option-info {
    flex: 1;
}

.account-option-name {
    font-size: 14px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 3px;
}

.account-option-details {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.amount-input {
    background-color: #7d0d1a;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
}

.amount-input-field {
    width: 100%;
    background: transparent;
    border: none;
    color: #fff;
    font-size: 24px;
    font-weight: 500;
    text-align: center;
}

.amount-input-field::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.withdrawal-notice {
    background-color: rgba(255, 204, 0, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
}

.withdrawal-notice p {
    color: #ffcc00;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
}


.pin-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    color: #fff;
    font-size: 16px;
}

.pin-icon {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.pin-grid {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.pin-box {
    width: 40px;
    height: 40px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #fff;
    position: relative;
}

.pin-box.filled::after {
    content: "•";
    font-size: 30px;
    color: #fff;
}

.pin-box.pin-box-highlight {
    border-color: #ffcc00;
    box-shadow: 0 0 5px rgba(255, 204, 0, 0.5);
}

.submit-btn {
    background-color: #ffcc00;
    color: #333;
    border-radius: 5px;
    padding: 15px;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
}

.submit-btn:active {
    background-color: #e6b800;
}
