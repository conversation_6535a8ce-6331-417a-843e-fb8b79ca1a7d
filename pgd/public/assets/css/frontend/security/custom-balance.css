/**
 * 自定义余额区域样式
 * 用于调整刷新按钮的大小和位置
 */

/* 刷新按钮样式 */
#refreshBalanceBtn {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    margin-left: 20px; /* 增加与余额值的距离 */
    border: 1px solid var(--secondary-color);
}

#refreshBalanceBtn:hover {
    background-color: rgba(0, 0, 0, 0.4);
}

#refreshBalanceBtn i {
    font-size: 16px;
    color: var(--white-add);
}

/* 旋转动画 */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

#refreshBalanceBtn.loading {
    animation: rotate 1s linear infinite;
}

/* 响应式调整 */
@media (max-width: 480px) {
    #refreshBalanceBtn {
        width: 28px;
        height: 28px;
        margin-left: 15px;
    }
    
    #refreshBalanceBtn i {
        font-size: 14px;
    }
}
