/* 账户管理页面样式 */
.account-management {
    padding: 15px;
}

/* 账户列表头部样式 */
.accounts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 0 10px;
    background-color: var(--secondary-color);
}

.accounts-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
}

.toggle-visibility {
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
}

.eye-icon {
    font-size: 18px;
    color: #fff;
    display: inline-block;
}

/* 账户项目样式 */
.account-item {
    background-color: var(--secondary-color);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 5px var(--card-bg);
    position: relative;
}

.account-item.default {
    border: 1px solid var(--card-bg);
    background-color: var(--secondary-color);
}

.account-item.default::before {
    content: "默认";
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--secondary-color);
    color: #333;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 0 10px 0 10px;
}

.account-content {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.account-icon {
    margin-right: 15px;
}

.account-type-icon {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.account-details {
    flex: 1;
}

.account-top-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.account-name {
    font-weight: 500;
    color: #fff;
}

.account-bottom-row {
    font-size: 14px;
    color: #fff;
    cursor: pointer;
    padding: 5px 0;
}

.account-number {
    word-break: break-all;
    transition: color 0.2s;
    color: var(--text-color);
}

.account-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.action-btn {
    background-color: var(--button-normal);
    color: #333;
    border: none;
    border-radius: 5px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.action-btn:hover {
    background-color: var(--button-active);
}

.action-btn.set-default {
    color: #0066cc;
}

.action-btn.delete {
    color: #cc0000;
}

/* 添加账户条目样式 */
.add-account-item {
    background-color: var(--secondary-color);
    border-radius: 0;
    padding: 15px;
    margin-top: 20px;
    width: 100%;
    max-width: 516px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    z-index: 10;
    box-sizing: border-box;
}

.add-account-action {
    display: flex;
    align-items: center;
}

.add-btn {
    color: var(--text-color);
    border: none;
    background: transparent;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: color 0.2s;
    text-decoration: none;
}

.add-btn:hover {
    color: var(--text-color);
    text-decoration: underline;
}

/* 为底部固定元素添加底部间距 */
#accounts-container {
    padding-bottom: 70px;
}

/* 取款密码弹窗样式 */
.withdraw-password-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.popup-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
}

.popup-container {
    position: relative;
    width: 90%;
    max-width: 400px;
    background-color: (--darker-bg-color);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    animation: zoomIn 0.3s ease-out;
    z-index: 1001;
}

@keyframes zoomIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.popup-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.popup-title {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #fff;
}

/* .popup-content {
    padding: 20px;
} */

.password-label {
    font-size: 16px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 15px;
}

.password-hint {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 15px;
    line-height: 1.4;
}

.forgot-password {
    text-align: right;
    margin-bottom: 10px;
}

.forgot-password a {
    color: var(--gray-blue);
    text-decoration: none;
    font-size: 14px;
}


/* Toast提示样式 */
.toast-container {
    position: fixed;
    top: 20px;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2000;
    pointer-events: none;
}

.toast-message {
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 10px 20px;
    border-radius: 5px;
    margin-bottom: 10px;
    max-width: 80%;
    text-align: center;
    transform: translateY(-20px);
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
}

.toast-message.show {
    transform: translateY(0);
    opacity: 1;
}
