/* 搜索页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: var(--darker-bg-color);
    color: var(--text-color);
    font-family: Arial, sans-serif;
}

.header {
    height: 50px;
    background-color: var(--darker-bg-color);
    display: flex;
    align-items: center;
    padding: 0 15px;
    position: relative;
}

.back-btn {
    font-size: 24px;
    margin-right: 15px;
}

.search-box {
    flex: 1;
    position: relative;
}

.search-input {
    width: 100%;
    height: 36px;
    background-color: rgba(255,255,255,0.1);
    border: none;
    border-radius: 18px;
    padding: 0 40px 0 15px;
    font-size: 14px;
    color: var(--text-color);
}

.search-input::placeholder {
    color: rgba(255,255,255,0.5);
}

.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: rgba(255,255,255,0.5);
}

.content {
    padding: 15px;
}

.section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
}

.history-tags {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.history-tag {
    background-color: rgba(255,255,255,0.1);
    padding: 8px 15px;
    border-radius: 15px;
    margin-right: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.tag-close {
    margin-left: 8px;
    font-size: 12px;
    color: rgba(255,255,255,0.5);
}

.popular-searches {
    margin-bottom: 20px;
}

.popular-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.popular-rank {
    width: 24px;
    height: 24px;
    border-radius: 12px;
    background-color: var(--secondary-color);
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
}

.popular-name {
    flex: 1;
    font-size: 14px;
}

.suggested-games {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -5px;
}

.game-item {
    width: calc(33.333% - 10px);
    margin: 5px;
    border-radius: 5px;
    overflow: hidden;
    background-color: rgba(255,255,255,0.1);
}

.game-img {
    width: 100%;
    padding-top: 100%;
    position: relative;
    background-color: #555;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-img::before {
    content: attr(data-name);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s;
}

.game-img:hover::before {
    opacity: 1;
}

.game-name {
    padding: 8px;
    font-size: 12px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 空状态样式 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
}

.empty-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin-top: 10px;
}
