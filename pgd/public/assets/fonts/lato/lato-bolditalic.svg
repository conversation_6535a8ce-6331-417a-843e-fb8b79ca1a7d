<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="latobold_italic" horiz-adv-x="1187" >
<font-face units-per-em="2048" ascent="1649" descent="-399" />
<missing-glyph horiz-adv-x="395" />
<glyph unicode="&#xfb01;" horiz-adv-x="1138" d="M454 1041h604l-126 -1041h-249l107 861h-349l-100 -819l-51 -193q-13 -50 -44.5 -77t-86.5 -27h-102l138 1115l-78 14q-26 5 -41 17t-15 36l12 114h144l4 34q11 96 50.5 177.5t107.5 141.5t164.5 94t222.5 34q40 0 84 -5t74 -15l-25 -126q-5 -24 -27 -27t-53 -3 q-94 0 -159 -17t-107.5 -51.5t-65 -86t-30.5 -121.5z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1165" d="M442 861l-101 -819l-51 -193q-13 -50 -44.5 -77t-86.5 -27h-102l138 1115l-78 14q-26 5 -41 17t-15 36l13 114h142l6 46q10 84 44 160.5t91.5 135t138.5 92.5t186 34q80 0 151.5 -6t143.5 -6h164l-185 -1497h-244l159 1317q-44 5 -88 9.5t-85 4.5q-101 0 -163.5 -61.5 t-74.5 -182.5l-5 -46h222l-21 -180h-214z" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="395" />
<glyph unicode="&#x09;" horiz-adv-x="395" />
<glyph unicode="&#xa0;" horiz-adv-x="395" />
<glyph unicode="!" horiz-adv-x="684" d="M580 1481l-72 -587q-11 -93 -31.5 -182.5t-47.5 -191.5h-165q0 20 -0.5 40.5t-0.5 39.5q0 77 3.5 148.5t13.5 145.5l71 587h229zM158 139q0 32 11.5 60.5t32 49t49.5 33t61 12.5q33 0 61 -12.5t49 -33t33.5 -49t12.5 -60.5q0 -33 -12.5 -61t-33.5 -48.5t-49 -32 t-61 -11.5t-61.5 11.5t-49 32t-32 48.5t-11.5 61z" />
<glyph unicode="&#x22;" horiz-adv-x="802" d="M452 1480l-37 -295l-41 -161q-11 -43 -32 -66.5t-64 -23.5q-36 0 -53 23.5t-22 66.5v161l37 295h212zM805 1480l-37 -295l-41 -161q-11 -43 -32 -66.5t-64 -23.5q-36 0 -53 23.5t-22 66.5v161l37 295h212z" />
<glyph unicode="#" d="M828 419l-130 -419h-112q-27 0 -44.5 19.5t-17.5 49.5q0 14 5 32l100 318h-216l-102 -330q-14 -48 -48.5 -68.5t-72.5 -20.5h-108l131 419h-122q-28 0 -43 12t-15 40q0 17 8 47l21 77h197l90 291h-221l31 100q11 39 38 57t81 18h118l106 336q12 39 44 61t70 22h112 l-132 -419h215l131 419h110q31 0 50 -17.5t19 -44.5q0 -13 -4 -24l-105 -333h210l-33 -102q-12 -38 -38 -55.5t-78 -17.5h-108l-91 -291h151q29 0 44 -11.5t15 -39.5q0 -17 -8 -47l-22 -78h-226zM459 595h216l90 291h-215z" />
<glyph unicode="$" d="M416 -9q-118 15 -217 64t-163 123l89 112q11 14 28.5 23.5t36.5 9.5q22 0 45 -16.5t53 -38t70.5 -43.5t98.5 -32l96 470q-65 19 -127 44t-111.5 65t-80 100t-30.5 148q0 89 35.5 172t103.5 148t164 106.5t217 46.5l27 130q5 25 25.5 44t47.5 19h90l-41 -201 q106 -17 179.5 -61t124.5 -100l-70 -89q-15 -20 -30 -30t-36 -10q-15 0 -35 10t-45 24.5t-57.5 29t-72.5 23.5l-89 -434q44 -14 88 -29.5t84.5 -35.5t75 -45t60 -59t40.5 -77t15 -98q0 -100 -37.5 -191t-107 -160.5t-169.5 -114t-225 -52.5l-31 -155q-5 -24 -26 -42.5 t-48 -18.5h-90zM866 444q0 35 -12.5 61.5t-35.5 47t-54.5 36t-68.5 28.5l-87 -428q125 14 191.5 82.5t66.5 172.5zM438 1074q0 -35 12.5 -61.5t34 -48t51 -38t65.5 -30.5l80 390q-65 -7 -111 -26.5t-75 -47.5t-43 -63.5t-14 -74.5z" />
<glyph unicode="%" horiz-adv-x="1518" d="M771 1178q0 -98 -31.5 -176.5t-83.5 -133.5t-118 -84t-135 -29q-61 0 -112 22t-87 63t-56 98.5t-20 129.5q0 97 28.5 176t78.5 134.5t117 85.5t143 30q61 0 112 -22t87.5 -63.5t56.5 -99.5t20 -131zM582 1177q0 86 -30.5 122.5t-76.5 36.5q-34 0 -63 -14.5t-50.5 -47 t-33.5 -83t-12 -123.5q0 -42 8 -71.5t22 -48t33.5 -27t43.5 -8.5q33 0 62 14t50.5 45.5t34 81.5t12.5 123zM1254 1441q14 14 33.5 26.5t50.5 12.5h178l-1194 -1443q-13 -16 -33 -26.5t-46 -10.5h-181zM1449 404q0 -98 -32 -176t-83 -133t-117.5 -84t-135.5 -29 q-61 0 -112 22.5t-87 63t-56 98t-20 128.5q0 98 28.5 177.5t78.5 135t117 85.5t142 30q61 0 112.5 -22.5t88 -64t56.5 -100t20 -131.5zM1261 403q0 86 -31 122.5t-77 36.5q-34 0 -63 -15t-50.5 -47t-33.5 -82.5t-12 -123.5q0 -83 30 -119t77 -36q34 0 63 14t50.5 45t34 81.5 t12.5 123.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1347" d="M745 1496q78 0 141.5 -23.5t108 -66.5t69.5 -103.5t25 -135.5l-146 -28q-14 -3 -24 -3q-37 0 -48 41q-7 29 -19 52.5t-30.5 41.5t-42.5 28.5t-56 10.5q-43 0 -77.5 -17.5t-58.5 -48t-36.5 -70.5t-12.5 -87q0 -49 17.5 -101.5t61.5 -109.5l304 -394q34 61 57.5 128 t37.5 138q5 24 16.5 38t37.5 14h171q-17 -136 -67 -259t-124 -226l243 -315h-212q-30 0 -50.5 3.5t-37.5 12.5t-31 22t-29 33l-53 69q-95 -74 -205 -115t-230 -41q-94 0 -168.5 26t-127 74.5t-80 117.5t-27.5 155q0 79 25 149.5t69.5 129t105.5 105.5t132 80 q-33 65 -48.5 128.5t-15.5 121.5q0 87 31 164.5t87 135t136.5 91.5t180.5 34zM293 390q0 -99 52.5 -155.5t152.5 -56.5q74 0 141.5 29.5t126.5 81.5l-303 400q-91 -59 -130.5 -135.5t-39.5 -163.5z" />
<glyph unicode="'" horiz-adv-x="451" d="M452 1480l-37 -295l-41 -161q-11 -43 -32 -66.5t-64 -23.5q-36 0 -54 23.5t-21 66.5v161l37 295h212z" />
<glyph unicode="(" horiz-adv-x="565" d="M309 443q0 -71 6 -149.5t17.5 -158t29 -154.5t41.5 -140q7 -18 8 -36q0 -23 -12 -36.5t-29 -23.5l-116 -64q-42 87 -71.5 181t-48.5 189.5t-28.5 190.5t-9.5 183q0 153 19 296.5t64 287t121 290.5t189 306l102 -65q11 -8 19 -18.5t8 -25.5q0 -10 -4 -23t-14 -29 q-70 -117 -124 -233t-91 -238t-56.5 -253.5t-19.5 -276.5z" />
<glyph unicode=")" horiz-adv-x="565" d="M291 842q0 71 -5.5 149.5t-17.5 158t-29.5 154.5t-41.5 140q-7 18 -8 36q0 23 12 36.5t29 23.5l116 65q41 -87 71 -181.5t49 -190t28.5 -190.5t9.5 -183q0 -153 -19 -296.5t-64 -287t-121 -290.5t-189 -305l-102 64q-11 8 -19 18.5t-8 25.5q0 10 4 23t14 29 q70 117 124 233t91 238t56.5 253.5t19.5 276.5z" />
<glyph unicode="*" horiz-adv-x="753" d="M356 860l24 189q2 20 6.5 38t12.5 35q-13 -13 -28.5 -22.5t-32.5 -21.5l-159 -96l-45 99l159 96q19 11 38 18.5t39 9.5q-39 6 -70 29l-136 96l69 100l137 -98l28 -22t23 -25q-3 10 -4 20.5t-1 21.5q0 8 0.5 16.5t1.5 16.5l23 190h117l-23 -188q-2 -20 -7.5 -38.5 t-13.5 -35.5q11 11 25.5 21t29.5 19l166 100l45 -99l-158 -95q-37 -23 -75 -29q17 -3 34 -10t33 -18l136 -97l-69 -98l-136 96q-14 11 -28 21.5t-23 22.5q5 -23 5 -43q0 -7 -0.5 -14t-1.5 -14l-25 -191h-116z" />
<glyph unicode="+" d="M777 1200l-52 -422h401l-22 -181h-401l-52 -424h-198l52 424h-399l23 181h398l53 422h197z" />
<glyph unicode="," horiz-adv-x="460" d="M41 156q0 29 10.5 53.5t30 43.5t47 30t59.5 11q39 0 68.5 -14t48.5 -38t28.5 -55t9.5 -66q0 -49 -15 -103.5t-43.5 -108.5t-70.5 -105.5t-97 -94.5l-44 40q-18 15 -19 37q0 15 17.5 35t39.5 46.5t43.5 60t30.5 75.5q-33 0 -59 12t-45.5 32.5t-29.5 48t-10 60.5z" />
<glyph unicode="-" horiz-adv-x="679" d="M121 720h492l-25 -204h-492z" />
<glyph unicode="." horiz-adv-x="460" d="M35 139q0 32 11.5 60.5t32.5 49t49.5 33t60.5 12.5q33 0 61.5 -12.5t49.5 -33t33 -49t12 -60.5q0 -33 -12 -61t-33 -48.5t-49.5 -32t-61.5 -11.5t-61 11.5t-49 32t-32.5 48.5t-11.5 61z" />
<glyph unicode="/" horiz-adv-x="741" d="M155 -7q-22 -45 -62.5 -68.5t-77.5 -23.5h-104l720 1503q20 42 55.5 64t79.5 22h102z" />
<glyph unicode="0" d="M713 1496q100 0 183.5 -38t143.5 -113t93 -188.5t33 -263.5q0 -230 -50 -401t-137 -284.5t-204 -168.5t-250 -55q-100 0 -183.5 37.5t-143.5 113t-93 188.5t-33 264q0 230 50 401t137 284t204 168.5t250 55.5zM558 186q74 0 138.5 43t112 130t75 219t27.5 310 q0 115 -19 192.5t-51 125t-74 67.5t-87 20q-74 0 -138.5 -43t-112 -130t-75 -218.5t-27.5 -309.5q0 -115 19 -192.5t51 -125t74 -68t87 -20.5z" />
<glyph unicode="1" d="M239 183h302l110 904l20 87l-249 -191q-14 -11 -27.5 -15t-25.5 -4q-20 0 -36.5 8.5t-22.5 19.5l-62 107l495 384h202l-161 -1300h271l-23 -183h-815z" />
<glyph unicode="2" d="M731 1496q93 0 169 -26.5t129.5 -74.5t83 -117t29.5 -152q0 -92 -28.5 -168.5t-77 -145.5t-114 -133.5t-140.5 -131.5l-382 -348q49 13 97 20.5t90 7.5h394q36 0 56.5 -19.5t20.5 -50.5q0 -2 -0.5 -6t-2 -18.5t-5.5 -46t-12 -86.5h-1009l9 81q3 24 16.5 51.5t38.5 49.5 l508 454q74 66 127.5 123t89.5 112.5t53.5 111t17.5 119.5q0 91 -53.5 139.5t-144.5 48.5q-46 0 -86.5 -12.5t-73 -36t-57.5 -56.5t-42 -72q-15 -38 -39 -54.5t-58 -16.5q-14 0 -33 4l-125 22q29 105 82 185.5t126 134.5t161 81t185 27z" />
<glyph unicode="3" d="M762 1496q94 0 168.5 -26t126 -72t78.5 -108.5t27 -135.5q0 -81 -19 -141.5t-54.5 -105t-88 -74t-118.5 -48.5q115 -39 171 -116t56 -186q0 -120 -46.5 -213t-125 -156.5t-180 -96.5t-211.5 -33q-104 0 -183 23t-136 71t-93 121.5t-55 174.5l114 44q31 12 58 12t44.5 -10 t25.5 -31q16 -51 37.5 -88.5t51 -62.5t68.5 -37.5t90 -12.5q71 0 126.5 25t93 65t57.5 92.5t20 107.5q0 42 -13 77.5t-49 60.5t-98.5 40t-160.5 15l23 173q95 1 162.5 19t109.5 51.5t61 81t19 109.5q0 91 -53 138t-143 47q-94 0 -161 -49t-100 -128q-15 -38 -39 -54.5 t-56 -16.5q-14 0 -33 4l-125 22q28 105 81 185.5t125 134.5t160.5 81t186.5 27z" />
<glyph unicode="4" d="M974 553h185l-19 -139q-2 -19 -17 -34t-40 -15h-133l-44 -365h-221l44 365h-614q-26 0 -49 15t-26 38l-9 124l818 940h237zM814 1056q8 59 27 133l-543 -636h455z" />
<glyph unicode="5" d="M1119 1376q-6 -51 -43.5 -83.5t-114.5 -32.5h-414l-95 -322q96 19 181 19q109 0 189.5 -29.5t135 -82t81.5 -124.5t27 -156q0 -86 -22 -162.5t-62 -141.5t-96.5 -116.5t-124.5 -87t-146.5 -54.5t-163.5 -19q-70 0 -130.5 13.5t-111.5 37t-93.5 55t-75.5 65.5l87 105 q14 17 31.5 24.5t35.5 7.5q26 0 50 -16.5t56.5 -35.5t77.5 -35.5t111 -16.5q79 0 140.5 27t103.5 74t64 109.5t22 135.5q0 102 -64 163t-194 61q-47 0 -101 -9t-119 -27l-146 43l211 716h726z" />
<glyph unicode="6" d="M735 922q78 0 148 -25.5t123.5 -73.5t84.5 -120t31 -165q0 -116 -44.5 -217t-122.5 -175.5t-185 -118t-233 -43.5q-106 0 -190.5 32.5t-142.5 91.5t-88 141.5t-30 180.5q0 67 15 130t44 126t73 127t103 131l414 477q20 25 57 42t80 17h218l-505 -540l-62 -67 q47 24 100 36.5t112 12.5zM317 421q0 -52 15.5 -96t46 -76t75 -49.5t102.5 -17.5q68 0 126 24t101 66t68 100.5t25 126.5q0 55 -17.5 99.5t-50 76t-77.5 47.5t-101 16q-68 0 -125.5 -24t-98.5 -66.5t-65 -100t-24 -126.5z" />
<glyph unicode="7" d="M1242 1481l-11 -97q-4 -34 -13 -63.5t-20 -48.5l-741 -1215q-14 -24 -41 -40.5t-57 -16.5h-203l718 1144q22 35 44.5 62.5t48.5 53.5h-716q-22 0 -36.5 14.5t-14.5 35.5l21 171h1021z" />
<glyph unicode="8" d="M530 -16q-105 0 -190.5 27t-146.5 77t-93.5 120.5t-32.5 156.5q0 153 78 257t239 150q-91 41 -136 112.5t-45 168.5q0 94 39 175.5t107.5 140.5t162.5 93t204 34q95 0 172 -27.5t131 -75.5t84 -113t30 -139q0 -133 -66.5 -228t-195.5 -144q116 -40 172.5 -118.5 t56.5 -187.5q0 -110 -43 -198.5t-119 -151t-180.5 -96t-227.5 -33.5zM553 175q71 0 125.5 21t92.5 58.5t57.5 89.5t19.5 115q0 52 -16.5 91.5t-47 66t-73.5 40t-97 13.5q-68 0 -121.5 -19t-92 -55t-58.5 -89t-20 -121q0 -46 14.5 -84.5t44 -66.5t72.5 -44t100 -16zM637 860 q75 0 124 24.5t78 63t41 85t12 90.5q0 38 -11.5 72t-35.5 59.5t-61.5 41t-89.5 15.5q-63 0 -111 -20.5t-80 -55t-48 -80.5t-16 -98q0 -38 10.5 -73.5t34 -63t61.5 -44t92 -16.5z" />
<glyph unicode="9" d="M557 593q-72 0 -137.5 23.5t-115.5 70t-79.5 115t-29.5 160.5q0 111 43 208t118.5 169.5t179 114.5t224.5 42q102 0 183 -31t137 -87.5t85 -134.5t29 -171q0 -78 -16 -146t-45 -131.5t-72 -125.5t-96 -126l-402 -487q-20 -24 -54.5 -40t-77.5 -16h-224l529 575 q20 23 39.5 44.5t36.5 43.5q-56 -35 -120.5 -52.5t-134.5 -17.5zM963 1070q0 52 -17 94t-47.5 71.5t-73 45t-93.5 15.5q-63 0 -116.5 -22.5t-92 -62.5t-60 -95.5t-21.5 -121.5q0 -104 57 -160.5t166 -56.5q69 0 124 22.5t94 62.5t59.5 93.5t20.5 114.5z" />
<glyph unicode=":" horiz-adv-x="460" d="M35 139q0 32 11.5 60.5t32.5 49t49.5 33t60.5 12.5q33 0 61.5 -12.5t49.5 -33t33 -49t12 -60.5q0 -33 -12 -61t-33 -48.5t-49.5 -32t-61.5 -11.5t-61 11.5t-49 32t-32.5 48.5t-11.5 61zM135 869q0 32 12 60.5t33 49t49 33t61 12.5t61 -12.5t49 -33t33 -49t12 -60.5 q0 -33 -12 -61t-33 -48.5t-49 -32t-61 -11.5t-61 11.5t-49 32t-33 48.5t-12 61z" />
<glyph unicode=";" horiz-adv-x="460" d="M36 156q0 29 10.5 53.5t30 43.5t47 30t59.5 11q39 0 68.5 -14t48 -38t28.5 -55t10 -66q0 -49 -15 -103.5t-43.5 -108.5t-70.5 -105.5t-97 -94.5l-44 40q-18 15 -19 37q0 15 17.5 35t39.5 46.5t43.5 60t30.5 75.5q-33 0 -59 12t-45.5 32.5t-29.5 48t-10 60.5zM137 869 q0 32 12 60.5t33 49t49 33t61 12.5t61 -12.5t49 -33t33 -49t12 -60.5q0 -33 -12 -61t-33 -48.5t-49 -32t-61 -11.5t-61 11.5t-49 32t-33 48.5t-12 61z" />
<glyph unicode="&#x3c;" horiz-adv-x="1092" d="M163 741l863 423l-22 -180q-2 -20 -14.5 -37t-38.5 -29l-395 -191q-26 -12 -53 -21t-57 -16q28 -7 53 -15.5t48 -21.5l349 -191q20 -11 28.5 -25t8.5 -31q0 -1 -0.5 -5.5t-3 -23t-7 -56t-13.5 -106.5l-758 424z" />
<glyph unicode="=" d="M152 600h904l-23 -181h-904zM197 958h902l-21 -182h-904z" />
<glyph unicode="&#x3e;" horiz-adv-x="1092" d="M979 639l-863 -423l22 180q2 20 14.5 37.5t37.5 29.5l396 190q26 12 53 21t56 16q-28 7 -53 16t-47 21l-349 192q-20 11 -28.5 24.5t-8.5 31.5q0 1 0.5 5t2.5 22.5t7 56.5t13 106l759 -424z" />
<glyph unicode="?" horiz-adv-x="798" d="M138 1346q37 31 78.5 58t89 48t101.5 32.5t114 11.5q80 0 142 -23t104 -63t63.5 -93.5t21.5 -115.5q0 -65 -13.5 -114.5t-36 -87.5t-51 -68t-60 -55t-62.5 -47t-58 -45.5t-47.5 -50t-29.5 -61.5l-41 -152h-168v168q0 51 25 91t63.5 74t82.5 65t82.5 66t63.5 76t25 95 q0 67 -38 105t-113 38q-52 0 -90.5 -13t-66 -29t-47 -29.5t-35.5 -13.5q-15 0 -27.5 7.5t-19.5 23.5zM187 139q0 32 12 60.5t32.5 49t49 33t61.5 12.5t61 -12.5t49 -33t33.5 -49t12.5 -60.5q0 -33 -12.5 -61t-33.5 -48.5t-49 -32t-61 -11.5t-61.5 11.5t-49 32t-32.5 48.5 t-12 61z" />
<glyph unicode="@" horiz-adv-x="1526" d="M1046 188q-65 0 -111 32.5t-57 105.5q-65 -74 -131.5 -105.5t-139.5 -31.5q-52 0 -90 18.5t-63.5 51.5t-38 77.5t-12.5 95.5q0 62 16.5 127.5t49 127t80 115t110 94t140 64t168.5 23.5q38 0 68.5 -3t57 -8.5t50 -14t47.5 -19.5l-138 -379q-17 -46 -23.5 -80.5t-6.5 -58.5 q0 -46 21.5 -63t55.5 -17q46 0 88 35t74 96t51.5 145t19.5 183q0 111 -31 196t-89 143t-140.5 87.5t-183.5 29.5q-90 0 -173.5 -26.5t-155.5 -75.5t-130.5 -118t-100.5 -155t-65 -186t-23 -211q0 -139 38 -245.5t108 -178.5t167 -109.5t215 -37.5q73 0 136 8t114.5 22 t92 31.5t70.5 34.5q14 8 26.5 11.5t22.5 3.5q22 0 33 -12.5t16 -29.5l18 -70q-106 -70 -242 -114t-305 -44q-156 0 -282.5 51t-216.5 144.5t-139.5 226.5t-49.5 299q0 128 31 247.5t86 224t133 190t172 146.5t201.5 95t221.5 34q124 0 232.5 -42.5t190 -122t128.5 -193 t47 -254.5q0 -129 -36 -240.5t-98.5 -193.5t-146 -129.5t-178.5 -47.5zM676 347q24 0 48.5 9t48.5 30.5t46.5 57.5t42.5 91l106 290q-28 6 -60 6q-61 0 -118 -31.5t-100.5 -83.5t-69.5 -119.5t-26 -137.5q0 -52 20.5 -82t61.5 -30z" />
<glyph unicode="A" horiz-adv-x="1313" d="M1267 0h-206q-35 0 -55.5 16.5t-25.5 43.5l-61 310h-552l-138 -310q-11 -24 -36.5 -42t-57.5 -18h-209l716 1481h271zM450 557h431l-101 507q-8 37 -16 78t-15 92q-19 -51 -38.5 -94.5t-35.5 -76.5z" />
<glyph unicode="B" horiz-adv-x="1228" d="M72 0l182 1481h475q123 0 210.5 -24t143.5 -68t82.5 -106.5t26.5 -139.5q0 -63 -17 -121t-53 -107.5t-91 -88.5t-132 -64q126 -32 187.5 -103t61.5 -181q0 -102 -38 -189.5t-109.5 -151.5t-174.5 -100.5t-233 -36.5h-521zM417 652l-55 -445h251q72 0 123 19.5t84.5 54.5 t49.5 85t16 109q0 81 -51.5 129t-167.5 48h-250zM439 836h197q137 0 211.5 64.5t74.5 197.5q0 93 -53 135.5t-164 42.5h-211z" />
<glyph unicode="C" horiz-adv-x="1235" d="M694 209q62 0 109 9.5t81.5 25t59 32.5t43 32.5t32.5 25t29 9.5q12 0 20 -5t13 -10l94 -116q-96 -111 -226.5 -169.5t-299.5 -58.5q-136 0 -242.5 48.5t-180 134.5t-112 203.5t-38.5 257.5q0 128 27.5 242t78.5 211t123 173.5t159 131t188 83t210 28.5q81 0 147.5 -15 t121 -41.5t97.5 -64t78 -83.5l-93 -110q-10 -12 -23.5 -21.5t-34.5 -9.5q-23 0 -43 18.5t-51.5 40.5t-81 40.5t-130.5 18.5q-106 0 -197.5 -43.5t-159 -124.5t-106 -195t-38.5 -257q0 -103 26 -185t72.5 -139t109.5 -87t138 -30z" />
<glyph unicode="D" horiz-adv-x="1406" d="M1386 840q0 -184 -59 -339t-165 -266t-251.5 -173t-317.5 -62h-521l182 1481h522q141 0 254.5 -48.5t192.5 -133.5t121 -202.5t42 -256.5zM1114 834q0 99 -24.5 180t-71 137.5t-114 87.5t-154.5 31h-255l-130 -1058h255q112 0 202.5 43t155.5 123.5t100.5 196t35.5 259.5 z" />
<glyph unicode="E" horiz-adv-x="1076" d="M1117 1481l-26 -212h-596l-52 -421h468l-25 -204h-468l-53 -432h598l-27 -212h-864l182 1481h863z" />
<glyph unicode="F" horiz-adv-x="1056" d="M1091 1269h-596l-56 -451h503l-28 -212h-500l-75 -606h-267l182 1481h863z" />
<glyph unicode="G" horiz-adv-x="1358" d="M724 196q83 0 146.5 15.5t121.5 44.5l35 283h-164q-24 0 -36.5 11.5t-12.5 30.5l17 157h461l-73 -595q-113 -82 -243 -120.5t-297 -38.5q-136 0 -247 49t-190.5 136.5t-122.5 208t-43 262.5q0 125 27.5 237t78 207.5t122 171.5t160 130t191 82.5t214.5 28.5q90 0 164 -16 t133.5 -43.5t106.5 -64.5t84 -78l-86 -110q-28 -36 -66 -36q-24 0 -45 14q-34 23 -65.5 42.5t-67.5 33.5t-78.5 22.5t-98.5 8.5q-111 0 -203 -44.5t-158.5 -126t-103.5 -195.5t-37 -255q0 -106 27 -190t76.5 -142.5t118.5 -89.5t154 -31z" />
<glyph unicode="H" horiz-adv-x="1425" d="M1229 0h-267l79 650h-622l-80 -650h-267l182 1481h267l-80 -642h624l80 642h267z" />
<glyph unicode="I" horiz-adv-x="593" d="M369 0h-268l182 1481h267z" />
<glyph unicode="J" horiz-adv-x="821" d="M689 513q-15 -123 -55 -221t-104 -166.5t-152 -105t-200 -36.5q-54 0 -103.5 6.5t-102.5 20.5l20 156q2 22 14.5 36t39.5 14q24 0 54.5 -7t78.5 -7q47 0 87 16.5t72 52.5t53.5 94t31.5 141l119 974h266z" />
<glyph unicode="K" horiz-adv-x="1320" d="M449 855h57q36 0 61 11t45 35l428 522q27 33 56.5 45.5t69.5 12.5h229l-525 -620q-24 -28 -45.5 -47t-46.5 -31q31 -12 53 -33.5t40 -56.5l376 -693h-233q-24 0 -40.5 3.5t-28 10t-19 16.5t-14.5 22l-305 552q-14 27 -36.5 38.5t-66.5 11.5h-80l-81 -654h-264l181 1482 h266z" />
<glyph unicode="L" horiz-adv-x="974" d="M364 220h538l-25 -220h-805l182 1481h265z" />
<glyph unicode="M" horiz-adv-x="1756" d="M845 592q9 -27 16 -54.5t15 -56.5q12 29 26 56.5t31 54.5l514 846q9 15 19 23.5t20.5 13t24 5.5t30.5 1h202l-182 -1481h-234l119 965q3 27 9 58.5t13 63.5l-509 -845q-17 -29 -43 -43.5t-57 -14.5h-37q-31 0 -54 14.5t-33 43.5l-305 842q-1 -32 -2.5 -61.5t-4.5 -53.5 l-117 -969h-234l182 1481h200q17 0 30.5 -1t23.5 -5.5t17 -13t14 -23.5l306 -846v0z" />
<glyph unicode="N" horiz-adv-x="1425" d="M393 1481q17 0 29 -1.5t21.5 -6.5t16.5 -13t15 -22l577 -1011q2 30 4.5 58t5.5 53l116 943h234l-183 -1481h-136q-31 0 -52.5 9.5t-35.5 36.5l-574 1011q-2 -25 -4 -48t-4 -43l-117 -966h-234l182 1481h139v0z" />
<glyph unicode="O" horiz-adv-x="1499" d="M1481 840q0 -123 -27.5 -234t-78 -206.5t-121.5 -172t-159.5 -131t-192 -83.5t-217.5 -29q-141 0 -254 50t-192 138t-121 208t-42 260q0 123 27 234t77.5 206.5t122 172t160 131.5t192 84t217.5 29q141 0 254 -50t192 -138t121 -208.5t42 -260.5zM1208 834q0 100 -25 182 t-72.5 140t-116 90t-154.5 32q-111 0 -201.5 -45t-155 -128t-100 -200t-35.5 -259q0 -100 24.5 -182t72 -140t115.5 -89t155 -31q112 0 203 45t155.5 127t99.5 198.5t35 259.5z" />
<glyph unicode="P" horiz-adv-x="1182" d="M408 524l-65 -524h-264l181 1481h451q126 0 216.5 -30.5t149 -84.5t86.5 -128.5t28 -163.5q0 -119 -37.5 -219.5t-112 -174t-187 -115t-261.5 -41.5h-185zM501 1276l-68 -545h187q78 0 135 23.5t95 66t56 101t18 129.5q0 50 -14.5 92t-44 71t-74.5 45.5t-106 16.5h-184z " />
<glyph unicode="Q" horiz-adv-x="1499" d="M1481 840q0 -118 -25 -225.5t-72.5 -200t-113.5 -169t-149 -131.5l296 -405h-217q-48 0 -84.5 12.5t-61.5 48.5l-170 238q-47 -11 -97 -17.5t-102 -6.5q-141 0 -254 50t-192 138t-121 208t-42 260q0 123 27 234t77.5 206.5t122 172t160 131.5t192 84t217.5 29 q141 0 254 -50t192 -138t121 -208.5t42 -260.5zM1208 834q0 100 -25 182t-72.5 140t-116 90t-154.5 32q-111 0 -201.5 -45t-155 -128t-100 -200t-35.5 -259q0 -100 24.5 -182t72 -140t115.5 -89t155 -31q112 0 203 45t155.5 127t99.5 198.5t35 259.5z" />
<glyph unicode="R" horiz-adv-x="1238" d="M415 584l-72 -584h-264l181 1481h421q128 0 219 -27.5t149.5 -76t86 -116t27.5 -148.5q0 -86 -24 -161t-69 -136.5t-110.5 -107.5t-148.5 -72q24 -14 43 -34.5t34 -49.5l264 -552h-239q-34 0 -56.5 14t-34.5 39l-217 481q-12 27 -30 38.5t-56 11.5h-104zM501 1276 l-63 -503h154q80 0 137.5 23t95 63.5t56 95t18.5 116.5q0 96 -59.5 150.5t-183.5 54.5h-155z" />
<glyph unicode="S" horiz-adv-x="1012" d="M957 1214q-14 -20 -28.5 -29.5t-34.5 -9.5t-42 16t-51.5 36t-70 36.5t-97.5 16.5q-56 0 -99.5 -16.5t-72.5 -45t-44 -67.5t-15 -84q0 -47 24.5 -78.5t63 -55t89 -42t102 -38.5t102 -47t89 -66t62.5 -93.5t24 -132.5q0 -109 -37 -205t-107.5 -168t-170 -114.5 t-224.5 -42.5q-65 0 -127.5 13.5t-117.5 39t-102 61.5t-82 80l94 127q11 14 28.5 23.5t34.5 9.5q16 0 32.5 -10.5t35 -25t41 -32.5t51 -33t64.5 -25t83 -10q116 0 181 65.5t65 181.5q0 51 -23.5 83.5t-62.5 55t-88 39.5t-100.5 36t-101 44t-88 64.5t-62 97t-23.5 141.5 q0 95 35 183t100.5 155t159.5 107.5t211 40.5q60 0 116 -12.5t104.5 -35t89 -53.5t69.5 -69z" />
<glyph unicode="T" horiz-adv-x="1122" d="M1221 1481l-28 -218h-406l-155 -1263h-264l154 1263h-406l26 218h1079z" />
<glyph unicode="U" horiz-adv-x="1370" d="M650 213q72 0 132 27t105 77t74 119.5t39 154.5l110 890h265l-108 -890q-16 -131 -69.5 -242.5t-137 -192.5t-194.5 -126.5t-243 -45.5q-120 0 -213.5 37t-157.5 105t-97 161.5t-33 206.5q0 46 6 97l108 890h266l-110 -889q-2 -19 -3 -37.5t-1 -36.5q0 -70 17 -126 t50 -96t82.5 -61.5t112.5 -21.5z" />
<glyph unicode="V" horiz-adv-x="1347" d="M124 1481h213q35 0 55.5 -17t25.5 -44l197 -922q10 -48 19.5 -97t17.5 -104q17 55 39 106t42 95l421 922q10 23 36.5 42t58.5 19h215l-731 -1481h-241z" />
<glyph unicode="W" horiz-adv-x="1979" d="M131 1481h209q35 0 56 -17t25 -44l130 -908q5 -33 6.5 -71t3.5 -80q27 84 54 151l369 908q9 23 35.5 42t58.5 19h62q35 0 56.5 -17t26.5 -44l147 -908q5 -33 9.5 -69.5t6.5 -76.5q11 40 22.5 77t24.5 69l351 908q9 24 36 42.5t60 18.5h210l-605 -1481h-241l-167 1026 q-3 19 -6.5 42.5t-5.5 48.5q-8 -26 -17 -48.5l-17 -42.5l-419 -1026h-239z" />
<glyph unicode="X" horiz-adv-x="1291" d="M497 791l-333 690h240q27 0 39 -8t20 -26l227 -511q10 22 25 43l314 465q25 37 61 37h277l-504 -678l372 -803h-238q-27 0 -41.5 14.5t-22.5 31.5l-257 593q-5 -8 -9.5 -16.5t-10.5 -16.5l-383 -560q-17 -26 -37.5 -36t-45.5 -10h-265z" />
<glyph unicode="Y" horiz-adv-x="1241" d="M763 577l-72 -577h-266l72 578l-385 903h236q35 0 53.5 -17t28.5 -44l192 -512q14 -37 22.5 -72t14.5 -69q16 34 35.5 68.5t42.5 72.5l319 512q14 24 37 42.5t57 18.5h220z" />
<glyph unicode="Z" horiz-adv-x="1160" d="M1233 1481l-11 -83q-2 -17 -10.5 -32.5t-19.5 -31.5l-818 -1122h686l-27 -212h-1040l10 81q2 17 9.5 33t19.5 30l820 1125h-657l25 212h1013z" />
<glyph unicode="[" horiz-adv-x="565" d="M4 -312l233 1894h392l-12 -96q-3 -25 -23.5 -43.5t-47.5 -18.5h-124l-196 -1580h127q23 0 39.5 -12.5t16.5 -35.5l-13 -108h-392z" />
<glyph unicode="\" horiz-adv-x="739" d="M106 1490h103q44 0 74.5 -22.5t40.5 -63.5l350 -1503h-103q-39 0 -73 23.5t-45 68.5z" />
<glyph unicode="]" horiz-adv-x="565" d="M-32 -312l13 94q3 27 23 44.5t47 17.5h126l195 1580h-126q-26 0 -41.5 16t-15.5 39l13 103h392l-234 -1894h-392z" />
<glyph unicode="^" d="M1108 796h-185q-24 0 -39 12t-25 30l-162 296q-15 29 -28 54.5t-22 51.5q-8 -27 -19.5 -53t-26.5 -53l-159 -296q-9 -17 -25 -29.5t-42 -12.5h-195l382 685h164z" />
<glyph unicode="_" horiz-adv-x="743" d="M664 -141l-20 -159h-743l19 159h744z" />
<glyph unicode="`" horiz-adv-x="605" d="M266 1496q39 0 56.5 -13.5t28.5 -39.5l103 -249h-140q-27 0 -43.5 7t-30.5 28l-203 267h229z" />
<glyph unicode="a" horiz-adv-x="1059" d="M879 0h-134q-46 0 -64 21.5t-18 54.5l8 143q-70 -109 -157 -171.5t-191 -62.5q-59 0 -111 23.5t-90.5 70t-60.5 116.5t-22 164t23.5 183.5t67 167.5t105 144t138 113t165 73t186.5 26q71 0 142 -10t140 -37zM422 191q42 0 82.5 29t76 79.5t66.5 119t53 147.5l40 318 q-28 3 -51 3q-85 0 -157.5 -39t-126 -105t-83.5 -153.5t-30 -185.5q0 -111 35 -162t95 -51z" />
<glyph unicode="b" horiz-adv-x="1101" d="M61 0l186 1522h247l-83 -679q74 103 164.5 163.5t192.5 60.5q66 0 119.5 -24t91.5 -72t58.5 -122t20.5 -173q0 -89 -18.5 -175t-52.5 -163t-80.5 -141.5t-104 -111.5t-122.5 -73.5t-134 -26.5q-81 0 -144 32t-106 87l-8 -34q-8 -37 -27.5 -53.5t-61.5 -16.5h-138z M670 868q-42 0 -85 -28.5t-83.5 -78.5t-75 -118.5t-58.5 -145.5l-31 -251q35 -38 80 -53.5t89 -15.5q68 0 123 42t94.5 109.5t61 152t21.5 169.5q0 111 -35.5 164.5t-100.5 53.5z" />
<glyph unicode="c" horiz-adv-x="891" d="M858 186q-52 -57 -99.5 -96t-97 -62.5t-103.5 -33t-119 -9.5q-91 0 -163 31t-122.5 88.5t-76.5 138.5t-26 180q0 128 41 244t115 205t175 141.5t221 52.5q104 0 177.5 -37t130.5 -110l-81 -97q-9 -9 -20.5 -16.5t-26.5 -7.5q-17 0 -31.5 10.5t-33.5 23t-47 23t-73 10.5 q-58 0 -111.5 -31.5t-94 -90t-64.5 -140.5t-24 -181q0 -116 51.5 -179t138.5 -63q58 0 96.5 17t65 37t47.5 37t46 17q12 0 23.5 -7t21.5 -17l64 -78v0z" />
<glyph unicode="d" horiz-adv-x="1103" d="M779 0q-46 0 -64.5 21.5t-18.5 54.5l9 153q-76 -113 -170.5 -178.5t-200.5 -65.5q-66 0 -119.5 24t-91.5 72t-58.5 121.5t-20.5 174.5q0 89 18.5 174.5t52 162.5t80.5 141.5t104 111.5t122 73.5t135 26.5q71 0 128 -25.5t100 -69.5l69 550h248l-188 -1522h-134zM432 183 q41 0 84 28.5t83 78t74.5 116.5t58.5 145l32 255q-35 38 -80 53t-88 15q-68 0 -123 -41.5t-94.5 -109.5t-61 -152.5t-21.5 -169.5q0 -111 35.5 -164.5t100.5 -53.5z" />
<glyph unicode="e" horiz-adv-x="968" d="M940 807q0 -71 -28 -129t-101 -104.5t-197.5 -79.5t-316.5 -53v-13q0 -248 210 -248q45 0 80 9t61 21t47 27.5t39 28t35 21t38 8.5q12 0 24.5 -5.5t20.5 -17.5l64 -77q-54 -54 -106 -94.5t-106.5 -66t-115 -37.5t-131.5 -12q-92 0 -167 31t-128 88t-82.5 137.5 t-29.5 179.5q0 83 18 163t52 151.5t83 132t111 104.5t136 68.5t159 24.5q83 0 145 -23.5t103.5 -61t62 -83.5t20.5 -90zM595 889q-51 0 -95 -21t-79.5 -59t-61.5 -90.5t-42 -115.5q127 17 204.5 38t120 45.5t56.5 52t14 59.5q0 15 -7 31.5t-20.5 29t-35.5 21.5t-54 9z" />
<glyph unicode="f" horiz-adv-x="651" d="M442 861l-99 -818l-52 -193q-14 -50 -44.5 -76.5t-85.5 -26.5h-102l135 1114l-81 15q-23 4 -37.5 16.5t-14.5 36.5q0 1 0.5 4t2 13.5t4 32.5t7.5 62h141l9 78q11 90 47.5 162t92.5 122t128.5 76.5t156.5 26.5q66 0 121 -19l-22 -131q-2 -11 -10 -18t-19 -10t-25 -4 t-28 -1q-41 0 -75.5 -11.5t-60.5 -36.5t-43 -65.5t-24 -98.5l-8 -70h235l-23 -180h-226z" />
<glyph unicode="g" horiz-adv-x="993" d="M881 746q0 -47 -10.5 -84t-25.5 -69t-32 -60t-32.5 -57.5t-25.5 -61.5t-10 -71q0 -42 17.5 -77.5t38 -72t38 -78.5t17.5 -98q0 -78 -35 -148t-100.5 -122.5t-159.5 -83.5t-213 -31q-97 0 -174.5 20t-132 56t-84 84.5t-29.5 107.5q0 87 44 146t114 95.5t156.5 52.5 t172.5 16q42 0 82 -3.5t75 -9.5q-4 16 -6.5 32.5t-2.5 34.5q0 59 33 131q-39 -27 -88 -41t-119 -14q-59 0 -113 19.5t-95 59.5t-65 100t-24 139q0 77 29 150.5t85 131t139 92.5t193 35q58 0 110.5 -14t96.5 -40h294l-11 -82q-4 -25 -16 -42.5t-34 -23.5l-108 -28 q5 -20 8 -43t3 -48zM461 493q46 0 81 21.5t59 56t36 78t12 88.5q0 77 -35.5 118t-104.5 41q-46 0 -81 -20t-58.5 -53t-35 -76.5t-11.5 -89.5q0 -79 35 -121.5t103 -42.5zM626 -4q0 14 -1.5 28t-3.5 26q-55 9 -102 13.5t-88 4.5q-70 0 -119.5 -11.5t-81.5 -30t-47 -43 t-15 -52.5q0 -61 49 -93t152 -32q57 0 104 14.5t81 40t53 60.5t19 75z" />
<glyph unicode="h" horiz-adv-x="1111" d="M61 0l186 1522h252l-79 -651q77 99 166.5 147.5t182.5 48.5q59 0 107.5 -21t82 -61.5t52 -99.5t18.5 -135q0 -19 -1.5 -39.5t-3.5 -41.5l-79 -669h-253l79 669q2 18 3.5 34.5t1.5 30.5q0 68 -25.5 97.5t-77.5 29.5q-39 0 -81 -22t-81 -62.5t-73 -97.5t-57 -128l-67 -551 h-253z" />
<glyph unicode="i" horiz-adv-x="527" d="M452 1051l-126 -1051h-250l126 1051h250zM515 1357q0 -33 -14 -61.5t-36 -50t-50.5 -34.5t-59.5 -13q-30 0 -58 13t-49.5 34.5t-34 50t-12.5 61.5t13 62.5t34.5 51.5t50 35t57.5 13q31 0 59.5 -13t50.5 -34.5t35.5 -51t13.5 -63.5z" />
<glyph unicode="j" horiz-adv-x="518" d="M452 1051l-134 -1087q-8 -69 -34 -129t-69.5 -105.5t-105.5 -72t-143 -26.5q-40 0 -69 5t-58 15l25 136q6 20 19.5 26t47.5 6q35 0 59 7.5t40.5 24.5t26 44.5t14.5 68.5l133 1087h248zM507 1357q0 -33 -14 -61.5t-36 -50t-50.5 -34.5t-59.5 -13q-30 0 -58 13t-49.5 34.5 t-34 50t-12.5 61.5t13 62.5t34.5 51.5t49.5 35t58 13q31 0 59.5 -13t50.5 -34.5t35.5 -51t13.5 -63.5z" />
<glyph unicode="k" horiz-adv-x="1043" d="M500 1522l-107 -872h26q24 0 38.5 7.5t30.5 26.5l279 323q18 20 37.5 32t49.5 12h225l-346 -391q-19 -22 -38.5 -39.5t-41.5 -30.5q19 -14 32 -35t26 -45l260 -510h-219q-28 0 -46 10t-30 35l-204 398q-12 24 -25 31t-41 7h-34l-59 -481h-250l187 1522h250z" />
<glyph unicode="l" horiz-adv-x="510" d="M67 0l184 1522h249l-185 -1522h-248z" />
<glyph unicode="m" horiz-adv-x="1618" d="M53 0l128 1052h128q39 0 61.5 -18.5t22.5 -58.5v-3t-0.5 -14l-1.5 -36t-3 -68q71 108 154 160.5t173 52.5q97 0 153 -64.5t65 -186.5q74 129 163.5 190t194.5 61q118 0 182 -79.5t64 -228.5q0 -20 -1 -43.5t-4 -46.5l-79 -669h-250q20 174 34.5 295.5t24 203t14.5 127.5 t7.5 71t3 33.5t0.5 13.5q0 61 -20.5 88.5t-66.5 27.5q-39 0 -76 -20.5t-70 -59.5t-61 -95t-50 -127l-65 -558h-249l79 669q2 22 3.5 40.5t1.5 34.5q0 61 -20 88.5t-66 27.5q-40 0 -78 -21.5t-71.5 -62t-61.5 -98t-49 -129.5l-66 -549h-248z" />
<glyph unicode="n" horiz-adv-x="1102" d="M53 0l126 1052h129q40 0 62 -19.5t22 -62.5l-7 -136q80 118 177 175.5t201 57.5q58 0 105.5 -21t81.5 -61.5t52.5 -99.5t18.5 -135q0 -19 -1.5 -39.5t-3.5 -41.5l-79 -669h-253q20 173 34.5 293.5t24 200t14.5 125.5t7.5 69.5t3 31t0.5 12.5q0 68 -24.5 98t-76.5 30 q-42 0 -87 -24.5t-86 -69.5t-75 -108.5t-55 -141.5l-58 -516h-253z" />
<glyph unicode="o" horiz-adv-x="1044" d="M471 179q63 0 114.5 36.5t88 97.5t56 142t19.5 170q0 128 -43.5 188t-132.5 60q-63 0 -114 -35.5t-87 -97t-55.5 -142.5t-19.5 -171q0 -128 43 -188t131 -60zM449 -15q-87 0 -161.5 29t-128.5 85.5t-84.5 138t-30.5 187.5q0 136 43.5 254t119 204t175.5 135t214 49 q87 0 161 -29t128.5 -85.5t85 -138.5t30.5 -187q0 -135 -44 -252.5t-119 -204t-175 -136t-214 -49.5z" />
<glyph unicode="p" horiz-adv-x="1085" d="M11 -343l170 1395h128q35 0 59.5 -15t24.5 -49v-3t-1 -19t-2 -50t-4 -97q76 114 171.5 181t202.5 67q66 0 119.5 -24t91.5 -72t58.5 -122t20.5 -173q0 -89 -18.5 -175t-52.5 -163t-80.5 -141.5t-104 -111.5t-122.5 -73.5t-134 -26.5q-71 0 -129 25t-100 70l-52 -423h-246 zM662 868q-42 0 -85 -28t-83 -77.5t-74.5 -117t-59.5 -145.5l-30 -255q35 -37 79.5 -52.5t88.5 -15.5q68 0 123 42t94.5 109.5t61 152t21.5 169.5q0 111 -35.5 164.5t-100.5 53.5z" />
<glyph unicode="q" horiz-adv-x="1059" d="M836 -343h-164q-22 0 -36.5 7t-23.5 18.5t-13 25.5t-4 29q0 1 0.5 4.5t2 17t4.5 41t9 77t16 126t24 187.5q-68 -96 -149.5 -150.5t-178.5 -54.5q-59 0 -111 23.5t-90.5 70t-60.5 116.5t-22 164t23.5 183.5t67 167.5t105 144t138 113t165 73t186.5 26q71 0 142 -10 t140 -37zM422 191q41 0 81.5 28.5t76 78t66 117t52.5 145.5l42 325q-14 1 -26.5 1.5t-24.5 0.5q-85 0 -157.5 -39t-126 -105t-83.5 -153.5t-30 -185.5q0 -111 35 -162t95 -51z" />
<glyph unicode="r" horiz-adv-x="744" d="M53 0l128 1052h128q37 0 60.5 -16.5t23.5 -52.5v-3q0 -4 -1 -21.5t-2 -55t-4 -104.5q65 124 141.5 191.5t159.5 67.5q42 0 85 -19l-46 -241q-51 22 -97 21q-91 0 -160 -87t-113 -272l-55 -460h-248z" />
<glyph unicode="s" horiz-adv-x="803" d="M751 853q-11 -16 -21.5 -23t-27.5 -7t-35.5 10.5t-42.5 23t-55 23t-75 10.5q-81 0 -124 -36.5t-43 -90.5q0 -32 17.5 -54t46.5 -38.5t66 -30t75.5 -29.5t75.5 -36t65.5 -48.5t46.5 -69t18 -97.5q0 -76 -30 -144.5t-86.5 -120t-136 -81.5t-178.5 -30q-50 0 -96.5 9.5 t-87.5 27t-74.5 41t-56.5 51.5l61 96q11 17 27 26.5t38 9.5t39 -13.5t40.5 -29.5t57 -29t85.5 -13q42 0 74 11t54 30.5t33.5 45t11.5 54.5q0 35 -17.5 58t-46.5 40t-66 29.5t-75.5 27t-74.5 33t-65.5 46.5t-47 68.5t-17.5 98.5q0 70 27 136t78.5 117t127 81.5t172.5 30.5 q100 0 177.5 -34.5t130.5 -87.5z" />
<glyph unicode="t" horiz-adv-x="726" d="M151 214v17t1.5 29t5 50t9.5 80l58 471h-99q-17 0 -28 11t-11 32q0 1 0.5 4t1.5 14t3.5 32.5t6.5 60.5l160 27l84 287q6 20 21 31.5t36 11.5h136l-41 -331h254l-22 -180h-255l-56 -458q-5 -42 -7.5 -66.5t-4.5 -38t-2.5 -19.5t-0.5 -8q0 -39 19 -61.5t54 -22.5q22 0 37 6 t26.5 12t20 11.5t16.5 5.5q11 0 18 -5.5t12 -16.5l54 -123q-56 -46 -124.5 -69.5t-138.5 -23.5q-112 0 -176.5 60t-67.5 170z" />
<glyph unicode="u" horiz-adv-x="1100" d="M419 1051q-20 -173 -34.5 -293t-24 -200t-14.5 -126t-7.5 -69.5t-3 -31.5t-0.5 -14q0 -68 25 -97t76 -29q40 0 82.5 22.5t82 63t73.5 99t57 130.5l66 545h253l-127 -1051h-128q-17 0 -33.5 4.5t-29 14t-20 23.5t-7.5 34v4l6 128q-80 -113 -175 -168t-196 -55 q-58 0 -106 21t-81.5 60.5t-52 98.5t-18.5 135q0 20 1 40.5t4 43.5l79 667h253z" />
<glyph unicode="v" horiz-adv-x="1017" d="M71 1051h207q27 0 42.5 -13.5t19.5 -33.5l111 -562q10 -50 14 -99.5t7 -98.5q14 49 31 97.5t41 100.5l251 562q9 20 28 33.5t41 13.5h200l-518 -1051h-213z" />
<glyph unicode="w" horiz-adv-x="1502" d="M79 1051h191q27 0 44.5 -13.5t19.5 -33.5l61 -562q5 -46 5.5 -89t2.5 -86q15 43 32.5 86t35.5 89l228 564q8 20 26.5 33.5t41.5 13.5h109q27 0 42 -13.5t18 -33.5l88 -564q7 -46 12 -89l10 -86q11 43 23.5 86t30.5 89l204 562q7 20 25.5 33.5t41.5 13.5h184l-433 -1051 h-195q-32 0 -39 45l-101 607q-6 41 -9 79l-14 -40t-15 -41l-250 -605q-18 -45 -55 -45h-187z" />
<glyph unicode="x" horiz-adv-x="1030" d="M361 568l-243 483h229q26 0 37 -7.5t19 -24.5l145 -313q6 12 13 24.5t17 26.5l184 258q12 17 25.5 26.5t30.5 9.5h230l-370 -484l270 -567h-230q-26 0 -40.5 14t-23.5 32l-160 369q-5 -11 -11.5 -22.5t-12.5 -20.5l-233 -326q-13 -17 -30 -31.5t-40 -14.5h-222z" />
<glyph unicode="y" horiz-adv-x="1017" d="M380 -291q-27 -52 -82 -52h-187l233 423l-265 971h212q28 0 41.5 -14t18.5 -33l129 -545q6 -27 10 -52.5t6 -52.5q11 27 22.5 53t24.5 53l259 545q10 20 29.5 33t39.5 13h203z" />
<glyph unicode="z" horiz-adv-x="897" d="M890 946q-3 -27 -14 -52.5t-27 -41.5l-525 -657h461l-22 -195h-764l13 105q2 17 13 42t28 46l530 664h-452l24 194h748z" />
<glyph unicode="{" horiz-adv-x="565" d="M128 452q0 49 -23 80.5t-73 31.5l17 142q76 0 105.5 62.5t29.5 193.5q0 16 -1 37.5t-1.5 44.5t-1.5 45t-1 39q0 109 24.5 193.5t74.5 142.5t125.5 88t179.5 30h48l-14 -109q-2 -12 -8 -21.5t-14.5 -15.5t-18 -9t-16.5 -3h-7q-68 0 -106.5 -48.5t-47.5 -138.5 q-5 -52 -6 -100t-1 -94t-1 -89.5t-5 -87.5q-4 -43 -18 -80t-37 -67.5t-53.5 -52.5t-67.5 -32q59 -20 91 -71t32 -116q0 -59 -13.5 -116t-30 -113.5t-29.5 -114.5t-13 -120q0 -61 28 -100t83 -39h9q14 0 29.5 -9.5t15.5 -33.5v-3.5t-1.5 -13t-4.5 -33t-8 -63.5h-49 q-82 0 -141.5 21.5t-97.5 59.5t-57 91.5t-19 117.5q0 67 14.5 127.5t32 118t32.5 114t15 114.5z" />
<glyph unicode="|" horiz-adv-x="614" d="M147 1584h197v-1927h-197v1927z" />
<glyph unicode="}" horiz-adv-x="565" d="M469 818q0 -49 23 -80.5t73 -31.5l-17 -142q-76 0 -105.5 -63t-29.5 -193q0 -16 1 -37.5t1.5 -44.5t1.5 -45.5t1 -39.5q0 -109 -24.5 -193t-74.5 -142t-126 -88t-179 -30h-48l14 109q1 12 7.5 21.5t15 15.5t18 8.5t16.5 2.5h7q68 0 106.5 49t47.5 139q5 52 6 100t1 93.5 t1 89t5 87.5q4 43 18 80.5t37 68t53.5 52t67.5 32.5q-59 20 -91 71t-32 115q0 59 13.5 116t30 114t29.5 115t13 120q0 61 -28 99.5t-83 38.5h-9q-14 0 -29.5 10t-15.5 33v4t1.5 13.5t4.5 33t8 63.5h49q81 0 141 -21.5t98 -60t57 -91.5t19 -118q0 -67 -14.5 -127t-32 -117.5 t-32.5 -114t-15 -114.5z" />
<glyph unicode="~" d="M701 664q57 0 89 36.5t32 103.5h204q0 -79 -21 -143t-60.5 -109.5t-96.5 -70t-131 -24.5q-54 0 -104 14t-94.5 31t-83.5 31.5t-71 14.5q-57 0 -89 -37t-32 -103h-204q0 79 21 142.5t60.5 109.5t96.5 70.5t131 24.5q54 0 104 -14.5t94.5 -31.5t83 -31t71.5 -14z" />
<glyph unicode="&#xa1;" horiz-adv-x="684" d="M131 -345l68 548q11 93 30.5 182t44.5 190h160q0 -17 0.5 -34.5t0.5 -34.5q0 -80 -4 -153t-13 -150l-67 -548h-220zM238 912q0 32 11.5 60.5t32.5 49t49 33t61 12.5t61 -12.5t49 -33t33.5 -49t12.5 -60.5q0 -33 -12.5 -61t-33.5 -48.5t-49 -32t-61 -11.5t-61 11.5t-49 32 t-32.5 48.5t-11.5 61z" />
<glyph unicode="&#xa2;" d="M493 -9q-81 12 -149 49t-116 96t-75 137.5t-27 176.5q0 127 40 237t115 192t184.5 130.5t248.5 53.5l34 164q5 24 25.5 43t47.5 19h90l-49 -235q80 -14 142 -48t111 -85l-74 -85q-12 -14 -22.5 -21.5t-30.5 -7.5q-14 0 -28.5 7t-33.5 17t-43.5 20.5t-57.5 18.5l-144 -700 q56 5 95 20t67 30.5t49 27.5t42 12q31 0 43 -20l56 -86q-40 -41 -86 -71t-95.5 -50t-102.5 -30.5t-106 -14.5l-32 -155q-5 -24 -25.5 -42.5t-47.5 -18.5h-91zM367 462q0 -110 42.5 -181t121.5 -98l144 695q-78 -8 -136 -41.5t-96.5 -87.5t-57 -126.5t-18.5 -160.5z" />
<glyph unicode="&#xa3;" d="M932 678q-2 -24 -23.5 -43t-51.5 -19h-391l-25 -210q-8 -70 -32 -122.5t-67 -96.5q40 9 80 14.5t80 5.5h612l-13 -102q-2 -18 -12 -37t-25 -34t-35.5 -24.5t-44.5 -9.5h-983l19 154q71 16 111 58.5t51 127.5l34 276h-159l13 99q3 23 24 41.5t51 18.5h91l29 247 q11 96 53.5 182.5t112 151t164.5 102.5t213 38q88 0 153.5 -20t113 -55.5t79.5 -84t52 -105.5l-106 -64q-30 -15 -58 -15q-17 0 -33.5 7t-28.5 25q-18 25 -35 44.5t-38.5 33.5t-51 21t-71.5 7q-54 0 -100 -18.5t-81.5 -52.5t-58 -83.5t-29.5 -111.5l-30 -249h458l-11 -97v0z " />
<glyph unicode="&#xa5;" d="M156 646h286l-307 834h205q33 0 53 -17t28 -44l169 -511q14 -43 21 -80.5t13 -74.5q12 37 28 74.5t40 80.5l293 511q13 24 37.5 42.5t56.5 18.5h206l-513 -834h288l-17 -135h-329l-13 -98h329l-16 -137h-330l-34 -276h-243l33 276h-330l18 137h329l11 98h-329z" />
<glyph unicode="&#xa7;" horiz-adv-x="952" d="M876 1278q-11 -15 -23 -22.5t-29 -7.5q-18 0 -36.5 11t-42 23t-55 23t-74.5 11q-41 0 -73 -11.5t-54 -30.5t-34 -44.5t-12 -52.5q0 -30 20 -53.5t52.5 -44.5t73.5 -40.5t84.5 -41t84.5 -47.5t73 -59t52 -75.5t20 -96.5q0 -91 -44 -167t-141 -121q34 -33 55 -76t21 -100 q0 -80 -29.5 -149t-85 -120.5t-136.5 -80.5t-186 -29q-50 0 -97 10t-89 26.5t-77 40t-59 51.5l68 92q12 17 27.5 26t39.5 9q23 0 40.5 -13.5t41 -28.5t58 -28.5t89.5 -13.5q83 0 129 41.5t46 109.5q0 38 -19.5 65t-52.5 49t-75 40.5t-86 38.5t-86 44t-75 56.5t-52.5 75.5 t-19.5 102q0 93 48 165.5t145 113.5q-34 35 -55 81t-21 109q0 72 27.5 136t79.5 113.5t128.5 78t174.5 28.5q102 0 180.5 -35.5t124.5 -92.5zM330 730q0 -40 24.5 -69t63.5 -53t87 -46.5t96 -48.5q44 25 63 59t19 76q0 41 -23.5 71t-61.5 53.5t-85 45t-94 46.5 q-49 -28 -69 -60t-20 -74z" />
<glyph unicode="&#xa8;" horiz-adv-x="605" d="M357 1346q0 -29 -11 -54t-31 -43.5t-46 -29.5t-56 -11q-28 0 -53 11t-44 29.5t-30 43.5t-11 54q0 30 11 55.5t30 45t44 31t53 11.5q30 0 56 -11.5t46 -31t31 -45t11 -55.5zM739 1346q0 -29 -11 -54t-30 -43.5t-45 -29.5t-56 -11q-29 0 -54.5 11t-44.5 29.5t-30 43.5 t-11 54q0 30 11 55.5t30 45t44.5 31t54.5 11.5q30 0 56 -11.5t45 -31t30 -45t11 -55.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1620" d="M1070 524q11 0 20.5 -4.5t15.5 -12.5l84 -89q-57 -73 -144.5 -111.5t-204.5 -38.5q-102 0 -185.5 36.5t-142 100.5t-90.5 150.5t-32 187.5q0 104 36 191.5t99.5 150.5t149 98t185.5 35q114 0 196 -38.5t137 -98.5l-67 -91q-6 -8 -16.5 -16t-27.5 -8t-32.5 10t-37 22.5 t-54.5 22.5t-86 10q-61 0 -108.5 -20t-81 -57.5t-51 -90.5t-17.5 -120q0 -70 18 -123t49.5 -89t75 -55t95.5 -19q50 0 82.5 8t55 19.5t39.5 22.5t40 17zM83 740q0 104 27 201t76.5 181t118.5 153.5t152.5 118.5t179.5 76t201 27q104 0 201 -27t181 -76t153 -118.5 t118 -153.5t76 -180.5t27 -201.5q0 -103 -27 -200t-76 -180.5t-118 -152.5t-153 -118t-181 -76t-201 -27q-103 0 -200 27t-180.5 76t-152.5 118t-118.5 152.5t-76.5 180t-27 200.5zM221 740q0 -133 47.5 -248.5t130.5 -201t196 -135t243 -49.5q87 0 166.5 23t149 63.5 t126 98t96.5 128t62 151.5t22 170t-22 171t-62 153.5t-96.5 129t-126 99t-149 64t-166.5 22.5q-86 0 -165.5 -22.5t-148 -64t-124.5 -99t-96 -129t-61.5 -153.5t-21.5 -171z" />
<glyph unicode="&#xaa;" horiz-adv-x="657" d="M552 861q-24 0 -35 7.5t-19 28.5l-8 37q-25 -20 -47 -35.5t-45 -25.5t-48 -15t-56 -5q-73 0 -115.5 38t-42.5 108q0 39 17 78.5t59.5 72t114 53.5t182.5 24l3 43q0 19 -3 35.5t-11.5 28.5t-21.5 18.5t-35 6.5q-31 0 -51.5 -8t-37.5 -16.5t-33 -16t-35 -7.5q-18 0 -29 9.5 t-17 21.5l-25 58q59 52 126.5 75.5t140.5 23.5q47 0 85 -17t64.5 -45.5t41 -67t14.5 -82.5v-7.5t-1.5 -21t-4.5 -43t-8.5 -74t-15 -115.5t-21.5 -165h-82zM365 973q35 0 62.5 13.5t57.5 42.5l11 90q-54 -2 -90.5 -9.5t-58.5 -20.5t-31.5 -30.5t-9.5 -37.5q0 -27 15.5 -37.5 t43.5 -10.5z" />
<glyph unicode="&#xab;" horiz-adv-x="943" d="M124 535l5 30l292 405l75 -38q33 -13 32 -45q0 -11 -5.5 -23.5t-13.5 -26.5l-157 -245q-17 -26 -38 -43q8 -7 14.5 -18t12.5 -23l97 -245q8 -19 8 -36q0 -42 -44 -61l-85 -37zM440 535l5 30l292 405l75 -38q33 -13 33 -45q0 -11 -5.5 -23.5t-14.5 -26.5l-156 -245 q-17 -26 -38 -43q8 -7 14 -18t12 -23l98 -245q8 -19 8 -36q0 -42 -44 -61l-85 -37z" />
<glyph unicode="&#xac;" d="M175 778h904l-58 -471h-207l36 290h-696z" />
<glyph unicode="&#xad;" horiz-adv-x="679" d="M121 720h492l-25 -204h-492z" />
<glyph unicode="&#xae;" horiz-adv-x="1620" d="M81 740q0 157 59.5 294.5t161.5 240.5t239.5 162.5t294.5 59.5q104 0 201 -27t181 -76t153 -118.5t118 -153.5t76 -180.5t27 -201.5q0 -103 -27 -200t-76 -180.5t-118 -152.5t-153 -118t-181 -76t-201 -27t-200.5 27t-180.5 76t-153 118t-118 152.5t-76 180t-27 200.5z M218 740q0 -133 47.5 -248.5t131 -201t196.5 -135t243 -49.5q87 0 166.5 23t149 63.5t126 98t96.5 128t62 151.5t22 170t-22 171t-62 153.5t-96.5 129t-126 99t-149 64t-166.5 22.5t-166.5 -22.5t-148 -64t-124.5 -99t-96 -129t-61.5 -153.5t-21.5 -171zM721 616v-332h-219 v918h328q191 0 280.5 -68.5t89.5 -196.5q0 -91 -47 -159t-146 -99q24 -13 39 -33.5t30 -47.5l186 -314h-211q-45 0 -64 33l-148 270q-8 15 -21.5 22t-40.5 7h-56zM721 773h91q53 0 86.5 9t52.5 26t26 43t7 58t-6 56t-23 40t-47 23.5t-78 7.5h-109v-263z" />
<glyph unicode="&#xaf;" horiz-adv-x="605" d="M132 1402h566l-19 -157h-566z" />
<glyph unicode="&#xb0;" horiz-adv-x="802" d="M130 1150q0 73 27 137t75 111t112.5 74t138.5 27q75 0 140 -27t112.5 -74t75.5 -111t28 -137q0 -71 -28 -134.5t-75.5 -111t-112.5 -75t-140 -27.5t-139 27.5t-112 75t-75 111t-27 134.5zM305 1148q0 -38 13.5 -71t37.5 -57t56 -37.5t71 -13.5q38 0 70.5 13.5t56.5 37.5 t37 57t13 71q0 39 -13 72t-37 58t-56.5 39t-70.5 14q-39 0 -71 -14t-56 -39t-37.5 -58t-13.5 -72z" />
<glyph unicode="&#xb1;" d="M785 1266l-45 -366h401l-23 -181h-400l-43 -352h-198l43 352h-397l20 181h400l45 366h197zM66 263h996l-23 -181h-995z" />
<glyph unicode="&#xb4;" horiz-adv-x="605" d="M760 1496l-268 -267q-20 -20 -39 -27.5t-46 -7.5h-146l164 249q17 27 38 40t60 13h237z" />
<glyph unicode="&#xb5;" horiz-adv-x="1075" d="M415 1050q-22 -175 -37.5 -296t-25 -200t-14.5 -122.5t-7.5 -64.5t-3 -26t-0.5 -7q0 -76 34 -117.5t103 -41.5q57 0 111 28t106 79l96 768h243l-129 -1050h-150q-46 0 -55 45l-7 83q-33 -31 -62.5 -53t-60 -35.5t-62 -20t-66.5 -6.5q-52 0 -93.5 17t-71.5 49 q2 -14 2 -27.5v-27.5q0 -30 -2 -60t-5 -56l-31 -253h-121q-46 0 -71 22t-25 63v8t1 9l160 1293h244z" />
<glyph unicode="&#xb6;" horiz-adv-x="1434" d="M1522 1481l-26 -212h-220l-182 -1483h-221l182 1483h-241l-181 -1483h-221l105 859q-96 0 -174 27t-132 76.5t-84 117t-30 148.5q0 95 38 180t106.5 149t165 101t213.5 37h902z" />
<glyph unicode="&#xb7;" horiz-adv-x="572" d="M114 609q0 40 15 75.5t41.5 61.5t61 41t74.5 15q41 0 76.5 -15t61.5 -41t41.5 -61.5t15.5 -75.5t-15.5 -74t-41.5 -60t-61.5 -41t-76.5 -15q-40 0 -74.5 15t-61 41t-41.5 60t-15 74z" />
<glyph unicode="&#xb8;" horiz-adv-x="605" d="M85 -236q7 0 13.5 -2.5t15 -5.5t18.5 -5.5t25 -2.5q32 0 48 16t16 37q0 27 -30.5 40t-97.5 22l61 151h152l-28 -73q75 -19 105.5 -53.5t30.5 -78.5q0 -39 -19 -70t-53 -52.5t-80.5 -32.5t-101.5 -11q-39 0 -72 6t-65 18l33 76q9 21 29 21z" />
<glyph unicode="&#xba;" horiz-adv-x="734" d="M490 1498q62 0 112.5 -19.5t86 -56t55 -87.5t19.5 -113q0 -86 -26.5 -155t-73.5 -117.5t-111.5 -74.5t-139.5 -26q-63 0 -114.5 19.5t-87.5 56t-55.5 88t-19.5 115.5q0 85 26.5 153.5t74.5 116.5t112.5 74t141.5 26zM429 991q77 0 110.5 61t33.5 170q0 65 -25 99.5 t-75 34.5q-44 0 -72.5 -17t-45.5 -47t-23.5 -72.5t-6.5 -94.5q0 -66 25.5 -100t78.5 -34z" />
<glyph unicode="&#xbb;" horiz-adv-x="943" d="M516 565l-3 -30l-292 -406l-75 37q-32 15 -31 46q0 23 17 51l159 245q8 12 17 23t19 18q-17 16 -26 43l-99 245q-6 16 -6 32q0 42 45 63l84 38zM833 565l-4 -30l-291 -406l-75 37q-32 15 -32 46q0 23 18 51l158 245q8 12 17.5 23t18.5 18q-17 16 -26 43l-99 245 q-6 16 -6 32q0 42 45 63l84 38z" />
<glyph unicode="&#xbf;" horiz-adv-x="798" d="M692 -210q-37 -30 -78.5 -57t-89 -47.5t-100.5 -32.5t-115 -12q-76 0 -136.5 20.5t-103.5 58.5t-66 93.5t-23 124.5q0 82 23.5 140t58.5 100t78 73t82.5 57.5t69.5 53.5t40 63l42 150h166v-167q0 -46 -23.5 -81t-59 -65.5t-76.5 -58.5t-76.5 -59.5t-59 -70.5t-23.5 -90 q0 -36 11 -63.5t29 -45.5t42 -27.5t51 -9.5q52 0 90.5 13.5t66.5 29.5t47.5 29t34.5 13q34 0 46 -28zM336 913q0 32 11.5 60.5t32 49t49.5 33t61 12.5q33 0 61.5 -12.5t49.5 -33t33 -49t12 -60.5q0 -33 -12 -61t-33 -48.5t-49.5 -32t-61.5 -11.5t-61.5 11.5t-49 32t-32 48.5 t-11.5 61z" />
<glyph unicode="&#xc0;" horiz-adv-x="1313" d="M0 0zM1267 0h-206q-35 0 -55.5 16.5t-25.5 43.5l-61 310h-552l-138 -310q-11 -24 -36.5 -42t-57.5 -18h-209l716 1481h271zM450 557h431l-101 507q-8 37 -16 78t-15 92q-19 -51 -38.5 -94.5t-35.5 -76.5zM681 1832q19 0 32.5 -1.5t23.5 -6t19 -12t20 -19.5l157 -207h-207 q-28 0 -43 9t-34 26l-240 211h272z" />
<glyph unicode="&#xc1;" horiz-adv-x="1313" d="M0 0zM1267 0h-206q-35 0 -55.5 16.5t-25.5 43.5l-61 310h-552l-138 -310q-11 -24 -36.5 -42t-57.5 -18h-209l716 1481h271zM450 557h431l-101 507q-8 37 -16 78t-15 92q-19 -51 -38.5 -94.5t-35.5 -76.5zM1231 1832l-291 -211q-24 -17 -46 -26t-49 -9h-198l209 207 q12 12 23 20t23 12t25.5 5.5t34.5 1.5h269z" />
<glyph unicode="&#xc2;" horiz-adv-x="1313" d="M0 0zM1267 0h-206q-35 0 -55.5 16.5t-25.5 43.5l-61 310h-552l-138 -310q-11 -24 -36.5 -42t-57.5 -18h-209l716 1481h271zM450 557h431l-101 507q-8 37 -16 78t-15 92q-19 -51 -38.5 -94.5t-35.5 -76.5zM1135 1586h-185q-14 0 -30 5t-27 12l-81 71q-3 3 -7.5 6.5 t-7.5 8.5l-16 -15l-99 -71q-11 -8 -29 -12.5t-34 -4.5h-183l255 223h244z" />
<glyph unicode="&#xc3;" horiz-adv-x="1313" d="M0 0zM1267 0h-206q-35 0 -55.5 16.5t-25.5 43.5l-61 310h-552l-138 -310q-11 -24 -36.5 -42t-57.5 -18h-209l716 1481h271zM450 557h431l-101 507q-8 37 -16 78t-15 92q-19 -51 -38.5 -94.5t-35.5 -76.5zM924 1736q25 0 42.5 16t21.5 49h133q-6 -49 -23.5 -90.5 t-44.5 -71.5t-63 -46.5t-79 -16.5q-34 0 -64.5 11.5t-57.5 25.5t-50.5 26t-42.5 12q-25 0 -41.5 -18t-20.5 -52h-136q6 50 24 92t45.5 72t63 46.5t77.5 16.5q35 0 66 -11t58 -25t50 -25t42 -11z" />
<glyph unicode="&#xc4;" horiz-adv-x="1313" d="M0 0zM1267 0h-206q-35 0 -55.5 16.5t-25.5 43.5l-61 310h-552l-138 -310q-11 -24 -36.5 -42t-57.5 -18h-209l716 1481h271zM450 557h431l-101 507q-8 37 -16 78t-15 92q-19 -51 -38.5 -94.5t-35.5 -76.5zM723 1706q0 -25 -10.5 -47t-27.5 -38t-40 -26t-48 -10t-47 10 t-39 26t-26.5 38t-9.5 47q0 26 9.5 49t26.5 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5t27.5 -39.5t10.5 -49zM1082 1706q0 -25 -9.5 -47t-26.5 -38t-39.5 -26t-48.5 -10t-47.5 10t-38.5 26t-27 38t-10 47q0 26 10 49t27 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5 t26.5 -39.5t9.5 -49z" />
<glyph unicode="&#xc5;" horiz-adv-x="1313" d="M0 0zM1267 0h-206q-35 0 -55.5 16.5t-25.5 43.5l-61 310h-552l-138 -310q-11 -24 -36.5 -42t-57.5 -18h-209l716 1481h271zM450 557h431l-101 507q-8 37 -16 78t-15 92q-19 -51 -38.5 -94.5t-35.5 -76.5zM575 1723q0 44 17.5 80t46.5 61.5t67 40t80 14.5q43 0 82.5 -14.5 t68.5 -40t46.5 -61.5t17.5 -80q0 -43 -17.5 -78t-46.5 -60.5t-68.5 -39.5t-82.5 -14q-42 0 -80 14t-67 39.5t-46.5 60.5t-17.5 78zM697 1723q0 -40 23.5 -65.5t68.5 -25.5q41 0 66 25.5t25 65.5q0 43 -25 68t-66 25q-45 0 -68.5 -25t-23.5 -68z" />
<glyph unicode="&#xc6;" horiz-adv-x="1752" d="M761 1481h1031l-26 -212h-658v-422h478l-25 -204h-453v-431h528l-26 -212h-753v370h-478l-169 -310q-13 -24 -40.5 -42t-59.5 -18h-207zM481 557h376v707q-18 -46 -36.5 -85.5t-39.5 -74.5z" />
<glyph unicode="&#xc7;" horiz-adv-x="1235" d="M508 -236q7 0 13.5 -2.5t15 -5.5t18.5 -5.5t25 -2.5q32 0 48 16t16 37q0 27 -30.5 40t-97.5 22l50 126q-117 14 -208.5 67.5t-154 138t-95 195t-32.5 238.5t27.5 242t78.5 211t123 173.5t159 131t188 83t210 28.5q81 0 147.5 -15t121 -41.5t97.5 -64t78 -83.5l-93 -110 q-10 -12 -23.5 -21.5t-34.5 -9.5q-23 0 -43 18.5t-51.5 40.5t-81 40.5t-130.5 18.5q-106 0 -197.5 -43.5t-159 -124.5t-106 -195t-38.5 -257q0 -103 26 -185t72.5 -139t110 -87t137.5 -30q62 0 109 9.5t81.5 25t59 32.5t43 32.5t32.5 25t29 9.5q12 0 20 -5t13 -10l94 -116 q-87 -99 -200 -156.5t-257 -68.5l-18 -46q75 -19 106 -53.5t31 -78.5q0 -39 -19 -70t-53 -52.5t-80.5 -32.5t-101.5 -11q-39 0 -72 6t-66 18l34 76q9 21 29 21z" />
<glyph unicode="&#xc8;" horiz-adv-x="1076" d="M72 0zM1117 1481l-26 -212h-596l-52 -421h468l-25 -204h-468l-53 -432h598l-27 -212h-864l182 1481h863zM595 1832q19 0 32.5 -1.5t23.5 -6t19 -12t20 -19.5l157 -207h-207q-28 0 -43 9t-34 26l-240 211h272z" />
<glyph unicode="&#xc9;" horiz-adv-x="1076" d="M72 0zM1117 1481l-26 -212h-596l-52 -421h468l-25 -204h-468l-53 -432h598l-27 -212h-864l182 1481h863zM1121 1832l-291 -211q-24 -17 -46 -26t-49 -9h-198l209 207q12 12 23 20t23 12t25.5 5.5t34.5 1.5h269z" />
<glyph unicode="&#xca;" horiz-adv-x="1076" d="M72 0zM1117 1481l-26 -212h-596l-52 -421h468l-25 -204h-468l-53 -432h598l-27 -212h-864l182 1481h863zM1049 1586h-185q-14 0 -30 5t-27 12l-81 71q-3 3 -7.5 6.5t-7.5 8.5l-16 -15l-99 -71q-11 -8 -29 -12.5t-34 -4.5h-183l255 223h244z" />
<glyph unicode="&#xcb;" horiz-adv-x="1076" d="M72 0zM1117 1481l-26 -212h-596l-52 -421h468l-25 -204h-468l-53 -432h598l-27 -212h-864l182 1481h863zM637 1706q0 -25 -10.5 -47t-27.5 -38t-40 -26t-48 -10t-47 10t-39 26t-26.5 38t-9.5 47q0 26 9.5 49t26.5 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5 t27.5 -39.5t10.5 -49zM996 1706q0 -25 -9.5 -47t-26.5 -38t-39.5 -26t-48.5 -10t-47.5 10t-38.5 26t-27 38t-10 47q0 26 10 49t27 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5t26.5 -39.5t9.5 -49z" />
<glyph unicode="&#xcc;" horiz-adv-x="593" d="M53 0zM369 0h-268l182 1481h267zM325 1832q19 0 32.5 -1.5t23.5 -6t19 -12t20 -19.5l157 -207h-207q-28 0 -43 9t-34 26l-240 211h272z" />
<glyph unicode="&#xcd;" horiz-adv-x="593" d="M101 0zM369 0h-268l182 1481h267zM851 1832l-291 -211q-24 -17 -46 -26t-49 -9h-198l209 207q12 12 23 20t23 12t25.5 5.5t34.5 1.5h269z" />
<glyph unicode="&#xce;" horiz-adv-x="593" d="M80 0zM369 0h-268l182 1481h267zM779 1586h-185q-14 0 -30 5t-27 12l-81 71q-3 3 -7.5 6.5t-7.5 8.5l-16 -15l-99 -71q-11 -8 -29 -12.5t-34 -4.5h-183l255 223h244z" />
<glyph unicode="&#xcf;" horiz-adv-x="593" d="M101 0zM369 0h-268l182 1481h267zM367 1706q0 -25 -10.5 -47t-27.5 -38t-40 -26t-48 -10t-47 10t-39 26t-26.5 38t-9.5 47q0 26 9.5 49t26.5 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5t27.5 -39.5t10.5 -49zM726 1706q0 -25 -9.5 -47t-26.5 -38t-39.5 -26t-48.5 -10 t-47.5 10t-38.5 26t-27 38t-10 47q0 26 10 49t27 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5t26.5 -39.5t9.5 -49z" />
<glyph unicode="&#xd0;" horiz-adv-x="1489" d="M90 820h166l81 661h522q141 0 254.5 -48.5t192.5 -133.5t121 -202.5t42 -256.5q0 -184 -59 -339t-164.5 -266t-250.5 -173t-316 -62h-522l81 669h-168zM1197 834q0 99 -24.5 180t-71 137.5t-114 87.5t-153.5 31h-255l-56 -450h337l-17 -151h-338l-55 -457h255 q112 0 202.5 43t155 123.5t99.5 196t35 259.5z" />
<glyph unicode="&#xd1;" horiz-adv-x="1425" d="M72 0zM393 1481q17 0 29 -1.5t21.5 -6.5t16.5 -13t15 -22l577 -1011q2 30 4.5 58t5.5 53l116 943h234l-183 -1481h-136q-31 0 -52.5 9.5t-35.5 36.5l-574 1011q-2 -25 -4 -48t-4 -43l-117 -966h-234l182 1481h139v0zM975 1736q25 0 42.5 16t21.5 49h133 q-6 -49 -23.5 -90.5t-44.5 -71.5t-63 -46.5t-79 -16.5q-34 0 -64.5 11.5t-57.5 25.5t-50.5 26t-42.5 12q-25 0 -41.5 -18t-20.5 -52h-136q6 50 24 92t45.5 72t63 46.5t77.5 16.5q35 0 66 -11t58 -25t50 -25t42 -11z" />
<glyph unicode="&#xd2;" horiz-adv-x="1499" d="M76 0zM1481 840q0 -123 -27.5 -234t-78 -206.5t-121.5 -172t-159.5 -131t-192 -83.5t-217.5 -29q-141 0 -254 50t-192 138t-121 208t-42 260q0 123 27 234t77.5 206.5t122 172t160 131.5t192 84t217.5 29q141 0 254 -50t192 -138t121 -208.5t42 -260.5zM1208 834 q0 100 -25 182t-72.5 140t-116 90t-154.5 32q-111 0 -201.5 -45t-155 -128t-100 -200t-35.5 -259q0 -100 24.5 -182t72 -140t115.5 -89t155 -31q112 0 203 45t155.5 127t99.5 198.5t35 259.5zM771 1832q19 0 32.5 -1.5t23.5 -6t19 -12t20 -19.5l157 -207h-207q-28 0 -43 9 t-34 26l-240 211h272z" />
<glyph unicode="&#xd3;" horiz-adv-x="1499" d="M76 0zM1481 840q0 -123 -27.5 -234t-78 -206.5t-121.5 -172t-159.5 -131t-192 -83.5t-217.5 -29q-141 0 -254 50t-192 138t-121 208t-42 260q0 123 27 234t77.5 206.5t122 172t160 131.5t192 84t217.5 29q141 0 254 -50t192 -138t121 -208.5t42 -260.5zM1208 834 q0 100 -25 182t-72.5 140t-116 90t-154.5 32q-111 0 -201.5 -45t-155 -128t-100 -200t-35.5 -259q0 -100 24.5 -182t72 -140t115.5 -89t155 -31q112 0 203 45t155.5 127t99.5 198.5t35 259.5zM1297 1832l-291 -211q-24 -17 -46 -26t-49 -9h-198l209 207q12 12 23 20t23 12 t25.5 5.5t34.5 1.5h269z" />
<glyph unicode="&#xd4;" horiz-adv-x="1499" d="M76 0zM1481 840q0 -123 -27.5 -234t-78 -206.5t-121.5 -172t-159.5 -131t-192 -83.5t-217.5 -29q-141 0 -254 50t-192 138t-121 208t-42 260q0 123 27 234t77.5 206.5t122 172t160 131.5t192 84t217.5 29q141 0 254 -50t192 -138t121 -208.5t42 -260.5zM1208 834 q0 100 -25 182t-72.5 140t-116 90t-154.5 32q-111 0 -201.5 -45t-155 -128t-100 -200t-35.5 -259q0 -100 24.5 -182t72 -140t115.5 -89t155 -31q112 0 203 45t155.5 127t99.5 198.5t35 259.5zM1225 1586h-185q-14 0 -30 5t-27 12l-81 71q-3 3 -7.5 6.5t-7.5 8.5l-16 -15 l-99 -71q-11 -8 -29 -12.5t-34 -4.5h-183l255 223h244z" />
<glyph unicode="&#xd5;" horiz-adv-x="1499" d="M76 0zM1481 840q0 -123 -27.5 -234t-78 -206.5t-121.5 -172t-159.5 -131t-192 -83.5t-217.5 -29q-141 0 -254 50t-192 138t-121 208t-42 260q0 123 27 234t77.5 206.5t122 172t160 131.5t192 84t217.5 29q141 0 254 -50t192 -138t121 -208.5t42 -260.5zM1208 834 q0 100 -25 182t-72.5 140t-116 90t-154.5 32q-111 0 -201.5 -45t-155 -128t-100 -200t-35.5 -259q0 -100 24.5 -182t72 -140t115.5 -89t155 -31q112 0 203 45t155.5 127t99.5 198.5t35 259.5zM1014 1736q25 0 42.5 16t21.5 49h133q-6 -49 -23.5 -90.5t-44.5 -71.5t-63 -46.5 t-79 -16.5q-34 0 -64.5 11.5t-57.5 25.5t-50.5 26t-42.5 12q-25 0 -41.5 -18t-20.5 -52h-136q6 50 24 92t45.5 72t63 46.5t77.5 16.5q35 0 66 -11t58 -25t50 -25t42 -11z" />
<glyph unicode="&#xd6;" horiz-adv-x="1499" d="M76 0zM1481 840q0 -123 -27.5 -234t-78 -206.5t-121.5 -172t-159.5 -131t-192 -83.5t-217.5 -29q-141 0 -254 50t-192 138t-121 208t-42 260q0 123 27 234t77.5 206.5t122 172t160 131.5t192 84t217.5 29q141 0 254 -50t192 -138t121 -208.5t42 -260.5zM1208 834 q0 100 -25 182t-72.5 140t-116 90t-154.5 32q-111 0 -201.5 -45t-155 -128t-100 -200t-35.5 -259q0 -100 24.5 -182t72 -140t115.5 -89t155 -31q112 0 203 45t155.5 127t99.5 198.5t35 259.5zM813 1706q0 -25 -10.5 -47t-27.5 -38t-40 -26t-48 -10t-47 10t-39 26t-26.5 38 t-9.5 47q0 26 9.5 49t26.5 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5t27.5 -39.5t10.5 -49zM1172 1706q0 -25 -9.5 -47t-26.5 -38t-39.5 -26t-48.5 -10t-47.5 10t-38.5 26t-27 38t-10 47q0 26 10 49t27 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5t26.5 -39.5t9.5 -49 z" />
<glyph unicode="&#xd8;" horiz-adv-x="1499" d="M1481 840q0 -123 -27.5 -234t-78 -206.5t-121.5 -172t-159.5 -131t-192 -83.5t-217.5 -29q-93 0 -172 21.5t-146 60.5l-85 -108q-33 -41 -75 -56.5t-80 -15.5h-104l221 280q-82 89 -125 210t-43 264q0 123 27 234t77.5 206.5t122 172t160 131.5t192 84t217.5 29 q101 0 187.5 -26t157.5 -74l66 84q14 18 26.5 30.5t25.5 20t28.5 11t37.5 3.5h136l-204 -257q72 -88 110 -202t38 -247zM348 646q0 -81 16 -148.5t46 -121.5l654 827q-45 36 -101 55.5t-123 19.5q-111 0 -201.5 -45t-155 -128t-100 -200t-35.5 -259zM1208 834 q0 69 -11.5 128.5t-34.5 109.5l-644 -815q83 -53 197 -53q112 0 203 45t155.5 127t99.5 198.5t35 259.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1370" d="M122 0zM650 213q72 0 132 27t105 77t74 119.5t39 154.5l110 890h265l-108 -890q-16 -131 -69.5 -242.5t-137 -192.5t-194.5 -126.5t-243 -45.5q-120 0 -213.5 37t-157.5 105t-97 161.5t-33 206.5q0 46 6 97l108 890h266l-110 -889q-2 -19 -3 -37.5t-1 -36.5 q0 -70 17 -126t50 -96t82.5 -61.5t112.5 -21.5zM711 1831q19 0 32.5 -1.5t23.5 -6t19 -12t20 -19.5l157 -207h-207q-28 0 -43 9t-34 26l-240 211h272z" />
<glyph unicode="&#xda;" horiz-adv-x="1370" d="M122 0zM650 213q72 0 132 27t105 77t74 119.5t39 154.5l110 890h265l-108 -890q-16 -131 -69.5 -242.5t-137 -192.5t-194.5 -126.5t-243 -45.5q-120 0 -213.5 37t-157.5 105t-97 161.5t-33 206.5q0 46 6 97l108 890h266l-110 -889q-2 -19 -3 -37.5t-1 -36.5 q0 -70 17 -126t50 -96t82.5 -61.5t112.5 -21.5zM1237 1831l-291 -211q-24 -17 -46 -26t-49 -9h-198l209 207q12 12 23 20t23 12t25.5 5.5t34.5 1.5h269z" />
<glyph unicode="&#xdb;" horiz-adv-x="1370" d="M122 0zM650 213q72 0 132 27t105 77t74 119.5t39 154.5l110 890h265l-108 -890q-16 -131 -69.5 -242.5t-137 -192.5t-194.5 -126.5t-243 -45.5q-120 0 -213.5 37t-157.5 105t-97 161.5t-33 206.5q0 46 6 97l108 890h266l-110 -889q-2 -19 -3 -37.5t-1 -36.5 q0 -70 17 -126t50 -96t82.5 -61.5t112.5 -21.5zM1165 1585h-185q-14 0 -30 5t-27 12l-81 71q-3 3 -7.5 6.5t-7.5 8.5l-16 -15l-99 -71q-11 -8 -29 -12.5t-34 -4.5h-183l255 223h244z" />
<glyph unicode="&#xdc;" horiz-adv-x="1370" d="M122 0zM650 213q72 0 132 27t105 77t74 119.5t39 154.5l110 890h265l-108 -890q-16 -131 -69.5 -242.5t-137 -192.5t-194.5 -126.5t-243 -45.5q-120 0 -213.5 37t-157.5 105t-97 161.5t-33 206.5q0 46 6 97l108 890h266l-110 -889q-2 -19 -3 -37.5t-1 -36.5 q0 -70 17 -126t50 -96t82.5 -61.5t112.5 -21.5zM753 1705q0 -25 -10.5 -47t-27.5 -38t-40 -26t-48 -10t-47 10t-39 26t-26.5 38t-9.5 47q0 26 9.5 49t26.5 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5t27.5 -39.5t10.5 -49zM1112 1705q0 -25 -9.5 -47t-26.5 -38t-39.5 -26 t-48.5 -10t-47.5 10t-38.5 26t-27 38t-10 47q0 26 10 49t27 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5t26.5 -39.5t9.5 -49z" />
<glyph unicode="&#xdd;" horiz-adv-x="1241" d="M112 0zM763 577l-72 -577h-266l72 578l-385 903h236q35 0 53.5 -17t28.5 -44l192 -512q14 -37 22.5 -72t14.5 -69q16 34 35.5 68.5t42.5 72.5l319 512q14 24 37 42.5t57 18.5h220zM1194 1830l-291 -211q-24 -17 -46 -26t-49 -9h-198l209 207q12 12 23 20t23 12t25.5 5.5 t34.5 1.5h269z" />
<glyph unicode="&#xde;" horiz-adv-x="1182" d="M556 1223h186q124 0 215 -30.5t149.5 -84t87 -128t28.5 -162.5q0 -120 -37.5 -220t-113 -173.5t-187.5 -114.5t-260 -41h-187l-33 -269h-264l182 1481h266zM530 1017l-67 -542h187q79 0 136.5 23.5t95 66t55 101.5t17.5 130q0 50 -14 91t-44 69.5t-74.5 44.5t-105.5 16 h-186z" />
<glyph unicode="&#xdf;" horiz-adv-x="1185" d="M776 1505q104 0 175.5 -31t116 -77.5t64 -102.5t19.5 -105q0 -68 -22 -115.5t-54.5 -82.5t-70.5 -60t-70.5 -48.5t-54.5 -48.5t-22 -59q0 -31 20.5 -54t51 -44.5t67 -45.5t67.5 -57.5t51.5 -80.5t20.5 -112q0 -97 -35.5 -171t-94 -124.5t-134 -76t-156.5 -25.5 q-43 0 -84.5 9.5t-79 27t-69.5 41t-58 51.5l69 93q11 15 27.5 24.5t38.5 9.5t40 -13.5t39.5 -30t49.5 -29.5t69 -13q30 0 56.5 12t46 34t30.5 52t11 63q0 42 -22.5 71.5t-57 53t-73.5 45.5t-73.5 51t-57 68.5t-22.5 98.5q0 69 23.5 116.5t59.5 83t77 64t76.5 59t59 66.5 t23.5 89q0 29 -9.5 56t-30 47t-52.5 32t-79 12q-61 0 -111 -35t-87.5 -92.5t-62 -132.5t-33.5 -155l-106 -870l-52 -193q-14 -50 -44.5 -76.5t-85.5 -26.5h-102l136 1114l-94 17q-27 5 -41 17t-14 35l11 108h170q28 92 79.5 176.5t122.5 149t158.5 103t188.5 38.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="1059" d="M39 0zM879 0h-134q-46 0 -64 21.5t-18 54.5l8 143q-70 -109 -157 -171.5t-191 -62.5q-59 0 -111 23.5t-90.5 70t-60.5 116.5t-22 164t23.5 183.5t67 167.5t105 144t138 113t165 73t186.5 26q71 0 142 -10t140 -37zM422 191q42 0 82.5 29t76 79.5t66.5 119t53 147.5 l40 318q-28 3 -51 3q-85 0 -157.5 -39t-126 -105t-83.5 -153.5t-30 -185.5q0 -111 35 -162t95 -51zM654 1496q39 0 56.5 -13.5t28.5 -39.5l103 -249h-140q-27 0 -43.5 7t-30.5 28l-203 267h229z" />
<glyph unicode="&#xe1;" horiz-adv-x="1059" d="M39 0zM879 0h-134q-46 0 -64 21.5t-18 54.5l8 143q-70 -109 -157 -171.5t-191 -62.5q-59 0 -111 23.5t-90.5 70t-60.5 116.5t-22 164t23.5 183.5t67 167.5t105 144t138 113t165 73t186.5 26q71 0 142 -10t140 -37zM422 191q42 0 82.5 29t76 79.5t66.5 119t53 147.5 l40 318q-28 3 -51 3q-85 0 -157.5 -39t-126 -105t-83.5 -153.5t-30 -185.5q0 -111 35 -162t95 -51zM1065 1496l-268 -267q-20 -20 -39 -27.5t-46 -7.5h-146l164 249q17 27 38 40t60 13h237z" />
<glyph unicode="&#xe2;" horiz-adv-x="1059" d="M39 0zM879 0h-134q-46 0 -64 21.5t-18 54.5l8 143q-70 -109 -157 -171.5t-191 -62.5q-59 0 -111 23.5t-90.5 70t-60.5 116.5t-22 164t23.5 183.5t67 167.5t105 144t138 113t165 73t186.5 26q71 0 142 -10t140 -37zM422 191q42 0 82.5 29t76 79.5t66.5 119t53 147.5 l40 318q-28 3 -51 3q-85 0 -157.5 -39t-126 -105t-83.5 -153.5t-30 -185.5q0 -111 35 -162t95 -51zM1020 1196h-162q-14 0 -27.5 5.5t-19.5 12.5l-83 102q-8 8 -17 20q-6 -6 -12 -11t-11 -9l-111 -102q-8 -7 -21.5 -12.5t-29.5 -5.5h-169l252 284h230z" />
<glyph unicode="&#xe3;" horiz-adv-x="1059" d="M39 0zM879 0h-134q-46 0 -64 21.5t-18 54.5l8 143q-70 -109 -157 -171.5t-191 -62.5q-59 0 -111 23.5t-90.5 70t-60.5 116.5t-22 164t23.5 183.5t67 167.5t105 144t138 113t165 73t186.5 26q71 0 142 -10t140 -37zM422 191q42 0 82.5 29t76 79.5t66.5 119t53 147.5 l40 318q-28 3 -51 3q-85 0 -157.5 -39t-126 -105t-83.5 -153.5t-30 -185.5q0 -111 35 -162t95 -51zM815 1388q27 0 44 17t25 61h147q-7 -56 -27.5 -101.5t-50.5 -78t-68.5 -50t-81.5 -17.5q-34 0 -62 13t-51.5 29t-44 29.5t-39.5 13.5q-27 0 -44 -18.5t-22 -61.5h-150 q7 56 27.5 101.5t51.5 78.5t69 51t80 18q34 0 62.5 -13.5t52.5 -29.5t44 -29t38 -13z" />
<glyph unicode="&#xe4;" horiz-adv-x="1059" d="M39 0zM879 0h-134q-46 0 -64 21.5t-18 54.5l8 143q-70 -109 -157 -171.5t-191 -62.5q-59 0 -111 23.5t-90.5 70t-60.5 116.5t-22 164t23.5 183.5t67 167.5t105 144t138 113t165 73t186.5 26q71 0 142 -10t140 -37zM422 191q42 0 82.5 29t76 79.5t66.5 119t53 147.5 l40 318q-28 3 -51 3q-85 0 -157.5 -39t-126 -105t-83.5 -153.5t-30 -185.5q0 -111 35 -162t95 -51zM658 1346q0 -29 -11 -54t-31 -43.5t-46 -29.5t-56 -11q-28 0 -53 11t-44 29.5t-30 43.5t-11 54q0 30 11 55.5t30 45t44 31t53 11.5q30 0 56 -11.5t46 -31t31 -45t11 -55.5z M1040 1346q0 -29 -11 -54t-30 -43.5t-45 -29.5t-56 -11q-29 0 -54.5 11t-44.5 29.5t-30 43.5t-11 54q0 30 11 55.5t30 45t44.5 31t54.5 11.5q30 0 56 -11.5t45 -31t30 -45t11 -55.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1059" d="M39 0zM879 0h-134q-46 0 -64 21.5t-18 54.5l8 143q-70 -109 -157 -171.5t-191 -62.5q-59 0 -111 23.5t-90.5 70t-60.5 116.5t-22 164t23.5 183.5t67 167.5t105 144t138 113t165 73t186.5 26q71 0 142 -10t140 -37zM422 191q42 0 82.5 29t76 79.5t66.5 119t53 147.5 l40 318q-28 3 -51 3q-85 0 -157.5 -39t-126 -105t-83.5 -153.5t-30 -185.5q0 -111 35 -162t95 -51zM500 1357q0 47 18.5 86t50 66.5t72 43t85.5 15.5q46 0 88.5 -15.5t74 -43t50.5 -66.5t19 -86q0 -46 -19 -84t-50.5 -65t-73.5 -42t-89 -15q-45 0 -85.5 15t-72 42t-50 65 t-18.5 84zM637 1357q0 -39 24.5 -65t69.5 -26q41 0 65.5 26t24.5 65q0 43 -24.5 68t-65.5 25q-45 0 -69.5 -25t-24.5 -68z" />
<glyph unicode="&#xe6;" horiz-adv-x="1495" d="M1146 1066q66 0 122 -21t97.5 -58t65 -86t23.5 -104q0 -38 -10.5 -76.5t-37 -74t-70.5 -66.5t-111 -55t-160 -39.5t-216 -18.5q0 -150 55 -218.5t155 -68.5q69 0 112.5 18t75.5 39.5t57 39.5t55 18q12 0 24.5 -5.5t20.5 -17.5l64 -77q-54 -54 -105.5 -94.5t-106.5 -66 t-115.5 -37.5t-131.5 -12q-99 0 -181.5 50t-129.5 154q-35 -54 -79.5 -93t-94 -64t-102 -36.5t-103.5 -11.5q-152 0 -228 65t-76 188q0 69 30 135t101.5 120t191.5 89t299 41q5 46 6.5 65t1.5 31q0 80 -33.5 118t-99.5 38q-55 0 -93.5 -14.5t-69 -32t-55.5 -32.5t-54 -15 q-25 0 -41.5 12.5t-24.5 31.5l-34 79q93 83 190.5 124.5t209.5 41.5q108 0 171.5 -42.5t92.5 -117.5q63 74 148.5 115.5t193.5 41.5zM617 472q-108 -5 -178 -22t-112 -44t-59 -62.5t-17 -76.5q0 -58 30 -87t86 -29q45 0 86 15.5t73 49.5t54.5 85.5t31.5 123.5zM1114 890 q-53 0 -95 -20t-74 -56.5t-53 -87.5t-32 -115q115 9 187.5 26t114 39.5t57 50.5t15.5 57q0 46 -29.5 76t-90.5 30z" />
<glyph unicode="&#xe7;" horiz-adv-x="891" d="M294 -236q7 0 13.5 -2.5t15 -5.5t18.5 -5.5t25 -2.5q32 0 48 16t16 37q0 27 -30.5 40t-97.5 22l51 130q-72 13 -128 49.5t-94.5 91.5t-59 128.5t-20.5 160.5q0 128 41 244t115 205t175 141.5t221 52.5q104 0 177.5 -37t130.5 -110l-81 -97q-8 -10 -20 -17t-27 -7 q-17 0 -31.5 10.5t-33.5 23t-47 23t-73 10.5q-58 0 -111.5 -31.5t-94 -90t-64.5 -140.5t-24 -181q0 -116 51.5 -179t138.5 -63q58 0 96.5 17t65 37t47.5 37t46 17q27 0 45 -24l64 -78q-45 -50 -86.5 -85t-84 -59t-87 -37t-95.5 -17l-19 -47q75 -19 106 -53.5t31 -78.5 q0 -39 -19 -70t-53 -52.5t-80.5 -32.5t-101.5 -11q-39 0 -72 6t-66 18l34 76q9 21 29 21z" />
<glyph unicode="&#xe8;" horiz-adv-x="968" d="M50 0zM940 807q0 -71 -28 -129t-101 -104.5t-197.5 -79.5t-316.5 -53v-13q0 -248 210 -248q45 0 80 9t61 21t47 27.5t39 28t35 21t38 8.5q12 0 24.5 -5.5t20.5 -17.5l64 -77q-54 -54 -106 -94.5t-106.5 -66t-115 -37.5t-131.5 -12q-92 0 -167 31t-128 88t-82.5 137.5 t-29.5 179.5q0 83 18 163t52 151.5t83 132t111 104.5t136 68.5t159 24.5q83 0 145 -23.5t103.5 -61t62 -83.5t20.5 -90zM595 889q-51 0 -95 -21t-79.5 -59t-61.5 -90.5t-42 -115.5q127 17 204.5 38t120 45.5t56.5 52t14 59.5q0 15 -7 31.5t-20.5 29t-35.5 21.5t-54 9z M536 1496q39 0 56.5 -13.5t28.5 -39.5l103 -249h-140q-27 0 -43.5 7t-30.5 28l-203 267h229z" />
<glyph unicode="&#xe9;" horiz-adv-x="968" d="M50 0zM940 807q0 -71 -28 -129t-101 -104.5t-197.5 -79.5t-316.5 -53v-13q0 -248 210 -248q45 0 80 9t61 21t47 27.5t39 28t35 21t38 8.5q12 0 24.5 -5.5t20.5 -17.5l64 -77q-54 -54 -106 -94.5t-106.5 -66t-115 -37.5t-131.5 -12q-92 0 -167 31t-128 88t-82.5 137.5 t-29.5 179.5q0 83 18 163t52 151.5t83 132t111 104.5t136 68.5t159 24.5q83 0 145 -23.5t103.5 -61t62 -83.5t20.5 -90zM595 889q-51 0 -95 -21t-79.5 -59t-61.5 -90.5t-42 -115.5q127 17 204.5 38t120 45.5t56.5 52t14 59.5q0 15 -7 31.5t-20.5 29t-35.5 21.5t-54 9z M984 1496l-268 -267q-20 -20 -39 -27.5t-46 -7.5h-146l164 249q17 27 38 40t60 13h237z" />
<glyph unicode="&#xea;" horiz-adv-x="968" d="M50 0zM940 807q0 -71 -28 -129t-101 -104.5t-197.5 -79.5t-316.5 -53v-13q0 -248 210 -248q45 0 80 9t61 21t47 27.5t39 28t35 21t38 8.5q12 0 24.5 -5.5t20.5 -17.5l64 -77q-54 -54 -106 -94.5t-106.5 -66t-115 -37.5t-131.5 -12q-92 0 -167 31t-128 88t-82.5 137.5 t-29.5 179.5q0 83 18 163t52 151.5t83 132t111 104.5t136 68.5t159 24.5q83 0 145 -23.5t103.5 -61t62 -83.5t20.5 -90zM595 889q-51 0 -95 -21t-79.5 -59t-61.5 -90.5t-42 -115.5q127 17 204.5 38t120 45.5t56.5 52t14 59.5q0 15 -7 31.5t-20.5 29t-35.5 21.5t-54 9z M943 1196h-162q-14 0 -27.5 5.5t-19.5 12.5l-83 102q-8 8 -17 20q-6 -6 -12 -11t-11 -9l-111 -102q-8 -7 -21.5 -12.5t-29.5 -5.5h-169l252 284h230z" />
<glyph unicode="&#xeb;" horiz-adv-x="968" d="M50 0zM940 807q0 -71 -28 -129t-101 -104.5t-197.5 -79.5t-316.5 -53v-13q0 -248 210 -248q45 0 80 9t61 21t47 27.5t39 28t35 21t38 8.5q12 0 24.5 -5.5t20.5 -17.5l64 -77q-54 -54 -106 -94.5t-106.5 -66t-115 -37.5t-131.5 -12q-92 0 -167 31t-128 88t-82.5 137.5 t-29.5 179.5q0 83 18 163t52 151.5t83 132t111 104.5t136 68.5t159 24.5q83 0 145 -23.5t103.5 -61t62 -83.5t20.5 -90zM595 889q-51 0 -95 -21t-79.5 -59t-61.5 -90.5t-42 -115.5q127 17 204.5 38t120 45.5t56.5 52t14 59.5q0 15 -7 31.5t-20.5 29t-35.5 21.5t-54 9z M581 1346q0 -29 -11 -54t-31 -43.5t-46 -29.5t-56 -11q-28 0 -53 11t-44 29.5t-30 43.5t-11 54q0 30 11 55.5t30 45t44 31t53 11.5q30 0 56 -11.5t46 -31t31 -45t11 -55.5zM963 1346q0 -29 -11 -54t-30 -43.5t-45 -29.5t-56 -11q-29 0 -54.5 11t-44.5 29.5t-30 43.5t-11 54 q0 30 11 55.5t30 45t44.5 31t54.5 11.5q30 0 56 -11.5t45 -31t30 -45t11 -55.5z" />
<glyph unicode="&#xec;" horiz-adv-x="527" d="M34 0zM452 1051l-126 -1051h-250l126 1051h250zM263 1496q39 0 56.5 -13.5t28.5 -39.5l103 -249h-140q-27 0 -43.5 7t-30.5 28l-203 267h229z" />
<glyph unicode="&#xed;" horiz-adv-x="527" d="M76 0zM452 1051l-126 -1051h-250l126 1051h250zM710 1496l-268 -267q-20 -20 -39 -27.5t-46 -7.5h-146l164 249q17 27 38 40t60 13h237z" />
<glyph unicode="&#xee;" horiz-adv-x="527" d="M6 0zM452 1051l-126 -1051h-250l126 1051h250zM669 1196h-162q-14 0 -27.5 5.5t-19.5 12.5l-83 102q-8 8 -17 20q-6 -6 -12 -11t-11 -9l-111 -102q-8 -7 -21.5 -12.5t-29.5 -5.5h-169l252 284h230z" />
<glyph unicode="&#xef;" horiz-adv-x="527" d="M25 0zM452 1051l-126 -1051h-250l126 1051h250zM307 1346q0 -29 -11 -54t-31 -43.5t-46 -29.5t-56 -11q-28 0 -53 11t-44 29.5t-30 43.5t-11 54q0 30 11 55.5t30 45t44 31t53 11.5q30 0 56 -11.5t46 -31t31 -45t11 -55.5zM689 1346q0 -29 -11 -54t-30 -43.5t-45 -29.5 t-56 -11q-29 0 -54.5 11t-44.5 29.5t-30 43.5t-11 54q0 30 11 55.5t30 45t44.5 31t54.5 11.5q30 0 56 -11.5t45 -31t30 -45t11 -55.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1070" d="M440 1085q-7 11 -7 25q0 25 22 38l118 77q-69 36 -154 61q-50 16 -50 54q0 20 12 45l37 78q96 -17 183 -51t161 -87l188 117l41 -80q7 -14 7 -27q0 -25 -25 -42l-105 -66q77 -87 120 -207t43 -277q0 -174 -38.5 -314t-111.5 -239t-180 -152t-243 -53q-89 0 -164 29.5 t-128.5 85.5t-84 135.5t-30.5 179.5q0 110 37.5 207.5t103.5 171t156 116.5t197 43q96 0 169 -38.5t123 -115.5q-5 112 -38 195t-101 147l-212 -136zM487 171q52 0 100 21t87.5 69t69 126t46.5 192q-10 38 -28 72.5t-44 60t-62 41t-85 15.5q-65 0 -116.5 -26.5t-87 -72.5 t-54.5 -108.5t-19 -134.5q0 -60 14.5 -108t40 -80.5t61 -49.5t77.5 -17z" />
<glyph unicode="&#xf1;" horiz-adv-x="1102" d="M53 0zM53 0l126 1052h129q40 0 62 -19.5t22 -62.5l-7 -136q80 118 177 175.5t201 57.5q58 0 105.5 -21t81.5 -61.5t52.5 -99.5t18.5 -135q0 -19 -1.5 -39.5t-3.5 -41.5l-79 -669h-253q20 173 34.5 293.5t24 200t14.5 125.5t7.5 69.5t3 31t0.5 12.5q0 68 -24.5 98 t-76.5 30q-42 0 -87 -24.5t-86 -69.5t-75 -108.5t-55 -141.5l-58 -516h-253zM777 1390q27 0 44 17t25 61h147q-7 -56 -27.5 -101.5t-50.5 -78t-68.5 -50t-81.5 -17.5q-34 0 -62 13t-51.5 29t-44 29.5t-39.5 13.5q-27 0 -44 -18.5t-22 -61.5h-150q7 56 27.5 101.5t51.5 78.5 t69 51t80 18q34 0 62.5 -13.5t52.5 -29.5t44 -29t38 -13z" />
<glyph unicode="&#xf2;" horiz-adv-x="1044" d="M44 0zM471 179q63 0 114.5 36.5t88 97.5t56 142t19.5 170q0 128 -43.5 188t-132.5 60q-63 0 -114 -35.5t-87 -97t-55.5 -142.5t-19.5 -171q0 -128 43 -188t131 -60zM449 -15q-87 0 -161.5 29t-128.5 85.5t-84.5 138t-30.5 187.5q0 136 43.5 254t119 204t175.5 135t214 49 q87 0 161 -29t128.5 -85.5t85 -138.5t30.5 -187q0 -135 -44 -252.5t-119 -204t-175 -136t-214 -49.5zM525 1498q39 0 56.5 -13.5t28.5 -39.5l103 -249h-140q-27 0 -43.5 7t-30.5 28l-203 267h229z" />
<glyph unicode="&#xf3;" horiz-adv-x="1044" d="M44 0zM471 179q63 0 114.5 36.5t88 97.5t56 142t19.5 170q0 128 -43.5 188t-132.5 60q-63 0 -114 -35.5t-87 -97t-55.5 -142.5t-19.5 -171q0 -128 43 -188t131 -60zM449 -15q-87 0 -161.5 29t-128.5 85.5t-84.5 138t-30.5 187.5q0 136 43.5 254t119 204t175.5 135t214 49 q87 0 161 -29t128.5 -85.5t85 -138.5t30.5 -187q0 -135 -44 -252.5t-119 -204t-175 -136t-214 -49.5zM973 1498l-268 -267q-20 -20 -39 -27.5t-46 -7.5h-146l164 249q17 27 38 40t60 13h237z" />
<glyph unicode="&#xf4;" horiz-adv-x="1044" d="M44 0zM471 179q63 0 114.5 36.5t88 97.5t56 142t19.5 170q0 128 -43.5 188t-132.5 60q-63 0 -114 -35.5t-87 -97t-55.5 -142.5t-19.5 -171q0 -128 43 -188t131 -60zM449 -15q-87 0 -161.5 29t-128.5 85.5t-84.5 138t-30.5 187.5q0 136 43.5 254t119 204t175.5 135t214 49 q87 0 161 -29t128.5 -85.5t85 -138.5t30.5 -187q0 -135 -44 -252.5t-119 -204t-175 -136t-214 -49.5zM932 1198h-162q-14 0 -27.5 5.5t-19.5 12.5l-83 102q-8 8 -17 20q-6 -6 -12 -11t-11 -9l-111 -102q-8 -7 -21.5 -12.5t-29.5 -5.5h-169l252 284h230z" />
<glyph unicode="&#xf5;" horiz-adv-x="1044" d="M44 0zM471 179q63 0 114.5 36.5t88 97.5t56 142t19.5 170q0 128 -43.5 188t-132.5 60q-63 0 -114 -35.5t-87 -97t-55.5 -142.5t-19.5 -171q0 -128 43 -188t131 -60zM449 -15q-87 0 -161.5 29t-128.5 85.5t-84.5 138t-30.5 187.5q0 136 43.5 254t119 204t175.5 135t214 49 q87 0 161 -29t128.5 -85.5t85 -138.5t30.5 -187q0 -135 -44 -252.5t-119 -204t-175 -136t-214 -49.5zM727 1390q27 0 44 17t25 61h147q-7 -56 -27.5 -101.5t-50.5 -78t-68.5 -50t-81.5 -17.5q-34 0 -62 13t-51.5 29t-44 29.5t-39.5 13.5q-27 0 -44 -18.5t-22 -61.5h-150 q7 56 27.5 101.5t51.5 78.5t69 51t80 18q34 0 62.5 -13.5t52.5 -29.5t44 -29t38 -13z" />
<glyph unicode="&#xf6;" horiz-adv-x="1044" d="M44 0zM471 179q63 0 114.5 36.5t88 97.5t56 142t19.5 170q0 128 -43.5 188t-132.5 60q-63 0 -114 -35.5t-87 -97t-55.5 -142.5t-19.5 -171q0 -128 43 -188t131 -60zM449 -15q-87 0 -161.5 29t-128.5 85.5t-84.5 138t-30.5 187.5q0 136 43.5 254t119 204t175.5 135t214 49 q87 0 161 -29t128.5 -85.5t85 -138.5t30.5 -187q0 -135 -44 -252.5t-119 -204t-175 -136t-214 -49.5zM570 1348q0 -29 -11 -54t-31 -43.5t-46 -29.5t-56 -11q-28 0 -53 11t-44 29.5t-30 43.5t-11 54q0 30 11 55.5t30 45t44 31t53 11.5q30 0 56 -11.5t46 -31t31 -45t11 -55.5 zM952 1348q0 -29 -11 -54t-30 -43.5t-45 -29.5t-56 -11q-29 0 -54.5 11t-44.5 29.5t-30 43.5t-11 54q0 30 11 55.5t30 45t44.5 31t54.5 11.5q30 0 56 -11.5t45 -31t30 -45t11 -55.5z" />
<glyph unicode="&#xf7;" d="M129 778h997l-22 -181h-998zM508 1034q0 35 14 65.5t37 54t54 36.5t66 13q29 0 54 -10.5t42.5 -29t27.5 -43t10 -53.5q0 -36 -14 -66t-38 -52t-54.5 -35t-64.5 -13q-30 0 -55 10.5t-42.5 28t-27 42t-9.5 52.5zM418 305q0 35 13.5 65.5t37 53.5t54.5 36.5t66 13.5 q29 0 54 -10.5t42 -29t27.5 -43t10.5 -53.5q0 -36 -14 -66t-38 -52t-54.5 -35t-64.5 -13q-30 0 -55 10.5t-42.5 28t-27 42t-9.5 52.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1044" d="M449 -15q-120 0 -210 52l-11 -13q-30 -41 -70.5 -57.5t-80.5 -16.5h-93l140 191q-39 54 -59.5 125t-20.5 159q0 136 43.5 254t119 204t175.5 135t214 49q119 0 211 -53l13 18q13 18 25 31t24.5 21.5t27.5 12t38 3.5h125l-139 -191q39 -54 59.5 -124.5t20.5 -157.5 q0 -135 -44 -252.5t-119 -204t-175 -136t-214 -49.5zM281 427q0 -49 8 -93l382 526q-43 29 -98 29q-63 0 -117 -34t-92.5 -95t-60.5 -146t-22 -187zM471 163q63 0 117 34t93 95.5t61 146t22 186.5q0 25 -2 47t-6 43l-382 -525q41 -27 97 -27z" />
<glyph unicode="&#xf9;" horiz-adv-x="1100" d="M82 0zM419 1051q-20 -173 -34.5 -293t-24 -200t-14.5 -126t-7.5 -69.5t-3 -31.5t-0.5 -14q0 -68 25 -97t76 -29q40 0 82.5 22.5t82 63t73.5 99t57 130.5l66 545h253l-127 -1051h-128q-17 0 -33.5 4.5t-29 14t-20 23.5t-7.5 34v4l6 128q-80 -113 -175 -168t-196 -55 q-58 0 -106 21t-81.5 60.5t-52 98.5t-18.5 135q0 20 1 40.5t4 43.5l79 667h253zM542 1498q39 0 56.5 -13.5t28.5 -39.5l103 -249h-140q-27 0 -43.5 7t-30.5 28l-203 267h229z" />
<glyph unicode="&#xfa;" horiz-adv-x="1100" d="M82 0zM419 1051q-20 -173 -34.5 -293t-24 -200t-14.5 -126t-7.5 -69.5t-3 -31.5t-0.5 -14q0 -68 25 -97t76 -29q40 0 82.5 22.5t82 63t73.5 99t57 130.5l66 545h253l-127 -1051h-128q-17 0 -33.5 4.5t-29 14t-20 23.5t-7.5 34v4l6 128q-80 -113 -175 -168t-196 -55 q-58 0 -106 21t-81.5 60.5t-52 98.5t-18.5 135q0 20 1 40.5t4 43.5l79 667h253zM990 1498l-268 -267q-20 -20 -39 -27.5t-46 -7.5h-146l164 249q17 27 38 40t60 13h237z" />
<glyph unicode="&#xfb;" horiz-adv-x="1100" d="M82 0zM419 1051q-20 -173 -34.5 -293t-24 -200t-14.5 -126t-7.5 -69.5t-3 -31.5t-0.5 -14q0 -68 25 -97t76 -29q40 0 82.5 22.5t82 63t73.5 99t57 130.5l66 545h253l-127 -1051h-128q-17 0 -33.5 4.5t-29 14t-20 23.5t-7.5 34v4l6 128q-80 -113 -175 -168t-196 -55 q-58 0 -106 21t-81.5 60.5t-52 98.5t-18.5 135q0 20 1 40.5t4 43.5l79 667h253zM949 1198h-162q-14 0 -27.5 5.5t-19.5 12.5l-83 102q-8 8 -17 20q-6 -6 -12 -11t-11 -9l-111 -102q-8 -7 -21.5 -12.5t-29.5 -5.5h-169l252 284h230z" />
<glyph unicode="&#xfc;" horiz-adv-x="1100" d="M82 0zM419 1051q-20 -173 -34.5 -293t-24 -200t-14.5 -126t-7.5 -69.5t-3 -31.5t-0.5 -14q0 -68 25 -97t76 -29q40 0 82.5 22.5t82 63t73.5 99t57 130.5l66 545h253l-127 -1051h-128q-17 0 -33.5 4.5t-29 14t-20 23.5t-7.5 34v4l6 128q-80 -113 -175 -168t-196 -55 q-58 0 -106 21t-81.5 60.5t-52 98.5t-18.5 135q0 20 1 40.5t4 43.5l79 667h253zM587 1348q0 -29 -11 -54t-31 -43.5t-46 -29.5t-56 -11q-28 0 -53 11t-44 29.5t-30 43.5t-11 54q0 30 11 55.5t30 45t44 31t53 11.5q30 0 56 -11.5t46 -31t31 -45t11 -55.5zM969 1348 q0 -29 -11 -54t-30 -43.5t-45 -29.5t-56 -11q-29 0 -54.5 11t-44.5 29.5t-30 43.5t-11 54q0 30 11 55.5t30 45t44.5 31t54.5 11.5q30 0 56 -11.5t45 -31t30 -45t11 -55.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1017" d="M79 0zM380 -291q-27 -52 -82 -52h-187l233 423l-265 971h212q28 0 41.5 -14t18.5 -33l129 -545q6 -27 10 -52.5t6 -52.5q11 27 22.5 53t24.5 53l259 545q10 20 29.5 33t39.5 13h203zM960 1498l-268 -267q-20 -20 -39 -27.5t-46 -7.5h-146l164 249q17 27 38 40t60 13h237z " />
<glyph unicode="&#xfe;" horiz-adv-x="1094" d="M19 -343l228 1865h249l-84 -678q74 103 164.5 163t192.5 60q66 0 119 -24t91 -72t58.5 -122t20.5 -173q0 -89 -18.5 -175t-52.5 -163t-80.5 -141.5t-104 -111.5t-122.5 -73.5t-134 -26.5q-71 0 -128.5 25t-100.5 70l-41 -339q-4 -33 -30.5 -58.5t-69.5 -25.5h-157z M670 868q-41 0 -83.5 -28t-82.5 -77t-75 -116t-59 -144l-32 -258q35 -37 80 -52.5t89 -15.5q68 0 123 42t94.5 109.5t61 152t21.5 169.5q0 111 -36 164.5t-101 53.5z" />
<glyph unicode="&#xff;" horiz-adv-x="1017" d="M79 0zM380 -291q-27 -52 -82 -52h-187l233 423l-265 971h212q28 0 41.5 -14t18.5 -33l129 -545q6 -27 10 -52.5t6 -52.5q11 27 22.5 53t24.5 53l259 545q10 20 29.5 33t39.5 13h203zM557 1348q0 -29 -11 -54t-31 -43.5t-46 -29.5t-56 -11q-28 0 -53 11t-44 29.5t-30 43.5 t-11 54q0 30 11 55.5t30 45t44 31t53 11.5q30 0 56 -11.5t46 -31t31 -45t11 -55.5zM939 1348q0 -29 -11 -54t-30 -43.5t-45 -29.5t-56 -11q-29 0 -54.5 11t-44.5 29.5t-30 43.5t-11 54q0 30 11 55.5t30 45t44.5 31t54.5 11.5q30 0 56 -11.5t45 -31t30 -45t11 -55.5z" />
<glyph unicode="&#x104;" horiz-adv-x="1313" d="M1243 -195q18 0 22 -16l24 -90q-33 -23 -82 -37.5t-101 -14.5q-87 0 -132 37.5t-45 96.5q0 61 41 117t117 102h-26q-35 0 -55.5 16.5t-25.5 43.5l-61 310h-552l-138 -310q-11 -24 -36.5 -42t-57.5 -18h-209l716 1481h271l354 -1481h-34q-22 -11 -43 -27t-36.5 -35.5 t-26 -42t-10.5 -47.5q0 -27 15 -43t42 -16q17 0 28 2.5t19 5.5t12.5 5.5t9.5 2.5zM450 557h431l-101 507q-6 33 -15 76t-16 94q-19 -51 -38.5 -94.5t-35.5 -76.5z" />
<glyph unicode="&#x105;" horiz-adv-x="1068" d="M865 -195q18 0 22 -16l24 -90q-33 -23 -82 -37.5t-101 -14.5q-87 0 -132 37.5t-45 96.5q0 61 42 118.5t122 103.5q-30 8 -41 29.5t-11 51.5l8 135q-70 -109 -157 -171.5t-191 -62.5q-59 0 -111 23.5t-90.5 70t-60.5 116.5t-22 164t23.5 183.5t67 167.5t105 144t138 113 t165 73t186.5 26q71 0 142 -10t140 -37l-127 -1019h-24q-22 -11 -42.5 -27t-36.5 -35.5t-26.5 -42t-10.5 -47.5q0 -27 15 -43t42 -16q17 0 28.5 2.5t19 5.5t12 5.5t9.5 2.5zM422 191q42 0 82.5 29t76 79.5t66.5 119t53 147.5l40 318q-28 3 -51 3q-85 0 -157.5 -39t-126 -105 t-83.5 -153.5t-30 -185.5q0 -111 35 -162t95 -51z" />
<glyph unicode="&#x106;" horiz-adv-x="1259" d="M76 0zM694 209q62 0 109 9.5t81.5 25t59 32.5t43 32.5t32.5 25t29 9.5q12 0 20 -5t13 -10l94 -116q-96 -111 -226.5 -169.5t-299.5 -58.5q-136 0 -242.5 48.5t-180 134.5t-112 203.5t-38.5 257.5q0 128 27.5 242t78.5 211t123 173.5t159 131t188 83t210 28.5 q81 0 147.5 -15t121 -41.5t97.5 -64t78 -83.5l-93 -110q-10 -12 -23.5 -21.5t-34.5 -9.5q-23 0 -43 18.5t-51.5 40.5t-81 40.5t-130.5 18.5q-106 0 -197.5 -43.5t-159 -124.5t-106 -195t-38.5 -257q0 -103 26 -185t72.5 -139t109.5 -87t138 -30zM1285 1832l-291 -211 q-24 -17 -46 -26t-49 -9h-198l209 207q12 12 23 20t23 12t25.5 5.5t34.5 1.5h269z" />
<glyph unicode="&#x107;" horiz-adv-x="891" d="M51 0zM858 186q-52 -57 -99.5 -96t-97 -62.5t-103.5 -33t-119 -9.5q-91 0 -163 31t-122.5 88.5t-76.5 138.5t-26 180q0 128 41 244t115 205t175 141.5t221 52.5q104 0 177.5 -37t130.5 -110l-81 -97q-9 -9 -20.5 -16.5t-26.5 -7.5q-17 0 -31.5 10.5t-33.5 23t-47 23 t-73 10.5q-58 0 -111.5 -31.5t-94 -90t-64.5 -140.5t-24 -181q0 -116 51.5 -179t138.5 -63q58 0 96.5 17t65 37t47.5 37t46 17q12 0 23.5 -7t21.5 -17l64 -78v0zM957 1498l-268 -267q-20 -20 -39 -27.5t-46 -7.5h-146l164 249q17 27 38 40t60 13h237z" />
<glyph unicode="&#x118;" horiz-adv-x="1076" d="M843 -195q18 0 21 -16l25 -90q-33 -23 -82 -37.5t-101 -14.5q-87 0 -132.5 37.5t-45.5 96.5q0 61 41 117t118 102h-615l182 1481h863l-26 -212h-596l-52 -421h468l-25 -204h-468l-53 -432h598l-27 -212h-103q-22 -11 -43 -27t-37 -35.5t-26 -42t-10 -47.5q0 -27 14.5 -43 t41.5 -16q17 0 28.5 2.5t19 5.5t12.5 5.5t10 2.5z" />
<glyph unicode="&#x119;" horiz-adv-x="968" d="M604 -195q18 0 22 -16l24 -90q-33 -23 -82 -37.5t-101 -14.5q-87 0 -132 37.5t-45 96.5q0 56 34.5 108.5t101.5 96.5q-85 5 -154.5 38t-119 89.5t-76 134.5t-26.5 173q0 83 18 163t52 151.5t83 132t111 104.5t136 68.5t159 24.5q83 0 145 -23.5t103.5 -61t62 -83.5 t20.5 -90q0 -71 -28 -129t-101 -104.5t-197.5 -79.5t-316.5 -53v-13q0 -248 210 -248q45 0 80 9t61 21t47 27.5t39 28t35 21t38 8.5q12 0 24 -6t21 -17l64 -77q-82 -82 -159.5 -131t-168.5 -67q-20 -11 -40.5 -27t-35.5 -35t-24.5 -40.5t-9.5 -46.5q0 -27 15 -43t42 -16 q17 0 28 2.5t19 5.5t12.5 5.5t9.5 2.5zM595 889q-51 0 -95 -21t-79.5 -59t-61.5 -90.5t-42 -115.5q127 17 204.5 38t120 45.5t56.5 52t14 59.5q0 15 -7 31.5t-20.5 29t-35.5 21.5t-54 9z" />
<glyph unicode="&#x131;" horiz-adv-x="527" d="M452 1051l-126 -1051h-250l126 1051h250z" />
<glyph unicode="&#x141;" horiz-adv-x="1040" d="M512 893l343 175l-22 -178q-2 -19 -12.5 -32t-26.5 -21l-310 -165l-55 -452h539l-26 -220h-805l67 546l-193 -94l24 181q4 34 35 49l160 82l89 717h266z" />
<glyph unicode="&#x142;" horiz-adv-x="649" d="M74 755q0 36 30 49l137 57l80 661h248l-69 -569l160 66v-146q0 -40 -34 -55l-150 -64l-91 -754h-249l80 663l-142 -58v150z" />
<glyph unicode="&#x143;" horiz-adv-x="1425" d="M72 0zM393 1481q17 0 29 -1.5t21.5 -6.5t16.5 -13t15 -22l577 -1011q2 30 4.5 58t5.5 53l116 943h234l-183 -1481h-136q-31 0 -52.5 9.5t-35.5 36.5l-574 1011q-2 -25 -4 -48t-4 -43l-117 -966h-234l182 1481h139v0zM1258 1832l-291 -211q-24 -17 -46 -26t-49 -9h-198 l209 207q12 12 23 20t23 12t25.5 5.5t34.5 1.5h269z" />
<glyph unicode="&#x144;" horiz-adv-x="1102" d="M53 0zM53 0l126 1052h129q40 0 62 -19.5t22 -62.5l-7 -136q80 118 177 175.5t201 57.5q58 0 105.5 -21t81.5 -61.5t52.5 -99.5t18.5 -135q0 -19 -1.5 -39.5t-3.5 -41.5l-79 -669h-253q20 173 34.5 293.5t24 200t14.5 125.5t7.5 69.5t3 31t0.5 12.5q0 68 -24.5 98 t-76.5 30q-42 0 -87 -24.5t-86 -69.5t-75 -108.5t-55 -141.5l-58 -516h-253zM1023 1498l-268 -267q-20 -20 -39 -27.5t-46 -7.5h-146l164 249q17 27 38 40t60 13h237z" />
<glyph unicode="&#x152;" horiz-adv-x="2027" d="M2068 1481l-26 -212h-595l-52 -422h467l-25 -204h-467l-52 -431h594l-26 -212h-831l21 177q-90 -92 -206 -142t-254 -50q-126 0 -226 49t-169.5 135t-107 204.5t-37.5 256.5q0 124 25 236.5t70.5 208.5t111 174t146 133.5t174.5 85t198 29.5q138 0 241 -59t169 -161 l26 204h831zM1121 844q0 98 -21 179.5t-61.5 140t-99.5 90.5t-136 32q-103 0 -187 -47.5t-143.5 -133t-92 -205.5t-32.5 -264q0 -98 21 -179t61.5 -139.5t100.5 -90t138 -31.5q102 0 185.5 47t143 132.5t91.5 205t32 263.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1594" d="M1260 1066q70 0 126.5 -19t96.5 -54t62 -83.5t22 -106.5q0 -68 -28.5 -125.5t-99 -104t-192 -79.5t-307.5 -52q3 -135 58.5 -198.5t151.5 -63.5q69 0 113 18t75 39.5t56 39.5t55 18q29 0 46 -23l65 -77q-54 -54 -106 -94.5t-107.5 -66t-116 -37.5t-130.5 -12 q-97 0 -178.5 49t-127.5 149q-75 -95 -178 -146.5t-227 -51.5q-85 0 -150 31t-109 83.5t-66.5 122t-22.5 146.5q0 171 46.5 301.5t125 218.5t181 133t212.5 45q101 0 173.5 -43t114.5 -120q67 77 158 120t208 43zM450 172q58 0 102.5 23t77.5 61.5t54.5 88.5t34.5 104.5 t18.5 108.5t5.5 102q0 103 -39 160.5t-120 57.5q-70 0 -125 -36t-93 -101t-58.5 -156.5t-20.5 -203.5q0 -41 8 -79t27.5 -66.5t51 -46t76.5 -17.5zM1220 891q-100 0 -172.5 -76t-97.5 -219q124 16 203 38.5t124 48.5t62 54t17 57q0 39 -32.5 68t-103.5 29z" />
<glyph unicode="&#x15a;" horiz-adv-x="1012" d="M0 0zM957 1214q-14 -20 -28.5 -29.5t-34.5 -9.5t-42 16t-51.5 36t-70 36.5t-97.5 16.5q-56 0 -99.5 -16.5t-72.5 -45t-44 -67.5t-15 -84q0 -47 24.5 -78.5t63 -55t89 -42t102 -38.5t102 -47t89 -66t62.5 -93.5t24 -132.5q0 -109 -37 -205t-107.5 -168t-170 -114.5 t-224.5 -42.5q-65 0 -127.5 13.5t-117.5 39t-102 61.5t-82 80l94 127q11 14 28.5 23.5t34.5 9.5q16 0 32.5 -10.5t35 -25t41 -32.5t51 -33t64.5 -25t83 -10q116 0 181 65.5t65 181.5q0 51 -23.5 83.5t-62.5 55t-88 39.5t-100.5 36t-101 44t-88 64.5t-62 97t-23.5 141.5 q0 95 35 183t100.5 155t159.5 107.5t211 40.5q60 0 116 -12.5t104.5 -35t89 -53.5t69.5 -69zM1103 1832l-291 -211q-24 -17 -46 -26t-49 -9h-198l209 207q12 12 23 20t23 12t25.5 5.5t34.5 1.5h269z" />
<glyph unicode="&#x15b;" horiz-adv-x="803" d="M0 0zM751 853q-11 -16 -21.5 -23t-27.5 -7t-35.5 10.5t-42.5 23t-55 23t-75 10.5q-81 0 -124 -36.5t-43 -90.5q0 -32 17.5 -54t46.5 -38.5t66 -30t75.5 -29.5t75.5 -36t65.5 -48.5t46.5 -69t18 -97.5q0 -76 -30 -144.5t-86.5 -120t-136 -81.5t-178.5 -30q-50 0 -96.5 9.5 t-87.5 27t-74.5 41t-56.5 51.5l61 96q11 17 27 26.5t38 9.5t39 -13.5t40.5 -29.5t57 -29t85.5 -13q42 0 74 11t54 30.5t33.5 45t11.5 54.5q0 35 -17.5 58t-46.5 40t-66 29.5t-75.5 27t-74.5 33t-65.5 46.5t-47 68.5t-17.5 98.5q0 70 27 136t78.5 117t127 81.5t172.5 30.5 q100 0 177.5 -34.5t130.5 -87.5zM885 1498l-268 -267q-20 -20 -39 -27.5t-46 -7.5h-146l164 249q17 27 38 40t60 13h237z" />
<glyph unicode="&#x160;" horiz-adv-x="1012" d="M0 0zM957 1214q-14 -20 -28.5 -29.5t-34.5 -9.5t-42 16t-51.5 36t-70 36.5t-97.5 16.5q-56 0 -99.5 -16.5t-72.5 -45t-44 -67.5t-15 -84q0 -47 24.5 -78.5t63 -55t89 -42t102 -38.5t102 -47t89 -66t62.5 -93.5t24 -132.5q0 -109 -37 -205t-107.5 -168t-170 -114.5 t-224.5 -42.5q-65 0 -127.5 13.5t-117.5 39t-102 61.5t-82 80l94 127q11 14 28.5 23.5t34.5 9.5q16 0 32.5 -10.5t35 -25t41 -32.5t51 -33t64.5 -25t83 -10q116 0 181 65.5t65 181.5q0 51 -23.5 83.5t-62.5 55t-88 39.5t-100.5 36t-101 44t-88 64.5t-62 97t-23.5 141.5 q0 95 35 183t100.5 155t159.5 107.5t211 40.5q60 0 116 -12.5t104.5 -35t89 -53.5t69.5 -69zM352 1809h222l116 -100l140 100h222l-256 -223h-244z" />
<glyph unicode="&#x161;" horiz-adv-x="803" d="M0 0zM751 853q-11 -16 -21.5 -23t-27.5 -7t-35.5 10.5t-42.5 23t-55 23t-75 10.5q-81 0 -124 -36.5t-43 -90.5q0 -32 17.5 -54t46.5 -38.5t66 -30t75.5 -29.5t75.5 -36t65.5 -48.5t46.5 -69t18 -97.5q0 -76 -30 -144.5t-86.5 -120t-136 -81.5t-178.5 -30q-50 0 -96.5 9.5 t-87.5 27t-74.5 41t-56.5 51.5l61 96q11 17 27 26.5t38 9.5t39 -13.5t40.5 -29.5t57 -29t85.5 -13q42 0 74 11t54 30.5t33.5 45t11.5 54.5q0 35 -17.5 58t-46.5 40t-66 29.5t-75.5 27t-74.5 33t-65.5 46.5t-47 68.5t-17.5 98.5q0 70 27 136t78.5 117t127 81.5t172.5 30.5 q100 0 177.5 -34.5t130.5 -87.5zM627 1198h-229l-181 284h168q14 0 27 -6t19 -13l84 -102q8 -10 18 -24q6 7 12.5 13t11.5 11l109 102q7 8 21.5 13.5t29.5 5.5h162z" />
<glyph unicode="&#x178;" horiz-adv-x="1241" d="M112 0zM763 577l-72 -577h-266l72 578l-385 903h236q35 0 53.5 -17t28.5 -44l192 -512q14 -37 22.5 -72t14.5 -69q16 34 35.5 68.5t42.5 72.5l319 512q14 24 37 42.5t57 18.5h220zM710 1704q0 -25 -10.5 -47t-27.5 -38t-40 -26t-48 -10t-47 10t-39 26t-26.5 38t-9.5 47 q0 26 9.5 49t26.5 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5t27.5 -39.5t10.5 -49zM1069 1704q0 -25 -9.5 -47t-26.5 -38t-39.5 -26t-48.5 -10t-47.5 10t-38.5 26t-27 38t-10 47q0 26 10 49t27 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5t26.5 -39.5t9.5 -49z" />
<glyph unicode="&#x179;" horiz-adv-x="1160" d="M0 0zM1233 1481l-11 -83q-2 -17 -10.5 -32.5t-19.5 -31.5l-818 -1122h686l-27 -212h-1040l10 81q2 17 9.5 33t19.5 30l820 1125h-657l25 212h1013zM1160 1831l-291 -211q-24 -17 -46 -26t-49 -9h-198l209 207q12 12 23 20t23 12t25.5 5.5t34.5 1.5h269z" />
<glyph unicode="&#x17a;" horiz-adv-x="897" d="M0 0zM890 946q-3 -27 -14 -52.5t-27 -41.5l-525 -657h461l-22 -195h-764l13 105q2 17 13 42t28 46l530 664h-452l24 194h748zM912 1498l-268 -267q-20 -20 -39 -27.5t-46 -7.5h-146l164 249q17 27 38 40t60 13h237z" />
<glyph unicode="&#x17b;" horiz-adv-x="1160" d="M0 0zM1233 1481l-11 -83q-2 -17 -10.5 -32.5t-19.5 -31.5l-818 -1122h686l-27 -212h-1040l10 81q2 17 9.5 33t19.5 30l820 1125h-657l25 212h1013zM903 1736q0 -31 -13 -58t-34.5 -48t-50 -33.5t-60.5 -12.5q-30 0 -57.5 12.5t-48.5 33.5t-33 48t-12 58q0 32 12 60t33 49 t49 33t57 12q32 0 60.5 -12t50 -33t34.5 -49t13 -60z" />
<glyph unicode="&#x17c;" horiz-adv-x="897" d="M0 0zM890 946q-3 -27 -14 -52.5t-27 -41.5l-525 -657h461l-22 -195h-764l13 105q2 17 13 42t28 46l530 664h-452l24 194h748zM731 1363q0 -33 -13.5 -62t-36 -50.5t-52.5 -34.5t-63 -13t-62 13t-50.5 34.5t-34.5 50.5t-13 62q0 34 13 63t34.5 51t50.5 35t62 13 q34 0 63.5 -13t52 -35t36 -51t13.5 -63z" />
<glyph unicode="&#x17d;" horiz-adv-x="1160" d="M0 0zM1233 1481l-11 -83q-2 -17 -10.5 -32.5t-19.5 -31.5l-818 -1122h686l-27 -212h-1040l10 81q2 17 9.5 33t19.5 30l820 1125h-657l25 212h1013zM420 1808h222l116 -100l140 100h222l-256 -223h-244z" />
<glyph unicode="&#x17e;" horiz-adv-x="897" d="M0 0zM890 946q-3 -27 -14 -52.5t-27 -41.5l-525 -657h461l-22 -195h-764l13 105q2 17 13 42t28 46l530 664h-452l24 194h748zM654 1198h-229l-181 284h168q14 0 27 -6t19 -13l84 -102q8 -10 18 -24q6 7 12.5 13t11.5 11l109 102q7 8 21.5 13.5t29.5 5.5h162z" />
<glyph unicode="&#x192;" d="M752 766l-173 -711q-27 -108 -74.5 -185.5t-120 -127t-168.5 -73t-219 -23.5l16 127q9 76 87 75q42 0 79 11.5t68 37t54 66t37 99.5l172 699l-129 18q-48 11 -48 50q0 2 0.5 5.5t0.5 5.5l11 100h209l38 155q49 204 190.5 307t389.5 103l-16 -134q-5 -39 -26 -54t-61 -15 q-42 0 -80 -11.5t-69 -37t-55 -66.5t-40 -100l-38 -147h307l-22 -174h-320z" />
<glyph unicode="&#x2c6;" horiz-adv-x="605" d="M719 1196h-162q-14 0 -27.5 5.5t-19.5 12.5l-83 102q-8 8 -17 20q-6 -6 -12 -11t-11 -9l-111 -102q-8 -7 -21.5 -12.5t-29.5 -5.5h-169l252 284h230z" />
<glyph unicode="&#x2c7;" horiz-adv-x="605" d="M502 1196h-229l-181 284h168q14 0 27 -6t19 -13l84 -102q8 -10 18 -24q6 7 12.5 13t11.5 11l109 102q7 8 21.5 13.5t29.5 5.5h162z" />
<glyph unicode="&#x2d8;" horiz-adv-x="605" d="M389 1189q-72 0 -121 18t-79 49.5t-43.5 74.5t-13.5 92q0 14 1 28t3 29h171q-3 -22 -3 -40q0 -46 22.5 -71.5t82.5 -25.5q36 0 60 9.5t39.5 27.5t24 43t11.5 57h170q-7 -59 -28 -112t-59.5 -93t-97 -63t-140.5 -23z" />
<glyph unicode="&#x2d9;" horiz-adv-x="605" d="M579 1361q0 -33 -13.5 -62t-36 -50.5t-52.5 -34.5t-63 -13t-62 13t-50.5 34.5t-34.5 50.5t-13 62q0 34 13 63t34.5 51t50.5 35t62 13q34 0 63.5 -13t52 -35t36 -51t13.5 -63z" />
<glyph unicode="&#x2da;" horiz-adv-x="605" d="M178 1357q0 47 18.5 86t50 66.5t72 43t85.5 15.5q46 0 88.5 -15.5t74 -43t50.5 -66.5t19 -86q0 -46 -19 -84t-50.5 -65t-73.5 -42t-89 -15q-45 0 -85.5 15t-72 42t-50 65t-18.5 84zM315 1357q0 -39 24.5 -65t69.5 -26q41 0 65.5 26t24.5 65q0 43 -24.5 68t-65.5 25 q-45 0 -69.5 -25t-24.5 -68z" />
<glyph unicode="&#x2db;" horiz-adv-x="605" d="M355 -195q18 0 22 -16l24 -90q-33 -23 -82 -37.5t-101 -14.5q-87 0 -132 37.5t-45 96.5q0 65 46.5 124.5t133.5 106.5l124 -12q-22 -11 -42.5 -27t-36.5 -35.5t-26.5 -42t-10.5 -47.5q0 -27 15 -43t42 -16q17 0 28.5 2.5t19 5.5t12 5.5t9.5 2.5z" />
<glyph unicode="&#x2dc;" horiz-adv-x="605" d="M514 1388q27 0 44 17t25 61h147q-7 -56 -27.5 -101.5t-50.5 -78t-68.5 -50t-81.5 -17.5q-34 0 -62 13t-51.5 29t-44 29.5t-39.5 13.5q-27 0 -44 -18.5t-22 -61.5h-150q7 56 27.5 101.5t51.5 78.5t69 51t80 18q34 0 62.5 -13.5t52.5 -29.5t44 -29t38 -13z" />
<glyph unicode="&#x2dd;" horiz-adv-x="605" d="M553 1496l-220 -267q-17 -22 -38 -28.5t-47 -6.5h-101l149 249q16 27 37.5 40t60.5 13h159zM887 1496l-273 -267q-20 -19 -38 -27t-46 -8h-115l197 249q19 25 40 39t60 14h175z" />
<glyph unicode="&#x3c0;" horiz-adv-x="1222" d="M1254 1050l-12 -99q-4 -31 -26 -53t-58 -22h-96l-109 -876h-242l105 876h-308l-76 -607q-8 -65 -29 -117t-57.5 -88.5t-89.5 -57t-126 -20.5q-34 0 -70.5 7t-69.5 26l19 102q4 20 17.5 30t51.5 10q29 0 48 6t32 20t20 35t11 50l74 604h-167l12 87q2 14 9.5 29.5t21 28 t31 21t39.5 8.5h1045z" />
<glyph unicode="&#x2000;" horiz-adv-x="959" />
<glyph unicode="&#x2001;" horiz-adv-x="1919" />
<glyph unicode="&#x2002;" horiz-adv-x="959" />
<glyph unicode="&#x2003;" horiz-adv-x="1919" />
<glyph unicode="&#x2004;" horiz-adv-x="639" />
<glyph unicode="&#x2005;" horiz-adv-x="479" />
<glyph unicode="&#x2006;" horiz-adv-x="319" />
<glyph unicode="&#x2007;" horiz-adv-x="319" />
<glyph unicode="&#x2008;" horiz-adv-x="239" />
<glyph unicode="&#x2009;" horiz-adv-x="383" />
<glyph unicode="&#x200a;" horiz-adv-x="106" />
<glyph unicode="&#x2010;" horiz-adv-x="679" d="M121 720h492l-25 -204h-492z" />
<glyph unicode="&#x2011;" horiz-adv-x="679" d="M121 720h492l-25 -204h-492z" />
<glyph unicode="&#x2012;" horiz-adv-x="679" d="M121 720h492l-25 -204h-492z" />
<glyph unicode="&#x2013;" horiz-adv-x="1075" d="M180 699h765l-21 -174h-765z" />
<glyph unicode="&#x2014;" horiz-adv-x="1592" d="M180 699h1282l-21 -174h-1282z" />
<glyph unicode="&#x2018;" horiz-adv-x="427" d="M217 1006q-41 88 -41 178q0 114 61.5 218.5t175.5 191.5l68 -45q15 -9 16 -28q0 -11 -6 -21t-19 -24q-12 -14 -28 -36.5t-29.5 -50.5t-22.5 -62t-9 -72q0 -30 6.5 -61.5t22.5 -65.5q3 -8 3 -16q0 -15 -11.5 -28t-31.5 -21z" />
<glyph unicode="&#x2019;" horiz-adv-x="427" d="M414 1563q41 -88 41 -179q0 -114 -61.5 -218.5t-176.5 -191.5l-69 45q-15 10 -15 29q0 11 6 20t20 24q12 14 28 36t29 50.5t22.5 62.5t9.5 72q0 30 -7 61.5t-22 64.5q-3 9 -3 16q0 16 11.5 29t31.5 21z" />
<glyph unicode="&#x201a;" horiz-adv-x="470" d="M271 292q41 -88 41 -178q0 -114 -61.5 -219t-175.5 -192l-69 45q-15 10 -15 29q0 11 6 20t19 24q12 14 28 36t29.5 51t22.5 62.5t9 71.5q0 30 -6.5 61.5t-21.5 64.5q-3 9 -3 16q0 16 11 29t32 21z" />
<glyph unicode="&#x201c;" horiz-adv-x="744" d="M217 1006q-41 88 -41 178q0 114 61.5 218.5t175.5 191.5l68 -45q15 -9 16 -28q0 -11 -6 -21t-19 -24q-12 -14 -28 -36.5t-29.5 -50.5t-22.5 -62t-9 -72q0 -30 6.5 -61.5t22.5 -65.5q3 -8 3 -16q0 -15 -11.5 -28t-31.5 -21zM536 1006q-41 88 -41 178q0 114 61 218.5 t175 191.5l69 -45q15 -9 15 -28q0 -11 -5.5 -21t-18.5 -24q-12 -14 -28.5 -36.5t-29.5 -50.5t-22.5 -62t-9.5 -72q0 -30 7 -61.5t22 -65.5q3 -8 3 -16q0 -15 -11 -28t-32 -21z" />
<glyph unicode="&#x201d;" horiz-adv-x="744" d="M418 1563q41 -88 41 -179q0 -114 -61.5 -218.5t-176.5 -191.5l-68 45q-15 10 -16 29q0 11 6.5 20t19.5 24q12 14 28 36t29 50.5t22.5 62.5t9.5 72q0 30 -6.5 61.5t-22.5 64.5q-3 9 -3 16q0 16 11.5 29t31.5 21zM735 1563q41 -88 41 -179q0 -114 -61.5 -218.5 t-175.5 -191.5l-69 45q-15 10 -15 29q0 11 6 20t19 24q12 14 28 36t29.5 50.5t22.5 62.5t9 72q0 30 -6.5 61.5t-21.5 64.5q-3 9 -3 16q0 16 11 29t32 21z" />
<glyph unicode="&#x201e;" horiz-adv-x="744" d="M253 292q41 -88 41 -178q0 -114 -61.5 -219t-176.5 -192l-68 45q-15 10 -16 29q0 11 6.5 20t19.5 24q12 14 28 36t29.5 51t22.5 62.5t9 71.5q0 30 -6.5 61.5t-22.5 64.5q-3 9 -3 16q0 16 11.5 29t31.5 21zM571 292q41 -88 41 -178q0 -114 -61.5 -219t-175.5 -192l-69 45 q-15 10 -15 29q0 11 6 20t19 24q12 14 28 36t29.5 51t23 62.5t9.5 71.5q0 30 -7 61.5t-22 64.5q-3 9 -3 16q0 16 11 29t32 21z" />
<glyph unicode="&#x2020;" horiz-adv-x="1093" d="M131 967q2 19 11.5 38t25 34t38 23.5t52.5 8.5q29 0 60 -4.5t64.5 -11t68 -14.5t68.5 -13l27 471q27 14 60.5 22.5t69.5 8.5q73 0 121 -31l-88 -471q35 5 71 13t71 14.5t67.5 11t62.5 4.5q54 0 77.5 -27t23.5 -63v-5t-1 -12.5t-3.5 -27.5t-6.5 -50h-380l-49 -403 l-69 -816q-29 -15 -62 -23t-69 -8t-66.5 8t-53.5 23l131 816l48 403h-380z" />
<glyph unicode="&#x2021;" horiz-adv-x="1093" d="M427 281l75 605h-381l10 81q2 19 11.5 38t25 34t38 23.5t52.5 8.5q29 0 60 -4.5t64.5 -11t68 -14.5t68.5 -13l27 471q27 14 60.5 22.5t69.5 8.5q73 0 121 -31l-88 -471q35 5 71 13t71 14.5t67.5 11t62.5 4.5q54 0 77.5 -27t23.5 -63q0 -9 -1 -14l-10 -81h-381l-76 -605 h382l-9 -81q-3 -20 -11.5 -40t-24.5 -34.5t-38.5 -23.5t-52.5 -9q-29 0 -60.5 4.5t-65.5 11.5t-68.5 14.5t-68.5 12.5l-24 -469q-29 -15 -62 -23t-69 -8t-66.5 8t-53.5 23l87 469q-36 -5 -72 -12.5t-71 -14.5t-67.5 -11.5t-61.5 -4.5q-53 0 -77 26.5t-24 63.5v5t1 13 t3.5 28.5t6.5 51.5h380z" />
<glyph unicode="&#x2022;" d="M166 607q0 92 35.5 173t95.5 142t141 96t173 35q93 0 174.5 -35t142 -96t96 -142t35.5 -173q0 -91 -35.5 -171.5t-96 -141t-141.5 -95t-175 -34.5q-92 0 -173 34.5t-141 95t-95.5 141t-35.5 171.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1553" d="M35 139q0 32 11.5 60.5t32.5 49t49.5 33t60.5 12.5q33 0 61.5 -12.5t49.5 -33t33 -49t12 -60.5q0 -33 -12 -61t-33 -48.5t-49.5 -32t-61.5 -11.5t-61 11.5t-49 32t-32.5 48.5t-11.5 61zM582 139q0 32 11.5 60.5t32.5 49t49 33t61 12.5t61 -12.5t49 -33t33.5 -49 t12.5 -60.5q0 -33 -12.5 -61t-33.5 -48.5t-49 -32t-61 -11.5t-61 11.5t-49 32t-32.5 48.5t-11.5 61zM1128 139q0 32 12 60.5t32.5 49t49 33t61.5 12.5t61 -12.5t49 -33t33.5 -49t12.5 -60.5q0 -33 -12.5 -61t-33.5 -48.5t-49 -32t-61 -11.5t-61.5 11.5t-49 32t-32.5 48.5 t-12 61z" />
<glyph unicode="&#x202f;" horiz-adv-x="383" />
<glyph unicode="&#x2030;" horiz-adv-x="2217" d="M771 1178q0 -98 -31.5 -176.5t-83.5 -133.5t-118 -84t-135 -29q-61 0 -112 22t-87 63t-56 98.5t-20 129.5q0 97 28.5 176t78.5 134.5t117 85.5t143 30q61 0 112 -22t87.5 -63.5t56.5 -99.5t20 -131zM582 1177q0 86 -30.5 122.5t-76.5 36.5q-34 0 -63 -14.5t-50.5 -47 t-33.5 -83t-12 -123.5q0 -42 8 -71.5t22 -48t33.5 -27t43.5 -8.5q33 0 62 14t50.5 45.5t34 81.5t12.5 123zM1254 1441q14 14 33.5 26.5t50.5 12.5h178l-1194 -1443q-13 -16 -33 -26.5t-46 -10.5h-181zM1449 404q0 -98 -32 -176t-83 -133t-117.5 -84t-135.5 -29 q-61 0 -112 22.5t-87 63t-56 98t-20 128.5q0 98 28.5 177.5t78.5 135t117 85.5t142 30q61 0 112.5 -22.5t88 -64t56.5 -100t20 -131.5zM1261 403q0 86 -31 122.5t-77 36.5q-34 0 -63 -15t-50.5 -47t-33.5 -82.5t-12 -123.5q0 -83 30 -119t77 -36q34 0 63 14t50.5 45t34 81.5 t12.5 123.5zM2148 404q0 -98 -31.5 -176t-83 -133t-118 -84t-134.5 -29q-61 0 -112.5 22.5t-87.5 63t-56 98t-20 128.5q0 98 29 177.5t78.5 135t116.5 85.5t143 30q61 0 112 -22.5t87.5 -64t56.5 -100t20 -131.5zM1960 403q0 86 -31 122.5t-77 36.5q-34 0 -63 -15t-50 -47 t-32.5 -82.5t-11.5 -123.5q0 -83 29 -119t76 -36q33 0 62 14t50.5 45t34.5 81.5t13 123.5z" />
<glyph unicode="&#x2039;" horiz-adv-x="627" d="M124 535l5 30l292 405l75 -38q33 -13 32 -45q0 -11 -5.5 -23.5t-13.5 -26.5l-157 -245q-17 -26 -38 -43q8 -7 14.5 -18t12.5 -23l97 -245q8 -19 8 -36q0 -42 -44 -61l-85 -37z" />
<glyph unicode="&#x203a;" horiz-adv-x="627" d="M516 565l-3 -30l-292 -406l-75 37q-32 15 -31 46q0 23 17 51l159 245q8 12 17 23t19 18q-17 16 -26 43l-99 245q-6 16 -6 32q0 42 45 63l84 38z" />
<glyph unicode="&#x2044;" horiz-adv-x="686" d="M9 73q-30 -43 -62 -58t-71 -15h-100l960 1396q29 41 63 62.5t79 21.5h100z" />
<glyph unicode="&#x205f;" horiz-adv-x="479" />
<glyph unicode="&#x20ac;" d="M92 947h132q37 127 100 228.5t146 172t183.5 108.5t212.5 38q141 0 235.5 -55t151.5 -146l-97 -93q-11 -11 -22.5 -19.5t-31.5 -8.5t-36 17.5t-41.5 38.5t-65.5 38.5t-109 17.5q-130 0 -228.5 -86t-148.5 -251h528l-8 -77q-2 -22 -23 -40.5t-54 -18.5h-474 q-9 -58 -12 -122h444l-9 -74q-3 -24 -24.5 -42.5t-50.5 -18.5h-361q11 -176 83.5 -267t193.5 -91q50 0 87.5 9.5t66 24t49.5 31.5t37.5 31t30.5 24t29 10q11 0 17.5 -4t15.5 -13l94 -97q-85 -109 -202.5 -167.5t-262.5 -58.5q-117 0 -206.5 41.5t-150.5 116.5t-94.5 179.5 t-38.5 230.5h-134l16 135h120q2 32 5 62t9 60h-118z" />
<glyph unicode="&#x2122;" horiz-adv-x="1370" d="M988 1185q11 -36 18 -69q8 17 16 33.5t19 35.5l153 268q11 19 24 23t34 4h144l-75 -617h-147l38 318q2 20 8 42t15 53l-177 -311q-10 -17 -26.5 -26.5t-36.5 -9.5h-23q-18 0 -33 9t-20 27l-99 305q0 -36 -0.5 -57.5t-1.5 -31.5l-38 -318h-147l75 617h145q22 0 33.5 -3.5 t17.5 -23.5zM644 1480l-17 -140h-153l-58 -477h-167l57 477h-151l18 140h471z" />
<glyph unicode="&#x2202;" horiz-adv-x="1092" d="M387 1367q42 31 83.5 54.5t86 40t93.5 25t108 8.5q78 0 142 -30t109 -88t69.5 -141t24.5 -188q0 -29 -1.5 -59t-5.5 -61q-27 -212 -84 -386t-144 -298t-204 -191.5t-264 -67.5q-79 0 -144 25.5t-111.5 72.5t-72 114.5t-25.5 152.5t21 165.5t59 151t90.5 129t115.5 101 t134.5 65.5t145.5 23q90 0 150.5 -32t96.5 -93l4 34t4 33q3 29 5.5 54.5t2.5 48.5q0 134 -48 199.5t-130 65.5q-35 0 -67 -10t-60 -22.5t-51 -22.5t-39 -10q-14 0 -27 8t-24 33zM454 181q47 0 94 22t91 71t83.5 126t70.5 188q-4 40 -15 76t-31.5 64.5t-52 45t-78.5 16.5 q-67 0 -118.5 -27.5t-89.5 -76t-62 -115t-34 -146.5q0 -2 -0.5 -2.5t-0.5 -2.5q-3 -28 -3 -52q0 -90 38 -138.5t108 -48.5z" />
<glyph unicode="&#x2206;" horiz-adv-x="1393" d="M1321 0h-1372l747 1480h242zM322 210h678l-188 854q-8 28 -14.5 64t-14.5 79q-16 -43 -32 -79.5t-30 -64.5z" />
<glyph unicode="&#x220f;" horiz-adv-x="1404" d="M1471 1480l-24 -205h-175l-199 -1620h-251l197 1620h-446l-198 -1620h-251l199 1620h-175l25 205h1298z" />
<glyph unicode="&#x2211;" horiz-adv-x="1294" d="M1348 1481l-27 -212h-758l393 -664l-10 -73l-555 -663h758l-26 -212h-1140l10 88q2 18 12 37.5t25 37.5l644 752l-462 744q-9 14 -13 30t-4 31v5t1.5 13.5t3.5 30.5t7 55h1141z" />
<glyph unicode="&#x221a;" horiz-adv-x="1189" d="M296 660h-130q-34 0 -58 18t-24 61v6t1 13.5t3 28t6 50.5h360q27 0 43.5 -14t20.5 -32l70 -333q8 -42 11.5 -84.5t3.5 -85.5q10 34 21.5 68.5t27.5 71.5l556 1273q9 20 29 33.5t46 13.5h160l-778 -1748h-205z" />
<glyph unicode="&#x221e;" horiz-adv-x="1293" d="M907 238q-52 0 -92.5 14.5t-72.5 39.5t-58 58.5t-46 71.5q-31 -38 -64.5 -71.5t-72 -58.5t-83 -39.5t-96.5 -14.5q-57 0 -107 22.5t-86.5 63.5t-57.5 98.5t-21 127.5q0 76 25.5 151.5t73 136t114.5 98.5t150 38q52 0 93 -15t73 -40t58 -59t46 -72q30 38 63.5 72t72 59 t83 40t96.5 15q58 0 108.5 -22.5t87.5 -64t58 -99.5t21 -128q0 -76 -25.5 -151t-73.5 -135.5t-115.5 -98t-151.5 -37.5zM358 441q28 0 53.5 13t49.5 35t47 51.5t47 64.5q-15 35 -31 64.5t-34.5 52t-41 35.5t-50.5 13q-39 0 -67 -18t-46.5 -46.5t-27.5 -63.5t-9 -70 q0 -70 32 -100.5t78 -30.5zM923 441q37 0 65 17.5t47 45.5t28.5 64t9.5 73q0 34 -8.5 58.5t-24 40t-35.5 23t-42 7.5q-28 0 -53.5 -13t-49.5 -35t-47 -52t-47 -64q15 -35 31 -65t34.5 -52t41 -35t50.5 -13z" />
<glyph unicode="&#x222b;" horiz-adv-x="786" d="M470 1120q20 89 55 159t84 118.5t112.5 74t140.5 25.5q36 0 74.5 -7.5t71.5 -27.5l-28 -123q-2 -10 -6 -19.5t-12.5 -18t-21 -13.5t-32.5 -5q-45 0 -77 -12.5t-55.5 -36.5t-39.5 -60.5t-28 -86.5l-248 -1029q-26 -106 -68 -183.5t-97.5 -127t-123.5 -73.5t-144 -24 q-35 0 -73.5 7.5t-71.5 27.5l27 105q3 13 8 23t14 16t23 8.5t37 2.5q54 0 91.5 12t63.5 37t43 65t30 96z" />
<glyph unicode="&#x2248;" d="M861 912q32 0 62.5 7.5t57 18t46 22.5t28.5 21l10 -154q-46 -52 -110.5 -75.5t-132.5 -23.5q-54 0 -105.5 16.5t-101 35.5t-96.5 35t-91 16q-33 0 -64.5 -7.5t-57.5 -18t-45.5 -24t-29.5 -23.5l-18 149q46 55 113.5 81.5t140.5 26.5q53 0 105 -16t101.5 -35.5t96 -35.5 t91.5 -16zM816 550q32 0 63 7t57.5 18t45.5 22.5t28 20.5l10 -154q-45 -51 -110 -74t-132 -23q-54 0 -105.5 15.5t-101 34.5t-96.5 35t-92 16q-33 0 -64.5 -7t-58 -18t-45.5 -23.5t-28 -23.5l-18 149q46 56 113 82t140 26q54 0 106.5 -16t101.5 -35t95.5 -35.5t90.5 -16.5z " />
<glyph unicode="&#x2260;" d="M197 958h512l142 229h174l-142 -229h216l-21 -182h-308l-110 -176h396l-23 -181h-484l-151 -243h-174l151 243h-246l23 181h334l110 176h-422z" />
<glyph unicode="&#x2264;" horiz-adv-x="1092" d="M177 859l859 388l-21 -172q-2 -20 -16 -38t-48 -32l-381 -162q-27 -11 -57 -19t-62 -15q32 -7 60 -15.5t51 -19.5l340 -165q27 -13 37.5 -28.5t10.5 -32.5q0 -7 -2.5 -28.5t-5.5 -45t-5.5 -42.5t-2.5 -20l-6 -45l-764 391zM104 269h812l-23 -187h-812z" />
<glyph unicode="&#x2265;" horiz-adv-x="1092" d="M908 82h-811l24 187h812zM991 756l-859 -388l22 172q2 20 15.5 38t47.5 32l381 162q27 11 57 19t63 15q-32 7 -60 15t-52 20l-340 165q-27 13 -37.5 28.5t-10.5 32.5q0 7 2.5 28.5t5.5 45t5.5 42.5t2.5 20l7 45l764 -391z" />
<glyph unicode="&#x25ca;" d="M134 737l390 831h176l391 -831l-391 -830h-176zM341 737l237 -513q12 -27 20 -50.5t14 -46.5q6 23 14.5 47t20.5 50l241 513l-241 513q-12 27 -19.5 51t-15.5 47q-7 -23 -14.5 -47t-19.5 -51z" />
<glyph unicode="&#xe000;" horiz-adv-x="1049" d="M0 1050h1050v-1050h-1050v1050z" />
<glyph horiz-adv-x="605" d="M326 1832q19 0 32.5 -1.5t23.5 -6t19 -12t20 -19.5l157 -207h-207q-28 0 -43 9t-34 26l-240 211h272z" />
<glyph horiz-adv-x="605" d="M368 1706q0 -25 -10.5 -47t-27.5 -38t-40 -26t-48 -10t-47 10t-39 26t-26.5 38t-9.5 47q0 26 9.5 49t26.5 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5t27.5 -39.5t10.5 -49zM727 1706q0 -25 -9.5 -47t-26.5 -38t-39.5 -26t-48.5 -10t-47.5 10t-38.5 26t-27 38t-10 47 q0 26 10 49t27 39.5t39 26.5t47 10q26 0 48.5 -10t39.5 -26.5t26.5 -39.5t9.5 -49z" />
<glyph horiz-adv-x="605" d="M852 1832l-291 -211q-24 -17 -46 -26t-49 -9h-198l209 207q12 12 23 20t23 12t25.5 5.5t34.5 1.5h269z" />
<glyph horiz-adv-x="605" d="M780 1586h-185q-14 0 -30 5t-27 12l-81 71q-3 3 -7.5 6.5t-7.5 8.5l-16 -15l-99 -71q-11 -8 -29 -12.5t-34 -4.5h-183l255 223h244z" />
<glyph horiz-adv-x="605" d="M112 1809h222l116 -100l140 100h222l-256 -223h-244z" />
<glyph horiz-adv-x="605" d="M595 1737q0 -31 -13 -58t-34.5 -48t-50 -33.5t-60.5 -12.5q-30 0 -57.5 12.5t-48.5 33.5t-33 48t-12 58q0 32 12 60t33 49t49 33t57 12q32 0 60.5 -12t50 -33t34.5 -49t13 -60z" />
<glyph horiz-adv-x="605" d="M217 1723q0 44 17.5 80t46.5 61.5t67 40t80 14.5q43 0 82.5 -14.5t68.5 -40t46.5 -61.5t17.5 -80q0 -43 -17.5 -78t-46.5 -60.5t-68.5 -39.5t-82.5 -14q-42 0 -80 14t-67 39.5t-46.5 60.5t-17.5 78zM339 1723q0 -40 23.5 -65.5t68.5 -25.5q41 0 66 25.5t25 65.5 q0 43 -25 68t-66 25q-45 0 -68.5 -25t-23.5 -68z" />
<glyph horiz-adv-x="605" d="M569 1736q25 0 42.5 16t21.5 49h133q-6 -49 -23.5 -90.5t-44.5 -71.5t-63 -46.5t-79 -16.5q-34 0 -64.5 11.5t-57.5 25.5t-50.5 26t-42.5 12q-25 0 -41.5 -18t-20.5 -52h-136q6 50 24 92t45.5 72t63 46.5t77.5 16.5q35 0 66 -11t58 -25t50 -25t42 -11z" />
<glyph horiz-adv-x="605" d="M0 0z" />
<hkern u1="&#x22;" u2="&#x2206;" k="180" />
<hkern u1="&#x22;" u2="&#x203a;" k="41" />
<hkern u1="&#x22;" u2="&#x2039;" k="41" />
<hkern u1="&#x22;" u2="&#x2026;" k="45" />
<hkern u1="&#x22;" u2="&#x2022;" k="41" />
<hkern u1="&#x22;" u2="&#x201e;" k="45" />
<hkern u1="&#x22;" u2="&#x201a;" k="45" />
<hkern u1="&#x22;" u2="&#x2014;" k="41" />
<hkern u1="&#x22;" u2="&#x2013;" k="41" />
<hkern u1="&#x22;" u2="&#x178;" k="-43" />
<hkern u1="&#x22;" u2="&#x153;" k="98" />
<hkern u1="&#x22;" u2="&#x119;" k="98" />
<hkern u1="&#x22;" u2="&#x107;" k="98" />
<hkern u1="&#x22;" u2="&#x105;" k="98" />
<hkern u1="&#x22;" u2="&#x104;" k="180" />
<hkern u1="&#x22;" u2="&#xf8;" k="98" />
<hkern u1="&#x22;" u2="&#xf6;" k="98" />
<hkern u1="&#x22;" u2="&#xf5;" k="98" />
<hkern u1="&#x22;" u2="&#xf4;" k="98" />
<hkern u1="&#x22;" u2="&#xf3;" k="98" />
<hkern u1="&#x22;" u2="&#xf2;" k="98" />
<hkern u1="&#x22;" u2="&#xf0;" k="98" />
<hkern u1="&#x22;" u2="&#xeb;" k="98" />
<hkern u1="&#x22;" u2="&#xea;" k="98" />
<hkern u1="&#x22;" u2="&#xe9;" k="98" />
<hkern u1="&#x22;" u2="&#xe8;" k="98" />
<hkern u1="&#x22;" u2="&#xe7;" k="98" />
<hkern u1="&#x22;" u2="&#xe6;" k="98" />
<hkern u1="&#x22;" u2="&#xe5;" k="98" />
<hkern u1="&#x22;" u2="&#xe4;" k="98" />
<hkern u1="&#x22;" u2="&#xe3;" k="98" />
<hkern u1="&#x22;" u2="&#xe2;" k="98" />
<hkern u1="&#x22;" u2="&#xe1;" k="98" />
<hkern u1="&#x22;" u2="&#xe0;" k="98" />
<hkern u1="&#x22;" u2="&#xdd;" k="-43" />
<hkern u1="&#x22;" u2="&#xc6;" k="180" />
<hkern u1="&#x22;" u2="&#xc5;" k="180" />
<hkern u1="&#x22;" u2="&#xc4;" k="180" />
<hkern u1="&#x22;" u2="&#xc3;" k="180" />
<hkern u1="&#x22;" u2="&#xc2;" k="180" />
<hkern u1="&#x22;" u2="&#xc1;" k="180" />
<hkern u1="&#x22;" u2="&#xc0;" k="180" />
<hkern u1="&#x22;" u2="&#xbb;" k="41" />
<hkern u1="&#x22;" u2="&#xb7;" k="41" />
<hkern u1="&#x22;" u2="&#xab;" k="41" />
<hkern u1="&#x22;" u2="q" k="98" />
<hkern u1="&#x22;" u2="o" k="98" />
<hkern u1="&#x22;" u2="e" k="98" />
<hkern u1="&#x22;" u2="d" k="98" />
<hkern u1="&#x22;" u2="c" k="98" />
<hkern u1="&#x22;" u2="a" k="98" />
<hkern u1="&#x22;" u2="\" k="-48" />
<hkern u1="&#x22;" u2="Y" k="-43" />
<hkern u1="&#x22;" u2="W" k="-34" />
<hkern u1="&#x22;" u2="V" k="-48" />
<hkern u1="&#x22;" u2="A" k="180" />
<hkern u1="&#x22;" u2="&#x2f;" k="180" />
<hkern u1="&#x22;" u2="&#x2e;" k="45" />
<hkern u1="&#x22;" u2="&#x2d;" k="41" />
<hkern u1="&#x22;" u2="&#x2c;" k="45" />
<hkern u1="&#x22;" u2="&#x26;" k="180" />
<hkern u1="&#x27;" u2="&#x2206;" k="180" />
<hkern u1="&#x27;" u2="&#x203a;" k="41" />
<hkern u1="&#x27;" u2="&#x2039;" k="41" />
<hkern u1="&#x27;" u2="&#x2026;" k="45" />
<hkern u1="&#x27;" u2="&#x2022;" k="41" />
<hkern u1="&#x27;" u2="&#x201e;" k="45" />
<hkern u1="&#x27;" u2="&#x201a;" k="45" />
<hkern u1="&#x27;" u2="&#x2014;" k="41" />
<hkern u1="&#x27;" u2="&#x2013;" k="41" />
<hkern u1="&#x27;" u2="&#x178;" k="-43" />
<hkern u1="&#x27;" u2="&#x153;" k="98" />
<hkern u1="&#x27;" u2="&#x119;" k="98" />
<hkern u1="&#x27;" u2="&#x107;" k="98" />
<hkern u1="&#x27;" u2="&#x105;" k="98" />
<hkern u1="&#x27;" u2="&#x104;" k="180" />
<hkern u1="&#x27;" u2="&#xf8;" k="98" />
<hkern u1="&#x27;" u2="&#xf6;" k="98" />
<hkern u1="&#x27;" u2="&#xf5;" k="98" />
<hkern u1="&#x27;" u2="&#xf4;" k="98" />
<hkern u1="&#x27;" u2="&#xf3;" k="98" />
<hkern u1="&#x27;" u2="&#xf2;" k="98" />
<hkern u1="&#x27;" u2="&#xf0;" k="98" />
<hkern u1="&#x27;" u2="&#xeb;" k="98" />
<hkern u1="&#x27;" u2="&#xea;" k="98" />
<hkern u1="&#x27;" u2="&#xe9;" k="98" />
<hkern u1="&#x27;" u2="&#xe8;" k="98" />
<hkern u1="&#x27;" u2="&#xe7;" k="98" />
<hkern u1="&#x27;" u2="&#xe6;" k="98" />
<hkern u1="&#x27;" u2="&#xe5;" k="98" />
<hkern u1="&#x27;" u2="&#xe4;" k="98" />
<hkern u1="&#x27;" u2="&#xe3;" k="98" />
<hkern u1="&#x27;" u2="&#xe2;" k="98" />
<hkern u1="&#x27;" u2="&#xe1;" k="98" />
<hkern u1="&#x27;" u2="&#xe0;" k="98" />
<hkern u1="&#x27;" u2="&#xdd;" k="-43" />
<hkern u1="&#x27;" u2="&#xc6;" k="180" />
<hkern u1="&#x27;" u2="&#xc5;" k="180" />
<hkern u1="&#x27;" u2="&#xc4;" k="180" />
<hkern u1="&#x27;" u2="&#xc3;" k="180" />
<hkern u1="&#x27;" u2="&#xc2;" k="180" />
<hkern u1="&#x27;" u2="&#xc1;" k="180" />
<hkern u1="&#x27;" u2="&#xc0;" k="180" />
<hkern u1="&#x27;" u2="&#xbb;" k="41" />
<hkern u1="&#x27;" u2="&#xb7;" k="41" />
<hkern u1="&#x27;" u2="&#xab;" k="41" />
<hkern u1="&#x27;" u2="q" k="98" />
<hkern u1="&#x27;" u2="o" k="98" />
<hkern u1="&#x27;" u2="e" k="98" />
<hkern u1="&#x27;" u2="d" k="98" />
<hkern u1="&#x27;" u2="c" k="98" />
<hkern u1="&#x27;" u2="a" k="98" />
<hkern u1="&#x27;" u2="\" k="-48" />
<hkern u1="&#x27;" u2="Y" k="-43" />
<hkern u1="&#x27;" u2="W" k="-34" />
<hkern u1="&#x27;" u2="V" k="-48" />
<hkern u1="&#x27;" u2="A" k="180" />
<hkern u1="&#x27;" u2="&#x2f;" k="180" />
<hkern u1="&#x27;" u2="&#x2e;" k="45" />
<hkern u1="&#x27;" u2="&#x2d;" k="41" />
<hkern u1="&#x27;" u2="&#x2c;" k="45" />
<hkern u1="&#x27;" u2="&#x26;" k="180" />
<hkern u1="&#x28;" u2="&#x153;" k="37" />
<hkern u1="&#x28;" u2="&#x152;" k="41" />
<hkern u1="&#x28;" u2="&#x119;" k="37" />
<hkern u1="&#x28;" u2="&#x107;" k="37" />
<hkern u1="&#x28;" u2="&#x106;" k="41" />
<hkern u1="&#x28;" u2="&#x105;" k="37" />
<hkern u1="&#x28;" u2="&#xf8;" k="37" />
<hkern u1="&#x28;" u2="&#xf6;" k="37" />
<hkern u1="&#x28;" u2="&#xf5;" k="37" />
<hkern u1="&#x28;" u2="&#xf4;" k="37" />
<hkern u1="&#x28;" u2="&#xf3;" k="37" />
<hkern u1="&#x28;" u2="&#xf2;" k="37" />
<hkern u1="&#x28;" u2="&#xf0;" k="37" />
<hkern u1="&#x28;" u2="&#xeb;" k="37" />
<hkern u1="&#x28;" u2="&#xea;" k="37" />
<hkern u1="&#x28;" u2="&#xe9;" k="37" />
<hkern u1="&#x28;" u2="&#xe8;" k="37" />
<hkern u1="&#x28;" u2="&#xe7;" k="37" />
<hkern u1="&#x28;" u2="&#xe6;" k="37" />
<hkern u1="&#x28;" u2="&#xe5;" k="37" />
<hkern u1="&#x28;" u2="&#xe4;" k="37" />
<hkern u1="&#x28;" u2="&#xe3;" k="37" />
<hkern u1="&#x28;" u2="&#xe2;" k="37" />
<hkern u1="&#x28;" u2="&#xe1;" k="37" />
<hkern u1="&#x28;" u2="&#xe0;" k="37" />
<hkern u1="&#x28;" u2="&#xd8;" k="41" />
<hkern u1="&#x28;" u2="&#xd6;" k="41" />
<hkern u1="&#x28;" u2="&#xd5;" k="41" />
<hkern u1="&#x28;" u2="&#xd4;" k="41" />
<hkern u1="&#x28;" u2="&#xd3;" k="41" />
<hkern u1="&#x28;" u2="&#xd2;" k="41" />
<hkern u1="&#x28;" u2="&#xc7;" k="41" />
<hkern u1="&#x28;" u2="&#xae;" k="41" />
<hkern u1="&#x28;" u2="&#xa9;" k="41" />
<hkern u1="&#x28;" u2="q" k="37" />
<hkern u1="&#x28;" u2="o" k="37" />
<hkern u1="&#x28;" u2="e" k="37" />
<hkern u1="&#x28;" u2="d" k="37" />
<hkern u1="&#x28;" u2="c" k="37" />
<hkern u1="&#x28;" u2="a" k="37" />
<hkern u1="&#x28;" u2="Q" k="41" />
<hkern u1="&#x28;" u2="O" k="41" />
<hkern u1="&#x28;" u2="G" k="41" />
<hkern u1="&#x28;" u2="C" k="41" />
<hkern u1="&#x28;" u2="&#x40;" k="41" />
<hkern u1="&#x2a;" u2="&#x2206;" k="180" />
<hkern u1="&#x2a;" u2="&#x203a;" k="41" />
<hkern u1="&#x2a;" u2="&#x2039;" k="41" />
<hkern u1="&#x2a;" u2="&#x2026;" k="45" />
<hkern u1="&#x2a;" u2="&#x2022;" k="41" />
<hkern u1="&#x2a;" u2="&#x201e;" k="45" />
<hkern u1="&#x2a;" u2="&#x201a;" k="45" />
<hkern u1="&#x2a;" u2="&#x2014;" k="41" />
<hkern u1="&#x2a;" u2="&#x2013;" k="41" />
<hkern u1="&#x2a;" u2="&#x178;" k="-43" />
<hkern u1="&#x2a;" u2="&#x153;" k="98" />
<hkern u1="&#x2a;" u2="&#x119;" k="98" />
<hkern u1="&#x2a;" u2="&#x107;" k="98" />
<hkern u1="&#x2a;" u2="&#x105;" k="98" />
<hkern u1="&#x2a;" u2="&#x104;" k="180" />
<hkern u1="&#x2a;" u2="&#xf8;" k="98" />
<hkern u1="&#x2a;" u2="&#xf6;" k="98" />
<hkern u1="&#x2a;" u2="&#xf5;" k="98" />
<hkern u1="&#x2a;" u2="&#xf4;" k="98" />
<hkern u1="&#x2a;" u2="&#xf3;" k="98" />
<hkern u1="&#x2a;" u2="&#xf2;" k="98" />
<hkern u1="&#x2a;" u2="&#xf0;" k="98" />
<hkern u1="&#x2a;" u2="&#xeb;" k="98" />
<hkern u1="&#x2a;" u2="&#xea;" k="98" />
<hkern u1="&#x2a;" u2="&#xe9;" k="98" />
<hkern u1="&#x2a;" u2="&#xe8;" k="98" />
<hkern u1="&#x2a;" u2="&#xe7;" k="98" />
<hkern u1="&#x2a;" u2="&#xe6;" k="98" />
<hkern u1="&#x2a;" u2="&#xe5;" k="98" />
<hkern u1="&#x2a;" u2="&#xe4;" k="98" />
<hkern u1="&#x2a;" u2="&#xe3;" k="98" />
<hkern u1="&#x2a;" u2="&#xe2;" k="98" />
<hkern u1="&#x2a;" u2="&#xe1;" k="98" />
<hkern u1="&#x2a;" u2="&#xe0;" k="98" />
<hkern u1="&#x2a;" u2="&#xdd;" k="-43" />
<hkern u1="&#x2a;" u2="&#xc6;" k="180" />
<hkern u1="&#x2a;" u2="&#xc5;" k="180" />
<hkern u1="&#x2a;" u2="&#xc4;" k="180" />
<hkern u1="&#x2a;" u2="&#xc3;" k="180" />
<hkern u1="&#x2a;" u2="&#xc2;" k="180" />
<hkern u1="&#x2a;" u2="&#xc1;" k="180" />
<hkern u1="&#x2a;" u2="&#xc0;" k="180" />
<hkern u1="&#x2a;" u2="&#xbb;" k="41" />
<hkern u1="&#x2a;" u2="&#xb7;" k="41" />
<hkern u1="&#x2a;" u2="&#xab;" k="41" />
<hkern u1="&#x2a;" u2="q" k="98" />
<hkern u1="&#x2a;" u2="o" k="98" />
<hkern u1="&#x2a;" u2="e" k="98" />
<hkern u1="&#x2a;" u2="d" k="98" />
<hkern u1="&#x2a;" u2="c" k="98" />
<hkern u1="&#x2a;" u2="a" k="98" />
<hkern u1="&#x2a;" u2="\" k="-48" />
<hkern u1="&#x2a;" u2="Y" k="-43" />
<hkern u1="&#x2a;" u2="W" k="-34" />
<hkern u1="&#x2a;" u2="V" k="-48" />
<hkern u1="&#x2a;" u2="A" k="180" />
<hkern u1="&#x2a;" u2="&#x2f;" k="180" />
<hkern u1="&#x2a;" u2="&#x2e;" k="45" />
<hkern u1="&#x2a;" u2="&#x2d;" k="41" />
<hkern u1="&#x2a;" u2="&#x2c;" k="45" />
<hkern u1="&#x2a;" u2="&#x26;" k="180" />
<hkern u1="&#x2c;" u2="&#x203a;" k="214" />
<hkern u1="&#x2c;" u2="&#x2039;" k="214" />
<hkern u1="&#x2c;" u2="&#x2022;" k="214" />
<hkern u1="&#x2c;" u2="&#x201d;" k="58" />
<hkern u1="&#x2c;" u2="&#x201c;" k="58" />
<hkern u1="&#x2c;" u2="&#x2019;" k="58" />
<hkern u1="&#x2c;" u2="&#x2018;" k="58" />
<hkern u1="&#x2c;" u2="&#x2014;" k="214" />
<hkern u1="&#x2c;" u2="&#x2013;" k="214" />
<hkern u1="&#x2c;" u2="&#x178;" k="237" />
<hkern u1="&#x2c;" u2="&#x152;" k="53" />
<hkern u1="&#x2c;" u2="&#x106;" k="53" />
<hkern u1="&#x2c;" u2="&#xff;" k="155" />
<hkern u1="&#x2c;" u2="&#xfd;" k="155" />
<hkern u1="&#x2c;" u2="&#xdd;" k="237" />
<hkern u1="&#x2c;" u2="&#xd8;" k="53" />
<hkern u1="&#x2c;" u2="&#xd6;" k="53" />
<hkern u1="&#x2c;" u2="&#xd5;" k="53" />
<hkern u1="&#x2c;" u2="&#xd4;" k="53" />
<hkern u1="&#x2c;" u2="&#xd3;" k="53" />
<hkern u1="&#x2c;" u2="&#xd2;" k="53" />
<hkern u1="&#x2c;" u2="&#xc7;" k="53" />
<hkern u1="&#x2c;" u2="&#xbb;" k="214" />
<hkern u1="&#x2c;" u2="&#xba;" k="58" />
<hkern u1="&#x2c;" u2="&#xb7;" k="214" />
<hkern u1="&#x2c;" u2="&#xb0;" k="58" />
<hkern u1="&#x2c;" u2="&#xae;" k="53" />
<hkern u1="&#x2c;" u2="&#xab;" k="214" />
<hkern u1="&#x2c;" u2="&#xaa;" k="58" />
<hkern u1="&#x2c;" u2="&#xa9;" k="53" />
<hkern u1="&#x2c;" u2="y" k="151" />
<hkern u1="&#x2c;" u2="w" k="89" />
<hkern u1="&#x2c;" u2="v" k="155" />
<hkern u1="&#x2c;" u2="\" k="232" />
<hkern u1="&#x2c;" u2="Y" k="237" />
<hkern u1="&#x2c;" u2="W" k="122" />
<hkern u1="&#x2c;" u2="V" k="232" />
<hkern u1="&#x2c;" u2="T" k="218" />
<hkern u1="&#x2c;" u2="Q" k="53" />
<hkern u1="&#x2c;" u2="O" k="53" />
<hkern u1="&#x2c;" u2="G" k="53" />
<hkern u1="&#x2c;" u2="C" k="53" />
<hkern u1="&#x2c;" u2="&#x40;" k="53" />
<hkern u1="&#x2c;" u2="&#x2d;" k="214" />
<hkern u1="&#x2c;" u2="&#x2a;" k="58" />
<hkern u1="&#x2c;" u2="&#x27;" k="58" />
<hkern u1="&#x2c;" u2="&#x22;" k="58" />
<hkern u1="&#x2d;" u2="&#x2206;" k="69" />
<hkern u1="&#x2d;" u2="&#x2026;" k="175" />
<hkern u1="&#x2d;" u2="&#x201e;" k="175" />
<hkern u1="&#x2d;" u2="&#x201d;" k="41" />
<hkern u1="&#x2d;" u2="&#x201c;" k="41" />
<hkern u1="&#x2d;" u2="&#x201a;" k="175" />
<hkern u1="&#x2d;" u2="&#x2019;" k="41" />
<hkern u1="&#x2d;" u2="&#x2018;" k="41" />
<hkern u1="&#x2d;" u2="&#x17d;" k="59" />
<hkern u1="&#x2d;" u2="&#x17b;" k="59" />
<hkern u1="&#x2d;" u2="&#x179;" k="59" />
<hkern u1="&#x2d;" u2="&#x178;" k="212" />
<hkern u1="&#x2d;" u2="&#x104;" k="69" />
<hkern u1="&#x2d;" u2="&#xdd;" k="212" />
<hkern u1="&#x2d;" u2="&#xc6;" k="69" />
<hkern u1="&#x2d;" u2="&#xc5;" k="69" />
<hkern u1="&#x2d;" u2="&#xc4;" k="69" />
<hkern u1="&#x2d;" u2="&#xc3;" k="69" />
<hkern u1="&#x2d;" u2="&#xc2;" k="69" />
<hkern u1="&#x2d;" u2="&#xc1;" k="69" />
<hkern u1="&#x2d;" u2="&#xc0;" k="69" />
<hkern u1="&#x2d;" u2="&#xba;" k="41" />
<hkern u1="&#x2d;" u2="&#xb0;" k="41" />
<hkern u1="&#x2d;" u2="&#xaa;" k="41" />
<hkern u1="&#x2d;" u2="\" k="134" />
<hkern u1="&#x2d;" u2="Z" k="59" />
<hkern u1="&#x2d;" u2="Y" k="212" />
<hkern u1="&#x2d;" u2="X" k="72" />
<hkern u1="&#x2d;" u2="V" k="134" />
<hkern u1="&#x2d;" u2="T" k="201" />
<hkern u1="&#x2d;" u2="A" k="69" />
<hkern u1="&#x2d;" u2="&#x2f;" k="69" />
<hkern u1="&#x2d;" u2="&#x2e;" k="175" />
<hkern u1="&#x2d;" u2="&#x2c;" k="175" />
<hkern u1="&#x2d;" u2="&#x2a;" k="41" />
<hkern u1="&#x2d;" u2="&#x27;" k="41" />
<hkern u1="&#x2d;" u2="&#x26;" k="69" />
<hkern u1="&#x2d;" u2="&#x22;" k="41" />
<hkern u1="&#x2e;" u2="&#x203a;" k="214" />
<hkern u1="&#x2e;" u2="&#x2039;" k="214" />
<hkern u1="&#x2e;" u2="&#x2022;" k="214" />
<hkern u1="&#x2e;" u2="&#x201d;" k="58" />
<hkern u1="&#x2e;" u2="&#x201c;" k="58" />
<hkern u1="&#x2e;" u2="&#x2019;" k="58" />
<hkern u1="&#x2e;" u2="&#x2018;" k="58" />
<hkern u1="&#x2e;" u2="&#x2014;" k="214" />
<hkern u1="&#x2e;" u2="&#x2013;" k="214" />
<hkern u1="&#x2e;" u2="&#x178;" k="237" />
<hkern u1="&#x2e;" u2="&#x152;" k="53" />
<hkern u1="&#x2e;" u2="&#x106;" k="53" />
<hkern u1="&#x2e;" u2="&#xff;" k="155" />
<hkern u1="&#x2e;" u2="&#xfd;" k="155" />
<hkern u1="&#x2e;" u2="&#xdd;" k="237" />
<hkern u1="&#x2e;" u2="&#xd8;" k="53" />
<hkern u1="&#x2e;" u2="&#xd6;" k="53" />
<hkern u1="&#x2e;" u2="&#xd5;" k="53" />
<hkern u1="&#x2e;" u2="&#xd4;" k="53" />
<hkern u1="&#x2e;" u2="&#xd3;" k="53" />
<hkern u1="&#x2e;" u2="&#xd2;" k="53" />
<hkern u1="&#x2e;" u2="&#xc7;" k="53" />
<hkern u1="&#x2e;" u2="&#xbb;" k="214" />
<hkern u1="&#x2e;" u2="&#xba;" k="58" />
<hkern u1="&#x2e;" u2="&#xb7;" k="214" />
<hkern u1="&#x2e;" u2="&#xb0;" k="58" />
<hkern u1="&#x2e;" u2="&#xae;" k="53" />
<hkern u1="&#x2e;" u2="&#xab;" k="214" />
<hkern u1="&#x2e;" u2="&#xaa;" k="58" />
<hkern u1="&#x2e;" u2="&#xa9;" k="53" />
<hkern u1="&#x2e;" u2="y" k="151" />
<hkern u1="&#x2e;" u2="w" k="89" />
<hkern u1="&#x2e;" u2="v" k="155" />
<hkern u1="&#x2e;" u2="\" k="232" />
<hkern u1="&#x2e;" u2="Y" k="237" />
<hkern u1="&#x2e;" u2="W" k="122" />
<hkern u1="&#x2e;" u2="V" k="232" />
<hkern u1="&#x2e;" u2="T" k="218" />
<hkern u1="&#x2e;" u2="Q" k="53" />
<hkern u1="&#x2e;" u2="O" k="53" />
<hkern u1="&#x2e;" u2="G" k="53" />
<hkern u1="&#x2e;" u2="C" k="53" />
<hkern u1="&#x2e;" u2="&#x40;" k="53" />
<hkern u1="&#x2e;" u2="&#x2d;" k="214" />
<hkern u1="&#x2e;" u2="&#x2a;" k="58" />
<hkern u1="&#x2e;" u2="&#x27;" k="58" />
<hkern u1="&#x2e;" u2="&#x22;" k="58" />
<hkern u1="&#x2f;" u2="&#x2206;" k="141" />
<hkern u1="&#x2f;" u2="&#x203a;" k="134" />
<hkern u1="&#x2f;" u2="&#x2039;" k="134" />
<hkern u1="&#x2f;" u2="&#x2026;" k="201" />
<hkern u1="&#x2f;" u2="&#x2022;" k="134" />
<hkern u1="&#x2f;" u2="&#x201e;" k="201" />
<hkern u1="&#x2f;" u2="&#x201d;" k="-48" />
<hkern u1="&#x2f;" u2="&#x201c;" k="-48" />
<hkern u1="&#x2f;" u2="&#x201a;" k="201" />
<hkern u1="&#x2f;" u2="&#x2019;" k="-48" />
<hkern u1="&#x2f;" u2="&#x2018;" k="-48" />
<hkern u1="&#x2f;" u2="&#x2014;" k="134" />
<hkern u1="&#x2f;" u2="&#x2013;" k="134" />
<hkern u1="&#x2f;" u2="&#x17e;" k="89" />
<hkern u1="&#x2f;" u2="&#x17c;" k="89" />
<hkern u1="&#x2f;" u2="&#x17a;" k="89" />
<hkern u1="&#x2f;" u2="&#x161;" k="130" />
<hkern u1="&#x2f;" u2="&#x15b;" k="130" />
<hkern u1="&#x2f;" u2="&#x153;" k="132" />
<hkern u1="&#x2f;" u2="&#x152;" k="41" />
<hkern u1="&#x2f;" u2="&#x144;" k="90" />
<hkern u1="&#x2f;" u2="&#x119;" k="132" />
<hkern u1="&#x2f;" u2="&#x107;" k="132" />
<hkern u1="&#x2f;" u2="&#x106;" k="41" />
<hkern u1="&#x2f;" u2="&#x105;" k="132" />
<hkern u1="&#x2f;" u2="&#x104;" k="141" />
<hkern u1="&#x2f;" u2="&#xff;" k="44" />
<hkern u1="&#x2f;" u2="&#xfd;" k="44" />
<hkern u1="&#x2f;" u2="&#xfc;" k="90" />
<hkern u1="&#x2f;" u2="&#xfb;" k="90" />
<hkern u1="&#x2f;" u2="&#xfa;" k="90" />
<hkern u1="&#x2f;" u2="&#xf9;" k="90" />
<hkern u1="&#x2f;" u2="&#xf8;" k="132" />
<hkern u1="&#x2f;" u2="&#xf6;" k="132" />
<hkern u1="&#x2f;" u2="&#xf5;" k="132" />
<hkern u1="&#x2f;" u2="&#xf4;" k="132" />
<hkern u1="&#x2f;" u2="&#xf3;" k="132" />
<hkern u1="&#x2f;" u2="&#xf2;" k="132" />
<hkern u1="&#x2f;" u2="&#xf1;" k="90" />
<hkern u1="&#x2f;" u2="&#xf0;" k="132" />
<hkern u1="&#x2f;" u2="&#xeb;" k="132" />
<hkern u1="&#x2f;" u2="&#xea;" k="132" />
<hkern u1="&#x2f;" u2="&#xe9;" k="132" />
<hkern u1="&#x2f;" u2="&#xe8;" k="132" />
<hkern u1="&#x2f;" u2="&#xe7;" k="132" />
<hkern u1="&#x2f;" u2="&#xe6;" k="132" />
<hkern u1="&#x2f;" u2="&#xe5;" k="132" />
<hkern u1="&#x2f;" u2="&#xe4;" k="132" />
<hkern u1="&#x2f;" u2="&#xe3;" k="132" />
<hkern u1="&#x2f;" u2="&#xe2;" k="132" />
<hkern u1="&#x2f;" u2="&#xe1;" k="132" />
<hkern u1="&#x2f;" u2="&#xe0;" k="132" />
<hkern u1="&#x2f;" u2="&#xd8;" k="41" />
<hkern u1="&#x2f;" u2="&#xd6;" k="41" />
<hkern u1="&#x2f;" u2="&#xd5;" k="41" />
<hkern u1="&#x2f;" u2="&#xd4;" k="41" />
<hkern u1="&#x2f;" u2="&#xd3;" k="41" />
<hkern u1="&#x2f;" u2="&#xd2;" k="41" />
<hkern u1="&#x2f;" u2="&#xc7;" k="41" />
<hkern u1="&#x2f;" u2="&#xc6;" k="141" />
<hkern u1="&#x2f;" u2="&#xc5;" k="141" />
<hkern u1="&#x2f;" u2="&#xc4;" k="141" />
<hkern u1="&#x2f;" u2="&#xc3;" k="141" />
<hkern u1="&#x2f;" u2="&#xc2;" k="141" />
<hkern u1="&#x2f;" u2="&#xc1;" k="141" />
<hkern u1="&#x2f;" u2="&#xc0;" k="141" />
<hkern u1="&#x2f;" u2="&#xbb;" k="134" />
<hkern u1="&#x2f;" u2="&#xba;" k="-48" />
<hkern u1="&#x2f;" u2="&#xb7;" k="134" />
<hkern u1="&#x2f;" u2="&#xb5;" k="90" />
<hkern u1="&#x2f;" u2="&#xb0;" k="-48" />
<hkern u1="&#x2f;" u2="&#xae;" k="41" />
<hkern u1="&#x2f;" u2="&#xab;" k="134" />
<hkern u1="&#x2f;" u2="&#xaa;" k="-48" />
<hkern u1="&#x2f;" u2="&#xa9;" k="41" />
<hkern u1="&#x2f;" u2="z" k="89" />
<hkern u1="&#x2f;" u2="y" k="44" />
<hkern u1="&#x2f;" u2="w" k="44" />
<hkern u1="&#x2f;" u2="v" k="44" />
<hkern u1="&#x2f;" u2="u" k="90" />
<hkern u1="&#x2f;" u2="s" k="130" />
<hkern u1="&#x2f;" u2="r" k="90" />
<hkern u1="&#x2f;" u2="q" k="132" />
<hkern u1="&#x2f;" u2="p" k="90" />
<hkern u1="&#x2f;" u2="o" k="132" />
<hkern u1="&#x2f;" u2="n" k="90" />
<hkern u1="&#x2f;" u2="m" k="90" />
<hkern u1="&#x2f;" u2="e" k="132" />
<hkern u1="&#x2f;" u2="d" k="132" />
<hkern u1="&#x2f;" u2="c" k="132" />
<hkern u1="&#x2f;" u2="a" k="132" />
<hkern u1="&#x2f;" u2="Q" k="41" />
<hkern u1="&#x2f;" u2="O" k="41" />
<hkern u1="&#x2f;" u2="J" k="160" />
<hkern u1="&#x2f;" u2="G" k="41" />
<hkern u1="&#x2f;" u2="C" k="41" />
<hkern u1="&#x2f;" u2="A" k="141" />
<hkern u1="&#x2f;" u2="&#x40;" k="41" />
<hkern u1="&#x2f;" u2="&#x3f;" k="-50" />
<hkern u1="&#x2f;" u2="&#x3b;" k="90" />
<hkern u1="&#x2f;" u2="&#x3a;" k="90" />
<hkern u1="&#x2f;" u2="&#x2f;" k="141" />
<hkern u1="&#x2f;" u2="&#x2e;" k="201" />
<hkern u1="&#x2f;" u2="&#x2d;" k="134" />
<hkern u1="&#x2f;" u2="&#x2c;" k="201" />
<hkern u1="&#x2f;" u2="&#x2a;" k="-48" />
<hkern u1="&#x2f;" u2="&#x27;" k="-48" />
<hkern u1="&#x2f;" u2="&#x26;" k="141" />
<hkern u1="&#x2f;" u2="&#x22;" k="-48" />
<hkern u1="&#x40;" u2="&#x2206;" k="44" />
<hkern u1="&#x40;" u2="&#x201d;" k="53" />
<hkern u1="&#x40;" u2="&#x201c;" k="53" />
<hkern u1="&#x40;" u2="&#x2019;" k="53" />
<hkern u1="&#x40;" u2="&#x2018;" k="53" />
<hkern u1="&#x40;" u2="&#x17d;" k="66" />
<hkern u1="&#x40;" u2="&#x17b;" k="66" />
<hkern u1="&#x40;" u2="&#x179;" k="66" />
<hkern u1="&#x40;" u2="&#x178;" k="82" />
<hkern u1="&#x40;" u2="&#x104;" k="44" />
<hkern u1="&#x40;" u2="&#xdd;" k="82" />
<hkern u1="&#x40;" u2="&#xc6;" k="44" />
<hkern u1="&#x40;" u2="&#xc5;" k="44" />
<hkern u1="&#x40;" u2="&#xc4;" k="44" />
<hkern u1="&#x40;" u2="&#xc3;" k="44" />
<hkern u1="&#x40;" u2="&#xc2;" k="44" />
<hkern u1="&#x40;" u2="&#xc1;" k="44" />
<hkern u1="&#x40;" u2="&#xc0;" k="44" />
<hkern u1="&#x40;" u2="&#xba;" k="53" />
<hkern u1="&#x40;" u2="&#xb0;" k="53" />
<hkern u1="&#x40;" u2="&#xaa;" k="53" />
<hkern u1="&#x40;" u2="&#x7d;" k="41" />
<hkern u1="&#x40;" u2="]" k="41" />
<hkern u1="&#x40;" u2="\" k="57" />
<hkern u1="&#x40;" u2="Z" k="66" />
<hkern u1="&#x40;" u2="Y" k="82" />
<hkern u1="&#x40;" u2="X" k="87" />
<hkern u1="&#x40;" u2="V" k="57" />
<hkern u1="&#x40;" u2="T" k="80" />
<hkern u1="&#x40;" u2="A" k="44" />
<hkern u1="&#x40;" u2="&#x2f;" k="44" />
<hkern u1="&#x40;" u2="&#x2a;" k="53" />
<hkern u1="&#x40;" u2="&#x29;" k="41" />
<hkern u1="&#x40;" u2="&#x27;" k="53" />
<hkern u1="&#x40;" u2="&#x26;" k="44" />
<hkern u1="&#x40;" u2="&#x22;" k="53" />
<hkern u1="A" u2="&#x203a;" k="69" />
<hkern u1="A" u2="&#x2039;" k="69" />
<hkern u1="A" u2="&#x2022;" k="69" />
<hkern u1="A" u2="&#x201d;" k="196" />
<hkern u1="A" u2="&#x201c;" k="196" />
<hkern u1="A" u2="&#x2019;" k="196" />
<hkern u1="A" u2="&#x2018;" k="196" />
<hkern u1="A" u2="&#x2014;" k="69" />
<hkern u1="A" u2="&#x2013;" k="69" />
<hkern u1="A" u2="&#x178;" k="171" />
<hkern u1="A" u2="&#x152;" k="60" />
<hkern u1="A" u2="&#x106;" k="60" />
<hkern u1="A" u2="&#xff;" k="60" />
<hkern u1="A" u2="&#xfd;" k="60" />
<hkern u1="A" u2="&#xdd;" k="171" />
<hkern u1="A" u2="&#xdc;" k="41" />
<hkern u1="A" u2="&#xdb;" k="41" />
<hkern u1="A" u2="&#xda;" k="41" />
<hkern u1="A" u2="&#xd9;" k="41" />
<hkern u1="A" u2="&#xd8;" k="60" />
<hkern u1="A" u2="&#xd6;" k="60" />
<hkern u1="A" u2="&#xd5;" k="60" />
<hkern u1="A" u2="&#xd4;" k="60" />
<hkern u1="A" u2="&#xd3;" k="60" />
<hkern u1="A" u2="&#xd2;" k="60" />
<hkern u1="A" u2="&#xc7;" k="60" />
<hkern u1="A" u2="&#xbb;" k="69" />
<hkern u1="A" u2="&#xba;" k="196" />
<hkern u1="A" u2="&#xb7;" k="69" />
<hkern u1="A" u2="&#xb0;" k="196" />
<hkern u1="A" u2="&#xae;" k="60" />
<hkern u1="A" u2="&#xab;" k="69" />
<hkern u1="A" u2="&#xaa;" k="196" />
<hkern u1="A" u2="&#xa9;" k="60" />
<hkern u1="A" u2="y" k="60" />
<hkern u1="A" u2="w" k="48" />
<hkern u1="A" u2="v" k="60" />
<hkern u1="A" u2="t" k="71" />
<hkern u1="A" u2="\" k="141" />
<hkern u1="A" u2="Y" k="171" />
<hkern u1="A" u2="W" k="104" />
<hkern u1="A" u2="V" k="141" />
<hkern u1="A" u2="U" k="41" />
<hkern u1="A" u2="T" k="151" />
<hkern u1="A" u2="Q" k="60" />
<hkern u1="A" u2="O" k="60" />
<hkern u1="A" u2="J" k="-73" />
<hkern u1="A" u2="G" k="60" />
<hkern u1="A" u2="C" k="60" />
<hkern u1="A" u2="&#x40;" k="60" />
<hkern u1="A" u2="&#x2d;" k="69" />
<hkern u1="A" u2="&#x2a;" k="196" />
<hkern u1="A" u2="&#x27;" k="196" />
<hkern u1="A" u2="&#x22;" k="196" />
<hkern u1="C" u2="&#x203a;" k="116" />
<hkern u1="C" u2="&#x2039;" k="116" />
<hkern u1="C" u2="&#x2022;" k="116" />
<hkern u1="C" u2="&#x2014;" k="116" />
<hkern u1="C" u2="&#x2013;" k="116" />
<hkern u1="C" u2="&#xbb;" k="116" />
<hkern u1="C" u2="&#xb7;" k="116" />
<hkern u1="C" u2="&#xab;" k="116" />
<hkern u1="C" u2="&#x2d;" k="116" />
<hkern u1="D" u2="&#x2206;" k="44" />
<hkern u1="D" u2="&#x201d;" k="53" />
<hkern u1="D" u2="&#x201c;" k="53" />
<hkern u1="D" u2="&#x2019;" k="53" />
<hkern u1="D" u2="&#x2018;" k="53" />
<hkern u1="D" u2="&#x17d;" k="66" />
<hkern u1="D" u2="&#x17b;" k="66" />
<hkern u1="D" u2="&#x179;" k="66" />
<hkern u1="D" u2="&#x178;" k="82" />
<hkern u1="D" u2="&#x104;" k="44" />
<hkern u1="D" u2="&#xdd;" k="82" />
<hkern u1="D" u2="&#xc6;" k="44" />
<hkern u1="D" u2="&#xc5;" k="44" />
<hkern u1="D" u2="&#xc4;" k="44" />
<hkern u1="D" u2="&#xc3;" k="44" />
<hkern u1="D" u2="&#xc2;" k="44" />
<hkern u1="D" u2="&#xc1;" k="44" />
<hkern u1="D" u2="&#xc0;" k="44" />
<hkern u1="D" u2="&#xba;" k="53" />
<hkern u1="D" u2="&#xb0;" k="53" />
<hkern u1="D" u2="&#xaa;" k="53" />
<hkern u1="D" u2="&#x7d;" k="41" />
<hkern u1="D" u2="]" k="41" />
<hkern u1="D" u2="\" k="57" />
<hkern u1="D" u2="Z" k="66" />
<hkern u1="D" u2="Y" k="82" />
<hkern u1="D" u2="X" k="87" />
<hkern u1="D" u2="V" k="57" />
<hkern u1="D" u2="T" k="80" />
<hkern u1="D" u2="A" k="44" />
<hkern u1="D" u2="&#x2f;" k="44" />
<hkern u1="D" u2="&#x2a;" k="53" />
<hkern u1="D" u2="&#x29;" k="41" />
<hkern u1="D" u2="&#x27;" k="53" />
<hkern u1="D" u2="&#x26;" k="44" />
<hkern u1="D" u2="&#x22;" k="53" />
<hkern u1="F" u2="&#x2206;" k="151" />
<hkern u1="F" u2="&#x2026;" k="184" />
<hkern u1="F" u2="&#x201e;" k="184" />
<hkern u1="F" u2="&#x201a;" k="184" />
<hkern u1="F" u2="&#x153;" k="66" />
<hkern u1="F" u2="&#x144;" k="61" />
<hkern u1="F" u2="&#x119;" k="66" />
<hkern u1="F" u2="&#x107;" k="66" />
<hkern u1="F" u2="&#x105;" k="66" />
<hkern u1="F" u2="&#x104;" k="151" />
<hkern u1="F" u2="&#xfc;" k="61" />
<hkern u1="F" u2="&#xfb;" k="61" />
<hkern u1="F" u2="&#xfa;" k="61" />
<hkern u1="F" u2="&#xf9;" k="61" />
<hkern u1="F" u2="&#xf8;" k="66" />
<hkern u1="F" u2="&#xf6;" k="66" />
<hkern u1="F" u2="&#xf5;" k="66" />
<hkern u1="F" u2="&#xf4;" k="66" />
<hkern u1="F" u2="&#xf3;" k="66" />
<hkern u1="F" u2="&#xf2;" k="66" />
<hkern u1="F" u2="&#xf1;" k="61" />
<hkern u1="F" u2="&#xf0;" k="66" />
<hkern u1="F" u2="&#xeb;" k="66" />
<hkern u1="F" u2="&#xea;" k="66" />
<hkern u1="F" u2="&#xe9;" k="66" />
<hkern u1="F" u2="&#xe8;" k="66" />
<hkern u1="F" u2="&#xe7;" k="66" />
<hkern u1="F" u2="&#xe6;" k="66" />
<hkern u1="F" u2="&#xe5;" k="66" />
<hkern u1="F" u2="&#xe4;" k="66" />
<hkern u1="F" u2="&#xe3;" k="66" />
<hkern u1="F" u2="&#xe2;" k="66" />
<hkern u1="F" u2="&#xe1;" k="66" />
<hkern u1="F" u2="&#xe0;" k="66" />
<hkern u1="F" u2="&#xc6;" k="151" />
<hkern u1="F" u2="&#xc5;" k="151" />
<hkern u1="F" u2="&#xc4;" k="151" />
<hkern u1="F" u2="&#xc3;" k="151" />
<hkern u1="F" u2="&#xc2;" k="151" />
<hkern u1="F" u2="&#xc1;" k="151" />
<hkern u1="F" u2="&#xc0;" k="151" />
<hkern u1="F" u2="&#xb5;" k="61" />
<hkern u1="F" u2="u" k="61" />
<hkern u1="F" u2="r" k="61" />
<hkern u1="F" u2="q" k="66" />
<hkern u1="F" u2="p" k="61" />
<hkern u1="F" u2="o" k="66" />
<hkern u1="F" u2="n" k="61" />
<hkern u1="F" u2="m" k="61" />
<hkern u1="F" u2="e" k="66" />
<hkern u1="F" u2="d" k="66" />
<hkern u1="F" u2="c" k="66" />
<hkern u1="F" u2="a" k="66" />
<hkern u1="F" u2="J" k="194" />
<hkern u1="F" u2="A" k="151" />
<hkern u1="F" u2="&#x3f;" k="-20" />
<hkern u1="F" u2="&#x3b;" k="61" />
<hkern u1="F" u2="&#x3a;" k="61" />
<hkern u1="F" u2="&#x2f;" k="151" />
<hkern u1="F" u2="&#x2e;" k="184" />
<hkern u1="F" u2="&#x2c;" k="184" />
<hkern u1="F" u2="&#x26;" k="151" />
<hkern u1="J" u2="&#x2206;" k="41" />
<hkern u1="J" u2="&#x104;" k="41" />
<hkern u1="J" u2="&#xc6;" k="41" />
<hkern u1="J" u2="&#xc5;" k="41" />
<hkern u1="J" u2="&#xc4;" k="41" />
<hkern u1="J" u2="&#xc3;" k="41" />
<hkern u1="J" u2="&#xc2;" k="41" />
<hkern u1="J" u2="&#xc1;" k="41" />
<hkern u1="J" u2="&#xc0;" k="41" />
<hkern u1="J" u2="A" k="41" />
<hkern u1="J" u2="&#x2f;" k="41" />
<hkern u1="J" u2="&#x26;" k="41" />
<hkern u1="K" u2="&#x203a;" k="72" />
<hkern u1="K" u2="&#x2039;" k="72" />
<hkern u1="K" u2="&#x2022;" k="72" />
<hkern u1="K" u2="&#x201d;" k="-37" />
<hkern u1="K" u2="&#x201c;" k="-37" />
<hkern u1="K" u2="&#x2019;" k="-37" />
<hkern u1="K" u2="&#x2018;" k="-37" />
<hkern u1="K" u2="&#x2014;" k="72" />
<hkern u1="K" u2="&#x2013;" k="72" />
<hkern u1="K" u2="&#x152;" k="114" />
<hkern u1="K" u2="&#x106;" k="114" />
<hkern u1="K" u2="&#xff;" k="73" />
<hkern u1="K" u2="&#xfd;" k="73" />
<hkern u1="K" u2="&#xd8;" k="114" />
<hkern u1="K" u2="&#xd6;" k="114" />
<hkern u1="K" u2="&#xd5;" k="114" />
<hkern u1="K" u2="&#xd4;" k="114" />
<hkern u1="K" u2="&#xd3;" k="114" />
<hkern u1="K" u2="&#xd2;" k="114" />
<hkern u1="K" u2="&#xc7;" k="114" />
<hkern u1="K" u2="&#xbb;" k="72" />
<hkern u1="K" u2="&#xba;" k="-37" />
<hkern u1="K" u2="&#xb7;" k="72" />
<hkern u1="K" u2="&#xb0;" k="-37" />
<hkern u1="K" u2="&#xae;" k="114" />
<hkern u1="K" u2="&#xab;" k="72" />
<hkern u1="K" u2="&#xaa;" k="-37" />
<hkern u1="K" u2="&#xa9;" k="114" />
<hkern u1="K" u2="y" k="73" />
<hkern u1="K" u2="w" k="49" />
<hkern u1="K" u2="v" k="73" />
<hkern u1="K" u2="t" k="130" />
<hkern u1="K" u2="f" k="57" />
<hkern u1="K" u2="Q" k="114" />
<hkern u1="K" u2="O" k="114" />
<hkern u1="K" u2="G" k="114" />
<hkern u1="K" u2="C" k="114" />
<hkern u1="K" u2="&#x40;" k="114" />
<hkern u1="K" u2="&#x2d;" k="72" />
<hkern u1="K" u2="&#x2a;" k="-37" />
<hkern u1="K" u2="&#x27;" k="-37" />
<hkern u1="K" u2="&#x22;" k="-37" />
<hkern u1="L" u2="&#x203a;" k="182" />
<hkern u1="L" u2="&#x2039;" k="182" />
<hkern u1="L" u2="&#x2022;" k="182" />
<hkern u1="L" u2="&#x201d;" k="244" />
<hkern u1="L" u2="&#x201c;" k="244" />
<hkern u1="L" u2="&#x2019;" k="244" />
<hkern u1="L" u2="&#x2018;" k="244" />
<hkern u1="L" u2="&#x2014;" k="182" />
<hkern u1="L" u2="&#x2013;" k="182" />
<hkern u1="L" u2="&#x178;" k="232" />
<hkern u1="L" u2="&#x152;" k="98" />
<hkern u1="L" u2="&#x106;" k="98" />
<hkern u1="L" u2="&#xff;" k="134" />
<hkern u1="L" u2="&#xfd;" k="134" />
<hkern u1="L" u2="&#xdd;" k="232" />
<hkern u1="L" u2="&#xd8;" k="98" />
<hkern u1="L" u2="&#xd6;" k="98" />
<hkern u1="L" u2="&#xd5;" k="98" />
<hkern u1="L" u2="&#xd4;" k="98" />
<hkern u1="L" u2="&#xd3;" k="98" />
<hkern u1="L" u2="&#xd2;" k="98" />
<hkern u1="L" u2="&#xc7;" k="98" />
<hkern u1="L" u2="&#xbb;" k="182" />
<hkern u1="L" u2="&#xba;" k="244" />
<hkern u1="L" u2="&#xb7;" k="182" />
<hkern u1="L" u2="&#xb0;" k="244" />
<hkern u1="L" u2="&#xae;" k="98" />
<hkern u1="L" u2="&#xab;" k="182" />
<hkern u1="L" u2="&#xaa;" k="244" />
<hkern u1="L" u2="&#xa9;" k="98" />
<hkern u1="L" u2="y" k="134" />
<hkern u1="L" u2="w" k="90" />
<hkern u1="L" u2="v" k="134" />
<hkern u1="L" u2="\" k="196" />
<hkern u1="L" u2="Y" k="232" />
<hkern u1="L" u2="W" k="171" />
<hkern u1="L" u2="V" k="196" />
<hkern u1="L" u2="T" k="205" />
<hkern u1="L" u2="Q" k="98" />
<hkern u1="L" u2="O" k="98" />
<hkern u1="L" u2="G" k="98" />
<hkern u1="L" u2="C" k="98" />
<hkern u1="L" u2="&#x40;" k="98" />
<hkern u1="L" u2="&#x2d;" k="182" />
<hkern u1="L" u2="&#x2a;" k="244" />
<hkern u1="L" u2="&#x27;" k="244" />
<hkern u1="L" u2="&#x22;" k="244" />
<hkern u1="O" u2="&#x2206;" k="44" />
<hkern u1="O" u2="&#x201d;" k="53" />
<hkern u1="O" u2="&#x201c;" k="53" />
<hkern u1="O" u2="&#x2019;" k="53" />
<hkern u1="O" u2="&#x2018;" k="53" />
<hkern u1="O" u2="&#x17d;" k="66" />
<hkern u1="O" u2="&#x17b;" k="66" />
<hkern u1="O" u2="&#x179;" k="66" />
<hkern u1="O" u2="&#x178;" k="82" />
<hkern u1="O" u2="&#x104;" k="44" />
<hkern u1="O" u2="&#xdd;" k="82" />
<hkern u1="O" u2="&#xc6;" k="44" />
<hkern u1="O" u2="&#xc5;" k="44" />
<hkern u1="O" u2="&#xc4;" k="44" />
<hkern u1="O" u2="&#xc3;" k="44" />
<hkern u1="O" u2="&#xc2;" k="44" />
<hkern u1="O" u2="&#xc1;" k="44" />
<hkern u1="O" u2="&#xc0;" k="44" />
<hkern u1="O" u2="&#xba;" k="53" />
<hkern u1="O" u2="&#xb0;" k="53" />
<hkern u1="O" u2="&#xaa;" k="53" />
<hkern u1="O" u2="&#x7d;" k="41" />
<hkern u1="O" u2="]" k="41" />
<hkern u1="O" u2="\" k="57" />
<hkern u1="O" u2="Z" k="66" />
<hkern u1="O" u2="Y" k="82" />
<hkern u1="O" u2="X" k="87" />
<hkern u1="O" u2="V" k="57" />
<hkern u1="O" u2="T" k="80" />
<hkern u1="O" u2="A" k="44" />
<hkern u1="O" u2="&#x2f;" k="44" />
<hkern u1="O" u2="&#x2a;" k="53" />
<hkern u1="O" u2="&#x29;" k="41" />
<hkern u1="O" u2="&#x27;" k="53" />
<hkern u1="O" u2="&#x26;" k="44" />
<hkern u1="O" u2="&#x22;" k="53" />
<hkern u1="P" u2="&#x2206;" k="159" />
<hkern u1="P" u2="&#x2026;" k="188" />
<hkern u1="P" u2="&#x201e;" k="188" />
<hkern u1="P" u2="&#x201a;" k="188" />
<hkern u1="P" u2="&#x153;" k="31" />
<hkern u1="P" u2="&#x119;" k="31" />
<hkern u1="P" u2="&#x107;" k="31" />
<hkern u1="P" u2="&#x105;" k="31" />
<hkern u1="P" u2="&#x104;" k="159" />
<hkern u1="P" u2="&#xf8;" k="31" />
<hkern u1="P" u2="&#xf6;" k="31" />
<hkern u1="P" u2="&#xf5;" k="31" />
<hkern u1="P" u2="&#xf4;" k="31" />
<hkern u1="P" u2="&#xf3;" k="31" />
<hkern u1="P" u2="&#xf2;" k="31" />
<hkern u1="P" u2="&#xf0;" k="31" />
<hkern u1="P" u2="&#xeb;" k="31" />
<hkern u1="P" u2="&#xea;" k="31" />
<hkern u1="P" u2="&#xe9;" k="31" />
<hkern u1="P" u2="&#xe8;" k="31" />
<hkern u1="P" u2="&#xe7;" k="31" />
<hkern u1="P" u2="&#xe6;" k="31" />
<hkern u1="P" u2="&#xe5;" k="31" />
<hkern u1="P" u2="&#xe4;" k="31" />
<hkern u1="P" u2="&#xe3;" k="31" />
<hkern u1="P" u2="&#xe2;" k="31" />
<hkern u1="P" u2="&#xe1;" k="31" />
<hkern u1="P" u2="&#xe0;" k="31" />
<hkern u1="P" u2="&#xc6;" k="159" />
<hkern u1="P" u2="&#xc5;" k="159" />
<hkern u1="P" u2="&#xc4;" k="159" />
<hkern u1="P" u2="&#xc3;" k="159" />
<hkern u1="P" u2="&#xc2;" k="159" />
<hkern u1="P" u2="&#xc1;" k="159" />
<hkern u1="P" u2="&#xc0;" k="159" />
<hkern u1="P" u2="q" k="31" />
<hkern u1="P" u2="o" k="31" />
<hkern u1="P" u2="e" k="31" />
<hkern u1="P" u2="d" k="31" />
<hkern u1="P" u2="c" k="31" />
<hkern u1="P" u2="a" k="31" />
<hkern u1="P" u2="J" k="196" />
<hkern u1="P" u2="A" k="159" />
<hkern u1="P" u2="&#x2f;" k="159" />
<hkern u1="P" u2="&#x2e;" k="188" />
<hkern u1="P" u2="&#x2c;" k="188" />
<hkern u1="P" u2="&#x26;" k="159" />
<hkern u1="Q" u2="&#x2206;" k="44" />
<hkern u1="Q" u2="&#x201d;" k="53" />
<hkern u1="Q" u2="&#x201c;" k="53" />
<hkern u1="Q" u2="&#x2019;" k="53" />
<hkern u1="Q" u2="&#x2018;" k="53" />
<hkern u1="Q" u2="&#x17d;" k="66" />
<hkern u1="Q" u2="&#x17b;" k="66" />
<hkern u1="Q" u2="&#x179;" k="66" />
<hkern u1="Q" u2="&#x178;" k="82" />
<hkern u1="Q" u2="&#x104;" k="44" />
<hkern u1="Q" u2="&#xdd;" k="82" />
<hkern u1="Q" u2="&#xc6;" k="44" />
<hkern u1="Q" u2="&#xc5;" k="44" />
<hkern u1="Q" u2="&#xc4;" k="44" />
<hkern u1="Q" u2="&#xc3;" k="44" />
<hkern u1="Q" u2="&#xc2;" k="44" />
<hkern u1="Q" u2="&#xc1;" k="44" />
<hkern u1="Q" u2="&#xc0;" k="44" />
<hkern u1="Q" u2="&#xba;" k="53" />
<hkern u1="Q" u2="&#xb0;" k="53" />
<hkern u1="Q" u2="&#xaa;" k="53" />
<hkern u1="Q" u2="&#x7d;" k="41" />
<hkern u1="Q" u2="]" k="41" />
<hkern u1="Q" u2="\" k="57" />
<hkern u1="Q" u2="Z" k="66" />
<hkern u1="Q" u2="Y" k="82" />
<hkern u1="Q" u2="X" k="87" />
<hkern u1="Q" u2="V" k="57" />
<hkern u1="Q" u2="T" k="80" />
<hkern u1="Q" u2="A" k="44" />
<hkern u1="Q" u2="&#x2f;" k="44" />
<hkern u1="Q" u2="&#x2a;" k="53" />
<hkern u1="Q" u2="&#x29;" k="41" />
<hkern u1="Q" u2="&#x27;" k="53" />
<hkern u1="Q" u2="&#x26;" k="44" />
<hkern u1="Q" u2="&#x22;" k="53" />
<hkern u1="R" u2="&#x152;" k="41" />
<hkern u1="R" u2="&#x106;" k="41" />
<hkern u1="R" u2="&#xdc;" k="52" />
<hkern u1="R" u2="&#xdb;" k="52" />
<hkern u1="R" u2="&#xda;" k="52" />
<hkern u1="R" u2="&#xd9;" k="52" />
<hkern u1="R" u2="&#xd8;" k="41" />
<hkern u1="R" u2="&#xd6;" k="41" />
<hkern u1="R" u2="&#xd5;" k="41" />
<hkern u1="R" u2="&#xd4;" k="41" />
<hkern u1="R" u2="&#xd3;" k="41" />
<hkern u1="R" u2="&#xd2;" k="41" />
<hkern u1="R" u2="&#xc7;" k="41" />
<hkern u1="R" u2="&#xae;" k="41" />
<hkern u1="R" u2="&#xa9;" k="41" />
<hkern u1="R" u2="U" k="52" />
<hkern u1="R" u2="T" k="57" />
<hkern u1="R" u2="Q" k="41" />
<hkern u1="R" u2="O" k="41" />
<hkern u1="R" u2="G" k="41" />
<hkern u1="R" u2="C" k="41" />
<hkern u1="R" u2="&#x40;" k="41" />
<hkern u1="T" u2="&#x2206;" k="151" />
<hkern u1="T" u2="&#x203a;" k="184" />
<hkern u1="T" u2="&#x2039;" k="184" />
<hkern u1="T" u2="&#x2026;" k="184" />
<hkern u1="T" u2="&#x2022;" k="184" />
<hkern u1="T" u2="&#x201e;" k="184" />
<hkern u1="T" u2="&#x201a;" k="184" />
<hkern u1="T" u2="&#x2014;" k="184" />
<hkern u1="T" u2="&#x2013;" k="184" />
<hkern u1="T" u2="&#x17e;" k="170" />
<hkern u1="T" u2="&#x17c;" k="170" />
<hkern u1="T" u2="&#x17a;" k="170" />
<hkern u1="T" u2="&#x161;" k="182" />
<hkern u1="T" u2="&#x15b;" k="182" />
<hkern u1="T" u2="&#x153;" k="209" />
<hkern u1="T" u2="&#x152;" k="80" />
<hkern u1="T" u2="&#x144;" k="151" />
<hkern u1="T" u2="&#x119;" k="209" />
<hkern u1="T" u2="&#x107;" k="209" />
<hkern u1="T" u2="&#x106;" k="80" />
<hkern u1="T" u2="&#x105;" k="209" />
<hkern u1="T" u2="&#x104;" k="151" />
<hkern u1="T" u2="&#xff;" k="168" />
<hkern u1="T" u2="&#xfd;" k="168" />
<hkern u1="T" u2="&#xfc;" k="151" />
<hkern u1="T" u2="&#xfb;" k="151" />
<hkern u1="T" u2="&#xfa;" k="151" />
<hkern u1="T" u2="&#xf9;" k="151" />
<hkern u1="T" u2="&#xf8;" k="209" />
<hkern u1="T" u2="&#xf6;" k="209" />
<hkern u1="T" u2="&#xf5;" k="209" />
<hkern u1="T" u2="&#xf4;" k="209" />
<hkern u1="T" u2="&#xf3;" k="209" />
<hkern u1="T" u2="&#xf2;" k="209" />
<hkern u1="T" u2="&#xf1;" k="151" />
<hkern u1="T" u2="&#xf0;" k="209" />
<hkern u1="T" u2="&#xeb;" k="209" />
<hkern u1="T" u2="&#xea;" k="209" />
<hkern u1="T" u2="&#xe9;" k="209" />
<hkern u1="T" u2="&#xe8;" k="209" />
<hkern u1="T" u2="&#xe7;" k="209" />
<hkern u1="T" u2="&#xe6;" k="209" />
<hkern u1="T" u2="&#xe5;" k="209" />
<hkern u1="T" u2="&#xe4;" k="209" />
<hkern u1="T" u2="&#xe3;" k="209" />
<hkern u1="T" u2="&#xe2;" k="209" />
<hkern u1="T" u2="&#xe1;" k="209" />
<hkern u1="T" u2="&#xe0;" k="209" />
<hkern u1="T" u2="&#xd8;" k="80" />
<hkern u1="T" u2="&#xd6;" k="80" />
<hkern u1="T" u2="&#xd5;" k="80" />
<hkern u1="T" u2="&#xd4;" k="80" />
<hkern u1="T" u2="&#xd3;" k="80" />
<hkern u1="T" u2="&#xd2;" k="80" />
<hkern u1="T" u2="&#xc7;" k="80" />
<hkern u1="T" u2="&#xc6;" k="151" />
<hkern u1="T" u2="&#xc5;" k="151" />
<hkern u1="T" u2="&#xc4;" k="151" />
<hkern u1="T" u2="&#xc3;" k="151" />
<hkern u1="T" u2="&#xc2;" k="151" />
<hkern u1="T" u2="&#xc1;" k="151" />
<hkern u1="T" u2="&#xc0;" k="151" />
<hkern u1="T" u2="&#xbb;" k="184" />
<hkern u1="T" u2="&#xb7;" k="184" />
<hkern u1="T" u2="&#xb5;" k="151" />
<hkern u1="T" u2="&#xae;" k="80" />
<hkern u1="T" u2="&#xab;" k="184" />
<hkern u1="T" u2="&#xa9;" k="80" />
<hkern u1="T" u2="z" k="170" />
<hkern u1="T" u2="y" k="184" />
<hkern u1="T" u2="x" k="177" />
<hkern u1="T" u2="w" k="127" />
<hkern u1="T" u2="v" k="168" />
<hkern u1="T" u2="u" k="151" />
<hkern u1="T" u2="s" k="182" />
<hkern u1="T" u2="r" k="151" />
<hkern u1="T" u2="q" k="209" />
<hkern u1="T" u2="p" k="151" />
<hkern u1="T" u2="o" k="209" />
<hkern u1="T" u2="n" k="151" />
<hkern u1="T" u2="m" k="151" />
<hkern u1="T" u2="g" k="187" />
<hkern u1="T" u2="e" k="209" />
<hkern u1="T" u2="d" k="209" />
<hkern u1="T" u2="c" k="209" />
<hkern u1="T" u2="a" k="209" />
<hkern u1="T" u2="Q" k="80" />
<hkern u1="T" u2="O" k="80" />
<hkern u1="T" u2="J" k="205" />
<hkern u1="T" u2="G" k="80" />
<hkern u1="T" u2="C" k="80" />
<hkern u1="T" u2="A" k="151" />
<hkern u1="T" u2="&#x40;" k="80" />
<hkern u1="T" u2="&#x3f;" k="-35" />
<hkern u1="T" u2="&#x3b;" k="151" />
<hkern u1="T" u2="&#x3a;" k="151" />
<hkern u1="T" u2="&#x2f;" k="151" />
<hkern u1="T" u2="&#x2e;" k="184" />
<hkern u1="T" u2="&#x2d;" k="184" />
<hkern u1="T" u2="&#x2c;" k="184" />
<hkern u1="T" u2="&#x26;" k="151" />
<hkern u1="U" u2="&#x2206;" k="41" />
<hkern u1="U" u2="&#x104;" k="41" />
<hkern u1="U" u2="&#xc6;" k="41" />
<hkern u1="U" u2="&#xc5;" k="41" />
<hkern u1="U" u2="&#xc4;" k="41" />
<hkern u1="U" u2="&#xc3;" k="41" />
<hkern u1="U" u2="&#xc2;" k="41" />
<hkern u1="U" u2="&#xc1;" k="41" />
<hkern u1="U" u2="&#xc0;" k="41" />
<hkern u1="U" u2="A" k="41" />
<hkern u1="U" u2="&#x2f;" k="41" />
<hkern u1="U" u2="&#x26;" k="41" />
<hkern u1="V" u2="&#x2206;" k="141" />
<hkern u1="V" u2="&#x203a;" k="134" />
<hkern u1="V" u2="&#x2039;" k="134" />
<hkern u1="V" u2="&#x2026;" k="201" />
<hkern u1="V" u2="&#x2022;" k="134" />
<hkern u1="V" u2="&#x201e;" k="201" />
<hkern u1="V" u2="&#x201d;" k="-48" />
<hkern u1="V" u2="&#x201c;" k="-48" />
<hkern u1="V" u2="&#x201a;" k="201" />
<hkern u1="V" u2="&#x2019;" k="-48" />
<hkern u1="V" u2="&#x2018;" k="-48" />
<hkern u1="V" u2="&#x2014;" k="134" />
<hkern u1="V" u2="&#x2013;" k="134" />
<hkern u1="V" u2="&#x17e;" k="89" />
<hkern u1="V" u2="&#x17c;" k="89" />
<hkern u1="V" u2="&#x17a;" k="89" />
<hkern u1="V" u2="&#x161;" k="130" />
<hkern u1="V" u2="&#x15b;" k="130" />
<hkern u1="V" u2="&#x153;" k="132" />
<hkern u1="V" u2="&#x152;" k="41" />
<hkern u1="V" u2="&#x144;" k="90" />
<hkern u1="V" u2="&#x119;" k="132" />
<hkern u1="V" u2="&#x107;" k="132" />
<hkern u1="V" u2="&#x106;" k="41" />
<hkern u1="V" u2="&#x105;" k="132" />
<hkern u1="V" u2="&#x104;" k="141" />
<hkern u1="V" u2="&#xff;" k="44" />
<hkern u1="V" u2="&#xfd;" k="44" />
<hkern u1="V" u2="&#xfc;" k="90" />
<hkern u1="V" u2="&#xfb;" k="90" />
<hkern u1="V" u2="&#xfa;" k="90" />
<hkern u1="V" u2="&#xf9;" k="90" />
<hkern u1="V" u2="&#xf8;" k="132" />
<hkern u1="V" u2="&#xf6;" k="132" />
<hkern u1="V" u2="&#xf5;" k="132" />
<hkern u1="V" u2="&#xf4;" k="132" />
<hkern u1="V" u2="&#xf3;" k="132" />
<hkern u1="V" u2="&#xf2;" k="132" />
<hkern u1="V" u2="&#xf1;" k="90" />
<hkern u1="V" u2="&#xf0;" k="132" />
<hkern u1="V" u2="&#xeb;" k="132" />
<hkern u1="V" u2="&#xea;" k="132" />
<hkern u1="V" u2="&#xe9;" k="132" />
<hkern u1="V" u2="&#xe8;" k="132" />
<hkern u1="V" u2="&#xe7;" k="132" />
<hkern u1="V" u2="&#xe6;" k="132" />
<hkern u1="V" u2="&#xe5;" k="132" />
<hkern u1="V" u2="&#xe4;" k="132" />
<hkern u1="V" u2="&#xe3;" k="132" />
<hkern u1="V" u2="&#xe2;" k="132" />
<hkern u1="V" u2="&#xe1;" k="132" />
<hkern u1="V" u2="&#xe0;" k="132" />
<hkern u1="V" u2="&#xd8;" k="41" />
<hkern u1="V" u2="&#xd6;" k="41" />
<hkern u1="V" u2="&#xd5;" k="41" />
<hkern u1="V" u2="&#xd4;" k="41" />
<hkern u1="V" u2="&#xd3;" k="41" />
<hkern u1="V" u2="&#xd2;" k="41" />
<hkern u1="V" u2="&#xc7;" k="41" />
<hkern u1="V" u2="&#xc6;" k="141" />
<hkern u1="V" u2="&#xc5;" k="141" />
<hkern u1="V" u2="&#xc4;" k="141" />
<hkern u1="V" u2="&#xc3;" k="141" />
<hkern u1="V" u2="&#xc2;" k="141" />
<hkern u1="V" u2="&#xc1;" k="141" />
<hkern u1="V" u2="&#xc0;" k="141" />
<hkern u1="V" u2="&#xbb;" k="134" />
<hkern u1="V" u2="&#xba;" k="-48" />
<hkern u1="V" u2="&#xb7;" k="134" />
<hkern u1="V" u2="&#xb5;" k="90" />
<hkern u1="V" u2="&#xb0;" k="-48" />
<hkern u1="V" u2="&#xae;" k="41" />
<hkern u1="V" u2="&#xab;" k="134" />
<hkern u1="V" u2="&#xaa;" k="-48" />
<hkern u1="V" u2="&#xa9;" k="41" />
<hkern u1="V" u2="z" k="89" />
<hkern u1="V" u2="y" k="44" />
<hkern u1="V" u2="w" k="44" />
<hkern u1="V" u2="v" k="44" />
<hkern u1="V" u2="u" k="90" />
<hkern u1="V" u2="s" k="130" />
<hkern u1="V" u2="r" k="90" />
<hkern u1="V" u2="q" k="132" />
<hkern u1="V" u2="p" k="90" />
<hkern u1="V" u2="o" k="132" />
<hkern u1="V" u2="n" k="90" />
<hkern u1="V" u2="m" k="90" />
<hkern u1="V" u2="e" k="132" />
<hkern u1="V" u2="d" k="132" />
<hkern u1="V" u2="c" k="132" />
<hkern u1="V" u2="a" k="132" />
<hkern u1="V" u2="Q" k="41" />
<hkern u1="V" u2="O" k="41" />
<hkern u1="V" u2="J" k="160" />
<hkern u1="V" u2="G" k="41" />
<hkern u1="V" u2="C" k="41" />
<hkern u1="V" u2="A" k="141" />
<hkern u1="V" u2="&#x40;" k="41" />
<hkern u1="V" u2="&#x3f;" k="-50" />
<hkern u1="V" u2="&#x3b;" k="90" />
<hkern u1="V" u2="&#x3a;" k="90" />
<hkern u1="V" u2="&#x2f;" k="141" />
<hkern u1="V" u2="&#x2e;" k="201" />
<hkern u1="V" u2="&#x2d;" k="134" />
<hkern u1="V" u2="&#x2c;" k="201" />
<hkern u1="V" u2="&#x2a;" k="-48" />
<hkern u1="V" u2="&#x27;" k="-48" />
<hkern u1="V" u2="&#x26;" k="141" />
<hkern u1="V" u2="&#x22;" k="-48" />
<hkern u1="W" u2="&#x2206;" k="89" />
<hkern u1="W" u2="&#x2026;" k="102" />
<hkern u1="W" u2="&#x201e;" k="102" />
<hkern u1="W" u2="&#x201d;" k="-50" />
<hkern u1="W" u2="&#x201c;" k="-50" />
<hkern u1="W" u2="&#x201a;" k="102" />
<hkern u1="W" u2="&#x2019;" k="-50" />
<hkern u1="W" u2="&#x2018;" k="-50" />
<hkern u1="W" u2="&#x161;" k="95" />
<hkern u1="W" u2="&#x15b;" k="95" />
<hkern u1="W" u2="&#x153;" k="124" />
<hkern u1="W" u2="&#x144;" k="75" />
<hkern u1="W" u2="&#x119;" k="124" />
<hkern u1="W" u2="&#x107;" k="124" />
<hkern u1="W" u2="&#x105;" k="124" />
<hkern u1="W" u2="&#x104;" k="89" />
<hkern u1="W" u2="&#xfc;" k="75" />
<hkern u1="W" u2="&#xfb;" k="75" />
<hkern u1="W" u2="&#xfa;" k="75" />
<hkern u1="W" u2="&#xf9;" k="75" />
<hkern u1="W" u2="&#xf8;" k="124" />
<hkern u1="W" u2="&#xf6;" k="124" />
<hkern u1="W" u2="&#xf5;" k="124" />
<hkern u1="W" u2="&#xf4;" k="124" />
<hkern u1="W" u2="&#xf3;" k="124" />
<hkern u1="W" u2="&#xf2;" k="124" />
<hkern u1="W" u2="&#xf1;" k="75" />
<hkern u1="W" u2="&#xf0;" k="124" />
<hkern u1="W" u2="&#xeb;" k="124" />
<hkern u1="W" u2="&#xea;" k="124" />
<hkern u1="W" u2="&#xe9;" k="124" />
<hkern u1="W" u2="&#xe8;" k="124" />
<hkern u1="W" u2="&#xe7;" k="124" />
<hkern u1="W" u2="&#xe6;" k="124" />
<hkern u1="W" u2="&#xe5;" k="124" />
<hkern u1="W" u2="&#xe4;" k="124" />
<hkern u1="W" u2="&#xe3;" k="124" />
<hkern u1="W" u2="&#xe2;" k="124" />
<hkern u1="W" u2="&#xe1;" k="124" />
<hkern u1="W" u2="&#xe0;" k="124" />
<hkern u1="W" u2="&#xc6;" k="89" />
<hkern u1="W" u2="&#xc5;" k="89" />
<hkern u1="W" u2="&#xc4;" k="89" />
<hkern u1="W" u2="&#xc3;" k="89" />
<hkern u1="W" u2="&#xc2;" k="89" />
<hkern u1="W" u2="&#xc1;" k="89" />
<hkern u1="W" u2="&#xc0;" k="89" />
<hkern u1="W" u2="&#xba;" k="-50" />
<hkern u1="W" u2="&#xb5;" k="75" />
<hkern u1="W" u2="&#xb0;" k="-50" />
<hkern u1="W" u2="&#xaa;" k="-50" />
<hkern u1="W" u2="u" k="75" />
<hkern u1="W" u2="s" k="95" />
<hkern u1="W" u2="r" k="75" />
<hkern u1="W" u2="q" k="124" />
<hkern u1="W" u2="p" k="75" />
<hkern u1="W" u2="o" k="124" />
<hkern u1="W" u2="n" k="75" />
<hkern u1="W" u2="m" k="75" />
<hkern u1="W" u2="g" k="92" />
<hkern u1="W" u2="e" k="124" />
<hkern u1="W" u2="d" k="124" />
<hkern u1="W" u2="c" k="124" />
<hkern u1="W" u2="a" k="124" />
<hkern u1="W" u2="J" k="114" />
<hkern u1="W" u2="A" k="89" />
<hkern u1="W" u2="&#x3b;" k="75" />
<hkern u1="W" u2="&#x3a;" k="75" />
<hkern u1="W" u2="&#x2f;" k="89" />
<hkern u1="W" u2="&#x2e;" k="102" />
<hkern u1="W" u2="&#x2c;" k="102" />
<hkern u1="W" u2="&#x2a;" k="-50" />
<hkern u1="W" u2="&#x27;" k="-50" />
<hkern u1="W" u2="&#x26;" k="89" />
<hkern u1="W" u2="&#x22;" k="-50" />
<hkern u1="X" u2="&#x203a;" k="72" />
<hkern u1="X" u2="&#x2039;" k="72" />
<hkern u1="X" u2="&#x2022;" k="72" />
<hkern u1="X" u2="&#x201d;" k="-37" />
<hkern u1="X" u2="&#x201c;" k="-37" />
<hkern u1="X" u2="&#x2019;" k="-37" />
<hkern u1="X" u2="&#x2018;" k="-37" />
<hkern u1="X" u2="&#x2014;" k="72" />
<hkern u1="X" u2="&#x2013;" k="72" />
<hkern u1="X" u2="&#x152;" k="114" />
<hkern u1="X" u2="&#x106;" k="114" />
<hkern u1="X" u2="&#xff;" k="73" />
<hkern u1="X" u2="&#xfd;" k="73" />
<hkern u1="X" u2="&#xd8;" k="114" />
<hkern u1="X" u2="&#xd6;" k="114" />
<hkern u1="X" u2="&#xd5;" k="114" />
<hkern u1="X" u2="&#xd4;" k="114" />
<hkern u1="X" u2="&#xd3;" k="114" />
<hkern u1="X" u2="&#xd2;" k="114" />
<hkern u1="X" u2="&#xc7;" k="114" />
<hkern u1="X" u2="&#xbb;" k="72" />
<hkern u1="X" u2="&#xba;" k="-37" />
<hkern u1="X" u2="&#xb7;" k="72" />
<hkern u1="X" u2="&#xb0;" k="-37" />
<hkern u1="X" u2="&#xae;" k="114" />
<hkern u1="X" u2="&#xab;" k="72" />
<hkern u1="X" u2="&#xaa;" k="-37" />
<hkern u1="X" u2="&#xa9;" k="114" />
<hkern u1="X" u2="y" k="73" />
<hkern u1="X" u2="w" k="49" />
<hkern u1="X" u2="v" k="73" />
<hkern u1="X" u2="t" k="130" />
<hkern u1="X" u2="f" k="57" />
<hkern u1="X" u2="Q" k="114" />
<hkern u1="X" u2="O" k="114" />
<hkern u1="X" u2="G" k="114" />
<hkern u1="X" u2="C" k="114" />
<hkern u1="X" u2="&#x40;" k="114" />
<hkern u1="X" u2="&#x2d;" k="72" />
<hkern u1="X" u2="&#x2a;" k="-37" />
<hkern u1="X" u2="&#x27;" k="-37" />
<hkern u1="X" u2="&#x22;" k="-37" />
<hkern u1="Y" u2="&#x2206;" k="155" />
<hkern u1="Y" u2="&#x203a;" k="196" />
<hkern u1="Y" u2="&#x2039;" k="196" />
<hkern u1="Y" u2="&#x2026;" k="266" />
<hkern u1="Y" u2="&#x2022;" k="196" />
<hkern u1="Y" u2="&#x201e;" k="266" />
<hkern u1="Y" u2="&#x201d;" k="-59" />
<hkern u1="Y" u2="&#x201c;" k="-59" />
<hkern u1="Y" u2="&#x201a;" k="266" />
<hkern u1="Y" u2="&#x2019;" k="-59" />
<hkern u1="Y" u2="&#x2018;" k="-59" />
<hkern u1="Y" u2="&#x2014;" k="196" />
<hkern u1="Y" u2="&#x2013;" k="196" />
<hkern u1="Y" u2="&#x17e;" k="119" />
<hkern u1="Y" u2="&#x17c;" k="119" />
<hkern u1="Y" u2="&#x17a;" k="119" />
<hkern u1="Y" u2="&#x161;" k="201" />
<hkern u1="Y" u2="&#x15b;" k="201" />
<hkern u1="Y" u2="&#x153;" k="201" />
<hkern u1="Y" u2="&#x152;" k="66" />
<hkern u1="Y" u2="&#x144;" k="115" />
<hkern u1="Y" u2="&#x119;" k="201" />
<hkern u1="Y" u2="&#x107;" k="201" />
<hkern u1="Y" u2="&#x106;" k="66" />
<hkern u1="Y" u2="&#x105;" k="201" />
<hkern u1="Y" u2="&#x104;" k="155" />
<hkern u1="Y" u2="&#xfc;" k="115" />
<hkern u1="Y" u2="&#xfb;" k="115" />
<hkern u1="Y" u2="&#xfa;" k="115" />
<hkern u1="Y" u2="&#xf9;" k="115" />
<hkern u1="Y" u2="&#xf8;" k="201" />
<hkern u1="Y" u2="&#xf6;" k="201" />
<hkern u1="Y" u2="&#xf5;" k="201" />
<hkern u1="Y" u2="&#xf4;" k="201" />
<hkern u1="Y" u2="&#xf3;" k="201" />
<hkern u1="Y" u2="&#xf2;" k="201" />
<hkern u1="Y" u2="&#xf1;" k="115" />
<hkern u1="Y" u2="&#xf0;" k="201" />
<hkern u1="Y" u2="&#xeb;" k="201" />
<hkern u1="Y" u2="&#xea;" k="201" />
<hkern u1="Y" u2="&#xe9;" k="201" />
<hkern u1="Y" u2="&#xe8;" k="201" />
<hkern u1="Y" u2="&#xe7;" k="201" />
<hkern u1="Y" u2="&#xe6;" k="201" />
<hkern u1="Y" u2="&#xe5;" k="201" />
<hkern u1="Y" u2="&#xe4;" k="201" />
<hkern u1="Y" u2="&#xe3;" k="201" />
<hkern u1="Y" u2="&#xe2;" k="201" />
<hkern u1="Y" u2="&#xe1;" k="201" />
<hkern u1="Y" u2="&#xe0;" k="201" />
<hkern u1="Y" u2="&#xd8;" k="66" />
<hkern u1="Y" u2="&#xd6;" k="66" />
<hkern u1="Y" u2="&#xd5;" k="66" />
<hkern u1="Y" u2="&#xd4;" k="66" />
<hkern u1="Y" u2="&#xd3;" k="66" />
<hkern u1="Y" u2="&#xd2;" k="66" />
<hkern u1="Y" u2="&#xc7;" k="66" />
<hkern u1="Y" u2="&#xc6;" k="155" />
<hkern u1="Y" u2="&#xc5;" k="155" />
<hkern u1="Y" u2="&#xc4;" k="155" />
<hkern u1="Y" u2="&#xc3;" k="155" />
<hkern u1="Y" u2="&#xc2;" k="155" />
<hkern u1="Y" u2="&#xc1;" k="155" />
<hkern u1="Y" u2="&#xc0;" k="155" />
<hkern u1="Y" u2="&#xbb;" k="196" />
<hkern u1="Y" u2="&#xba;" k="-59" />
<hkern u1="Y" u2="&#xb7;" k="196" />
<hkern u1="Y" u2="&#xb5;" k="115" />
<hkern u1="Y" u2="&#xb0;" k="-59" />
<hkern u1="Y" u2="&#xae;" k="66" />
<hkern u1="Y" u2="&#xab;" k="196" />
<hkern u1="Y" u2="&#xaa;" k="-59" />
<hkern u1="Y" u2="&#xa9;" k="66" />
<hkern u1="Y" u2="z" k="119" />
<hkern u1="Y" u2="u" k="115" />
<hkern u1="Y" u2="s" k="201" />
<hkern u1="Y" u2="r" k="115" />
<hkern u1="Y" u2="q" k="201" />
<hkern u1="Y" u2="p" k="115" />
<hkern u1="Y" u2="o" k="201" />
<hkern u1="Y" u2="n" k="115" />
<hkern u1="Y" u2="m" k="115" />
<hkern u1="Y" u2="g" k="179" />
<hkern u1="Y" u2="e" k="201" />
<hkern u1="Y" u2="d" k="201" />
<hkern u1="Y" u2="c" k="201" />
<hkern u1="Y" u2="a" k="201" />
<hkern u1="Y" u2="Q" k="66" />
<hkern u1="Y" u2="O" k="66" />
<hkern u1="Y" u2="J" k="205" />
<hkern u1="Y" u2="G" k="66" />
<hkern u1="Y" u2="C" k="66" />
<hkern u1="Y" u2="A" k="155" />
<hkern u1="Y" u2="&#x40;" k="66" />
<hkern u1="Y" u2="&#x3f;" k="-51" />
<hkern u1="Y" u2="&#x3b;" k="115" />
<hkern u1="Y" u2="&#x3a;" k="115" />
<hkern u1="Y" u2="&#x2f;" k="155" />
<hkern u1="Y" u2="&#x2e;" k="266" />
<hkern u1="Y" u2="&#x2d;" k="196" />
<hkern u1="Y" u2="&#x2c;" k="266" />
<hkern u1="Y" u2="&#x2a;" k="-59" />
<hkern u1="Y" u2="&#x27;" k="-59" />
<hkern u1="Y" u2="&#x26;" k="155" />
<hkern u1="Y" u2="&#x22;" k="-59" />
<hkern u1="Z" u2="&#x203a;" k="84" />
<hkern u1="Z" u2="&#x2039;" k="84" />
<hkern u1="Z" u2="&#x2022;" k="84" />
<hkern u1="Z" u2="&#x2014;" k="84" />
<hkern u1="Z" u2="&#x2013;" k="84" />
<hkern u1="Z" u2="&#x152;" k="50" />
<hkern u1="Z" u2="&#x106;" k="50" />
<hkern u1="Z" u2="&#xd8;" k="50" />
<hkern u1="Z" u2="&#xd6;" k="50" />
<hkern u1="Z" u2="&#xd5;" k="50" />
<hkern u1="Z" u2="&#xd4;" k="50" />
<hkern u1="Z" u2="&#xd3;" k="50" />
<hkern u1="Z" u2="&#xd2;" k="50" />
<hkern u1="Z" u2="&#xc7;" k="50" />
<hkern u1="Z" u2="&#xbb;" k="84" />
<hkern u1="Z" u2="&#xb7;" k="84" />
<hkern u1="Z" u2="&#xae;" k="50" />
<hkern u1="Z" u2="&#xab;" k="84" />
<hkern u1="Z" u2="&#xa9;" k="50" />
<hkern u1="Z" u2="Q" k="50" />
<hkern u1="Z" u2="O" k="50" />
<hkern u1="Z" u2="G" k="50" />
<hkern u1="Z" u2="C" k="50" />
<hkern u1="Z" u2="&#x40;" k="50" />
<hkern u1="Z" u2="&#x3f;" k="-39" />
<hkern u1="Z" u2="&#x2d;" k="84" />
<hkern u1="[" u2="&#x153;" k="37" />
<hkern u1="[" u2="&#x152;" k="41" />
<hkern u1="[" u2="&#x119;" k="37" />
<hkern u1="[" u2="&#x107;" k="37" />
<hkern u1="[" u2="&#x106;" k="41" />
<hkern u1="[" u2="&#x105;" k="37" />
<hkern u1="[" u2="&#xf8;" k="37" />
<hkern u1="[" u2="&#xf6;" k="37" />
<hkern u1="[" u2="&#xf5;" k="37" />
<hkern u1="[" u2="&#xf4;" k="37" />
<hkern u1="[" u2="&#xf3;" k="37" />
<hkern u1="[" u2="&#xf2;" k="37" />
<hkern u1="[" u2="&#xf0;" k="37" />
<hkern u1="[" u2="&#xeb;" k="37" />
<hkern u1="[" u2="&#xea;" k="37" />
<hkern u1="[" u2="&#xe9;" k="37" />
<hkern u1="[" u2="&#xe8;" k="37" />
<hkern u1="[" u2="&#xe7;" k="37" />
<hkern u1="[" u2="&#xe6;" k="37" />
<hkern u1="[" u2="&#xe5;" k="37" />
<hkern u1="[" u2="&#xe4;" k="37" />
<hkern u1="[" u2="&#xe3;" k="37" />
<hkern u1="[" u2="&#xe2;" k="37" />
<hkern u1="[" u2="&#xe1;" k="37" />
<hkern u1="[" u2="&#xe0;" k="37" />
<hkern u1="[" u2="&#xd8;" k="41" />
<hkern u1="[" u2="&#xd6;" k="41" />
<hkern u1="[" u2="&#xd5;" k="41" />
<hkern u1="[" u2="&#xd4;" k="41" />
<hkern u1="[" u2="&#xd3;" k="41" />
<hkern u1="[" u2="&#xd2;" k="41" />
<hkern u1="[" u2="&#xc7;" k="41" />
<hkern u1="[" u2="&#xae;" k="41" />
<hkern u1="[" u2="&#xa9;" k="41" />
<hkern u1="[" u2="q" k="37" />
<hkern u1="[" u2="o" k="37" />
<hkern u1="[" u2="e" k="37" />
<hkern u1="[" u2="d" k="37" />
<hkern u1="[" u2="c" k="37" />
<hkern u1="[" u2="a" k="37" />
<hkern u1="[" u2="Q" k="41" />
<hkern u1="[" u2="O" k="41" />
<hkern u1="[" u2="G" k="41" />
<hkern u1="[" u2="C" k="41" />
<hkern u1="[" u2="&#x40;" k="41" />
<hkern u1="\" u2="&#x203a;" k="69" />
<hkern u1="\" u2="&#x2039;" k="69" />
<hkern u1="\" u2="&#x2022;" k="69" />
<hkern u1="\" u2="&#x201d;" k="196" />
<hkern u1="\" u2="&#x201c;" k="196" />
<hkern u1="\" u2="&#x2019;" k="196" />
<hkern u1="\" u2="&#x2018;" k="196" />
<hkern u1="\" u2="&#x2014;" k="69" />
<hkern u1="\" u2="&#x2013;" k="69" />
<hkern u1="\" u2="&#x178;" k="171" />
<hkern u1="\" u2="&#x152;" k="60" />
<hkern u1="\" u2="&#x106;" k="60" />
<hkern u1="\" u2="&#xff;" k="60" />
<hkern u1="\" u2="&#xfd;" k="60" />
<hkern u1="\" u2="&#xdd;" k="171" />
<hkern u1="\" u2="&#xdc;" k="41" />
<hkern u1="\" u2="&#xdb;" k="41" />
<hkern u1="\" u2="&#xda;" k="41" />
<hkern u1="\" u2="&#xd9;" k="41" />
<hkern u1="\" u2="&#xd8;" k="60" />
<hkern u1="\" u2="&#xd6;" k="60" />
<hkern u1="\" u2="&#xd5;" k="60" />
<hkern u1="\" u2="&#xd4;" k="60" />
<hkern u1="\" u2="&#xd3;" k="60" />
<hkern u1="\" u2="&#xd2;" k="60" />
<hkern u1="\" u2="&#xc7;" k="60" />
<hkern u1="\" u2="&#xbb;" k="69" />
<hkern u1="\" u2="&#xba;" k="196" />
<hkern u1="\" u2="&#xb7;" k="69" />
<hkern u1="\" u2="&#xb0;" k="196" />
<hkern u1="\" u2="&#xae;" k="60" />
<hkern u1="\" u2="&#xab;" k="69" />
<hkern u1="\" u2="&#xaa;" k="196" />
<hkern u1="\" u2="&#xa9;" k="60" />
<hkern u1="\" u2="y" k="60" />
<hkern u1="\" u2="w" k="48" />
<hkern u1="\" u2="v" k="60" />
<hkern u1="\" u2="t" k="71" />
<hkern u1="\" u2="\" k="141" />
<hkern u1="\" u2="Y" k="171" />
<hkern u1="\" u2="W" k="104" />
<hkern u1="\" u2="V" k="141" />
<hkern u1="\" u2="U" k="41" />
<hkern u1="\" u2="T" k="151" />
<hkern u1="\" u2="Q" k="60" />
<hkern u1="\" u2="O" k="60" />
<hkern u1="\" u2="J" k="-73" />
<hkern u1="\" u2="G" k="60" />
<hkern u1="\" u2="C" k="60" />
<hkern u1="\" u2="&#x40;" k="60" />
<hkern u1="\" u2="&#x2d;" k="69" />
<hkern u1="\" u2="&#x2a;" k="196" />
<hkern u1="\" u2="&#x27;" k="196" />
<hkern u1="\" u2="&#x22;" k="196" />
<hkern u1="b" u2="&#x201d;" k="82" />
<hkern u1="b" u2="&#x201c;" k="82" />
<hkern u1="b" u2="&#x2019;" k="82" />
<hkern u1="b" u2="&#x2018;" k="82" />
<hkern u1="b" u2="&#xba;" k="82" />
<hkern u1="b" u2="&#xb0;" k="82" />
<hkern u1="b" u2="&#xaa;" k="82" />
<hkern u1="b" u2="&#x7d;" k="37" />
<hkern u1="b" u2="x" k="57" />
<hkern u1="b" u2="]" k="37" />
<hkern u1="b" u2="&#x2a;" k="82" />
<hkern u1="b" u2="&#x29;" k="37" />
<hkern u1="b" u2="&#x27;" k="82" />
<hkern u1="b" u2="&#x22;" k="82" />
<hkern u1="e" u2="&#x201d;" k="82" />
<hkern u1="e" u2="&#x201c;" k="82" />
<hkern u1="e" u2="&#x2019;" k="82" />
<hkern u1="e" u2="&#x2018;" k="82" />
<hkern u1="e" u2="&#xba;" k="82" />
<hkern u1="e" u2="&#xb0;" k="82" />
<hkern u1="e" u2="&#xaa;" k="82" />
<hkern u1="e" u2="&#x7d;" k="37" />
<hkern u1="e" u2="x" k="57" />
<hkern u1="e" u2="]" k="37" />
<hkern u1="e" u2="&#x2a;" k="82" />
<hkern u1="e" u2="&#x29;" k="37" />
<hkern u1="e" u2="&#x27;" k="82" />
<hkern u1="e" u2="&#x22;" k="82" />
<hkern u1="f" u2="&#x2026;" k="127" />
<hkern u1="f" u2="&#x201e;" k="127" />
<hkern u1="f" u2="&#x201d;" k="-66" />
<hkern u1="f" u2="&#x201c;" k="-66" />
<hkern u1="f" u2="&#x201a;" k="127" />
<hkern u1="f" u2="&#x2019;" k="-66" />
<hkern u1="f" u2="&#x2018;" k="-66" />
<hkern u1="f" u2="&#xba;" k="-66" />
<hkern u1="f" u2="&#xb0;" k="-66" />
<hkern u1="f" u2="&#xaa;" k="-66" />
<hkern u1="f" u2="&#x2e;" k="127" />
<hkern u1="f" u2="&#x2c;" k="127" />
<hkern u1="f" u2="&#x2a;" k="-66" />
<hkern u1="f" u2="&#x27;" k="-66" />
<hkern u1="f" u2="&#x22;" k="-66" />
<hkern u1="h" u2="&#x201d;" k="61" />
<hkern u1="h" u2="&#x201c;" k="61" />
<hkern u1="h" u2="&#x2019;" k="61" />
<hkern u1="h" u2="&#x2018;" k="61" />
<hkern u1="h" u2="&#xff;" k="37" />
<hkern u1="h" u2="&#xfd;" k="37" />
<hkern u1="h" u2="&#xba;" k="61" />
<hkern u1="h" u2="&#xb0;" k="61" />
<hkern u1="h" u2="&#xaa;" k="61" />
<hkern u1="h" u2="y" k="52" />
<hkern u1="h" u2="v" k="37" />
<hkern u1="h" u2="&#x2a;" k="61" />
<hkern u1="h" u2="&#x27;" k="61" />
<hkern u1="h" u2="&#x22;" k="61" />
<hkern u1="k" u2="&#x153;" k="57" />
<hkern u1="k" u2="&#x119;" k="57" />
<hkern u1="k" u2="&#x107;" k="57" />
<hkern u1="k" u2="&#x105;" k="57" />
<hkern u1="k" u2="&#xf8;" k="57" />
<hkern u1="k" u2="&#xf6;" k="57" />
<hkern u1="k" u2="&#xf5;" k="57" />
<hkern u1="k" u2="&#xf4;" k="57" />
<hkern u1="k" u2="&#xf3;" k="57" />
<hkern u1="k" u2="&#xf2;" k="57" />
<hkern u1="k" u2="&#xf0;" k="57" />
<hkern u1="k" u2="&#xeb;" k="57" />
<hkern u1="k" u2="&#xea;" k="57" />
<hkern u1="k" u2="&#xe9;" k="57" />
<hkern u1="k" u2="&#xe8;" k="57" />
<hkern u1="k" u2="&#xe7;" k="57" />
<hkern u1="k" u2="&#xe6;" k="57" />
<hkern u1="k" u2="&#xe5;" k="57" />
<hkern u1="k" u2="&#xe4;" k="57" />
<hkern u1="k" u2="&#xe3;" k="57" />
<hkern u1="k" u2="&#xe2;" k="57" />
<hkern u1="k" u2="&#xe1;" k="57" />
<hkern u1="k" u2="&#xe0;" k="57" />
<hkern u1="k" u2="q" k="57" />
<hkern u1="k" u2="o" k="57" />
<hkern u1="k" u2="e" k="57" />
<hkern u1="k" u2="d" k="57" />
<hkern u1="k" u2="c" k="57" />
<hkern u1="k" u2="a" k="57" />
<hkern u1="m" u2="&#x201d;" k="61" />
<hkern u1="m" u2="&#x201c;" k="61" />
<hkern u1="m" u2="&#x2019;" k="61" />
<hkern u1="m" u2="&#x2018;" k="61" />
<hkern u1="m" u2="&#xff;" k="37" />
<hkern u1="m" u2="&#xfd;" k="37" />
<hkern u1="m" u2="&#xba;" k="61" />
<hkern u1="m" u2="&#xb0;" k="61" />
<hkern u1="m" u2="&#xaa;" k="61" />
<hkern u1="m" u2="y" k="52" />
<hkern u1="m" u2="v" k="37" />
<hkern u1="m" u2="&#x2a;" k="61" />
<hkern u1="m" u2="&#x27;" k="61" />
<hkern u1="m" u2="&#x22;" k="61" />
<hkern u1="n" u2="&#x201d;" k="61" />
<hkern u1="n" u2="&#x201c;" k="61" />
<hkern u1="n" u2="&#x2019;" k="61" />
<hkern u1="n" u2="&#x2018;" k="61" />
<hkern u1="n" u2="&#xff;" k="37" />
<hkern u1="n" u2="&#xfd;" k="37" />
<hkern u1="n" u2="&#xba;" k="61" />
<hkern u1="n" u2="&#xb0;" k="61" />
<hkern u1="n" u2="&#xaa;" k="61" />
<hkern u1="n" u2="y" k="52" />
<hkern u1="n" u2="v" k="37" />
<hkern u1="n" u2="&#x2a;" k="61" />
<hkern u1="n" u2="&#x27;" k="61" />
<hkern u1="n" u2="&#x22;" k="61" />
<hkern u1="o" u2="&#x201d;" k="82" />
<hkern u1="o" u2="&#x201c;" k="82" />
<hkern u1="o" u2="&#x2019;" k="82" />
<hkern u1="o" u2="&#x2018;" k="82" />
<hkern u1="o" u2="&#xba;" k="82" />
<hkern u1="o" u2="&#xb0;" k="82" />
<hkern u1="o" u2="&#xaa;" k="82" />
<hkern u1="o" u2="&#x7d;" k="37" />
<hkern u1="o" u2="x" k="57" />
<hkern u1="o" u2="]" k="37" />
<hkern u1="o" u2="&#x2a;" k="82" />
<hkern u1="o" u2="&#x29;" k="37" />
<hkern u1="o" u2="&#x27;" k="82" />
<hkern u1="o" u2="&#x22;" k="82" />
<hkern u1="p" u2="&#x201d;" k="82" />
<hkern u1="p" u2="&#x201c;" k="82" />
<hkern u1="p" u2="&#x2019;" k="82" />
<hkern u1="p" u2="&#x2018;" k="82" />
<hkern u1="p" u2="&#xba;" k="82" />
<hkern u1="p" u2="&#xb0;" k="82" />
<hkern u1="p" u2="&#xaa;" k="82" />
<hkern u1="p" u2="&#x7d;" k="37" />
<hkern u1="p" u2="x" k="57" />
<hkern u1="p" u2="]" k="37" />
<hkern u1="p" u2="&#x2a;" k="82" />
<hkern u1="p" u2="&#x29;" k="37" />
<hkern u1="p" u2="&#x27;" k="82" />
<hkern u1="p" u2="&#x22;" k="82" />
<hkern u1="r" u2="&#x2026;" k="171" />
<hkern u1="r" u2="&#x201e;" k="171" />
<hkern u1="r" u2="&#x201a;" k="171" />
<hkern u1="r" u2="&#x153;" k="41" />
<hkern u1="r" u2="&#x119;" k="41" />
<hkern u1="r" u2="&#x107;" k="41" />
<hkern u1="r" u2="&#x105;" k="41" />
<hkern u1="r" u2="&#xf8;" k="41" />
<hkern u1="r" u2="&#xf6;" k="41" />
<hkern u1="r" u2="&#xf5;" k="41" />
<hkern u1="r" u2="&#xf4;" k="41" />
<hkern u1="r" u2="&#xf3;" k="41" />
<hkern u1="r" u2="&#xf2;" k="41" />
<hkern u1="r" u2="&#xf0;" k="41" />
<hkern u1="r" u2="&#xeb;" k="41" />
<hkern u1="r" u2="&#xea;" k="41" />
<hkern u1="r" u2="&#xe9;" k="41" />
<hkern u1="r" u2="&#xe8;" k="41" />
<hkern u1="r" u2="&#xe7;" k="41" />
<hkern u1="r" u2="&#xe6;" k="41" />
<hkern u1="r" u2="&#xe5;" k="41" />
<hkern u1="r" u2="&#xe4;" k="41" />
<hkern u1="r" u2="&#xe3;" k="41" />
<hkern u1="r" u2="&#xe2;" k="41" />
<hkern u1="r" u2="&#xe1;" k="41" />
<hkern u1="r" u2="&#xe0;" k="41" />
<hkern u1="r" u2="q" k="41" />
<hkern u1="r" u2="o" k="41" />
<hkern u1="r" u2="e" k="41" />
<hkern u1="r" u2="d" k="41" />
<hkern u1="r" u2="c" k="41" />
<hkern u1="r" u2="a" k="41" />
<hkern u1="r" u2="&#x2e;" k="171" />
<hkern u1="r" u2="&#x2c;" k="171" />
<hkern u1="v" u2="&#x2206;" k="60" />
<hkern u1="v" u2="&#x2026;" k="155" />
<hkern u1="v" u2="&#x201e;" k="155" />
<hkern u1="v" u2="&#x201a;" k="155" />
<hkern u1="v" u2="&#x153;" k="34" />
<hkern u1="v" u2="&#x119;" k="34" />
<hkern u1="v" u2="&#x107;" k="34" />
<hkern u1="v" u2="&#x105;" k="34" />
<hkern u1="v" u2="&#x104;" k="60" />
<hkern u1="v" u2="&#xf8;" k="34" />
<hkern u1="v" u2="&#xf6;" k="34" />
<hkern u1="v" u2="&#xf5;" k="34" />
<hkern u1="v" u2="&#xf4;" k="34" />
<hkern u1="v" u2="&#xf3;" k="34" />
<hkern u1="v" u2="&#xf2;" k="34" />
<hkern u1="v" u2="&#xf0;" k="34" />
<hkern u1="v" u2="&#xeb;" k="34" />
<hkern u1="v" u2="&#xea;" k="34" />
<hkern u1="v" u2="&#xe9;" k="34" />
<hkern u1="v" u2="&#xe8;" k="34" />
<hkern u1="v" u2="&#xe7;" k="34" />
<hkern u1="v" u2="&#xe6;" k="34" />
<hkern u1="v" u2="&#xe5;" k="34" />
<hkern u1="v" u2="&#xe4;" k="34" />
<hkern u1="v" u2="&#xe3;" k="34" />
<hkern u1="v" u2="&#xe2;" k="34" />
<hkern u1="v" u2="&#xe1;" k="34" />
<hkern u1="v" u2="&#xe0;" k="34" />
<hkern u1="v" u2="&#xc6;" k="60" />
<hkern u1="v" u2="&#xc5;" k="60" />
<hkern u1="v" u2="&#xc4;" k="60" />
<hkern u1="v" u2="&#xc3;" k="60" />
<hkern u1="v" u2="&#xc2;" k="60" />
<hkern u1="v" u2="&#xc1;" k="60" />
<hkern u1="v" u2="&#xc0;" k="60" />
<hkern u1="v" u2="q" k="34" />
<hkern u1="v" u2="o" k="34" />
<hkern u1="v" u2="e" k="34" />
<hkern u1="v" u2="d" k="34" />
<hkern u1="v" u2="c" k="34" />
<hkern u1="v" u2="a" k="34" />
<hkern u1="v" u2="A" k="60" />
<hkern u1="v" u2="&#x2f;" k="60" />
<hkern u1="v" u2="&#x2e;" k="155" />
<hkern u1="v" u2="&#x2c;" k="155" />
<hkern u1="v" u2="&#x26;" k="60" />
<hkern u1="w" u2="&#x2206;" k="48" />
<hkern u1="w" u2="&#x2026;" k="104" />
<hkern u1="w" u2="&#x201e;" k="104" />
<hkern u1="w" u2="&#x201a;" k="104" />
<hkern u1="w" u2="&#x104;" k="48" />
<hkern u1="w" u2="&#xc6;" k="48" />
<hkern u1="w" u2="&#xc5;" k="48" />
<hkern u1="w" u2="&#xc4;" k="48" />
<hkern u1="w" u2="&#xc3;" k="48" />
<hkern u1="w" u2="&#xc2;" k="48" />
<hkern u1="w" u2="&#xc1;" k="48" />
<hkern u1="w" u2="&#xc0;" k="48" />
<hkern u1="w" u2="A" k="48" />
<hkern u1="w" u2="&#x2f;" k="48" />
<hkern u1="w" u2="&#x2e;" k="104" />
<hkern u1="w" u2="&#x2c;" k="104" />
<hkern u1="w" u2="&#x26;" k="48" />
<hkern u1="x" u2="&#x153;" k="57" />
<hkern u1="x" u2="&#x119;" k="57" />
<hkern u1="x" u2="&#x107;" k="57" />
<hkern u1="x" u2="&#x105;" k="57" />
<hkern u1="x" u2="&#xf8;" k="57" />
<hkern u1="x" u2="&#xf6;" k="57" />
<hkern u1="x" u2="&#xf5;" k="57" />
<hkern u1="x" u2="&#xf4;" k="57" />
<hkern u1="x" u2="&#xf3;" k="57" />
<hkern u1="x" u2="&#xf2;" k="57" />
<hkern u1="x" u2="&#xf0;" k="57" />
<hkern u1="x" u2="&#xeb;" k="57" />
<hkern u1="x" u2="&#xea;" k="57" />
<hkern u1="x" u2="&#xe9;" k="57" />
<hkern u1="x" u2="&#xe8;" k="57" />
<hkern u1="x" u2="&#xe7;" k="57" />
<hkern u1="x" u2="&#xe6;" k="57" />
<hkern u1="x" u2="&#xe5;" k="57" />
<hkern u1="x" u2="&#xe4;" k="57" />
<hkern u1="x" u2="&#xe3;" k="57" />
<hkern u1="x" u2="&#xe2;" k="57" />
<hkern u1="x" u2="&#xe1;" k="57" />
<hkern u1="x" u2="&#xe0;" k="57" />
<hkern u1="x" u2="q" k="57" />
<hkern u1="x" u2="o" k="57" />
<hkern u1="x" u2="e" k="57" />
<hkern u1="x" u2="d" k="57" />
<hkern u1="x" u2="c" k="57" />
<hkern u1="x" u2="a" k="57" />
<hkern u1="y" u2="&#x2206;" k="60" />
<hkern u1="y" u2="&#x2026;" k="171" />
<hkern u1="y" u2="&#x201e;" k="171" />
<hkern u1="y" u2="&#x201a;" k="171" />
<hkern u1="y" u2="&#x153;" k="34" />
<hkern u1="y" u2="&#x119;" k="34" />
<hkern u1="y" u2="&#x107;" k="34" />
<hkern u1="y" u2="&#x105;" k="34" />
<hkern u1="y" u2="&#x104;" k="60" />
<hkern u1="y" u2="&#xf8;" k="34" />
<hkern u1="y" u2="&#xf6;" k="34" />
<hkern u1="y" u2="&#xf5;" k="34" />
<hkern u1="y" u2="&#xf4;" k="34" />
<hkern u1="y" u2="&#xf3;" k="34" />
<hkern u1="y" u2="&#xf2;" k="34" />
<hkern u1="y" u2="&#xf0;" k="34" />
<hkern u1="y" u2="&#xeb;" k="34" />
<hkern u1="y" u2="&#xea;" k="34" />
<hkern u1="y" u2="&#xe9;" k="34" />
<hkern u1="y" u2="&#xe8;" k="34" />
<hkern u1="y" u2="&#xe7;" k="34" />
<hkern u1="y" u2="&#xe6;" k="34" />
<hkern u1="y" u2="&#xe5;" k="34" />
<hkern u1="y" u2="&#xe4;" k="34" />
<hkern u1="y" u2="&#xe3;" k="34" />
<hkern u1="y" u2="&#xe2;" k="34" />
<hkern u1="y" u2="&#xe1;" k="34" />
<hkern u1="y" u2="&#xe0;" k="34" />
<hkern u1="y" u2="&#xc6;" k="60" />
<hkern u1="y" u2="&#xc5;" k="60" />
<hkern u1="y" u2="&#xc4;" k="60" />
<hkern u1="y" u2="&#xc3;" k="60" />
<hkern u1="y" u2="&#xc2;" k="60" />
<hkern u1="y" u2="&#xc1;" k="60" />
<hkern u1="y" u2="&#xc0;" k="60" />
<hkern u1="y" u2="q" k="34" />
<hkern u1="y" u2="o" k="34" />
<hkern u1="y" u2="e" k="34" />
<hkern u1="y" u2="d" k="34" />
<hkern u1="y" u2="c" k="34" />
<hkern u1="y" u2="a" k="34" />
<hkern u1="y" u2="A" k="60" />
<hkern u1="y" u2="&#x2f;" k="60" />
<hkern u1="y" u2="&#x2e;" k="171" />
<hkern u1="y" u2="&#x2c;" k="171" />
<hkern u1="y" u2="&#x26;" k="60" />
<hkern u1="&#x7b;" u2="&#x153;" k="37" />
<hkern u1="&#x7b;" u2="&#x152;" k="41" />
<hkern u1="&#x7b;" u2="&#x119;" k="37" />
<hkern u1="&#x7b;" u2="&#x107;" k="37" />
<hkern u1="&#x7b;" u2="&#x106;" k="41" />
<hkern u1="&#x7b;" u2="&#x105;" k="37" />
<hkern u1="&#x7b;" u2="&#xf8;" k="37" />
<hkern u1="&#x7b;" u2="&#xf6;" k="37" />
<hkern u1="&#x7b;" u2="&#xf5;" k="37" />
<hkern u1="&#x7b;" u2="&#xf4;" k="37" />
<hkern u1="&#x7b;" u2="&#xf3;" k="37" />
<hkern u1="&#x7b;" u2="&#xf2;" k="37" />
<hkern u1="&#x7b;" u2="&#xf0;" k="37" />
<hkern u1="&#x7b;" u2="&#xeb;" k="37" />
<hkern u1="&#x7b;" u2="&#xea;" k="37" />
<hkern u1="&#x7b;" u2="&#xe9;" k="37" />
<hkern u1="&#x7b;" u2="&#xe8;" k="37" />
<hkern u1="&#x7b;" u2="&#xe7;" k="37" />
<hkern u1="&#x7b;" u2="&#xe6;" k="37" />
<hkern u1="&#x7b;" u2="&#xe5;" k="37" />
<hkern u1="&#x7b;" u2="&#xe4;" k="37" />
<hkern u1="&#x7b;" u2="&#xe3;" k="37" />
<hkern u1="&#x7b;" u2="&#xe2;" k="37" />
<hkern u1="&#x7b;" u2="&#xe1;" k="37" />
<hkern u1="&#x7b;" u2="&#xe0;" k="37" />
<hkern u1="&#x7b;" u2="&#xd8;" k="41" />
<hkern u1="&#x7b;" u2="&#xd6;" k="41" />
<hkern u1="&#x7b;" u2="&#xd5;" k="41" />
<hkern u1="&#x7b;" u2="&#xd4;" k="41" />
<hkern u1="&#x7b;" u2="&#xd3;" k="41" />
<hkern u1="&#x7b;" u2="&#xd2;" k="41" />
<hkern u1="&#x7b;" u2="&#xc7;" k="41" />
<hkern u1="&#x7b;" u2="&#xae;" k="41" />
<hkern u1="&#x7b;" u2="&#xa9;" k="41" />
<hkern u1="&#x7b;" u2="q" k="37" />
<hkern u1="&#x7b;" u2="o" k="37" />
<hkern u1="&#x7b;" u2="e" k="37" />
<hkern u1="&#x7b;" u2="d" k="37" />
<hkern u1="&#x7b;" u2="c" k="37" />
<hkern u1="&#x7b;" u2="a" k="37" />
<hkern u1="&#x7b;" u2="Q" k="41" />
<hkern u1="&#x7b;" u2="O" k="41" />
<hkern u1="&#x7b;" u2="G" k="41" />
<hkern u1="&#x7b;" u2="C" k="41" />
<hkern u1="&#x7b;" u2="&#x40;" k="41" />
<hkern u1="&#xa9;" u2="&#x2206;" k="44" />
<hkern u1="&#xa9;" u2="&#x201d;" k="53" />
<hkern u1="&#xa9;" u2="&#x201c;" k="53" />
<hkern u1="&#xa9;" u2="&#x2019;" k="53" />
<hkern u1="&#xa9;" u2="&#x2018;" k="53" />
<hkern u1="&#xa9;" u2="&#x17d;" k="66" />
<hkern u1="&#xa9;" u2="&#x17b;" k="66" />
<hkern u1="&#xa9;" u2="&#x179;" k="66" />
<hkern u1="&#xa9;" u2="&#x178;" k="82" />
<hkern u1="&#xa9;" u2="&#x104;" k="44" />
<hkern u1="&#xa9;" u2="&#xdd;" k="82" />
<hkern u1="&#xa9;" u2="&#xc6;" k="44" />
<hkern u1="&#xa9;" u2="&#xc5;" k="44" />
<hkern u1="&#xa9;" u2="&#xc4;" k="44" />
<hkern u1="&#xa9;" u2="&#xc3;" k="44" />
<hkern u1="&#xa9;" u2="&#xc2;" k="44" />
<hkern u1="&#xa9;" u2="&#xc1;" k="44" />
<hkern u1="&#xa9;" u2="&#xc0;" k="44" />
<hkern u1="&#xa9;" u2="&#xba;" k="53" />
<hkern u1="&#xa9;" u2="&#xb0;" k="53" />
<hkern u1="&#xa9;" u2="&#xaa;" k="53" />
<hkern u1="&#xa9;" u2="&#x7d;" k="41" />
<hkern u1="&#xa9;" u2="]" k="41" />
<hkern u1="&#xa9;" u2="\" k="57" />
<hkern u1="&#xa9;" u2="Z" k="66" />
<hkern u1="&#xa9;" u2="Y" k="82" />
<hkern u1="&#xa9;" u2="X" k="87" />
<hkern u1="&#xa9;" u2="V" k="57" />
<hkern u1="&#xa9;" u2="T" k="80" />
<hkern u1="&#xa9;" u2="A" k="44" />
<hkern u1="&#xa9;" u2="&#x2f;" k="44" />
<hkern u1="&#xa9;" u2="&#x2a;" k="53" />
<hkern u1="&#xa9;" u2="&#x29;" k="41" />
<hkern u1="&#xa9;" u2="&#x27;" k="53" />
<hkern u1="&#xa9;" u2="&#x26;" k="44" />
<hkern u1="&#xa9;" u2="&#x22;" k="53" />
<hkern u1="&#xaa;" u2="&#x2206;" k="180" />
<hkern u1="&#xaa;" u2="&#x203a;" k="41" />
<hkern u1="&#xaa;" u2="&#x2039;" k="41" />
<hkern u1="&#xaa;" u2="&#x2026;" k="45" />
<hkern u1="&#xaa;" u2="&#x2022;" k="41" />
<hkern u1="&#xaa;" u2="&#x201e;" k="45" />
<hkern u1="&#xaa;" u2="&#x201a;" k="45" />
<hkern u1="&#xaa;" u2="&#x2014;" k="41" />
<hkern u1="&#xaa;" u2="&#x2013;" k="41" />
<hkern u1="&#xaa;" u2="&#x178;" k="-43" />
<hkern u1="&#xaa;" u2="&#x153;" k="98" />
<hkern u1="&#xaa;" u2="&#x119;" k="98" />
<hkern u1="&#xaa;" u2="&#x107;" k="98" />
<hkern u1="&#xaa;" u2="&#x105;" k="98" />
<hkern u1="&#xaa;" u2="&#x104;" k="180" />
<hkern u1="&#xaa;" u2="&#xf8;" k="98" />
<hkern u1="&#xaa;" u2="&#xf6;" k="98" />
<hkern u1="&#xaa;" u2="&#xf5;" k="98" />
<hkern u1="&#xaa;" u2="&#xf4;" k="98" />
<hkern u1="&#xaa;" u2="&#xf3;" k="98" />
<hkern u1="&#xaa;" u2="&#xf2;" k="98" />
<hkern u1="&#xaa;" u2="&#xf0;" k="98" />
<hkern u1="&#xaa;" u2="&#xeb;" k="98" />
<hkern u1="&#xaa;" u2="&#xea;" k="98" />
<hkern u1="&#xaa;" u2="&#xe9;" k="98" />
<hkern u1="&#xaa;" u2="&#xe8;" k="98" />
<hkern u1="&#xaa;" u2="&#xe7;" k="98" />
<hkern u1="&#xaa;" u2="&#xe6;" k="98" />
<hkern u1="&#xaa;" u2="&#xe5;" k="98" />
<hkern u1="&#xaa;" u2="&#xe4;" k="98" />
<hkern u1="&#xaa;" u2="&#xe3;" k="98" />
<hkern u1="&#xaa;" u2="&#xe2;" k="98" />
<hkern u1="&#xaa;" u2="&#xe1;" k="98" />
<hkern u1="&#xaa;" u2="&#xe0;" k="98" />
<hkern u1="&#xaa;" u2="&#xdd;" k="-43" />
<hkern u1="&#xaa;" u2="&#xc6;" k="180" />
<hkern u1="&#xaa;" u2="&#xc5;" k="180" />
<hkern u1="&#xaa;" u2="&#xc4;" k="180" />
<hkern u1="&#xaa;" u2="&#xc3;" k="180" />
<hkern u1="&#xaa;" u2="&#xc2;" k="180" />
<hkern u1="&#xaa;" u2="&#xc1;" k="180" />
<hkern u1="&#xaa;" u2="&#xc0;" k="180" />
<hkern u1="&#xaa;" u2="&#xbb;" k="41" />
<hkern u1="&#xaa;" u2="&#xb7;" k="41" />
<hkern u1="&#xaa;" u2="&#xab;" k="41" />
<hkern u1="&#xaa;" u2="q" k="98" />
<hkern u1="&#xaa;" u2="o" k="98" />
<hkern u1="&#xaa;" u2="e" k="98" />
<hkern u1="&#xaa;" u2="d" k="98" />
<hkern u1="&#xaa;" u2="c" k="98" />
<hkern u1="&#xaa;" u2="a" k="98" />
<hkern u1="&#xaa;" u2="\" k="-48" />
<hkern u1="&#xaa;" u2="Y" k="-43" />
<hkern u1="&#xaa;" u2="W" k="-34" />
<hkern u1="&#xaa;" u2="V" k="-48" />
<hkern u1="&#xaa;" u2="A" k="180" />
<hkern u1="&#xaa;" u2="&#x2f;" k="180" />
<hkern u1="&#xaa;" u2="&#x2e;" k="45" />
<hkern u1="&#xaa;" u2="&#x2d;" k="41" />
<hkern u1="&#xaa;" u2="&#x2c;" k="45" />
<hkern u1="&#xaa;" u2="&#x26;" k="180" />
<hkern u1="&#xab;" u2="&#x2206;" k="69" />
<hkern u1="&#xab;" u2="&#x2026;" k="175" />
<hkern u1="&#xab;" u2="&#x201e;" k="175" />
<hkern u1="&#xab;" u2="&#x201d;" k="41" />
<hkern u1="&#xab;" u2="&#x201c;" k="41" />
<hkern u1="&#xab;" u2="&#x201a;" k="175" />
<hkern u1="&#xab;" u2="&#x2019;" k="41" />
<hkern u1="&#xab;" u2="&#x2018;" k="41" />
<hkern u1="&#xab;" u2="&#x17d;" k="59" />
<hkern u1="&#xab;" u2="&#x17b;" k="59" />
<hkern u1="&#xab;" u2="&#x179;" k="59" />
<hkern u1="&#xab;" u2="&#x178;" k="212" />
<hkern u1="&#xab;" u2="&#x104;" k="69" />
<hkern u1="&#xab;" u2="&#xdd;" k="212" />
<hkern u1="&#xab;" u2="&#xc6;" k="69" />
<hkern u1="&#xab;" u2="&#xc5;" k="69" />
<hkern u1="&#xab;" u2="&#xc4;" k="69" />
<hkern u1="&#xab;" u2="&#xc3;" k="69" />
<hkern u1="&#xab;" u2="&#xc2;" k="69" />
<hkern u1="&#xab;" u2="&#xc1;" k="69" />
<hkern u1="&#xab;" u2="&#xc0;" k="69" />
<hkern u1="&#xab;" u2="&#xba;" k="41" />
<hkern u1="&#xab;" u2="&#xb0;" k="41" />
<hkern u1="&#xab;" u2="&#xaa;" k="41" />
<hkern u1="&#xab;" u2="\" k="134" />
<hkern u1="&#xab;" u2="Z" k="59" />
<hkern u1="&#xab;" u2="Y" k="212" />
<hkern u1="&#xab;" u2="X" k="72" />
<hkern u1="&#xab;" u2="V" k="134" />
<hkern u1="&#xab;" u2="T" k="201" />
<hkern u1="&#xab;" u2="A" k="69" />
<hkern u1="&#xab;" u2="&#x2f;" k="69" />
<hkern u1="&#xab;" u2="&#x2e;" k="175" />
<hkern u1="&#xab;" u2="&#x2c;" k="175" />
<hkern u1="&#xab;" u2="&#x2a;" k="41" />
<hkern u1="&#xab;" u2="&#x27;" k="41" />
<hkern u1="&#xab;" u2="&#x26;" k="69" />
<hkern u1="&#xab;" u2="&#x22;" k="41" />
<hkern u1="&#xae;" u2="&#x2206;" k="44" />
<hkern u1="&#xae;" u2="&#x201d;" k="53" />
<hkern u1="&#xae;" u2="&#x201c;" k="53" />
<hkern u1="&#xae;" u2="&#x2019;" k="53" />
<hkern u1="&#xae;" u2="&#x2018;" k="53" />
<hkern u1="&#xae;" u2="&#x17d;" k="66" />
<hkern u1="&#xae;" u2="&#x17b;" k="66" />
<hkern u1="&#xae;" u2="&#x179;" k="66" />
<hkern u1="&#xae;" u2="&#x178;" k="82" />
<hkern u1="&#xae;" u2="&#x104;" k="44" />
<hkern u1="&#xae;" u2="&#xdd;" k="82" />
<hkern u1="&#xae;" u2="&#xc6;" k="44" />
<hkern u1="&#xae;" u2="&#xc5;" k="44" />
<hkern u1="&#xae;" u2="&#xc4;" k="44" />
<hkern u1="&#xae;" u2="&#xc3;" k="44" />
<hkern u1="&#xae;" u2="&#xc2;" k="44" />
<hkern u1="&#xae;" u2="&#xc1;" k="44" />
<hkern u1="&#xae;" u2="&#xc0;" k="44" />
<hkern u1="&#xae;" u2="&#xba;" k="53" />
<hkern u1="&#xae;" u2="&#xb0;" k="53" />
<hkern u1="&#xae;" u2="&#xaa;" k="53" />
<hkern u1="&#xae;" u2="&#x7d;" k="41" />
<hkern u1="&#xae;" u2="]" k="41" />
<hkern u1="&#xae;" u2="\" k="57" />
<hkern u1="&#xae;" u2="Z" k="66" />
<hkern u1="&#xae;" u2="Y" k="82" />
<hkern u1="&#xae;" u2="X" k="87" />
<hkern u1="&#xae;" u2="V" k="57" />
<hkern u1="&#xae;" u2="T" k="80" />
<hkern u1="&#xae;" u2="A" k="44" />
<hkern u1="&#xae;" u2="&#x2f;" k="44" />
<hkern u1="&#xae;" u2="&#x2a;" k="53" />
<hkern u1="&#xae;" u2="&#x29;" k="41" />
<hkern u1="&#xae;" u2="&#x27;" k="53" />
<hkern u1="&#xae;" u2="&#x26;" k="44" />
<hkern u1="&#xae;" u2="&#x22;" k="53" />
<hkern u1="&#xb0;" u2="&#x2206;" k="180" />
<hkern u1="&#xb0;" u2="&#x203a;" k="41" />
<hkern u1="&#xb0;" u2="&#x2039;" k="41" />
<hkern u1="&#xb0;" u2="&#x2026;" k="45" />
<hkern u1="&#xb0;" u2="&#x2022;" k="41" />
<hkern u1="&#xb0;" u2="&#x201e;" k="45" />
<hkern u1="&#xb0;" u2="&#x201a;" k="45" />
<hkern u1="&#xb0;" u2="&#x2014;" k="41" />
<hkern u1="&#xb0;" u2="&#x2013;" k="41" />
<hkern u1="&#xb0;" u2="&#x178;" k="-43" />
<hkern u1="&#xb0;" u2="&#x153;" k="98" />
<hkern u1="&#xb0;" u2="&#x119;" k="98" />
<hkern u1="&#xb0;" u2="&#x107;" k="98" />
<hkern u1="&#xb0;" u2="&#x105;" k="98" />
<hkern u1="&#xb0;" u2="&#x104;" k="180" />
<hkern u1="&#xb0;" u2="&#xf8;" k="98" />
<hkern u1="&#xb0;" u2="&#xf6;" k="98" />
<hkern u1="&#xb0;" u2="&#xf5;" k="98" />
<hkern u1="&#xb0;" u2="&#xf4;" k="98" />
<hkern u1="&#xb0;" u2="&#xf3;" k="98" />
<hkern u1="&#xb0;" u2="&#xf2;" k="98" />
<hkern u1="&#xb0;" u2="&#xf0;" k="98" />
<hkern u1="&#xb0;" u2="&#xeb;" k="98" />
<hkern u1="&#xb0;" u2="&#xea;" k="98" />
<hkern u1="&#xb0;" u2="&#xe9;" k="98" />
<hkern u1="&#xb0;" u2="&#xe8;" k="98" />
<hkern u1="&#xb0;" u2="&#xe7;" k="98" />
<hkern u1="&#xb0;" u2="&#xe6;" k="98" />
<hkern u1="&#xb0;" u2="&#xe5;" k="98" />
<hkern u1="&#xb0;" u2="&#xe4;" k="98" />
<hkern u1="&#xb0;" u2="&#xe3;" k="98" />
<hkern u1="&#xb0;" u2="&#xe2;" k="98" />
<hkern u1="&#xb0;" u2="&#xe1;" k="98" />
<hkern u1="&#xb0;" u2="&#xe0;" k="98" />
<hkern u1="&#xb0;" u2="&#xdd;" k="-43" />
<hkern u1="&#xb0;" u2="&#xc6;" k="180" />
<hkern u1="&#xb0;" u2="&#xc5;" k="180" />
<hkern u1="&#xb0;" u2="&#xc4;" k="180" />
<hkern u1="&#xb0;" u2="&#xc3;" k="180" />
<hkern u1="&#xb0;" u2="&#xc2;" k="180" />
<hkern u1="&#xb0;" u2="&#xc1;" k="180" />
<hkern u1="&#xb0;" u2="&#xc0;" k="180" />
<hkern u1="&#xb0;" u2="&#xbb;" k="41" />
<hkern u1="&#xb0;" u2="&#xb7;" k="41" />
<hkern u1="&#xb0;" u2="&#xab;" k="41" />
<hkern u1="&#xb0;" u2="q" k="98" />
<hkern u1="&#xb0;" u2="o" k="98" />
<hkern u1="&#xb0;" u2="e" k="98" />
<hkern u1="&#xb0;" u2="d" k="98" />
<hkern u1="&#xb0;" u2="c" k="98" />
<hkern u1="&#xb0;" u2="a" k="98" />
<hkern u1="&#xb0;" u2="\" k="-48" />
<hkern u1="&#xb0;" u2="Y" k="-43" />
<hkern u1="&#xb0;" u2="W" k="-34" />
<hkern u1="&#xb0;" u2="V" k="-48" />
<hkern u1="&#xb0;" u2="A" k="180" />
<hkern u1="&#xb0;" u2="&#x2f;" k="180" />
<hkern u1="&#xb0;" u2="&#x2e;" k="45" />
<hkern u1="&#xb0;" u2="&#x2d;" k="41" />
<hkern u1="&#xb0;" u2="&#x2c;" k="45" />
<hkern u1="&#xb0;" u2="&#x26;" k="180" />
<hkern u1="&#xb7;" u2="&#x2206;" k="69" />
<hkern u1="&#xb7;" u2="&#x2026;" k="175" />
<hkern u1="&#xb7;" u2="&#x201e;" k="175" />
<hkern u1="&#xb7;" u2="&#x201d;" k="41" />
<hkern u1="&#xb7;" u2="&#x201c;" k="41" />
<hkern u1="&#xb7;" u2="&#x201a;" k="175" />
<hkern u1="&#xb7;" u2="&#x2019;" k="41" />
<hkern u1="&#xb7;" u2="&#x2018;" k="41" />
<hkern u1="&#xb7;" u2="&#x17d;" k="59" />
<hkern u1="&#xb7;" u2="&#x17b;" k="59" />
<hkern u1="&#xb7;" u2="&#x179;" k="59" />
<hkern u1="&#xb7;" u2="&#x178;" k="212" />
<hkern u1="&#xb7;" u2="&#x104;" k="69" />
<hkern u1="&#xb7;" u2="&#xdd;" k="212" />
<hkern u1="&#xb7;" u2="&#xc6;" k="69" />
<hkern u1="&#xb7;" u2="&#xc5;" k="69" />
<hkern u1="&#xb7;" u2="&#xc4;" k="69" />
<hkern u1="&#xb7;" u2="&#xc3;" k="69" />
<hkern u1="&#xb7;" u2="&#xc2;" k="69" />
<hkern u1="&#xb7;" u2="&#xc1;" k="69" />
<hkern u1="&#xb7;" u2="&#xc0;" k="69" />
<hkern u1="&#xb7;" u2="&#xba;" k="41" />
<hkern u1="&#xb7;" u2="&#xb0;" k="41" />
<hkern u1="&#xb7;" u2="&#xaa;" k="41" />
<hkern u1="&#xb7;" u2="\" k="134" />
<hkern u1="&#xb7;" u2="Z" k="59" />
<hkern u1="&#xb7;" u2="Y" k="212" />
<hkern u1="&#xb7;" u2="X" k="72" />
<hkern u1="&#xb7;" u2="V" k="134" />
<hkern u1="&#xb7;" u2="T" k="201" />
<hkern u1="&#xb7;" u2="A" k="69" />
<hkern u1="&#xb7;" u2="&#x2f;" k="69" />
<hkern u1="&#xb7;" u2="&#x2e;" k="175" />
<hkern u1="&#xb7;" u2="&#x2c;" k="175" />
<hkern u1="&#xb7;" u2="&#x2a;" k="41" />
<hkern u1="&#xb7;" u2="&#x27;" k="41" />
<hkern u1="&#xb7;" u2="&#x26;" k="69" />
<hkern u1="&#xb7;" u2="&#x22;" k="41" />
<hkern u1="&#xba;" u2="&#x2206;" k="180" />
<hkern u1="&#xba;" u2="&#x203a;" k="41" />
<hkern u1="&#xba;" u2="&#x2039;" k="41" />
<hkern u1="&#xba;" u2="&#x2026;" k="45" />
<hkern u1="&#xba;" u2="&#x2022;" k="41" />
<hkern u1="&#xba;" u2="&#x201e;" k="45" />
<hkern u1="&#xba;" u2="&#x201a;" k="45" />
<hkern u1="&#xba;" u2="&#x2014;" k="41" />
<hkern u1="&#xba;" u2="&#x2013;" k="41" />
<hkern u1="&#xba;" u2="&#x178;" k="-43" />
<hkern u1="&#xba;" u2="&#x153;" k="98" />
<hkern u1="&#xba;" u2="&#x119;" k="98" />
<hkern u1="&#xba;" u2="&#x107;" k="98" />
<hkern u1="&#xba;" u2="&#x105;" k="98" />
<hkern u1="&#xba;" u2="&#x104;" k="180" />
<hkern u1="&#xba;" u2="&#xf8;" k="98" />
<hkern u1="&#xba;" u2="&#xf6;" k="98" />
<hkern u1="&#xba;" u2="&#xf5;" k="98" />
<hkern u1="&#xba;" u2="&#xf4;" k="98" />
<hkern u1="&#xba;" u2="&#xf3;" k="98" />
<hkern u1="&#xba;" u2="&#xf2;" k="98" />
<hkern u1="&#xba;" u2="&#xf0;" k="98" />
<hkern u1="&#xba;" u2="&#xeb;" k="98" />
<hkern u1="&#xba;" u2="&#xea;" k="98" />
<hkern u1="&#xba;" u2="&#xe9;" k="98" />
<hkern u1="&#xba;" u2="&#xe8;" k="98" />
<hkern u1="&#xba;" u2="&#xe7;" k="98" />
<hkern u1="&#xba;" u2="&#xe6;" k="98" />
<hkern u1="&#xba;" u2="&#xe5;" k="98" />
<hkern u1="&#xba;" u2="&#xe4;" k="98" />
<hkern u1="&#xba;" u2="&#xe3;" k="98" />
<hkern u1="&#xba;" u2="&#xe2;" k="98" />
<hkern u1="&#xba;" u2="&#xe1;" k="98" />
<hkern u1="&#xba;" u2="&#xe0;" k="98" />
<hkern u1="&#xba;" u2="&#xdd;" k="-43" />
<hkern u1="&#xba;" u2="&#xc6;" k="180" />
<hkern u1="&#xba;" u2="&#xc5;" k="180" />
<hkern u1="&#xba;" u2="&#xc4;" k="180" />
<hkern u1="&#xba;" u2="&#xc3;" k="180" />
<hkern u1="&#xba;" u2="&#xc2;" k="180" />
<hkern u1="&#xba;" u2="&#xc1;" k="180" />
<hkern u1="&#xba;" u2="&#xc0;" k="180" />
<hkern u1="&#xba;" u2="&#xbb;" k="41" />
<hkern u1="&#xba;" u2="&#xb7;" k="41" />
<hkern u1="&#xba;" u2="&#xab;" k="41" />
<hkern u1="&#xba;" u2="q" k="98" />
<hkern u1="&#xba;" u2="o" k="98" />
<hkern u1="&#xba;" u2="e" k="98" />
<hkern u1="&#xba;" u2="d" k="98" />
<hkern u1="&#xba;" u2="c" k="98" />
<hkern u1="&#xba;" u2="a" k="98" />
<hkern u1="&#xba;" u2="\" k="-48" />
<hkern u1="&#xba;" u2="Y" k="-43" />
<hkern u1="&#xba;" u2="W" k="-34" />
<hkern u1="&#xba;" u2="V" k="-48" />
<hkern u1="&#xba;" u2="A" k="180" />
<hkern u1="&#xba;" u2="&#x2f;" k="180" />
<hkern u1="&#xba;" u2="&#x2e;" k="45" />
<hkern u1="&#xba;" u2="&#x2d;" k="41" />
<hkern u1="&#xba;" u2="&#x2c;" k="45" />
<hkern u1="&#xba;" u2="&#x26;" k="180" />
<hkern u1="&#xbb;" u2="&#x2206;" k="69" />
<hkern u1="&#xbb;" u2="&#x2026;" k="175" />
<hkern u1="&#xbb;" u2="&#x201e;" k="175" />
<hkern u1="&#xbb;" u2="&#x201d;" k="41" />
<hkern u1="&#xbb;" u2="&#x201c;" k="41" />
<hkern u1="&#xbb;" u2="&#x201a;" k="175" />
<hkern u1="&#xbb;" u2="&#x2019;" k="41" />
<hkern u1="&#xbb;" u2="&#x2018;" k="41" />
<hkern u1="&#xbb;" u2="&#x17d;" k="59" />
<hkern u1="&#xbb;" u2="&#x17b;" k="59" />
<hkern u1="&#xbb;" u2="&#x179;" k="59" />
<hkern u1="&#xbb;" u2="&#x178;" k="212" />
<hkern u1="&#xbb;" u2="&#x104;" k="69" />
<hkern u1="&#xbb;" u2="&#xdd;" k="212" />
<hkern u1="&#xbb;" u2="&#xc6;" k="69" />
<hkern u1="&#xbb;" u2="&#xc5;" k="69" />
<hkern u1="&#xbb;" u2="&#xc4;" k="69" />
<hkern u1="&#xbb;" u2="&#xc3;" k="69" />
<hkern u1="&#xbb;" u2="&#xc2;" k="69" />
<hkern u1="&#xbb;" u2="&#xc1;" k="69" />
<hkern u1="&#xbb;" u2="&#xc0;" k="69" />
<hkern u1="&#xbb;" u2="&#xba;" k="41" />
<hkern u1="&#xbb;" u2="&#xb0;" k="41" />
<hkern u1="&#xbb;" u2="&#xaa;" k="41" />
<hkern u1="&#xbb;" u2="\" k="134" />
<hkern u1="&#xbb;" u2="Z" k="59" />
<hkern u1="&#xbb;" u2="Y" k="212" />
<hkern u1="&#xbb;" u2="X" k="72" />
<hkern u1="&#xbb;" u2="V" k="134" />
<hkern u1="&#xbb;" u2="T" k="201" />
<hkern u1="&#xbb;" u2="A" k="69" />
<hkern u1="&#xbb;" u2="&#x2f;" k="69" />
<hkern u1="&#xbb;" u2="&#x2e;" k="175" />
<hkern u1="&#xbb;" u2="&#x2c;" k="175" />
<hkern u1="&#xbb;" u2="&#x2a;" k="41" />
<hkern u1="&#xbb;" u2="&#x27;" k="41" />
<hkern u1="&#xbb;" u2="&#x26;" k="69" />
<hkern u1="&#xbb;" u2="&#x22;" k="41" />
<hkern u1="&#xc0;" u2="&#x203a;" k="69" />
<hkern u1="&#xc0;" u2="&#x2039;" k="69" />
<hkern u1="&#xc0;" u2="&#x2022;" k="69" />
<hkern u1="&#xc0;" u2="&#x201d;" k="196" />
<hkern u1="&#xc0;" u2="&#x201c;" k="196" />
<hkern u1="&#xc0;" u2="&#x2019;" k="196" />
<hkern u1="&#xc0;" u2="&#x2018;" k="196" />
<hkern u1="&#xc0;" u2="&#x2014;" k="69" />
<hkern u1="&#xc0;" u2="&#x2013;" k="69" />
<hkern u1="&#xc0;" u2="&#x178;" k="171" />
<hkern u1="&#xc0;" u2="&#x152;" k="60" />
<hkern u1="&#xc0;" u2="&#x106;" k="60" />
<hkern u1="&#xc0;" u2="&#xff;" k="60" />
<hkern u1="&#xc0;" u2="&#xfd;" k="60" />
<hkern u1="&#xc0;" u2="&#xdd;" k="171" />
<hkern u1="&#xc0;" u2="&#xdc;" k="41" />
<hkern u1="&#xc0;" u2="&#xdb;" k="41" />
<hkern u1="&#xc0;" u2="&#xda;" k="41" />
<hkern u1="&#xc0;" u2="&#xd9;" k="41" />
<hkern u1="&#xc0;" u2="&#xd8;" k="60" />
<hkern u1="&#xc0;" u2="&#xd6;" k="60" />
<hkern u1="&#xc0;" u2="&#xd5;" k="60" />
<hkern u1="&#xc0;" u2="&#xd4;" k="60" />
<hkern u1="&#xc0;" u2="&#xd3;" k="60" />
<hkern u1="&#xc0;" u2="&#xd2;" k="60" />
<hkern u1="&#xc0;" u2="&#xc7;" k="60" />
<hkern u1="&#xc0;" u2="&#xbb;" k="69" />
<hkern u1="&#xc0;" u2="&#xba;" k="196" />
<hkern u1="&#xc0;" u2="&#xb7;" k="69" />
<hkern u1="&#xc0;" u2="&#xb0;" k="196" />
<hkern u1="&#xc0;" u2="&#xae;" k="60" />
<hkern u1="&#xc0;" u2="&#xab;" k="69" />
<hkern u1="&#xc0;" u2="&#xaa;" k="196" />
<hkern u1="&#xc0;" u2="&#xa9;" k="60" />
<hkern u1="&#xc0;" u2="y" k="60" />
<hkern u1="&#xc0;" u2="w" k="48" />
<hkern u1="&#xc0;" u2="v" k="60" />
<hkern u1="&#xc0;" u2="t" k="71" />
<hkern u1="&#xc0;" u2="\" k="141" />
<hkern u1="&#xc0;" u2="Y" k="171" />
<hkern u1="&#xc0;" u2="W" k="104" />
<hkern u1="&#xc0;" u2="V" k="141" />
<hkern u1="&#xc0;" u2="U" k="41" />
<hkern u1="&#xc0;" u2="T" k="151" />
<hkern u1="&#xc0;" u2="Q" k="60" />
<hkern u1="&#xc0;" u2="O" k="60" />
<hkern u1="&#xc0;" u2="J" k="-73" />
<hkern u1="&#xc0;" u2="G" k="60" />
<hkern u1="&#xc0;" u2="C" k="60" />
<hkern u1="&#xc0;" u2="&#x40;" k="60" />
<hkern u1="&#xc0;" u2="&#x2d;" k="69" />
<hkern u1="&#xc0;" u2="&#x2a;" k="196" />
<hkern u1="&#xc0;" u2="&#x27;" k="196" />
<hkern u1="&#xc0;" u2="&#x22;" k="196" />
<hkern u1="&#xc1;" u2="&#x203a;" k="69" />
<hkern u1="&#xc1;" u2="&#x2039;" k="69" />
<hkern u1="&#xc1;" u2="&#x2022;" k="69" />
<hkern u1="&#xc1;" u2="&#x201d;" k="196" />
<hkern u1="&#xc1;" u2="&#x201c;" k="196" />
<hkern u1="&#xc1;" u2="&#x2019;" k="196" />
<hkern u1="&#xc1;" u2="&#x2018;" k="196" />
<hkern u1="&#xc1;" u2="&#x2014;" k="69" />
<hkern u1="&#xc1;" u2="&#x2013;" k="69" />
<hkern u1="&#xc1;" u2="&#x178;" k="171" />
<hkern u1="&#xc1;" u2="&#x152;" k="60" />
<hkern u1="&#xc1;" u2="&#x106;" k="60" />
<hkern u1="&#xc1;" u2="&#xff;" k="60" />
<hkern u1="&#xc1;" u2="&#xfd;" k="60" />
<hkern u1="&#xc1;" u2="&#xdd;" k="171" />
<hkern u1="&#xc1;" u2="&#xdc;" k="41" />
<hkern u1="&#xc1;" u2="&#xdb;" k="41" />
<hkern u1="&#xc1;" u2="&#xda;" k="41" />
<hkern u1="&#xc1;" u2="&#xd9;" k="41" />
<hkern u1="&#xc1;" u2="&#xd8;" k="60" />
<hkern u1="&#xc1;" u2="&#xd6;" k="60" />
<hkern u1="&#xc1;" u2="&#xd5;" k="60" />
<hkern u1="&#xc1;" u2="&#xd4;" k="60" />
<hkern u1="&#xc1;" u2="&#xd3;" k="60" />
<hkern u1="&#xc1;" u2="&#xd2;" k="60" />
<hkern u1="&#xc1;" u2="&#xc7;" k="60" />
<hkern u1="&#xc1;" u2="&#xbb;" k="69" />
<hkern u1="&#xc1;" u2="&#xba;" k="196" />
<hkern u1="&#xc1;" u2="&#xb7;" k="69" />
<hkern u1="&#xc1;" u2="&#xb0;" k="196" />
<hkern u1="&#xc1;" u2="&#xae;" k="60" />
<hkern u1="&#xc1;" u2="&#xab;" k="69" />
<hkern u1="&#xc1;" u2="&#xaa;" k="196" />
<hkern u1="&#xc1;" u2="&#xa9;" k="60" />
<hkern u1="&#xc1;" u2="y" k="60" />
<hkern u1="&#xc1;" u2="w" k="48" />
<hkern u1="&#xc1;" u2="v" k="60" />
<hkern u1="&#xc1;" u2="t" k="71" />
<hkern u1="&#xc1;" u2="\" k="141" />
<hkern u1="&#xc1;" u2="Y" k="171" />
<hkern u1="&#xc1;" u2="W" k="104" />
<hkern u1="&#xc1;" u2="V" k="141" />
<hkern u1="&#xc1;" u2="U" k="41" />
<hkern u1="&#xc1;" u2="T" k="151" />
<hkern u1="&#xc1;" u2="Q" k="60" />
<hkern u1="&#xc1;" u2="O" k="60" />
<hkern u1="&#xc1;" u2="J" k="-73" />
<hkern u1="&#xc1;" u2="G" k="60" />
<hkern u1="&#xc1;" u2="C" k="60" />
<hkern u1="&#xc1;" u2="&#x40;" k="60" />
<hkern u1="&#xc1;" u2="&#x2d;" k="69" />
<hkern u1="&#xc1;" u2="&#x2a;" k="196" />
<hkern u1="&#xc1;" u2="&#x27;" k="196" />
<hkern u1="&#xc1;" u2="&#x22;" k="196" />
<hkern u1="&#xc2;" u2="&#x203a;" k="69" />
<hkern u1="&#xc2;" u2="&#x2039;" k="69" />
<hkern u1="&#xc2;" u2="&#x2022;" k="69" />
<hkern u1="&#xc2;" u2="&#x201d;" k="196" />
<hkern u1="&#xc2;" u2="&#x201c;" k="196" />
<hkern u1="&#xc2;" u2="&#x2019;" k="196" />
<hkern u1="&#xc2;" u2="&#x2018;" k="196" />
<hkern u1="&#xc2;" u2="&#x2014;" k="69" />
<hkern u1="&#xc2;" u2="&#x2013;" k="69" />
<hkern u1="&#xc2;" u2="&#x178;" k="171" />
<hkern u1="&#xc2;" u2="&#x152;" k="60" />
<hkern u1="&#xc2;" u2="&#x106;" k="60" />
<hkern u1="&#xc2;" u2="&#xff;" k="60" />
<hkern u1="&#xc2;" u2="&#xfd;" k="60" />
<hkern u1="&#xc2;" u2="&#xdd;" k="171" />
<hkern u1="&#xc2;" u2="&#xdc;" k="41" />
<hkern u1="&#xc2;" u2="&#xdb;" k="41" />
<hkern u1="&#xc2;" u2="&#xda;" k="41" />
<hkern u1="&#xc2;" u2="&#xd9;" k="41" />
<hkern u1="&#xc2;" u2="&#xd8;" k="60" />
<hkern u1="&#xc2;" u2="&#xd6;" k="60" />
<hkern u1="&#xc2;" u2="&#xd5;" k="60" />
<hkern u1="&#xc2;" u2="&#xd4;" k="60" />
<hkern u1="&#xc2;" u2="&#xd3;" k="60" />
<hkern u1="&#xc2;" u2="&#xd2;" k="60" />
<hkern u1="&#xc2;" u2="&#xc7;" k="60" />
<hkern u1="&#xc2;" u2="&#xbb;" k="69" />
<hkern u1="&#xc2;" u2="&#xba;" k="196" />
<hkern u1="&#xc2;" u2="&#xb7;" k="69" />
<hkern u1="&#xc2;" u2="&#xb0;" k="196" />
<hkern u1="&#xc2;" u2="&#xae;" k="60" />
<hkern u1="&#xc2;" u2="&#xab;" k="69" />
<hkern u1="&#xc2;" u2="&#xaa;" k="196" />
<hkern u1="&#xc2;" u2="&#xa9;" k="60" />
<hkern u1="&#xc2;" u2="y" k="60" />
<hkern u1="&#xc2;" u2="w" k="48" />
<hkern u1="&#xc2;" u2="v" k="60" />
<hkern u1="&#xc2;" u2="t" k="71" />
<hkern u1="&#xc2;" u2="\" k="141" />
<hkern u1="&#xc2;" u2="Y" k="171" />
<hkern u1="&#xc2;" u2="W" k="104" />
<hkern u1="&#xc2;" u2="V" k="141" />
<hkern u1="&#xc2;" u2="U" k="41" />
<hkern u1="&#xc2;" u2="T" k="151" />
<hkern u1="&#xc2;" u2="Q" k="60" />
<hkern u1="&#xc2;" u2="O" k="60" />
<hkern u1="&#xc2;" u2="J" k="-73" />
<hkern u1="&#xc2;" u2="G" k="60" />
<hkern u1="&#xc2;" u2="C" k="60" />
<hkern u1="&#xc2;" u2="&#x40;" k="60" />
<hkern u1="&#xc2;" u2="&#x2d;" k="69" />
<hkern u1="&#xc2;" u2="&#x2a;" k="196" />
<hkern u1="&#xc2;" u2="&#x27;" k="196" />
<hkern u1="&#xc2;" u2="&#x22;" k="196" />
<hkern u1="&#xc3;" u2="&#x203a;" k="69" />
<hkern u1="&#xc3;" u2="&#x2039;" k="69" />
<hkern u1="&#xc3;" u2="&#x2022;" k="69" />
<hkern u1="&#xc3;" u2="&#x201d;" k="196" />
<hkern u1="&#xc3;" u2="&#x201c;" k="196" />
<hkern u1="&#xc3;" u2="&#x2019;" k="196" />
<hkern u1="&#xc3;" u2="&#x2018;" k="196" />
<hkern u1="&#xc3;" u2="&#x2014;" k="69" />
<hkern u1="&#xc3;" u2="&#x2013;" k="69" />
<hkern u1="&#xc3;" u2="&#x178;" k="171" />
<hkern u1="&#xc3;" u2="&#x152;" k="60" />
<hkern u1="&#xc3;" u2="&#x106;" k="60" />
<hkern u1="&#xc3;" u2="&#xff;" k="60" />
<hkern u1="&#xc3;" u2="&#xfd;" k="60" />
<hkern u1="&#xc3;" u2="&#xdd;" k="171" />
<hkern u1="&#xc3;" u2="&#xdc;" k="41" />
<hkern u1="&#xc3;" u2="&#xdb;" k="41" />
<hkern u1="&#xc3;" u2="&#xda;" k="41" />
<hkern u1="&#xc3;" u2="&#xd9;" k="41" />
<hkern u1="&#xc3;" u2="&#xd8;" k="60" />
<hkern u1="&#xc3;" u2="&#xd6;" k="60" />
<hkern u1="&#xc3;" u2="&#xd5;" k="60" />
<hkern u1="&#xc3;" u2="&#xd4;" k="60" />
<hkern u1="&#xc3;" u2="&#xd3;" k="60" />
<hkern u1="&#xc3;" u2="&#xd2;" k="60" />
<hkern u1="&#xc3;" u2="&#xc7;" k="60" />
<hkern u1="&#xc3;" u2="&#xbb;" k="69" />
<hkern u1="&#xc3;" u2="&#xba;" k="196" />
<hkern u1="&#xc3;" u2="&#xb7;" k="69" />
<hkern u1="&#xc3;" u2="&#xb0;" k="196" />
<hkern u1="&#xc3;" u2="&#xae;" k="60" />
<hkern u1="&#xc3;" u2="&#xab;" k="69" />
<hkern u1="&#xc3;" u2="&#xaa;" k="196" />
<hkern u1="&#xc3;" u2="&#xa9;" k="60" />
<hkern u1="&#xc3;" u2="y" k="60" />
<hkern u1="&#xc3;" u2="w" k="48" />
<hkern u1="&#xc3;" u2="v" k="60" />
<hkern u1="&#xc3;" u2="t" k="71" />
<hkern u1="&#xc3;" u2="\" k="141" />
<hkern u1="&#xc3;" u2="Y" k="171" />
<hkern u1="&#xc3;" u2="W" k="104" />
<hkern u1="&#xc3;" u2="V" k="141" />
<hkern u1="&#xc3;" u2="U" k="41" />
<hkern u1="&#xc3;" u2="T" k="151" />
<hkern u1="&#xc3;" u2="Q" k="60" />
<hkern u1="&#xc3;" u2="O" k="60" />
<hkern u1="&#xc3;" u2="J" k="-73" />
<hkern u1="&#xc3;" u2="G" k="60" />
<hkern u1="&#xc3;" u2="C" k="60" />
<hkern u1="&#xc3;" u2="&#x40;" k="60" />
<hkern u1="&#xc3;" u2="&#x2d;" k="69" />
<hkern u1="&#xc3;" u2="&#x2a;" k="196" />
<hkern u1="&#xc3;" u2="&#x27;" k="196" />
<hkern u1="&#xc3;" u2="&#x22;" k="196" />
<hkern u1="&#xc4;" u2="&#x203a;" k="69" />
<hkern u1="&#xc4;" u2="&#x2039;" k="69" />
<hkern u1="&#xc4;" u2="&#x2022;" k="69" />
<hkern u1="&#xc4;" u2="&#x201d;" k="196" />
<hkern u1="&#xc4;" u2="&#x201c;" k="196" />
<hkern u1="&#xc4;" u2="&#x2019;" k="196" />
<hkern u1="&#xc4;" u2="&#x2018;" k="196" />
<hkern u1="&#xc4;" u2="&#x2014;" k="69" />
<hkern u1="&#xc4;" u2="&#x2013;" k="69" />
<hkern u1="&#xc4;" u2="&#x178;" k="171" />
<hkern u1="&#xc4;" u2="&#x152;" k="60" />
<hkern u1="&#xc4;" u2="&#x106;" k="60" />
<hkern u1="&#xc4;" u2="&#xff;" k="60" />
<hkern u1="&#xc4;" u2="&#xfd;" k="60" />
<hkern u1="&#xc4;" u2="&#xdd;" k="171" />
<hkern u1="&#xc4;" u2="&#xdc;" k="41" />
<hkern u1="&#xc4;" u2="&#xdb;" k="41" />
<hkern u1="&#xc4;" u2="&#xda;" k="41" />
<hkern u1="&#xc4;" u2="&#xd9;" k="41" />
<hkern u1="&#xc4;" u2="&#xd8;" k="60" />
<hkern u1="&#xc4;" u2="&#xd6;" k="60" />
<hkern u1="&#xc4;" u2="&#xd5;" k="60" />
<hkern u1="&#xc4;" u2="&#xd4;" k="60" />
<hkern u1="&#xc4;" u2="&#xd3;" k="60" />
<hkern u1="&#xc4;" u2="&#xd2;" k="60" />
<hkern u1="&#xc4;" u2="&#xc7;" k="60" />
<hkern u1="&#xc4;" u2="&#xbb;" k="69" />
<hkern u1="&#xc4;" u2="&#xba;" k="196" />
<hkern u1="&#xc4;" u2="&#xb7;" k="69" />
<hkern u1="&#xc4;" u2="&#xb0;" k="196" />
<hkern u1="&#xc4;" u2="&#xae;" k="60" />
<hkern u1="&#xc4;" u2="&#xab;" k="69" />
<hkern u1="&#xc4;" u2="&#xaa;" k="196" />
<hkern u1="&#xc4;" u2="&#xa9;" k="60" />
<hkern u1="&#xc4;" u2="y" k="60" />
<hkern u1="&#xc4;" u2="w" k="48" />
<hkern u1="&#xc4;" u2="v" k="60" />
<hkern u1="&#xc4;" u2="t" k="71" />
<hkern u1="&#xc4;" u2="\" k="141" />
<hkern u1="&#xc4;" u2="Y" k="171" />
<hkern u1="&#xc4;" u2="W" k="104" />
<hkern u1="&#xc4;" u2="V" k="141" />
<hkern u1="&#xc4;" u2="U" k="41" />
<hkern u1="&#xc4;" u2="T" k="151" />
<hkern u1="&#xc4;" u2="Q" k="60" />
<hkern u1="&#xc4;" u2="O" k="60" />
<hkern u1="&#xc4;" u2="J" k="-73" />
<hkern u1="&#xc4;" u2="G" k="60" />
<hkern u1="&#xc4;" u2="C" k="60" />
<hkern u1="&#xc4;" u2="&#x40;" k="60" />
<hkern u1="&#xc4;" u2="&#x2d;" k="69" />
<hkern u1="&#xc4;" u2="&#x2a;" k="196" />
<hkern u1="&#xc4;" u2="&#x27;" k="196" />
<hkern u1="&#xc4;" u2="&#x22;" k="196" />
<hkern u1="&#xc5;" u2="&#x203a;" k="69" />
<hkern u1="&#xc5;" u2="&#x2039;" k="69" />
<hkern u1="&#xc5;" u2="&#x2022;" k="69" />
<hkern u1="&#xc5;" u2="&#x201d;" k="196" />
<hkern u1="&#xc5;" u2="&#x201c;" k="196" />
<hkern u1="&#xc5;" u2="&#x2019;" k="196" />
<hkern u1="&#xc5;" u2="&#x2018;" k="196" />
<hkern u1="&#xc5;" u2="&#x2014;" k="69" />
<hkern u1="&#xc5;" u2="&#x2013;" k="69" />
<hkern u1="&#xc5;" u2="&#x178;" k="171" />
<hkern u1="&#xc5;" u2="&#x152;" k="60" />
<hkern u1="&#xc5;" u2="&#x106;" k="60" />
<hkern u1="&#xc5;" u2="&#xff;" k="60" />
<hkern u1="&#xc5;" u2="&#xfd;" k="60" />
<hkern u1="&#xc5;" u2="&#xdd;" k="171" />
<hkern u1="&#xc5;" u2="&#xdc;" k="41" />
<hkern u1="&#xc5;" u2="&#xdb;" k="41" />
<hkern u1="&#xc5;" u2="&#xda;" k="41" />
<hkern u1="&#xc5;" u2="&#xd9;" k="41" />
<hkern u1="&#xc5;" u2="&#xd8;" k="60" />
<hkern u1="&#xc5;" u2="&#xd6;" k="60" />
<hkern u1="&#xc5;" u2="&#xd5;" k="60" />
<hkern u1="&#xc5;" u2="&#xd4;" k="60" />
<hkern u1="&#xc5;" u2="&#xd3;" k="60" />
<hkern u1="&#xc5;" u2="&#xd2;" k="60" />
<hkern u1="&#xc5;" u2="&#xc7;" k="60" />
<hkern u1="&#xc5;" u2="&#xbb;" k="69" />
<hkern u1="&#xc5;" u2="&#xba;" k="196" />
<hkern u1="&#xc5;" u2="&#xb7;" k="69" />
<hkern u1="&#xc5;" u2="&#xb0;" k="196" />
<hkern u1="&#xc5;" u2="&#xae;" k="60" />
<hkern u1="&#xc5;" u2="&#xab;" k="69" />
<hkern u1="&#xc5;" u2="&#xaa;" k="196" />
<hkern u1="&#xc5;" u2="&#xa9;" k="60" />
<hkern u1="&#xc5;" u2="y" k="60" />
<hkern u1="&#xc5;" u2="w" k="48" />
<hkern u1="&#xc5;" u2="v" k="60" />
<hkern u1="&#xc5;" u2="t" k="71" />
<hkern u1="&#xc5;" u2="\" k="141" />
<hkern u1="&#xc5;" u2="Y" k="171" />
<hkern u1="&#xc5;" u2="W" k="104" />
<hkern u1="&#xc5;" u2="V" k="141" />
<hkern u1="&#xc5;" u2="U" k="41" />
<hkern u1="&#xc5;" u2="T" k="151" />
<hkern u1="&#xc5;" u2="Q" k="60" />
<hkern u1="&#xc5;" u2="O" k="60" />
<hkern u1="&#xc5;" u2="J" k="-73" />
<hkern u1="&#xc5;" u2="G" k="60" />
<hkern u1="&#xc5;" u2="C" k="60" />
<hkern u1="&#xc5;" u2="&#x40;" k="60" />
<hkern u1="&#xc5;" u2="&#x2d;" k="69" />
<hkern u1="&#xc5;" u2="&#x2a;" k="196" />
<hkern u1="&#xc5;" u2="&#x27;" k="196" />
<hkern u1="&#xc5;" u2="&#x22;" k="196" />
<hkern u1="&#xc7;" u2="&#x203a;" k="116" />
<hkern u1="&#xc7;" u2="&#x2039;" k="116" />
<hkern u1="&#xc7;" u2="&#x2022;" k="116" />
<hkern u1="&#xc7;" u2="&#x2014;" k="116" />
<hkern u1="&#xc7;" u2="&#x2013;" k="116" />
<hkern u1="&#xc7;" u2="&#xbb;" k="116" />
<hkern u1="&#xc7;" u2="&#xb7;" k="116" />
<hkern u1="&#xc7;" u2="&#xab;" k="116" />
<hkern u1="&#xc7;" u2="&#x2d;" k="116" />
<hkern u1="&#xd0;" u2="&#x2206;" k="44" />
<hkern u1="&#xd0;" u2="&#x201d;" k="53" />
<hkern u1="&#xd0;" u2="&#x201c;" k="53" />
<hkern u1="&#xd0;" u2="&#x2019;" k="53" />
<hkern u1="&#xd0;" u2="&#x2018;" k="53" />
<hkern u1="&#xd0;" u2="&#x17d;" k="66" />
<hkern u1="&#xd0;" u2="&#x17b;" k="66" />
<hkern u1="&#xd0;" u2="&#x179;" k="66" />
<hkern u1="&#xd0;" u2="&#x178;" k="82" />
<hkern u1="&#xd0;" u2="&#x104;" k="44" />
<hkern u1="&#xd0;" u2="&#xdd;" k="82" />
<hkern u1="&#xd0;" u2="&#xc6;" k="44" />
<hkern u1="&#xd0;" u2="&#xc5;" k="44" />
<hkern u1="&#xd0;" u2="&#xc4;" k="44" />
<hkern u1="&#xd0;" u2="&#xc3;" k="44" />
<hkern u1="&#xd0;" u2="&#xc2;" k="44" />
<hkern u1="&#xd0;" u2="&#xc1;" k="44" />
<hkern u1="&#xd0;" u2="&#xc0;" k="44" />
<hkern u1="&#xd0;" u2="&#xba;" k="53" />
<hkern u1="&#xd0;" u2="&#xb0;" k="53" />
<hkern u1="&#xd0;" u2="&#xaa;" k="53" />
<hkern u1="&#xd0;" u2="&#x7d;" k="41" />
<hkern u1="&#xd0;" u2="]" k="41" />
<hkern u1="&#xd0;" u2="\" k="57" />
<hkern u1="&#xd0;" u2="Z" k="66" />
<hkern u1="&#xd0;" u2="Y" k="82" />
<hkern u1="&#xd0;" u2="X" k="87" />
<hkern u1="&#xd0;" u2="V" k="57" />
<hkern u1="&#xd0;" u2="T" k="80" />
<hkern u1="&#xd0;" u2="A" k="44" />
<hkern u1="&#xd0;" u2="&#x2f;" k="44" />
<hkern u1="&#xd0;" u2="&#x2a;" k="53" />
<hkern u1="&#xd0;" u2="&#x29;" k="41" />
<hkern u1="&#xd0;" u2="&#x27;" k="53" />
<hkern u1="&#xd0;" u2="&#x26;" k="44" />
<hkern u1="&#xd0;" u2="&#x22;" k="53" />
<hkern u1="&#xd2;" u2="&#x2206;" k="44" />
<hkern u1="&#xd2;" u2="&#x201d;" k="53" />
<hkern u1="&#xd2;" u2="&#x201c;" k="53" />
<hkern u1="&#xd2;" u2="&#x2019;" k="53" />
<hkern u1="&#xd2;" u2="&#x2018;" k="53" />
<hkern u1="&#xd2;" u2="&#x17d;" k="66" />
<hkern u1="&#xd2;" u2="&#x17b;" k="66" />
<hkern u1="&#xd2;" u2="&#x179;" k="66" />
<hkern u1="&#xd2;" u2="&#x178;" k="82" />
<hkern u1="&#xd2;" u2="&#x104;" k="44" />
<hkern u1="&#xd2;" u2="&#xdd;" k="82" />
<hkern u1="&#xd2;" u2="&#xc6;" k="44" />
<hkern u1="&#xd2;" u2="&#xc5;" k="44" />
<hkern u1="&#xd2;" u2="&#xc4;" k="44" />
<hkern u1="&#xd2;" u2="&#xc3;" k="44" />
<hkern u1="&#xd2;" u2="&#xc2;" k="44" />
<hkern u1="&#xd2;" u2="&#xc1;" k="44" />
<hkern u1="&#xd2;" u2="&#xc0;" k="44" />
<hkern u1="&#xd2;" u2="&#xba;" k="53" />
<hkern u1="&#xd2;" u2="&#xb0;" k="53" />
<hkern u1="&#xd2;" u2="&#xaa;" k="53" />
<hkern u1="&#xd2;" u2="&#x7d;" k="41" />
<hkern u1="&#xd2;" u2="]" k="41" />
<hkern u1="&#xd2;" u2="\" k="57" />
<hkern u1="&#xd2;" u2="Z" k="66" />
<hkern u1="&#xd2;" u2="Y" k="82" />
<hkern u1="&#xd2;" u2="X" k="87" />
<hkern u1="&#xd2;" u2="V" k="57" />
<hkern u1="&#xd2;" u2="T" k="80" />
<hkern u1="&#xd2;" u2="A" k="44" />
<hkern u1="&#xd2;" u2="&#x2f;" k="44" />
<hkern u1="&#xd2;" u2="&#x2a;" k="53" />
<hkern u1="&#xd2;" u2="&#x29;" k="41" />
<hkern u1="&#xd2;" u2="&#x27;" k="53" />
<hkern u1="&#xd2;" u2="&#x26;" k="44" />
<hkern u1="&#xd2;" u2="&#x22;" k="53" />
<hkern u1="&#xd3;" u2="&#x2206;" k="44" />
<hkern u1="&#xd3;" u2="&#x201d;" k="53" />
<hkern u1="&#xd3;" u2="&#x201c;" k="53" />
<hkern u1="&#xd3;" u2="&#x2019;" k="53" />
<hkern u1="&#xd3;" u2="&#x2018;" k="53" />
<hkern u1="&#xd3;" u2="&#x17d;" k="66" />
<hkern u1="&#xd3;" u2="&#x17b;" k="66" />
<hkern u1="&#xd3;" u2="&#x179;" k="66" />
<hkern u1="&#xd3;" u2="&#x178;" k="82" />
<hkern u1="&#xd3;" u2="&#x104;" k="44" />
<hkern u1="&#xd3;" u2="&#xdd;" k="82" />
<hkern u1="&#xd3;" u2="&#xc6;" k="44" />
<hkern u1="&#xd3;" u2="&#xc5;" k="44" />
<hkern u1="&#xd3;" u2="&#xc4;" k="44" />
<hkern u1="&#xd3;" u2="&#xc3;" k="44" />
<hkern u1="&#xd3;" u2="&#xc2;" k="44" />
<hkern u1="&#xd3;" u2="&#xc1;" k="44" />
<hkern u1="&#xd3;" u2="&#xc0;" k="44" />
<hkern u1="&#xd3;" u2="&#xba;" k="53" />
<hkern u1="&#xd3;" u2="&#xb0;" k="53" />
<hkern u1="&#xd3;" u2="&#xaa;" k="53" />
<hkern u1="&#xd3;" u2="&#x7d;" k="41" />
<hkern u1="&#xd3;" u2="]" k="41" />
<hkern u1="&#xd3;" u2="\" k="57" />
<hkern u1="&#xd3;" u2="Z" k="66" />
<hkern u1="&#xd3;" u2="Y" k="82" />
<hkern u1="&#xd3;" u2="X" k="87" />
<hkern u1="&#xd3;" u2="V" k="57" />
<hkern u1="&#xd3;" u2="T" k="80" />
<hkern u1="&#xd3;" u2="A" k="44" />
<hkern u1="&#xd3;" u2="&#x2f;" k="44" />
<hkern u1="&#xd3;" u2="&#x2a;" k="53" />
<hkern u1="&#xd3;" u2="&#x29;" k="41" />
<hkern u1="&#xd3;" u2="&#x27;" k="53" />
<hkern u1="&#xd3;" u2="&#x26;" k="44" />
<hkern u1="&#xd3;" u2="&#x22;" k="53" />
<hkern u1="&#xd4;" u2="&#x2206;" k="44" />
<hkern u1="&#xd4;" u2="&#x201d;" k="53" />
<hkern u1="&#xd4;" u2="&#x201c;" k="53" />
<hkern u1="&#xd4;" u2="&#x2019;" k="53" />
<hkern u1="&#xd4;" u2="&#x2018;" k="53" />
<hkern u1="&#xd4;" u2="&#x17d;" k="66" />
<hkern u1="&#xd4;" u2="&#x17b;" k="66" />
<hkern u1="&#xd4;" u2="&#x179;" k="66" />
<hkern u1="&#xd4;" u2="&#x178;" k="82" />
<hkern u1="&#xd4;" u2="&#x104;" k="44" />
<hkern u1="&#xd4;" u2="&#xdd;" k="82" />
<hkern u1="&#xd4;" u2="&#xc6;" k="44" />
<hkern u1="&#xd4;" u2="&#xc5;" k="44" />
<hkern u1="&#xd4;" u2="&#xc4;" k="44" />
<hkern u1="&#xd4;" u2="&#xc3;" k="44" />
<hkern u1="&#xd4;" u2="&#xc2;" k="44" />
<hkern u1="&#xd4;" u2="&#xc1;" k="44" />
<hkern u1="&#xd4;" u2="&#xc0;" k="44" />
<hkern u1="&#xd4;" u2="&#xba;" k="53" />
<hkern u1="&#xd4;" u2="&#xb0;" k="53" />
<hkern u1="&#xd4;" u2="&#xaa;" k="53" />
<hkern u1="&#xd4;" u2="&#x7d;" k="41" />
<hkern u1="&#xd4;" u2="]" k="41" />
<hkern u1="&#xd4;" u2="\" k="57" />
<hkern u1="&#xd4;" u2="Z" k="66" />
<hkern u1="&#xd4;" u2="Y" k="82" />
<hkern u1="&#xd4;" u2="X" k="87" />
<hkern u1="&#xd4;" u2="V" k="57" />
<hkern u1="&#xd4;" u2="T" k="80" />
<hkern u1="&#xd4;" u2="A" k="44" />
<hkern u1="&#xd4;" u2="&#x2f;" k="44" />
<hkern u1="&#xd4;" u2="&#x2a;" k="53" />
<hkern u1="&#xd4;" u2="&#x29;" k="41" />
<hkern u1="&#xd4;" u2="&#x27;" k="53" />
<hkern u1="&#xd4;" u2="&#x26;" k="44" />
<hkern u1="&#xd4;" u2="&#x22;" k="53" />
<hkern u1="&#xd5;" u2="&#x2206;" k="44" />
<hkern u1="&#xd5;" u2="&#x201d;" k="53" />
<hkern u1="&#xd5;" u2="&#x201c;" k="53" />
<hkern u1="&#xd5;" u2="&#x2019;" k="53" />
<hkern u1="&#xd5;" u2="&#x2018;" k="53" />
<hkern u1="&#xd5;" u2="&#x17d;" k="66" />
<hkern u1="&#xd5;" u2="&#x17b;" k="66" />
<hkern u1="&#xd5;" u2="&#x179;" k="66" />
<hkern u1="&#xd5;" u2="&#x178;" k="82" />
<hkern u1="&#xd5;" u2="&#x104;" k="44" />
<hkern u1="&#xd5;" u2="&#xdd;" k="82" />
<hkern u1="&#xd5;" u2="&#xc6;" k="44" />
<hkern u1="&#xd5;" u2="&#xc5;" k="44" />
<hkern u1="&#xd5;" u2="&#xc4;" k="44" />
<hkern u1="&#xd5;" u2="&#xc3;" k="44" />
<hkern u1="&#xd5;" u2="&#xc2;" k="44" />
<hkern u1="&#xd5;" u2="&#xc1;" k="44" />
<hkern u1="&#xd5;" u2="&#xc0;" k="44" />
<hkern u1="&#xd5;" u2="&#xba;" k="53" />
<hkern u1="&#xd5;" u2="&#xb0;" k="53" />
<hkern u1="&#xd5;" u2="&#xaa;" k="53" />
<hkern u1="&#xd5;" u2="&#x7d;" k="41" />
<hkern u1="&#xd5;" u2="]" k="41" />
<hkern u1="&#xd5;" u2="\" k="57" />
<hkern u1="&#xd5;" u2="Z" k="66" />
<hkern u1="&#xd5;" u2="Y" k="82" />
<hkern u1="&#xd5;" u2="X" k="87" />
<hkern u1="&#xd5;" u2="V" k="57" />
<hkern u1="&#xd5;" u2="T" k="80" />
<hkern u1="&#xd5;" u2="A" k="44" />
<hkern u1="&#xd5;" u2="&#x2f;" k="44" />
<hkern u1="&#xd5;" u2="&#x2a;" k="53" />
<hkern u1="&#xd5;" u2="&#x29;" k="41" />
<hkern u1="&#xd5;" u2="&#x27;" k="53" />
<hkern u1="&#xd5;" u2="&#x26;" k="44" />
<hkern u1="&#xd5;" u2="&#x22;" k="53" />
<hkern u1="&#xd6;" u2="&#x2206;" k="44" />
<hkern u1="&#xd6;" u2="&#x201d;" k="53" />
<hkern u1="&#xd6;" u2="&#x201c;" k="53" />
<hkern u1="&#xd6;" u2="&#x2019;" k="53" />
<hkern u1="&#xd6;" u2="&#x2018;" k="53" />
<hkern u1="&#xd6;" u2="&#x17d;" k="66" />
<hkern u1="&#xd6;" u2="&#x17b;" k="66" />
<hkern u1="&#xd6;" u2="&#x179;" k="66" />
<hkern u1="&#xd6;" u2="&#x178;" k="82" />
<hkern u1="&#xd6;" u2="&#x104;" k="44" />
<hkern u1="&#xd6;" u2="&#xdd;" k="82" />
<hkern u1="&#xd6;" u2="&#xc6;" k="44" />
<hkern u1="&#xd6;" u2="&#xc5;" k="44" />
<hkern u1="&#xd6;" u2="&#xc4;" k="44" />
<hkern u1="&#xd6;" u2="&#xc3;" k="44" />
<hkern u1="&#xd6;" u2="&#xc2;" k="44" />
<hkern u1="&#xd6;" u2="&#xc1;" k="44" />
<hkern u1="&#xd6;" u2="&#xc0;" k="44" />
<hkern u1="&#xd6;" u2="&#xba;" k="53" />
<hkern u1="&#xd6;" u2="&#xb0;" k="53" />
<hkern u1="&#xd6;" u2="&#xaa;" k="53" />
<hkern u1="&#xd6;" u2="&#x7d;" k="41" />
<hkern u1="&#xd6;" u2="]" k="41" />
<hkern u1="&#xd6;" u2="\" k="57" />
<hkern u1="&#xd6;" u2="Z" k="66" />
<hkern u1="&#xd6;" u2="Y" k="82" />
<hkern u1="&#xd6;" u2="X" k="87" />
<hkern u1="&#xd6;" u2="V" k="57" />
<hkern u1="&#xd6;" u2="T" k="80" />
<hkern u1="&#xd6;" u2="A" k="44" />
<hkern u1="&#xd6;" u2="&#x2f;" k="44" />
<hkern u1="&#xd6;" u2="&#x2a;" k="53" />
<hkern u1="&#xd6;" u2="&#x29;" k="41" />
<hkern u1="&#xd6;" u2="&#x27;" k="53" />
<hkern u1="&#xd6;" u2="&#x26;" k="44" />
<hkern u1="&#xd6;" u2="&#x22;" k="53" />
<hkern u1="&#xd9;" u2="&#x2206;" k="41" />
<hkern u1="&#xd9;" u2="&#x104;" k="41" />
<hkern u1="&#xd9;" u2="&#xc6;" k="41" />
<hkern u1="&#xd9;" u2="&#xc5;" k="41" />
<hkern u1="&#xd9;" u2="&#xc4;" k="41" />
<hkern u1="&#xd9;" u2="&#xc3;" k="41" />
<hkern u1="&#xd9;" u2="&#xc2;" k="41" />
<hkern u1="&#xd9;" u2="&#xc1;" k="41" />
<hkern u1="&#xd9;" u2="&#xc0;" k="41" />
<hkern u1="&#xd9;" u2="A" k="41" />
<hkern u1="&#xd9;" u2="&#x2f;" k="41" />
<hkern u1="&#xd9;" u2="&#x26;" k="41" />
<hkern u1="&#xda;" u2="&#x2206;" k="41" />
<hkern u1="&#xda;" u2="&#x104;" k="41" />
<hkern u1="&#xda;" u2="&#xc6;" k="41" />
<hkern u1="&#xda;" u2="&#xc5;" k="41" />
<hkern u1="&#xda;" u2="&#xc4;" k="41" />
<hkern u1="&#xda;" u2="&#xc3;" k="41" />
<hkern u1="&#xda;" u2="&#xc2;" k="41" />
<hkern u1="&#xda;" u2="&#xc1;" k="41" />
<hkern u1="&#xda;" u2="&#xc0;" k="41" />
<hkern u1="&#xda;" u2="A" k="41" />
<hkern u1="&#xda;" u2="&#x2f;" k="41" />
<hkern u1="&#xda;" u2="&#x26;" k="41" />
<hkern u1="&#xdb;" u2="&#x2206;" k="41" />
<hkern u1="&#xdb;" u2="&#x104;" k="41" />
<hkern u1="&#xdb;" u2="&#xc6;" k="41" />
<hkern u1="&#xdb;" u2="&#xc5;" k="41" />
<hkern u1="&#xdb;" u2="&#xc4;" k="41" />
<hkern u1="&#xdb;" u2="&#xc3;" k="41" />
<hkern u1="&#xdb;" u2="&#xc2;" k="41" />
<hkern u1="&#xdb;" u2="&#xc1;" k="41" />
<hkern u1="&#xdb;" u2="&#xc0;" k="41" />
<hkern u1="&#xdb;" u2="A" k="41" />
<hkern u1="&#xdb;" u2="&#x2f;" k="41" />
<hkern u1="&#xdb;" u2="&#x26;" k="41" />
<hkern u1="&#xdc;" u2="&#x2206;" k="41" />
<hkern u1="&#xdc;" u2="&#x104;" k="41" />
<hkern u1="&#xdc;" u2="&#xc6;" k="41" />
<hkern u1="&#xdc;" u2="&#xc5;" k="41" />
<hkern u1="&#xdc;" u2="&#xc4;" k="41" />
<hkern u1="&#xdc;" u2="&#xc3;" k="41" />
<hkern u1="&#xdc;" u2="&#xc2;" k="41" />
<hkern u1="&#xdc;" u2="&#xc1;" k="41" />
<hkern u1="&#xdc;" u2="&#xc0;" k="41" />
<hkern u1="&#xdc;" u2="A" k="41" />
<hkern u1="&#xdc;" u2="&#x2f;" k="41" />
<hkern u1="&#xdc;" u2="&#x26;" k="41" />
<hkern u1="&#xdd;" u2="&#x2206;" k="155" />
<hkern u1="&#xdd;" u2="&#x203a;" k="196" />
<hkern u1="&#xdd;" u2="&#x2039;" k="196" />
<hkern u1="&#xdd;" u2="&#x2026;" k="266" />
<hkern u1="&#xdd;" u2="&#x2022;" k="196" />
<hkern u1="&#xdd;" u2="&#x201e;" k="266" />
<hkern u1="&#xdd;" u2="&#x201d;" k="-59" />
<hkern u1="&#xdd;" u2="&#x201c;" k="-59" />
<hkern u1="&#xdd;" u2="&#x201a;" k="266" />
<hkern u1="&#xdd;" u2="&#x2019;" k="-59" />
<hkern u1="&#xdd;" u2="&#x2018;" k="-59" />
<hkern u1="&#xdd;" u2="&#x2014;" k="196" />
<hkern u1="&#xdd;" u2="&#x2013;" k="196" />
<hkern u1="&#xdd;" u2="&#x17e;" k="119" />
<hkern u1="&#xdd;" u2="&#x17c;" k="119" />
<hkern u1="&#xdd;" u2="&#x17a;" k="119" />
<hkern u1="&#xdd;" u2="&#x161;" k="201" />
<hkern u1="&#xdd;" u2="&#x15b;" k="201" />
<hkern u1="&#xdd;" u2="&#x153;" k="201" />
<hkern u1="&#xdd;" u2="&#x152;" k="66" />
<hkern u1="&#xdd;" u2="&#x144;" k="115" />
<hkern u1="&#xdd;" u2="&#x119;" k="201" />
<hkern u1="&#xdd;" u2="&#x107;" k="201" />
<hkern u1="&#xdd;" u2="&#x106;" k="66" />
<hkern u1="&#xdd;" u2="&#x105;" k="201" />
<hkern u1="&#xdd;" u2="&#x104;" k="155" />
<hkern u1="&#xdd;" u2="&#xfc;" k="115" />
<hkern u1="&#xdd;" u2="&#xfb;" k="115" />
<hkern u1="&#xdd;" u2="&#xfa;" k="115" />
<hkern u1="&#xdd;" u2="&#xf9;" k="115" />
<hkern u1="&#xdd;" u2="&#xf8;" k="201" />
<hkern u1="&#xdd;" u2="&#xf6;" k="201" />
<hkern u1="&#xdd;" u2="&#xf5;" k="201" />
<hkern u1="&#xdd;" u2="&#xf4;" k="201" />
<hkern u1="&#xdd;" u2="&#xf3;" k="201" />
<hkern u1="&#xdd;" u2="&#xf2;" k="201" />
<hkern u1="&#xdd;" u2="&#xf1;" k="115" />
<hkern u1="&#xdd;" u2="&#xf0;" k="201" />
<hkern u1="&#xdd;" u2="&#xeb;" k="201" />
<hkern u1="&#xdd;" u2="&#xea;" k="201" />
<hkern u1="&#xdd;" u2="&#xe9;" k="201" />
<hkern u1="&#xdd;" u2="&#xe8;" k="201" />
<hkern u1="&#xdd;" u2="&#xe7;" k="201" />
<hkern u1="&#xdd;" u2="&#xe6;" k="201" />
<hkern u1="&#xdd;" u2="&#xe5;" k="201" />
<hkern u1="&#xdd;" u2="&#xe4;" k="201" />
<hkern u1="&#xdd;" u2="&#xe3;" k="201" />
<hkern u1="&#xdd;" u2="&#xe2;" k="201" />
<hkern u1="&#xdd;" u2="&#xe1;" k="201" />
<hkern u1="&#xdd;" u2="&#xe0;" k="201" />
<hkern u1="&#xdd;" u2="&#xd8;" k="66" />
<hkern u1="&#xdd;" u2="&#xd6;" k="66" />
<hkern u1="&#xdd;" u2="&#xd5;" k="66" />
<hkern u1="&#xdd;" u2="&#xd4;" k="66" />
<hkern u1="&#xdd;" u2="&#xd3;" k="66" />
<hkern u1="&#xdd;" u2="&#xd2;" k="66" />
<hkern u1="&#xdd;" u2="&#xc7;" k="66" />
<hkern u1="&#xdd;" u2="&#xc6;" k="155" />
<hkern u1="&#xdd;" u2="&#xc5;" k="155" />
<hkern u1="&#xdd;" u2="&#xc4;" k="155" />
<hkern u1="&#xdd;" u2="&#xc3;" k="155" />
<hkern u1="&#xdd;" u2="&#xc2;" k="155" />
<hkern u1="&#xdd;" u2="&#xc1;" k="155" />
<hkern u1="&#xdd;" u2="&#xc0;" k="155" />
<hkern u1="&#xdd;" u2="&#xbb;" k="196" />
<hkern u1="&#xdd;" u2="&#xba;" k="-59" />
<hkern u1="&#xdd;" u2="&#xb7;" k="196" />
<hkern u1="&#xdd;" u2="&#xb5;" k="115" />
<hkern u1="&#xdd;" u2="&#xb0;" k="-59" />
<hkern u1="&#xdd;" u2="&#xae;" k="66" />
<hkern u1="&#xdd;" u2="&#xab;" k="196" />
<hkern u1="&#xdd;" u2="&#xaa;" k="-59" />
<hkern u1="&#xdd;" u2="&#xa9;" k="66" />
<hkern u1="&#xdd;" u2="z" k="119" />
<hkern u1="&#xdd;" u2="u" k="115" />
<hkern u1="&#xdd;" u2="s" k="201" />
<hkern u1="&#xdd;" u2="r" k="115" />
<hkern u1="&#xdd;" u2="q" k="201" />
<hkern u1="&#xdd;" u2="p" k="115" />
<hkern u1="&#xdd;" u2="o" k="201" />
<hkern u1="&#xdd;" u2="n" k="115" />
<hkern u1="&#xdd;" u2="m" k="115" />
<hkern u1="&#xdd;" u2="g" k="179" />
<hkern u1="&#xdd;" u2="e" k="201" />
<hkern u1="&#xdd;" u2="d" k="201" />
<hkern u1="&#xdd;" u2="c" k="201" />
<hkern u1="&#xdd;" u2="a" k="201" />
<hkern u1="&#xdd;" u2="Q" k="66" />
<hkern u1="&#xdd;" u2="O" k="66" />
<hkern u1="&#xdd;" u2="J" k="205" />
<hkern u1="&#xdd;" u2="G" k="66" />
<hkern u1="&#xdd;" u2="C" k="66" />
<hkern u1="&#xdd;" u2="A" k="155" />
<hkern u1="&#xdd;" u2="&#x40;" k="66" />
<hkern u1="&#xdd;" u2="&#x3f;" k="-51" />
<hkern u1="&#xdd;" u2="&#x3b;" k="115" />
<hkern u1="&#xdd;" u2="&#x3a;" k="115" />
<hkern u1="&#xdd;" u2="&#x2f;" k="155" />
<hkern u1="&#xdd;" u2="&#x2e;" k="266" />
<hkern u1="&#xdd;" u2="&#x2d;" k="196" />
<hkern u1="&#xdd;" u2="&#x2c;" k="266" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-59" />
<hkern u1="&#xdd;" u2="&#x27;" k="-59" />
<hkern u1="&#xdd;" u2="&#x26;" k="155" />
<hkern u1="&#xdd;" u2="&#x22;" k="-59" />
<hkern u1="&#xde;" u2="&#x2206;" k="44" />
<hkern u1="&#xde;" u2="&#x201d;" k="53" />
<hkern u1="&#xde;" u2="&#x201c;" k="53" />
<hkern u1="&#xde;" u2="&#x2019;" k="53" />
<hkern u1="&#xde;" u2="&#x2018;" k="53" />
<hkern u1="&#xde;" u2="&#x17d;" k="66" />
<hkern u1="&#xde;" u2="&#x17b;" k="66" />
<hkern u1="&#xde;" u2="&#x179;" k="66" />
<hkern u1="&#xde;" u2="&#x178;" k="82" />
<hkern u1="&#xde;" u2="&#x104;" k="44" />
<hkern u1="&#xde;" u2="&#xdd;" k="82" />
<hkern u1="&#xde;" u2="&#xc6;" k="44" />
<hkern u1="&#xde;" u2="&#xc5;" k="44" />
<hkern u1="&#xde;" u2="&#xc4;" k="44" />
<hkern u1="&#xde;" u2="&#xc3;" k="44" />
<hkern u1="&#xde;" u2="&#xc2;" k="44" />
<hkern u1="&#xde;" u2="&#xc1;" k="44" />
<hkern u1="&#xde;" u2="&#xc0;" k="44" />
<hkern u1="&#xde;" u2="&#xba;" k="53" />
<hkern u1="&#xde;" u2="&#xb0;" k="53" />
<hkern u1="&#xde;" u2="&#xaa;" k="53" />
<hkern u1="&#xde;" u2="&#x7d;" k="41" />
<hkern u1="&#xde;" u2="]" k="41" />
<hkern u1="&#xde;" u2="\" k="57" />
<hkern u1="&#xde;" u2="Z" k="66" />
<hkern u1="&#xde;" u2="Y" k="82" />
<hkern u1="&#xde;" u2="X" k="87" />
<hkern u1="&#xde;" u2="V" k="57" />
<hkern u1="&#xde;" u2="T" k="80" />
<hkern u1="&#xde;" u2="A" k="44" />
<hkern u1="&#xde;" u2="&#x2f;" k="44" />
<hkern u1="&#xde;" u2="&#x2a;" k="53" />
<hkern u1="&#xde;" u2="&#x29;" k="41" />
<hkern u1="&#xde;" u2="&#x27;" k="53" />
<hkern u1="&#xde;" u2="&#x26;" k="44" />
<hkern u1="&#xde;" u2="&#x22;" k="53" />
<hkern u1="&#xe6;" u2="&#x201d;" k="82" />
<hkern u1="&#xe6;" u2="&#x201c;" k="82" />
<hkern u1="&#xe6;" u2="&#x2019;" k="82" />
<hkern u1="&#xe6;" u2="&#x2018;" k="82" />
<hkern u1="&#xe6;" u2="&#xba;" k="82" />
<hkern u1="&#xe6;" u2="&#xb0;" k="82" />
<hkern u1="&#xe6;" u2="&#xaa;" k="82" />
<hkern u1="&#xe6;" u2="&#x7d;" k="37" />
<hkern u1="&#xe6;" u2="x" k="57" />
<hkern u1="&#xe6;" u2="]" k="37" />
<hkern u1="&#xe6;" u2="&#x2a;" k="82" />
<hkern u1="&#xe6;" u2="&#x29;" k="37" />
<hkern u1="&#xe6;" u2="&#x27;" k="82" />
<hkern u1="&#xe6;" u2="&#x22;" k="82" />
<hkern u1="&#xe8;" u2="&#x201d;" k="82" />
<hkern u1="&#xe8;" u2="&#x201c;" k="82" />
<hkern u1="&#xe8;" u2="&#x2019;" k="82" />
<hkern u1="&#xe8;" u2="&#x2018;" k="82" />
<hkern u1="&#xe8;" u2="&#xba;" k="82" />
<hkern u1="&#xe8;" u2="&#xb0;" k="82" />
<hkern u1="&#xe8;" u2="&#xaa;" k="82" />
<hkern u1="&#xe8;" u2="&#x7d;" k="37" />
<hkern u1="&#xe8;" u2="x" k="57" />
<hkern u1="&#xe8;" u2="]" k="37" />
<hkern u1="&#xe8;" u2="&#x2a;" k="82" />
<hkern u1="&#xe8;" u2="&#x29;" k="37" />
<hkern u1="&#xe8;" u2="&#x27;" k="82" />
<hkern u1="&#xe8;" u2="&#x22;" k="82" />
<hkern u1="&#xe9;" u2="&#x201d;" k="82" />
<hkern u1="&#xe9;" u2="&#x201c;" k="82" />
<hkern u1="&#xe9;" u2="&#x2019;" k="82" />
<hkern u1="&#xe9;" u2="&#x2018;" k="82" />
<hkern u1="&#xe9;" u2="&#xba;" k="82" />
<hkern u1="&#xe9;" u2="&#xb0;" k="82" />
<hkern u1="&#xe9;" u2="&#xaa;" k="82" />
<hkern u1="&#xe9;" u2="&#x7d;" k="37" />
<hkern u1="&#xe9;" u2="x" k="57" />
<hkern u1="&#xe9;" u2="]" k="37" />
<hkern u1="&#xe9;" u2="&#x2a;" k="82" />
<hkern u1="&#xe9;" u2="&#x29;" k="37" />
<hkern u1="&#xe9;" u2="&#x27;" k="82" />
<hkern u1="&#xe9;" u2="&#x22;" k="82" />
<hkern u1="&#xea;" u2="&#x201d;" k="82" />
<hkern u1="&#xea;" u2="&#x201c;" k="82" />
<hkern u1="&#xea;" u2="&#x2019;" k="82" />
<hkern u1="&#xea;" u2="&#x2018;" k="82" />
<hkern u1="&#xea;" u2="&#xba;" k="82" />
<hkern u1="&#xea;" u2="&#xb0;" k="82" />
<hkern u1="&#xea;" u2="&#xaa;" k="82" />
<hkern u1="&#xea;" u2="&#x7d;" k="37" />
<hkern u1="&#xea;" u2="x" k="57" />
<hkern u1="&#xea;" u2="]" k="37" />
<hkern u1="&#xea;" u2="&#x2a;" k="82" />
<hkern u1="&#xea;" u2="&#x29;" k="37" />
<hkern u1="&#xea;" u2="&#x27;" k="82" />
<hkern u1="&#xea;" u2="&#x22;" k="82" />
<hkern u1="&#xeb;" u2="&#x201d;" k="82" />
<hkern u1="&#xeb;" u2="&#x201c;" k="82" />
<hkern u1="&#xeb;" u2="&#x2019;" k="82" />
<hkern u1="&#xeb;" u2="&#x2018;" k="82" />
<hkern u1="&#xeb;" u2="&#xba;" k="82" />
<hkern u1="&#xeb;" u2="&#xb0;" k="82" />
<hkern u1="&#xeb;" u2="&#xaa;" k="82" />
<hkern u1="&#xeb;" u2="&#x7d;" k="37" />
<hkern u1="&#xeb;" u2="x" k="57" />
<hkern u1="&#xeb;" u2="]" k="37" />
<hkern u1="&#xeb;" u2="&#x2a;" k="82" />
<hkern u1="&#xeb;" u2="&#x29;" k="37" />
<hkern u1="&#xeb;" u2="&#x27;" k="82" />
<hkern u1="&#xeb;" u2="&#x22;" k="82" />
<hkern u1="&#xf1;" u2="&#x201d;" k="61" />
<hkern u1="&#xf1;" u2="&#x201c;" k="61" />
<hkern u1="&#xf1;" u2="&#x2019;" k="61" />
<hkern u1="&#xf1;" u2="&#x2018;" k="61" />
<hkern u1="&#xf1;" u2="&#xff;" k="37" />
<hkern u1="&#xf1;" u2="&#xfd;" k="37" />
<hkern u1="&#xf1;" u2="&#xba;" k="61" />
<hkern u1="&#xf1;" u2="&#xb0;" k="61" />
<hkern u1="&#xf1;" u2="&#xaa;" k="61" />
<hkern u1="&#xf1;" u2="y" k="52" />
<hkern u1="&#xf1;" u2="v" k="37" />
<hkern u1="&#xf1;" u2="&#x2a;" k="61" />
<hkern u1="&#xf1;" u2="&#x27;" k="61" />
<hkern u1="&#xf1;" u2="&#x22;" k="61" />
<hkern u1="&#xf2;" u2="&#x201d;" k="82" />
<hkern u1="&#xf2;" u2="&#x201c;" k="82" />
<hkern u1="&#xf2;" u2="&#x2019;" k="82" />
<hkern u1="&#xf2;" u2="&#x2018;" k="82" />
<hkern u1="&#xf2;" u2="&#xba;" k="82" />
<hkern u1="&#xf2;" u2="&#xb0;" k="82" />
<hkern u1="&#xf2;" u2="&#xaa;" k="82" />
<hkern u1="&#xf2;" u2="&#x7d;" k="37" />
<hkern u1="&#xf2;" u2="x" k="57" />
<hkern u1="&#xf2;" u2="]" k="37" />
<hkern u1="&#xf2;" u2="&#x2a;" k="82" />
<hkern u1="&#xf2;" u2="&#x29;" k="37" />
<hkern u1="&#xf2;" u2="&#x27;" k="82" />
<hkern u1="&#xf2;" u2="&#x22;" k="82" />
<hkern u1="&#xf3;" u2="&#x201d;" k="82" />
<hkern u1="&#xf3;" u2="&#x201c;" k="82" />
<hkern u1="&#xf3;" u2="&#x2019;" k="82" />
<hkern u1="&#xf3;" u2="&#x2018;" k="82" />
<hkern u1="&#xf3;" u2="&#xba;" k="82" />
<hkern u1="&#xf3;" u2="&#xb0;" k="82" />
<hkern u1="&#xf3;" u2="&#xaa;" k="82" />
<hkern u1="&#xf3;" u2="&#x7d;" k="37" />
<hkern u1="&#xf3;" u2="x" k="57" />
<hkern u1="&#xf3;" u2="]" k="37" />
<hkern u1="&#xf3;" u2="&#x2a;" k="82" />
<hkern u1="&#xf3;" u2="&#x29;" k="37" />
<hkern u1="&#xf3;" u2="&#x27;" k="82" />
<hkern u1="&#xf3;" u2="&#x22;" k="82" />
<hkern u1="&#xf4;" u2="&#x201d;" k="82" />
<hkern u1="&#xf4;" u2="&#x201c;" k="82" />
<hkern u1="&#xf4;" u2="&#x2019;" k="82" />
<hkern u1="&#xf4;" u2="&#x2018;" k="82" />
<hkern u1="&#xf4;" u2="&#xba;" k="82" />
<hkern u1="&#xf4;" u2="&#xb0;" k="82" />
<hkern u1="&#xf4;" u2="&#xaa;" k="82" />
<hkern u1="&#xf4;" u2="&#x7d;" k="37" />
<hkern u1="&#xf4;" u2="x" k="57" />
<hkern u1="&#xf4;" u2="]" k="37" />
<hkern u1="&#xf4;" u2="&#x2a;" k="82" />
<hkern u1="&#xf4;" u2="&#x29;" k="37" />
<hkern u1="&#xf4;" u2="&#x27;" k="82" />
<hkern u1="&#xf4;" u2="&#x22;" k="82" />
<hkern u1="&#xf5;" u2="&#x201d;" k="82" />
<hkern u1="&#xf5;" u2="&#x201c;" k="82" />
<hkern u1="&#xf5;" u2="&#x2019;" k="82" />
<hkern u1="&#xf5;" u2="&#x2018;" k="82" />
<hkern u1="&#xf5;" u2="&#xba;" k="82" />
<hkern u1="&#xf5;" u2="&#xb0;" k="82" />
<hkern u1="&#xf5;" u2="&#xaa;" k="82" />
<hkern u1="&#xf5;" u2="&#x7d;" k="37" />
<hkern u1="&#xf5;" u2="x" k="57" />
<hkern u1="&#xf5;" u2="]" k="37" />
<hkern u1="&#xf5;" u2="&#x2a;" k="82" />
<hkern u1="&#xf5;" u2="&#x29;" k="37" />
<hkern u1="&#xf5;" u2="&#x27;" k="82" />
<hkern u1="&#xf5;" u2="&#x22;" k="82" />
<hkern u1="&#xf6;" u2="&#x201d;" k="82" />
<hkern u1="&#xf6;" u2="&#x201c;" k="82" />
<hkern u1="&#xf6;" u2="&#x2019;" k="82" />
<hkern u1="&#xf6;" u2="&#x2018;" k="82" />
<hkern u1="&#xf6;" u2="&#xba;" k="82" />
<hkern u1="&#xf6;" u2="&#xb0;" k="82" />
<hkern u1="&#xf6;" u2="&#xaa;" k="82" />
<hkern u1="&#xf6;" u2="&#x7d;" k="37" />
<hkern u1="&#xf6;" u2="x" k="57" />
<hkern u1="&#xf6;" u2="]" k="37" />
<hkern u1="&#xf6;" u2="&#x2a;" k="82" />
<hkern u1="&#xf6;" u2="&#x29;" k="37" />
<hkern u1="&#xf6;" u2="&#x27;" k="82" />
<hkern u1="&#xf6;" u2="&#x22;" k="82" />
<hkern u1="&#xf8;" u2="&#x201d;" k="82" />
<hkern u1="&#xf8;" u2="&#x201c;" k="82" />
<hkern u1="&#xf8;" u2="&#x2019;" k="82" />
<hkern u1="&#xf8;" u2="&#x2018;" k="82" />
<hkern u1="&#xf8;" u2="&#xba;" k="82" />
<hkern u1="&#xf8;" u2="&#xb0;" k="82" />
<hkern u1="&#xf8;" u2="&#xaa;" k="82" />
<hkern u1="&#xf8;" u2="&#x7d;" k="37" />
<hkern u1="&#xf8;" u2="x" k="57" />
<hkern u1="&#xf8;" u2="]" k="37" />
<hkern u1="&#xf8;" u2="&#x2a;" k="82" />
<hkern u1="&#xf8;" u2="&#x29;" k="37" />
<hkern u1="&#xf8;" u2="&#x27;" k="82" />
<hkern u1="&#xf8;" u2="&#x22;" k="82" />
<hkern u1="&#xfd;" u2="&#x2206;" k="60" />
<hkern u1="&#xfd;" u2="&#x2026;" k="155" />
<hkern u1="&#xfd;" u2="&#x201e;" k="155" />
<hkern u1="&#xfd;" u2="&#x201a;" k="155" />
<hkern u1="&#xfd;" u2="&#x153;" k="34" />
<hkern u1="&#xfd;" u2="&#x119;" k="34" />
<hkern u1="&#xfd;" u2="&#x107;" k="34" />
<hkern u1="&#xfd;" u2="&#x105;" k="34" />
<hkern u1="&#xfd;" u2="&#x104;" k="60" />
<hkern u1="&#xfd;" u2="&#xf8;" k="34" />
<hkern u1="&#xfd;" u2="&#xf6;" k="34" />
<hkern u1="&#xfd;" u2="&#xf5;" k="34" />
<hkern u1="&#xfd;" u2="&#xf4;" k="34" />
<hkern u1="&#xfd;" u2="&#xf3;" k="34" />
<hkern u1="&#xfd;" u2="&#xf2;" k="34" />
<hkern u1="&#xfd;" u2="&#xf0;" k="34" />
<hkern u1="&#xfd;" u2="&#xeb;" k="34" />
<hkern u1="&#xfd;" u2="&#xea;" k="34" />
<hkern u1="&#xfd;" u2="&#xe9;" k="34" />
<hkern u1="&#xfd;" u2="&#xe8;" k="34" />
<hkern u1="&#xfd;" u2="&#xe7;" k="34" />
<hkern u1="&#xfd;" u2="&#xe6;" k="34" />
<hkern u1="&#xfd;" u2="&#xe5;" k="34" />
<hkern u1="&#xfd;" u2="&#xe4;" k="34" />
<hkern u1="&#xfd;" u2="&#xe3;" k="34" />
<hkern u1="&#xfd;" u2="&#xe2;" k="34" />
<hkern u1="&#xfd;" u2="&#xe1;" k="34" />
<hkern u1="&#xfd;" u2="&#xe0;" k="34" />
<hkern u1="&#xfd;" u2="&#xc6;" k="60" />
<hkern u1="&#xfd;" u2="&#xc5;" k="60" />
<hkern u1="&#xfd;" u2="&#xc4;" k="60" />
<hkern u1="&#xfd;" u2="&#xc3;" k="60" />
<hkern u1="&#xfd;" u2="&#xc2;" k="60" />
<hkern u1="&#xfd;" u2="&#xc1;" k="60" />
<hkern u1="&#xfd;" u2="&#xc0;" k="60" />
<hkern u1="&#xfd;" u2="q" k="34" />
<hkern u1="&#xfd;" u2="o" k="34" />
<hkern u1="&#xfd;" u2="e" k="34" />
<hkern u1="&#xfd;" u2="d" k="34" />
<hkern u1="&#xfd;" u2="c" k="34" />
<hkern u1="&#xfd;" u2="a" k="34" />
<hkern u1="&#xfd;" u2="A" k="60" />
<hkern u1="&#xfd;" u2="&#x2f;" k="60" />
<hkern u1="&#xfd;" u2="&#x2e;" k="155" />
<hkern u1="&#xfd;" u2="&#x2c;" k="155" />
<hkern u1="&#xfd;" u2="&#x26;" k="60" />
<hkern u1="&#xfe;" u2="&#x201d;" k="82" />
<hkern u1="&#xfe;" u2="&#x201c;" k="82" />
<hkern u1="&#xfe;" u2="&#x2019;" k="82" />
<hkern u1="&#xfe;" u2="&#x2018;" k="82" />
<hkern u1="&#xfe;" u2="&#xba;" k="82" />
<hkern u1="&#xfe;" u2="&#xb0;" k="82" />
<hkern u1="&#xfe;" u2="&#xaa;" k="82" />
<hkern u1="&#xfe;" u2="&#x7d;" k="37" />
<hkern u1="&#xfe;" u2="x" k="57" />
<hkern u1="&#xfe;" u2="]" k="37" />
<hkern u1="&#xfe;" u2="&#x2a;" k="82" />
<hkern u1="&#xfe;" u2="&#x29;" k="37" />
<hkern u1="&#xfe;" u2="&#x27;" k="82" />
<hkern u1="&#xfe;" u2="&#x22;" k="82" />
<hkern u1="&#xff;" u2="&#x2206;" k="60" />
<hkern u1="&#xff;" u2="&#x2026;" k="155" />
<hkern u1="&#xff;" u2="&#x201e;" k="155" />
<hkern u1="&#xff;" u2="&#x201a;" k="155" />
<hkern u1="&#xff;" u2="&#x153;" k="34" />
<hkern u1="&#xff;" u2="&#x119;" k="34" />
<hkern u1="&#xff;" u2="&#x107;" k="34" />
<hkern u1="&#xff;" u2="&#x105;" k="34" />
<hkern u1="&#xff;" u2="&#x104;" k="60" />
<hkern u1="&#xff;" u2="&#xf8;" k="34" />
<hkern u1="&#xff;" u2="&#xf6;" k="34" />
<hkern u1="&#xff;" u2="&#xf5;" k="34" />
<hkern u1="&#xff;" u2="&#xf4;" k="34" />
<hkern u1="&#xff;" u2="&#xf3;" k="34" />
<hkern u1="&#xff;" u2="&#xf2;" k="34" />
<hkern u1="&#xff;" u2="&#xf0;" k="34" />
<hkern u1="&#xff;" u2="&#xeb;" k="34" />
<hkern u1="&#xff;" u2="&#xea;" k="34" />
<hkern u1="&#xff;" u2="&#xe9;" k="34" />
<hkern u1="&#xff;" u2="&#xe8;" k="34" />
<hkern u1="&#xff;" u2="&#xe7;" k="34" />
<hkern u1="&#xff;" u2="&#xe6;" k="34" />
<hkern u1="&#xff;" u2="&#xe5;" k="34" />
<hkern u1="&#xff;" u2="&#xe4;" k="34" />
<hkern u1="&#xff;" u2="&#xe3;" k="34" />
<hkern u1="&#xff;" u2="&#xe2;" k="34" />
<hkern u1="&#xff;" u2="&#xe1;" k="34" />
<hkern u1="&#xff;" u2="&#xe0;" k="34" />
<hkern u1="&#xff;" u2="&#xc6;" k="60" />
<hkern u1="&#xff;" u2="&#xc5;" k="60" />
<hkern u1="&#xff;" u2="&#xc4;" k="60" />
<hkern u1="&#xff;" u2="&#xc3;" k="60" />
<hkern u1="&#xff;" u2="&#xc2;" k="60" />
<hkern u1="&#xff;" u2="&#xc1;" k="60" />
<hkern u1="&#xff;" u2="&#xc0;" k="60" />
<hkern u1="&#xff;" u2="q" k="34" />
<hkern u1="&#xff;" u2="o" k="34" />
<hkern u1="&#xff;" u2="e" k="34" />
<hkern u1="&#xff;" u2="d" k="34" />
<hkern u1="&#xff;" u2="c" k="34" />
<hkern u1="&#xff;" u2="a" k="34" />
<hkern u1="&#xff;" u2="A" k="60" />
<hkern u1="&#xff;" u2="&#x2f;" k="60" />
<hkern u1="&#xff;" u2="&#x2e;" k="155" />
<hkern u1="&#xff;" u2="&#x2c;" k="155" />
<hkern u1="&#xff;" u2="&#x26;" k="60" />
<hkern u1="&#x104;" u2="&#x203a;" k="69" />
<hkern u1="&#x104;" u2="&#x2039;" k="69" />
<hkern u1="&#x104;" u2="&#x2022;" k="69" />
<hkern u1="&#x104;" u2="&#x201d;" k="196" />
<hkern u1="&#x104;" u2="&#x201c;" k="196" />
<hkern u1="&#x104;" u2="&#x2019;" k="196" />
<hkern u1="&#x104;" u2="&#x2018;" k="196" />
<hkern u1="&#x104;" u2="&#x2014;" k="69" />
<hkern u1="&#x104;" u2="&#x2013;" k="69" />
<hkern u1="&#x104;" u2="&#x178;" k="171" />
<hkern u1="&#x104;" u2="&#x152;" k="60" />
<hkern u1="&#x104;" u2="&#x106;" k="60" />
<hkern u1="&#x104;" u2="&#xff;" k="60" />
<hkern u1="&#x104;" u2="&#xfd;" k="60" />
<hkern u1="&#x104;" u2="&#xdd;" k="171" />
<hkern u1="&#x104;" u2="&#xdc;" k="41" />
<hkern u1="&#x104;" u2="&#xdb;" k="41" />
<hkern u1="&#x104;" u2="&#xda;" k="41" />
<hkern u1="&#x104;" u2="&#xd9;" k="41" />
<hkern u1="&#x104;" u2="&#xd8;" k="60" />
<hkern u1="&#x104;" u2="&#xd6;" k="60" />
<hkern u1="&#x104;" u2="&#xd5;" k="60" />
<hkern u1="&#x104;" u2="&#xd4;" k="60" />
<hkern u1="&#x104;" u2="&#xd3;" k="60" />
<hkern u1="&#x104;" u2="&#xd2;" k="60" />
<hkern u1="&#x104;" u2="&#xc7;" k="60" />
<hkern u1="&#x104;" u2="&#xbb;" k="69" />
<hkern u1="&#x104;" u2="&#xba;" k="196" />
<hkern u1="&#x104;" u2="&#xb7;" k="69" />
<hkern u1="&#x104;" u2="&#xb0;" k="196" />
<hkern u1="&#x104;" u2="&#xae;" k="60" />
<hkern u1="&#x104;" u2="&#xab;" k="69" />
<hkern u1="&#x104;" u2="&#xaa;" k="196" />
<hkern u1="&#x104;" u2="&#xa9;" k="60" />
<hkern u1="&#x104;" u2="y" k="60" />
<hkern u1="&#x104;" u2="w" k="48" />
<hkern u1="&#x104;" u2="v" k="60" />
<hkern u1="&#x104;" u2="t" k="71" />
<hkern u1="&#x104;" u2="\" k="141" />
<hkern u1="&#x104;" u2="Y" k="171" />
<hkern u1="&#x104;" u2="W" k="104" />
<hkern u1="&#x104;" u2="V" k="141" />
<hkern u1="&#x104;" u2="U" k="41" />
<hkern u1="&#x104;" u2="T" k="151" />
<hkern u1="&#x104;" u2="Q" k="60" />
<hkern u1="&#x104;" u2="O" k="60" />
<hkern u1="&#x104;" u2="J" k="-73" />
<hkern u1="&#x104;" u2="G" k="60" />
<hkern u1="&#x104;" u2="C" k="60" />
<hkern u1="&#x104;" u2="&#x40;" k="60" />
<hkern u1="&#x104;" u2="&#x2d;" k="69" />
<hkern u1="&#x104;" u2="&#x2a;" k="196" />
<hkern u1="&#x104;" u2="&#x27;" k="196" />
<hkern u1="&#x104;" u2="&#x22;" k="196" />
<hkern u1="&#x106;" u2="&#x203a;" k="116" />
<hkern u1="&#x106;" u2="&#x2039;" k="116" />
<hkern u1="&#x106;" u2="&#x2022;" k="116" />
<hkern u1="&#x106;" u2="&#x2014;" k="116" />
<hkern u1="&#x106;" u2="&#x2013;" k="116" />
<hkern u1="&#x106;" u2="&#xbb;" k="116" />
<hkern u1="&#x106;" u2="&#xb7;" k="116" />
<hkern u1="&#x106;" u2="&#xab;" k="116" />
<hkern u1="&#x106;" u2="&#x2d;" k="116" />
<hkern u1="&#x119;" u2="&#x201d;" k="82" />
<hkern u1="&#x119;" u2="&#x201c;" k="82" />
<hkern u1="&#x119;" u2="&#x2019;" k="82" />
<hkern u1="&#x119;" u2="&#x2018;" k="82" />
<hkern u1="&#x119;" u2="&#xba;" k="82" />
<hkern u1="&#x119;" u2="&#xb0;" k="82" />
<hkern u1="&#x119;" u2="&#xaa;" k="82" />
<hkern u1="&#x119;" u2="&#x7d;" k="37" />
<hkern u1="&#x119;" u2="x" k="57" />
<hkern u1="&#x119;" u2="]" k="37" />
<hkern u1="&#x119;" u2="&#x2a;" k="82" />
<hkern u1="&#x119;" u2="&#x29;" k="37" />
<hkern u1="&#x119;" u2="&#x27;" k="82" />
<hkern u1="&#x119;" u2="&#x22;" k="82" />
<hkern u1="&#x141;" u2="&#x203a;" k="100" />
<hkern u1="&#x141;" u2="&#x2039;" k="100" />
<hkern u1="&#x141;" u2="&#x2022;" k="100" />
<hkern u1="&#x141;" u2="&#x201d;" k="160" />
<hkern u1="&#x141;" u2="&#x201c;" k="160" />
<hkern u1="&#x141;" u2="&#x2019;" k="160" />
<hkern u1="&#x141;" u2="&#x2018;" k="160" />
<hkern u1="&#x141;" u2="&#x2014;" k="100" />
<hkern u1="&#x141;" u2="&#x2013;" k="100" />
<hkern u1="&#x141;" u2="&#x178;" k="171" />
<hkern u1="&#x141;" u2="&#xff;" k="82" />
<hkern u1="&#x141;" u2="&#xfd;" k="82" />
<hkern u1="&#x141;" u2="&#xdd;" k="171" />
<hkern u1="&#x141;" u2="&#xbb;" k="100" />
<hkern u1="&#x141;" u2="&#xba;" k="160" />
<hkern u1="&#x141;" u2="&#xb7;" k="100" />
<hkern u1="&#x141;" u2="&#xb0;" k="160" />
<hkern u1="&#x141;" u2="&#xab;" k="100" />
<hkern u1="&#x141;" u2="&#xaa;" k="160" />
<hkern u1="&#x141;" u2="y" k="82" />
<hkern u1="&#x141;" u2="w" k="57" />
<hkern u1="&#x141;" u2="v" k="82" />
<hkern u1="&#x141;" u2="\" k="175" />
<hkern u1="&#x141;" u2="Y" k="171" />
<hkern u1="&#x141;" u2="W" k="134" />
<hkern u1="&#x141;" u2="V" k="175" />
<hkern u1="&#x141;" u2="&#x2d;" k="100" />
<hkern u1="&#x141;" u2="&#x2a;" k="160" />
<hkern u1="&#x141;" u2="&#x27;" k="160" />
<hkern u1="&#x141;" u2="&#x22;" k="160" />
<hkern u1="&#x144;" u2="&#x201d;" k="61" />
<hkern u1="&#x144;" u2="&#x201c;" k="61" />
<hkern u1="&#x144;" u2="&#x2019;" k="61" />
<hkern u1="&#x144;" u2="&#x2018;" k="61" />
<hkern u1="&#x144;" u2="&#xff;" k="37" />
<hkern u1="&#x144;" u2="&#xfd;" k="37" />
<hkern u1="&#x144;" u2="&#xba;" k="61" />
<hkern u1="&#x144;" u2="&#xb0;" k="61" />
<hkern u1="&#x144;" u2="&#xaa;" k="61" />
<hkern u1="&#x144;" u2="y" k="52" />
<hkern u1="&#x144;" u2="v" k="37" />
<hkern u1="&#x144;" u2="&#x2a;" k="61" />
<hkern u1="&#x144;" u2="&#x27;" k="61" />
<hkern u1="&#x144;" u2="&#x22;" k="61" />
<hkern u1="&#x153;" u2="&#x201d;" k="82" />
<hkern u1="&#x153;" u2="&#x201c;" k="82" />
<hkern u1="&#x153;" u2="&#x2019;" k="82" />
<hkern u1="&#x153;" u2="&#x2018;" k="82" />
<hkern u1="&#x153;" u2="&#xba;" k="82" />
<hkern u1="&#x153;" u2="&#xb0;" k="82" />
<hkern u1="&#x153;" u2="&#xaa;" k="82" />
<hkern u1="&#x153;" u2="&#x7d;" k="37" />
<hkern u1="&#x153;" u2="x" k="57" />
<hkern u1="&#x153;" u2="]" k="37" />
<hkern u1="&#x153;" u2="&#x2a;" k="82" />
<hkern u1="&#x153;" u2="&#x29;" k="37" />
<hkern u1="&#x153;" u2="&#x27;" k="82" />
<hkern u1="&#x153;" u2="&#x22;" k="82" />
<hkern u1="&#x178;" u2="&#x2206;" k="155" />
<hkern u1="&#x178;" u2="&#x203a;" k="196" />
<hkern u1="&#x178;" u2="&#x2039;" k="196" />
<hkern u1="&#x178;" u2="&#x2026;" k="266" />
<hkern u1="&#x178;" u2="&#x2022;" k="196" />
<hkern u1="&#x178;" u2="&#x201e;" k="266" />
<hkern u1="&#x178;" u2="&#x201d;" k="-59" />
<hkern u1="&#x178;" u2="&#x201c;" k="-59" />
<hkern u1="&#x178;" u2="&#x201a;" k="266" />
<hkern u1="&#x178;" u2="&#x2019;" k="-59" />
<hkern u1="&#x178;" u2="&#x2018;" k="-59" />
<hkern u1="&#x178;" u2="&#x2014;" k="196" />
<hkern u1="&#x178;" u2="&#x2013;" k="196" />
<hkern u1="&#x178;" u2="&#x17e;" k="119" />
<hkern u1="&#x178;" u2="&#x17c;" k="119" />
<hkern u1="&#x178;" u2="&#x17a;" k="119" />
<hkern u1="&#x178;" u2="&#x161;" k="201" />
<hkern u1="&#x178;" u2="&#x15b;" k="201" />
<hkern u1="&#x178;" u2="&#x153;" k="201" />
<hkern u1="&#x178;" u2="&#x152;" k="66" />
<hkern u1="&#x178;" u2="&#x144;" k="115" />
<hkern u1="&#x178;" u2="&#x119;" k="201" />
<hkern u1="&#x178;" u2="&#x107;" k="201" />
<hkern u1="&#x178;" u2="&#x106;" k="66" />
<hkern u1="&#x178;" u2="&#x105;" k="201" />
<hkern u1="&#x178;" u2="&#x104;" k="155" />
<hkern u1="&#x178;" u2="&#xfc;" k="115" />
<hkern u1="&#x178;" u2="&#xfb;" k="115" />
<hkern u1="&#x178;" u2="&#xfa;" k="115" />
<hkern u1="&#x178;" u2="&#xf9;" k="115" />
<hkern u1="&#x178;" u2="&#xf8;" k="201" />
<hkern u1="&#x178;" u2="&#xf6;" k="201" />
<hkern u1="&#x178;" u2="&#xf5;" k="201" />
<hkern u1="&#x178;" u2="&#xf4;" k="201" />
<hkern u1="&#x178;" u2="&#xf3;" k="201" />
<hkern u1="&#x178;" u2="&#xf2;" k="201" />
<hkern u1="&#x178;" u2="&#xf1;" k="115" />
<hkern u1="&#x178;" u2="&#xf0;" k="201" />
<hkern u1="&#x178;" u2="&#xeb;" k="201" />
<hkern u1="&#x178;" u2="&#xea;" k="201" />
<hkern u1="&#x178;" u2="&#xe9;" k="201" />
<hkern u1="&#x178;" u2="&#xe8;" k="201" />
<hkern u1="&#x178;" u2="&#xe7;" k="201" />
<hkern u1="&#x178;" u2="&#xe6;" k="201" />
<hkern u1="&#x178;" u2="&#xe5;" k="201" />
<hkern u1="&#x178;" u2="&#xe4;" k="201" />
<hkern u1="&#x178;" u2="&#xe3;" k="201" />
<hkern u1="&#x178;" u2="&#xe2;" k="201" />
<hkern u1="&#x178;" u2="&#xe1;" k="201" />
<hkern u1="&#x178;" u2="&#xe0;" k="201" />
<hkern u1="&#x178;" u2="&#xd8;" k="66" />
<hkern u1="&#x178;" u2="&#xd6;" k="66" />
<hkern u1="&#x178;" u2="&#xd5;" k="66" />
<hkern u1="&#x178;" u2="&#xd4;" k="66" />
<hkern u1="&#x178;" u2="&#xd3;" k="66" />
<hkern u1="&#x178;" u2="&#xd2;" k="66" />
<hkern u1="&#x178;" u2="&#xc7;" k="66" />
<hkern u1="&#x178;" u2="&#xc6;" k="155" />
<hkern u1="&#x178;" u2="&#xc5;" k="155" />
<hkern u1="&#x178;" u2="&#xc4;" k="155" />
<hkern u1="&#x178;" u2="&#xc3;" k="155" />
<hkern u1="&#x178;" u2="&#xc2;" k="155" />
<hkern u1="&#x178;" u2="&#xc1;" k="155" />
<hkern u1="&#x178;" u2="&#xc0;" k="155" />
<hkern u1="&#x178;" u2="&#xbb;" k="196" />
<hkern u1="&#x178;" u2="&#xba;" k="-59" />
<hkern u1="&#x178;" u2="&#xb7;" k="196" />
<hkern u1="&#x178;" u2="&#xb5;" k="115" />
<hkern u1="&#x178;" u2="&#xb0;" k="-59" />
<hkern u1="&#x178;" u2="&#xae;" k="66" />
<hkern u1="&#x178;" u2="&#xab;" k="196" />
<hkern u1="&#x178;" u2="&#xaa;" k="-59" />
<hkern u1="&#x178;" u2="&#xa9;" k="66" />
<hkern u1="&#x178;" u2="z" k="119" />
<hkern u1="&#x178;" u2="u" k="115" />
<hkern u1="&#x178;" u2="s" k="201" />
<hkern u1="&#x178;" u2="r" k="115" />
<hkern u1="&#x178;" u2="q" k="201" />
<hkern u1="&#x178;" u2="p" k="115" />
<hkern u1="&#x178;" u2="o" k="201" />
<hkern u1="&#x178;" u2="n" k="115" />
<hkern u1="&#x178;" u2="m" k="115" />
<hkern u1="&#x178;" u2="g" k="179" />
<hkern u1="&#x178;" u2="e" k="201" />
<hkern u1="&#x178;" u2="d" k="201" />
<hkern u1="&#x178;" u2="c" k="201" />
<hkern u1="&#x178;" u2="a" k="201" />
<hkern u1="&#x178;" u2="Q" k="66" />
<hkern u1="&#x178;" u2="O" k="66" />
<hkern u1="&#x178;" u2="J" k="205" />
<hkern u1="&#x178;" u2="G" k="66" />
<hkern u1="&#x178;" u2="C" k="66" />
<hkern u1="&#x178;" u2="A" k="155" />
<hkern u1="&#x178;" u2="&#x40;" k="66" />
<hkern u1="&#x178;" u2="&#x3f;" k="-51" />
<hkern u1="&#x178;" u2="&#x3b;" k="115" />
<hkern u1="&#x178;" u2="&#x3a;" k="115" />
<hkern u1="&#x178;" u2="&#x2f;" k="155" />
<hkern u1="&#x178;" u2="&#x2e;" k="266" />
<hkern u1="&#x178;" u2="&#x2d;" k="196" />
<hkern u1="&#x178;" u2="&#x2c;" k="266" />
<hkern u1="&#x178;" u2="&#x2a;" k="-59" />
<hkern u1="&#x178;" u2="&#x27;" k="-59" />
<hkern u1="&#x178;" u2="&#x26;" k="155" />
<hkern u1="&#x178;" u2="&#x22;" k="-59" />
<hkern u1="&#x179;" u2="&#x203a;" k="84" />
<hkern u1="&#x179;" u2="&#x2039;" k="84" />
<hkern u1="&#x179;" u2="&#x2022;" k="84" />
<hkern u1="&#x179;" u2="&#x2014;" k="84" />
<hkern u1="&#x179;" u2="&#x2013;" k="84" />
<hkern u1="&#x179;" u2="&#x152;" k="50" />
<hkern u1="&#x179;" u2="&#x106;" k="50" />
<hkern u1="&#x179;" u2="&#xd8;" k="50" />
<hkern u1="&#x179;" u2="&#xd6;" k="50" />
<hkern u1="&#x179;" u2="&#xd5;" k="50" />
<hkern u1="&#x179;" u2="&#xd4;" k="50" />
<hkern u1="&#x179;" u2="&#xd3;" k="50" />
<hkern u1="&#x179;" u2="&#xd2;" k="50" />
<hkern u1="&#x179;" u2="&#xc7;" k="50" />
<hkern u1="&#x179;" u2="&#xbb;" k="84" />
<hkern u1="&#x179;" u2="&#xb7;" k="84" />
<hkern u1="&#x179;" u2="&#xae;" k="50" />
<hkern u1="&#x179;" u2="&#xab;" k="84" />
<hkern u1="&#x179;" u2="&#xa9;" k="50" />
<hkern u1="&#x179;" u2="Q" k="50" />
<hkern u1="&#x179;" u2="O" k="50" />
<hkern u1="&#x179;" u2="G" k="50" />
<hkern u1="&#x179;" u2="C" k="50" />
<hkern u1="&#x179;" u2="&#x40;" k="50" />
<hkern u1="&#x179;" u2="&#x3f;" k="-39" />
<hkern u1="&#x179;" u2="&#x2d;" k="84" />
<hkern u1="&#x17b;" u2="&#x203a;" k="84" />
<hkern u1="&#x17b;" u2="&#x2039;" k="84" />
<hkern u1="&#x17b;" u2="&#x2022;" k="84" />
<hkern u1="&#x17b;" u2="&#x2014;" k="84" />
<hkern u1="&#x17b;" u2="&#x2013;" k="84" />
<hkern u1="&#x17b;" u2="&#x152;" k="50" />
<hkern u1="&#x17b;" u2="&#x106;" k="50" />
<hkern u1="&#x17b;" u2="&#xd8;" k="50" />
<hkern u1="&#x17b;" u2="&#xd6;" k="50" />
<hkern u1="&#x17b;" u2="&#xd5;" k="50" />
<hkern u1="&#x17b;" u2="&#xd4;" k="50" />
<hkern u1="&#x17b;" u2="&#xd3;" k="50" />
<hkern u1="&#x17b;" u2="&#xd2;" k="50" />
<hkern u1="&#x17b;" u2="&#xc7;" k="50" />
<hkern u1="&#x17b;" u2="&#xbb;" k="84" />
<hkern u1="&#x17b;" u2="&#xb7;" k="84" />
<hkern u1="&#x17b;" u2="&#xae;" k="50" />
<hkern u1="&#x17b;" u2="&#xab;" k="84" />
<hkern u1="&#x17b;" u2="&#xa9;" k="50" />
<hkern u1="&#x17b;" u2="Q" k="50" />
<hkern u1="&#x17b;" u2="O" k="50" />
<hkern u1="&#x17b;" u2="G" k="50" />
<hkern u1="&#x17b;" u2="C" k="50" />
<hkern u1="&#x17b;" u2="&#x40;" k="50" />
<hkern u1="&#x17b;" u2="&#x3f;" k="-39" />
<hkern u1="&#x17b;" u2="&#x2d;" k="84" />
<hkern u1="&#x17d;" u2="&#x203a;" k="84" />
<hkern u1="&#x17d;" u2="&#x2039;" k="84" />
<hkern u1="&#x17d;" u2="&#x2022;" k="84" />
<hkern u1="&#x17d;" u2="&#x2014;" k="84" />
<hkern u1="&#x17d;" u2="&#x2013;" k="84" />
<hkern u1="&#x17d;" u2="&#x152;" k="50" />
<hkern u1="&#x17d;" u2="&#x106;" k="50" />
<hkern u1="&#x17d;" u2="&#xd8;" k="50" />
<hkern u1="&#x17d;" u2="&#xd6;" k="50" />
<hkern u1="&#x17d;" u2="&#xd5;" k="50" />
<hkern u1="&#x17d;" u2="&#xd4;" k="50" />
<hkern u1="&#x17d;" u2="&#xd3;" k="50" />
<hkern u1="&#x17d;" u2="&#xd2;" k="50" />
<hkern u1="&#x17d;" u2="&#xc7;" k="50" />
<hkern u1="&#x17d;" u2="&#xbb;" k="84" />
<hkern u1="&#x17d;" u2="&#xb7;" k="84" />
<hkern u1="&#x17d;" u2="&#xae;" k="50" />
<hkern u1="&#x17d;" u2="&#xab;" k="84" />
<hkern u1="&#x17d;" u2="&#xa9;" k="50" />
<hkern u1="&#x17d;" u2="Q" k="50" />
<hkern u1="&#x17d;" u2="O" k="50" />
<hkern u1="&#x17d;" u2="G" k="50" />
<hkern u1="&#x17d;" u2="C" k="50" />
<hkern u1="&#x17d;" u2="&#x40;" k="50" />
<hkern u1="&#x17d;" u2="&#x3f;" k="-39" />
<hkern u1="&#x17d;" u2="&#x2d;" k="84" />
<hkern u1="&#x2013;" u2="&#x2206;" k="69" />
<hkern u1="&#x2013;" u2="&#x2026;" k="175" />
<hkern u1="&#x2013;" u2="&#x201e;" k="175" />
<hkern u1="&#x2013;" u2="&#x201d;" k="41" />
<hkern u1="&#x2013;" u2="&#x201c;" k="41" />
<hkern u1="&#x2013;" u2="&#x201a;" k="175" />
<hkern u1="&#x2013;" u2="&#x2019;" k="41" />
<hkern u1="&#x2013;" u2="&#x2018;" k="41" />
<hkern u1="&#x2013;" u2="&#x17d;" k="59" />
<hkern u1="&#x2013;" u2="&#x17b;" k="59" />
<hkern u1="&#x2013;" u2="&#x179;" k="59" />
<hkern u1="&#x2013;" u2="&#x178;" k="212" />
<hkern u1="&#x2013;" u2="&#x104;" k="69" />
<hkern u1="&#x2013;" u2="&#xdd;" k="212" />
<hkern u1="&#x2013;" u2="&#xc6;" k="69" />
<hkern u1="&#x2013;" u2="&#xc5;" k="69" />
<hkern u1="&#x2013;" u2="&#xc4;" k="69" />
<hkern u1="&#x2013;" u2="&#xc3;" k="69" />
<hkern u1="&#x2013;" u2="&#xc2;" k="69" />
<hkern u1="&#x2013;" u2="&#xc1;" k="69" />
<hkern u1="&#x2013;" u2="&#xc0;" k="69" />
<hkern u1="&#x2013;" u2="&#xba;" k="41" />
<hkern u1="&#x2013;" u2="&#xb0;" k="41" />
<hkern u1="&#x2013;" u2="&#xaa;" k="41" />
<hkern u1="&#x2013;" u2="\" k="134" />
<hkern u1="&#x2013;" u2="Z" k="59" />
<hkern u1="&#x2013;" u2="Y" k="212" />
<hkern u1="&#x2013;" u2="X" k="72" />
<hkern u1="&#x2013;" u2="V" k="134" />
<hkern u1="&#x2013;" u2="T" k="201" />
<hkern u1="&#x2013;" u2="A" k="69" />
<hkern u1="&#x2013;" u2="&#x2f;" k="69" />
<hkern u1="&#x2013;" u2="&#x2e;" k="175" />
<hkern u1="&#x2013;" u2="&#x2c;" k="175" />
<hkern u1="&#x2013;" u2="&#x2a;" k="41" />
<hkern u1="&#x2013;" u2="&#x27;" k="41" />
<hkern u1="&#x2013;" u2="&#x26;" k="69" />
<hkern u1="&#x2013;" u2="&#x22;" k="41" />
<hkern u1="&#x2014;" u2="&#x2206;" k="69" />
<hkern u1="&#x2014;" u2="&#x2026;" k="175" />
<hkern u1="&#x2014;" u2="&#x201e;" k="175" />
<hkern u1="&#x2014;" u2="&#x201d;" k="41" />
<hkern u1="&#x2014;" u2="&#x201c;" k="41" />
<hkern u1="&#x2014;" u2="&#x201a;" k="175" />
<hkern u1="&#x2014;" u2="&#x2019;" k="41" />
<hkern u1="&#x2014;" u2="&#x2018;" k="41" />
<hkern u1="&#x2014;" u2="&#x17d;" k="59" />
<hkern u1="&#x2014;" u2="&#x17b;" k="59" />
<hkern u1="&#x2014;" u2="&#x179;" k="59" />
<hkern u1="&#x2014;" u2="&#x178;" k="212" />
<hkern u1="&#x2014;" u2="&#x104;" k="69" />
<hkern u1="&#x2014;" u2="&#xdd;" k="212" />
<hkern u1="&#x2014;" u2="&#xc6;" k="69" />
<hkern u1="&#x2014;" u2="&#xc5;" k="69" />
<hkern u1="&#x2014;" u2="&#xc4;" k="69" />
<hkern u1="&#x2014;" u2="&#xc3;" k="69" />
<hkern u1="&#x2014;" u2="&#xc2;" k="69" />
<hkern u1="&#x2014;" u2="&#xc1;" k="69" />
<hkern u1="&#x2014;" u2="&#xc0;" k="69" />
<hkern u1="&#x2014;" u2="&#xba;" k="41" />
<hkern u1="&#x2014;" u2="&#xb0;" k="41" />
<hkern u1="&#x2014;" u2="&#xaa;" k="41" />
<hkern u1="&#x2014;" u2="\" k="134" />
<hkern u1="&#x2014;" u2="Z" k="59" />
<hkern u1="&#x2014;" u2="Y" k="212" />
<hkern u1="&#x2014;" u2="X" k="72" />
<hkern u1="&#x2014;" u2="V" k="134" />
<hkern u1="&#x2014;" u2="T" k="201" />
<hkern u1="&#x2014;" u2="A" k="69" />
<hkern u1="&#x2014;" u2="&#x2f;" k="69" />
<hkern u1="&#x2014;" u2="&#x2e;" k="175" />
<hkern u1="&#x2014;" u2="&#x2c;" k="175" />
<hkern u1="&#x2014;" u2="&#x2a;" k="41" />
<hkern u1="&#x2014;" u2="&#x27;" k="41" />
<hkern u1="&#x2014;" u2="&#x26;" k="69" />
<hkern u1="&#x2014;" u2="&#x22;" k="41" />
<hkern u1="&#x2018;" u2="&#x2206;" k="180" />
<hkern u1="&#x2018;" u2="&#x203a;" k="41" />
<hkern u1="&#x2018;" u2="&#x2039;" k="41" />
<hkern u1="&#x2018;" u2="&#x2026;" k="45" />
<hkern u1="&#x2018;" u2="&#x2022;" k="41" />
<hkern u1="&#x2018;" u2="&#x201e;" k="45" />
<hkern u1="&#x2018;" u2="&#x201a;" k="45" />
<hkern u1="&#x2018;" u2="&#x2014;" k="41" />
<hkern u1="&#x2018;" u2="&#x2013;" k="41" />
<hkern u1="&#x2018;" u2="&#x178;" k="-43" />
<hkern u1="&#x2018;" u2="&#x153;" k="98" />
<hkern u1="&#x2018;" u2="&#x119;" k="98" />
<hkern u1="&#x2018;" u2="&#x107;" k="98" />
<hkern u1="&#x2018;" u2="&#x105;" k="98" />
<hkern u1="&#x2018;" u2="&#x104;" k="180" />
<hkern u1="&#x2018;" u2="&#xf8;" k="98" />
<hkern u1="&#x2018;" u2="&#xf6;" k="98" />
<hkern u1="&#x2018;" u2="&#xf5;" k="98" />
<hkern u1="&#x2018;" u2="&#xf4;" k="98" />
<hkern u1="&#x2018;" u2="&#xf3;" k="98" />
<hkern u1="&#x2018;" u2="&#xf2;" k="98" />
<hkern u1="&#x2018;" u2="&#xf0;" k="98" />
<hkern u1="&#x2018;" u2="&#xeb;" k="98" />
<hkern u1="&#x2018;" u2="&#xea;" k="98" />
<hkern u1="&#x2018;" u2="&#xe9;" k="98" />
<hkern u1="&#x2018;" u2="&#xe8;" k="98" />
<hkern u1="&#x2018;" u2="&#xe7;" k="98" />
<hkern u1="&#x2018;" u2="&#xe6;" k="98" />
<hkern u1="&#x2018;" u2="&#xe5;" k="98" />
<hkern u1="&#x2018;" u2="&#xe4;" k="98" />
<hkern u1="&#x2018;" u2="&#xe3;" k="98" />
<hkern u1="&#x2018;" u2="&#xe2;" k="98" />
<hkern u1="&#x2018;" u2="&#xe1;" k="98" />
<hkern u1="&#x2018;" u2="&#xe0;" k="98" />
<hkern u1="&#x2018;" u2="&#xdd;" k="-43" />
<hkern u1="&#x2018;" u2="&#xc6;" k="180" />
<hkern u1="&#x2018;" u2="&#xc5;" k="180" />
<hkern u1="&#x2018;" u2="&#xc4;" k="180" />
<hkern u1="&#x2018;" u2="&#xc3;" k="180" />
<hkern u1="&#x2018;" u2="&#xc2;" k="180" />
<hkern u1="&#x2018;" u2="&#xc1;" k="180" />
<hkern u1="&#x2018;" u2="&#xc0;" k="180" />
<hkern u1="&#x2018;" u2="&#xbb;" k="41" />
<hkern u1="&#x2018;" u2="&#xb7;" k="41" />
<hkern u1="&#x2018;" u2="&#xab;" k="41" />
<hkern u1="&#x2018;" u2="q" k="98" />
<hkern u1="&#x2018;" u2="o" k="98" />
<hkern u1="&#x2018;" u2="e" k="98" />
<hkern u1="&#x2018;" u2="d" k="98" />
<hkern u1="&#x2018;" u2="c" k="98" />
<hkern u1="&#x2018;" u2="a" k="98" />
<hkern u1="&#x2018;" u2="\" k="-48" />
<hkern u1="&#x2018;" u2="Y" k="-43" />
<hkern u1="&#x2018;" u2="W" k="-34" />
<hkern u1="&#x2018;" u2="V" k="-48" />
<hkern u1="&#x2018;" u2="A" k="180" />
<hkern u1="&#x2018;" u2="&#x2f;" k="180" />
<hkern u1="&#x2018;" u2="&#x2e;" k="45" />
<hkern u1="&#x2018;" u2="&#x2d;" k="41" />
<hkern u1="&#x2018;" u2="&#x2c;" k="45" />
<hkern u1="&#x2018;" u2="&#x26;" k="180" />
<hkern u1="&#x2019;" u2="&#x2206;" k="180" />
<hkern u1="&#x2019;" u2="&#x203a;" k="41" />
<hkern u1="&#x2019;" u2="&#x2039;" k="41" />
<hkern u1="&#x2019;" u2="&#x2026;" k="45" />
<hkern u1="&#x2019;" u2="&#x2022;" k="41" />
<hkern u1="&#x2019;" u2="&#x201e;" k="45" />
<hkern u1="&#x2019;" u2="&#x201a;" k="45" />
<hkern u1="&#x2019;" u2="&#x2014;" k="41" />
<hkern u1="&#x2019;" u2="&#x2013;" k="41" />
<hkern u1="&#x2019;" u2="&#x178;" k="-43" />
<hkern u1="&#x2019;" u2="&#x153;" k="98" />
<hkern u1="&#x2019;" u2="&#x119;" k="98" />
<hkern u1="&#x2019;" u2="&#x107;" k="98" />
<hkern u1="&#x2019;" u2="&#x105;" k="98" />
<hkern u1="&#x2019;" u2="&#x104;" k="180" />
<hkern u1="&#x2019;" u2="&#xf8;" k="98" />
<hkern u1="&#x2019;" u2="&#xf6;" k="98" />
<hkern u1="&#x2019;" u2="&#xf5;" k="98" />
<hkern u1="&#x2019;" u2="&#xf4;" k="98" />
<hkern u1="&#x2019;" u2="&#xf3;" k="98" />
<hkern u1="&#x2019;" u2="&#xf2;" k="98" />
<hkern u1="&#x2019;" u2="&#xf0;" k="98" />
<hkern u1="&#x2019;" u2="&#xeb;" k="98" />
<hkern u1="&#x2019;" u2="&#xea;" k="98" />
<hkern u1="&#x2019;" u2="&#xe9;" k="98" />
<hkern u1="&#x2019;" u2="&#xe8;" k="98" />
<hkern u1="&#x2019;" u2="&#xe7;" k="98" />
<hkern u1="&#x2019;" u2="&#xe6;" k="98" />
<hkern u1="&#x2019;" u2="&#xe5;" k="98" />
<hkern u1="&#x2019;" u2="&#xe4;" k="98" />
<hkern u1="&#x2019;" u2="&#xe3;" k="98" />
<hkern u1="&#x2019;" u2="&#xe2;" k="98" />
<hkern u1="&#x2019;" u2="&#xe1;" k="98" />
<hkern u1="&#x2019;" u2="&#xe0;" k="98" />
<hkern u1="&#x2019;" u2="&#xdd;" k="-43" />
<hkern u1="&#x2019;" u2="&#xc6;" k="180" />
<hkern u1="&#x2019;" u2="&#xc5;" k="180" />
<hkern u1="&#x2019;" u2="&#xc4;" k="180" />
<hkern u1="&#x2019;" u2="&#xc3;" k="180" />
<hkern u1="&#x2019;" u2="&#xc2;" k="180" />
<hkern u1="&#x2019;" u2="&#xc1;" k="180" />
<hkern u1="&#x2019;" u2="&#xc0;" k="180" />
<hkern u1="&#x2019;" u2="&#xbb;" k="41" />
<hkern u1="&#x2019;" u2="&#xb7;" k="41" />
<hkern u1="&#x2019;" u2="&#xab;" k="41" />
<hkern u1="&#x2019;" u2="q" k="98" />
<hkern u1="&#x2019;" u2="o" k="98" />
<hkern u1="&#x2019;" u2="e" k="98" />
<hkern u1="&#x2019;" u2="d" k="98" />
<hkern u1="&#x2019;" u2="c" k="98" />
<hkern u1="&#x2019;" u2="a" k="98" />
<hkern u1="&#x2019;" u2="\" k="-48" />
<hkern u1="&#x2019;" u2="Y" k="-43" />
<hkern u1="&#x2019;" u2="W" k="-34" />
<hkern u1="&#x2019;" u2="V" k="-48" />
<hkern u1="&#x2019;" u2="A" k="180" />
<hkern u1="&#x2019;" u2="&#x2f;" k="180" />
<hkern u1="&#x2019;" u2="&#x2e;" k="45" />
<hkern u1="&#x2019;" u2="&#x2d;" k="41" />
<hkern u1="&#x2019;" u2="&#x2c;" k="45" />
<hkern u1="&#x2019;" u2="&#x26;" k="180" />
<hkern u1="&#x201a;" u2="&#x203a;" k="214" />
<hkern u1="&#x201a;" u2="&#x2039;" k="214" />
<hkern u1="&#x201a;" u2="&#x2022;" k="214" />
<hkern u1="&#x201a;" u2="&#x201d;" k="58" />
<hkern u1="&#x201a;" u2="&#x201c;" k="58" />
<hkern u1="&#x201a;" u2="&#x2019;" k="58" />
<hkern u1="&#x201a;" u2="&#x2018;" k="58" />
<hkern u1="&#x201a;" u2="&#x2014;" k="214" />
<hkern u1="&#x201a;" u2="&#x2013;" k="214" />
<hkern u1="&#x201a;" u2="&#x178;" k="237" />
<hkern u1="&#x201a;" u2="&#x152;" k="53" />
<hkern u1="&#x201a;" u2="&#x106;" k="53" />
<hkern u1="&#x201a;" u2="&#xff;" k="155" />
<hkern u1="&#x201a;" u2="&#xfd;" k="155" />
<hkern u1="&#x201a;" u2="&#xdd;" k="237" />
<hkern u1="&#x201a;" u2="&#xd8;" k="53" />
<hkern u1="&#x201a;" u2="&#xd6;" k="53" />
<hkern u1="&#x201a;" u2="&#xd5;" k="53" />
<hkern u1="&#x201a;" u2="&#xd4;" k="53" />
<hkern u1="&#x201a;" u2="&#xd3;" k="53" />
<hkern u1="&#x201a;" u2="&#xd2;" k="53" />
<hkern u1="&#x201a;" u2="&#xc7;" k="53" />
<hkern u1="&#x201a;" u2="&#xbb;" k="214" />
<hkern u1="&#x201a;" u2="&#xba;" k="58" />
<hkern u1="&#x201a;" u2="&#xb7;" k="214" />
<hkern u1="&#x201a;" u2="&#xb0;" k="58" />
<hkern u1="&#x201a;" u2="&#xae;" k="53" />
<hkern u1="&#x201a;" u2="&#xab;" k="214" />
<hkern u1="&#x201a;" u2="&#xaa;" k="58" />
<hkern u1="&#x201a;" u2="&#xa9;" k="53" />
<hkern u1="&#x201a;" u2="y" k="151" />
<hkern u1="&#x201a;" u2="w" k="89" />
<hkern u1="&#x201a;" u2="v" k="155" />
<hkern u1="&#x201a;" u2="\" k="232" />
<hkern u1="&#x201a;" u2="Y" k="237" />
<hkern u1="&#x201a;" u2="W" k="122" />
<hkern u1="&#x201a;" u2="V" k="232" />
<hkern u1="&#x201a;" u2="T" k="218" />
<hkern u1="&#x201a;" u2="Q" k="53" />
<hkern u1="&#x201a;" u2="O" k="53" />
<hkern u1="&#x201a;" u2="G" k="53" />
<hkern u1="&#x201a;" u2="C" k="53" />
<hkern u1="&#x201a;" u2="&#x40;" k="53" />
<hkern u1="&#x201a;" u2="&#x2d;" k="214" />
<hkern u1="&#x201a;" u2="&#x2a;" k="58" />
<hkern u1="&#x201a;" u2="&#x27;" k="58" />
<hkern u1="&#x201a;" u2="&#x22;" k="58" />
<hkern u1="&#x201c;" u2="&#x2206;" k="180" />
<hkern u1="&#x201c;" u2="&#x203a;" k="41" />
<hkern u1="&#x201c;" u2="&#x2039;" k="41" />
<hkern u1="&#x201c;" u2="&#x2026;" k="45" />
<hkern u1="&#x201c;" u2="&#x2022;" k="41" />
<hkern u1="&#x201c;" u2="&#x201e;" k="45" />
<hkern u1="&#x201c;" u2="&#x201a;" k="45" />
<hkern u1="&#x201c;" u2="&#x2014;" k="41" />
<hkern u1="&#x201c;" u2="&#x2013;" k="41" />
<hkern u1="&#x201c;" u2="&#x178;" k="-43" />
<hkern u1="&#x201c;" u2="&#x153;" k="98" />
<hkern u1="&#x201c;" u2="&#x119;" k="98" />
<hkern u1="&#x201c;" u2="&#x107;" k="98" />
<hkern u1="&#x201c;" u2="&#x105;" k="98" />
<hkern u1="&#x201c;" u2="&#x104;" k="180" />
<hkern u1="&#x201c;" u2="&#xf8;" k="98" />
<hkern u1="&#x201c;" u2="&#xf6;" k="98" />
<hkern u1="&#x201c;" u2="&#xf5;" k="98" />
<hkern u1="&#x201c;" u2="&#xf4;" k="98" />
<hkern u1="&#x201c;" u2="&#xf3;" k="98" />
<hkern u1="&#x201c;" u2="&#xf2;" k="98" />
<hkern u1="&#x201c;" u2="&#xf0;" k="98" />
<hkern u1="&#x201c;" u2="&#xeb;" k="98" />
<hkern u1="&#x201c;" u2="&#xea;" k="98" />
<hkern u1="&#x201c;" u2="&#xe9;" k="98" />
<hkern u1="&#x201c;" u2="&#xe8;" k="98" />
<hkern u1="&#x201c;" u2="&#xe7;" k="98" />
<hkern u1="&#x201c;" u2="&#xe6;" k="98" />
<hkern u1="&#x201c;" u2="&#xe5;" k="98" />
<hkern u1="&#x201c;" u2="&#xe4;" k="98" />
<hkern u1="&#x201c;" u2="&#xe3;" k="98" />
<hkern u1="&#x201c;" u2="&#xe2;" k="98" />
<hkern u1="&#x201c;" u2="&#xe1;" k="98" />
<hkern u1="&#x201c;" u2="&#xe0;" k="98" />
<hkern u1="&#x201c;" u2="&#xdd;" k="-43" />
<hkern u1="&#x201c;" u2="&#xc6;" k="180" />
<hkern u1="&#x201c;" u2="&#xc5;" k="180" />
<hkern u1="&#x201c;" u2="&#xc4;" k="180" />
<hkern u1="&#x201c;" u2="&#xc3;" k="180" />
<hkern u1="&#x201c;" u2="&#xc2;" k="180" />
<hkern u1="&#x201c;" u2="&#xc1;" k="180" />
<hkern u1="&#x201c;" u2="&#xc0;" k="180" />
<hkern u1="&#x201c;" u2="&#xbb;" k="41" />
<hkern u1="&#x201c;" u2="&#xb7;" k="41" />
<hkern u1="&#x201c;" u2="&#xab;" k="41" />
<hkern u1="&#x201c;" u2="q" k="98" />
<hkern u1="&#x201c;" u2="o" k="98" />
<hkern u1="&#x201c;" u2="e" k="98" />
<hkern u1="&#x201c;" u2="d" k="98" />
<hkern u1="&#x201c;" u2="c" k="98" />
<hkern u1="&#x201c;" u2="a" k="98" />
<hkern u1="&#x201c;" u2="\" k="-48" />
<hkern u1="&#x201c;" u2="Y" k="-43" />
<hkern u1="&#x201c;" u2="W" k="-34" />
<hkern u1="&#x201c;" u2="V" k="-48" />
<hkern u1="&#x201c;" u2="A" k="180" />
<hkern u1="&#x201c;" u2="&#x2f;" k="180" />
<hkern u1="&#x201c;" u2="&#x2e;" k="45" />
<hkern u1="&#x201c;" u2="&#x2d;" k="41" />
<hkern u1="&#x201c;" u2="&#x2c;" k="45" />
<hkern u1="&#x201c;" u2="&#x26;" k="180" />
<hkern u1="&#x201d;" u2="&#x2206;" k="180" />
<hkern u1="&#x201d;" u2="&#x203a;" k="41" />
<hkern u1="&#x201d;" u2="&#x2039;" k="41" />
<hkern u1="&#x201d;" u2="&#x2026;" k="45" />
<hkern u1="&#x201d;" u2="&#x2022;" k="41" />
<hkern u1="&#x201d;" u2="&#x201e;" k="45" />
<hkern u1="&#x201d;" u2="&#x201a;" k="45" />
<hkern u1="&#x201d;" u2="&#x2014;" k="41" />
<hkern u1="&#x201d;" u2="&#x2013;" k="41" />
<hkern u1="&#x201d;" u2="&#x178;" k="-43" />
<hkern u1="&#x201d;" u2="&#x153;" k="98" />
<hkern u1="&#x201d;" u2="&#x119;" k="98" />
<hkern u1="&#x201d;" u2="&#x107;" k="98" />
<hkern u1="&#x201d;" u2="&#x105;" k="98" />
<hkern u1="&#x201d;" u2="&#x104;" k="180" />
<hkern u1="&#x201d;" u2="&#xf8;" k="98" />
<hkern u1="&#x201d;" u2="&#xf6;" k="98" />
<hkern u1="&#x201d;" u2="&#xf5;" k="98" />
<hkern u1="&#x201d;" u2="&#xf4;" k="98" />
<hkern u1="&#x201d;" u2="&#xf3;" k="98" />
<hkern u1="&#x201d;" u2="&#xf2;" k="98" />
<hkern u1="&#x201d;" u2="&#xf0;" k="98" />
<hkern u1="&#x201d;" u2="&#xeb;" k="98" />
<hkern u1="&#x201d;" u2="&#xea;" k="98" />
<hkern u1="&#x201d;" u2="&#xe9;" k="98" />
<hkern u1="&#x201d;" u2="&#xe8;" k="98" />
<hkern u1="&#x201d;" u2="&#xe7;" k="98" />
<hkern u1="&#x201d;" u2="&#xe6;" k="98" />
<hkern u1="&#x201d;" u2="&#xe5;" k="98" />
<hkern u1="&#x201d;" u2="&#xe4;" k="98" />
<hkern u1="&#x201d;" u2="&#xe3;" k="98" />
<hkern u1="&#x201d;" u2="&#xe2;" k="98" />
<hkern u1="&#x201d;" u2="&#xe1;" k="98" />
<hkern u1="&#x201d;" u2="&#xe0;" k="98" />
<hkern u1="&#x201d;" u2="&#xdd;" k="-43" />
<hkern u1="&#x201d;" u2="&#xc6;" k="180" />
<hkern u1="&#x201d;" u2="&#xc5;" k="180" />
<hkern u1="&#x201d;" u2="&#xc4;" k="180" />
<hkern u1="&#x201d;" u2="&#xc3;" k="180" />
<hkern u1="&#x201d;" u2="&#xc2;" k="180" />
<hkern u1="&#x201d;" u2="&#xc1;" k="180" />
<hkern u1="&#x201d;" u2="&#xc0;" k="180" />
<hkern u1="&#x201d;" u2="&#xbb;" k="41" />
<hkern u1="&#x201d;" u2="&#xb7;" k="41" />
<hkern u1="&#x201d;" u2="&#xab;" k="41" />
<hkern u1="&#x201d;" u2="q" k="98" />
<hkern u1="&#x201d;" u2="o" k="98" />
<hkern u1="&#x201d;" u2="e" k="98" />
<hkern u1="&#x201d;" u2="d" k="98" />
<hkern u1="&#x201d;" u2="c" k="98" />
<hkern u1="&#x201d;" u2="a" k="98" />
<hkern u1="&#x201d;" u2="\" k="-48" />
<hkern u1="&#x201d;" u2="Y" k="-43" />
<hkern u1="&#x201d;" u2="W" k="-34" />
<hkern u1="&#x201d;" u2="V" k="-48" />
<hkern u1="&#x201d;" u2="A" k="180" />
<hkern u1="&#x201d;" u2="&#x2f;" k="180" />
<hkern u1="&#x201d;" u2="&#x2e;" k="45" />
<hkern u1="&#x201d;" u2="&#x2d;" k="41" />
<hkern u1="&#x201d;" u2="&#x2c;" k="45" />
<hkern u1="&#x201d;" u2="&#x26;" k="180" />
<hkern u1="&#x201e;" u2="&#x203a;" k="214" />
<hkern u1="&#x201e;" u2="&#x2039;" k="214" />
<hkern u1="&#x201e;" u2="&#x2022;" k="214" />
<hkern u1="&#x201e;" u2="&#x201d;" k="58" />
<hkern u1="&#x201e;" u2="&#x201c;" k="58" />
<hkern u1="&#x201e;" u2="&#x2019;" k="58" />
<hkern u1="&#x201e;" u2="&#x2018;" k="58" />
<hkern u1="&#x201e;" u2="&#x2014;" k="214" />
<hkern u1="&#x201e;" u2="&#x2013;" k="214" />
<hkern u1="&#x201e;" u2="&#x178;" k="237" />
<hkern u1="&#x201e;" u2="&#x152;" k="53" />
<hkern u1="&#x201e;" u2="&#x106;" k="53" />
<hkern u1="&#x201e;" u2="&#xff;" k="155" />
<hkern u1="&#x201e;" u2="&#xfd;" k="155" />
<hkern u1="&#x201e;" u2="&#xdd;" k="237" />
<hkern u1="&#x201e;" u2="&#xd8;" k="53" />
<hkern u1="&#x201e;" u2="&#xd6;" k="53" />
<hkern u1="&#x201e;" u2="&#xd5;" k="53" />
<hkern u1="&#x201e;" u2="&#xd4;" k="53" />
<hkern u1="&#x201e;" u2="&#xd3;" k="53" />
<hkern u1="&#x201e;" u2="&#xd2;" k="53" />
<hkern u1="&#x201e;" u2="&#xc7;" k="53" />
<hkern u1="&#x201e;" u2="&#xbb;" k="214" />
<hkern u1="&#x201e;" u2="&#xba;" k="58" />
<hkern u1="&#x201e;" u2="&#xb7;" k="214" />
<hkern u1="&#x201e;" u2="&#xb0;" k="58" />
<hkern u1="&#x201e;" u2="&#xae;" k="53" />
<hkern u1="&#x201e;" u2="&#xab;" k="214" />
<hkern u1="&#x201e;" u2="&#xaa;" k="58" />
<hkern u1="&#x201e;" u2="&#xa9;" k="53" />
<hkern u1="&#x201e;" u2="y" k="151" />
<hkern u1="&#x201e;" u2="w" k="89" />
<hkern u1="&#x201e;" u2="v" k="155" />
<hkern u1="&#x201e;" u2="\" k="232" />
<hkern u1="&#x201e;" u2="Y" k="237" />
<hkern u1="&#x201e;" u2="W" k="122" />
<hkern u1="&#x201e;" u2="V" k="232" />
<hkern u1="&#x201e;" u2="T" k="218" />
<hkern u1="&#x201e;" u2="Q" k="53" />
<hkern u1="&#x201e;" u2="O" k="53" />
<hkern u1="&#x201e;" u2="G" k="53" />
<hkern u1="&#x201e;" u2="C" k="53" />
<hkern u1="&#x201e;" u2="&#x40;" k="53" />
<hkern u1="&#x201e;" u2="&#x2d;" k="214" />
<hkern u1="&#x201e;" u2="&#x2a;" k="58" />
<hkern u1="&#x201e;" u2="&#x27;" k="58" />
<hkern u1="&#x201e;" u2="&#x22;" k="58" />
<hkern u1="&#x2022;" u2="&#x2206;" k="69" />
<hkern u1="&#x2022;" u2="&#x2026;" k="175" />
<hkern u1="&#x2022;" u2="&#x201e;" k="175" />
<hkern u1="&#x2022;" u2="&#x201d;" k="41" />
<hkern u1="&#x2022;" u2="&#x201c;" k="41" />
<hkern u1="&#x2022;" u2="&#x201a;" k="175" />
<hkern u1="&#x2022;" u2="&#x2019;" k="41" />
<hkern u1="&#x2022;" u2="&#x2018;" k="41" />
<hkern u1="&#x2022;" u2="&#x17d;" k="59" />
<hkern u1="&#x2022;" u2="&#x17b;" k="59" />
<hkern u1="&#x2022;" u2="&#x179;" k="59" />
<hkern u1="&#x2022;" u2="&#x178;" k="212" />
<hkern u1="&#x2022;" u2="&#x104;" k="69" />
<hkern u1="&#x2022;" u2="&#xdd;" k="212" />
<hkern u1="&#x2022;" u2="&#xc6;" k="69" />
<hkern u1="&#x2022;" u2="&#xc5;" k="69" />
<hkern u1="&#x2022;" u2="&#xc4;" k="69" />
<hkern u1="&#x2022;" u2="&#xc3;" k="69" />
<hkern u1="&#x2022;" u2="&#xc2;" k="69" />
<hkern u1="&#x2022;" u2="&#xc1;" k="69" />
<hkern u1="&#x2022;" u2="&#xc0;" k="69" />
<hkern u1="&#x2022;" u2="&#xba;" k="41" />
<hkern u1="&#x2022;" u2="&#xb0;" k="41" />
<hkern u1="&#x2022;" u2="&#xaa;" k="41" />
<hkern u1="&#x2022;" u2="\" k="134" />
<hkern u1="&#x2022;" u2="Z" k="59" />
<hkern u1="&#x2022;" u2="Y" k="212" />
<hkern u1="&#x2022;" u2="X" k="72" />
<hkern u1="&#x2022;" u2="V" k="134" />
<hkern u1="&#x2022;" u2="T" k="201" />
<hkern u1="&#x2022;" u2="A" k="69" />
<hkern u1="&#x2022;" u2="&#x2f;" k="69" />
<hkern u1="&#x2022;" u2="&#x2e;" k="175" />
<hkern u1="&#x2022;" u2="&#x2c;" k="175" />
<hkern u1="&#x2022;" u2="&#x2a;" k="41" />
<hkern u1="&#x2022;" u2="&#x27;" k="41" />
<hkern u1="&#x2022;" u2="&#x26;" k="69" />
<hkern u1="&#x2022;" u2="&#x22;" k="41" />
<hkern u1="&#x2026;" u2="&#x203a;" k="214" />
<hkern u1="&#x2026;" u2="&#x2039;" k="214" />
<hkern u1="&#x2026;" u2="&#x2022;" k="214" />
<hkern u1="&#x2026;" u2="&#x201d;" k="58" />
<hkern u1="&#x2026;" u2="&#x201c;" k="58" />
<hkern u1="&#x2026;" u2="&#x2019;" k="58" />
<hkern u1="&#x2026;" u2="&#x2018;" k="58" />
<hkern u1="&#x2026;" u2="&#x2014;" k="214" />
<hkern u1="&#x2026;" u2="&#x2013;" k="214" />
<hkern u1="&#x2026;" u2="&#x178;" k="237" />
<hkern u1="&#x2026;" u2="&#x152;" k="53" />
<hkern u1="&#x2026;" u2="&#x106;" k="53" />
<hkern u1="&#x2026;" u2="&#xff;" k="155" />
<hkern u1="&#x2026;" u2="&#xfd;" k="155" />
<hkern u1="&#x2026;" u2="&#xdd;" k="237" />
<hkern u1="&#x2026;" u2="&#xd8;" k="53" />
<hkern u1="&#x2026;" u2="&#xd6;" k="53" />
<hkern u1="&#x2026;" u2="&#xd5;" k="53" />
<hkern u1="&#x2026;" u2="&#xd4;" k="53" />
<hkern u1="&#x2026;" u2="&#xd3;" k="53" />
<hkern u1="&#x2026;" u2="&#xd2;" k="53" />
<hkern u1="&#x2026;" u2="&#xc7;" k="53" />
<hkern u1="&#x2026;" u2="&#xbb;" k="214" />
<hkern u1="&#x2026;" u2="&#xba;" k="58" />
<hkern u1="&#x2026;" u2="&#xb7;" k="214" />
<hkern u1="&#x2026;" u2="&#xb0;" k="58" />
<hkern u1="&#x2026;" u2="&#xae;" k="53" />
<hkern u1="&#x2026;" u2="&#xab;" k="214" />
<hkern u1="&#x2026;" u2="&#xaa;" k="58" />
<hkern u1="&#x2026;" u2="&#xa9;" k="53" />
<hkern u1="&#x2026;" u2="y" k="151" />
<hkern u1="&#x2026;" u2="w" k="89" />
<hkern u1="&#x2026;" u2="v" k="155" />
<hkern u1="&#x2026;" u2="\" k="232" />
<hkern u1="&#x2026;" u2="Y" k="237" />
<hkern u1="&#x2026;" u2="W" k="122" />
<hkern u1="&#x2026;" u2="V" k="232" />
<hkern u1="&#x2026;" u2="T" k="218" />
<hkern u1="&#x2026;" u2="Q" k="53" />
<hkern u1="&#x2026;" u2="O" k="53" />
<hkern u1="&#x2026;" u2="G" k="53" />
<hkern u1="&#x2026;" u2="C" k="53" />
<hkern u1="&#x2026;" u2="&#x40;" k="53" />
<hkern u1="&#x2026;" u2="&#x2d;" k="214" />
<hkern u1="&#x2026;" u2="&#x2a;" k="58" />
<hkern u1="&#x2026;" u2="&#x27;" k="58" />
<hkern u1="&#x2026;" u2="&#x22;" k="58" />
<hkern u1="&#x2039;" u2="&#x2206;" k="69" />
<hkern u1="&#x2039;" u2="&#x2026;" k="175" />
<hkern u1="&#x2039;" u2="&#x201e;" k="175" />
<hkern u1="&#x2039;" u2="&#x201d;" k="41" />
<hkern u1="&#x2039;" u2="&#x201c;" k="41" />
<hkern u1="&#x2039;" u2="&#x201a;" k="175" />
<hkern u1="&#x2039;" u2="&#x2019;" k="41" />
<hkern u1="&#x2039;" u2="&#x2018;" k="41" />
<hkern u1="&#x2039;" u2="&#x17d;" k="59" />
<hkern u1="&#x2039;" u2="&#x17b;" k="59" />
<hkern u1="&#x2039;" u2="&#x179;" k="59" />
<hkern u1="&#x2039;" u2="&#x178;" k="212" />
<hkern u1="&#x2039;" u2="&#x104;" k="69" />
<hkern u1="&#x2039;" u2="&#xdd;" k="212" />
<hkern u1="&#x2039;" u2="&#xc6;" k="69" />
<hkern u1="&#x2039;" u2="&#xc5;" k="69" />
<hkern u1="&#x2039;" u2="&#xc4;" k="69" />
<hkern u1="&#x2039;" u2="&#xc3;" k="69" />
<hkern u1="&#x2039;" u2="&#xc2;" k="69" />
<hkern u1="&#x2039;" u2="&#xc1;" k="69" />
<hkern u1="&#x2039;" u2="&#xc0;" k="69" />
<hkern u1="&#x2039;" u2="&#xba;" k="41" />
<hkern u1="&#x2039;" u2="&#xb0;" k="41" />
<hkern u1="&#x2039;" u2="&#xaa;" k="41" />
<hkern u1="&#x2039;" u2="\" k="134" />
<hkern u1="&#x2039;" u2="Z" k="59" />
<hkern u1="&#x2039;" u2="Y" k="212" />
<hkern u1="&#x2039;" u2="X" k="72" />
<hkern u1="&#x2039;" u2="V" k="134" />
<hkern u1="&#x2039;" u2="T" k="201" />
<hkern u1="&#x2039;" u2="A" k="69" />
<hkern u1="&#x2039;" u2="&#x2f;" k="69" />
<hkern u1="&#x2039;" u2="&#x2e;" k="175" />
<hkern u1="&#x2039;" u2="&#x2c;" k="175" />
<hkern u1="&#x2039;" u2="&#x2a;" k="41" />
<hkern u1="&#x2039;" u2="&#x27;" k="41" />
<hkern u1="&#x2039;" u2="&#x26;" k="69" />
<hkern u1="&#x2039;" u2="&#x22;" k="41" />
<hkern u1="&#x203a;" u2="&#x2206;" k="69" />
<hkern u1="&#x203a;" u2="&#x2026;" k="175" />
<hkern u1="&#x203a;" u2="&#x201e;" k="175" />
<hkern u1="&#x203a;" u2="&#x201d;" k="41" />
<hkern u1="&#x203a;" u2="&#x201c;" k="41" />
<hkern u1="&#x203a;" u2="&#x201a;" k="175" />
<hkern u1="&#x203a;" u2="&#x2019;" k="41" />
<hkern u1="&#x203a;" u2="&#x2018;" k="41" />
<hkern u1="&#x203a;" u2="&#x17d;" k="59" />
<hkern u1="&#x203a;" u2="&#x17b;" k="59" />
<hkern u1="&#x203a;" u2="&#x179;" k="59" />
<hkern u1="&#x203a;" u2="&#x178;" k="212" />
<hkern u1="&#x203a;" u2="&#x104;" k="69" />
<hkern u1="&#x203a;" u2="&#xdd;" k="212" />
<hkern u1="&#x203a;" u2="&#xc6;" k="69" />
<hkern u1="&#x203a;" u2="&#xc5;" k="69" />
<hkern u1="&#x203a;" u2="&#xc4;" k="69" />
<hkern u1="&#x203a;" u2="&#xc3;" k="69" />
<hkern u1="&#x203a;" u2="&#xc2;" k="69" />
<hkern u1="&#x203a;" u2="&#xc1;" k="69" />
<hkern u1="&#x203a;" u2="&#xc0;" k="69" />
<hkern u1="&#x203a;" u2="&#xba;" k="41" />
<hkern u1="&#x203a;" u2="&#xb0;" k="41" />
<hkern u1="&#x203a;" u2="&#xaa;" k="41" />
<hkern u1="&#x203a;" u2="\" k="134" />
<hkern u1="&#x203a;" u2="Z" k="59" />
<hkern u1="&#x203a;" u2="Y" k="212" />
<hkern u1="&#x203a;" u2="X" k="72" />
<hkern u1="&#x203a;" u2="V" k="134" />
<hkern u1="&#x203a;" u2="T" k="201" />
<hkern u1="&#x203a;" u2="A" k="69" />
<hkern u1="&#x203a;" u2="&#x2f;" k="69" />
<hkern u1="&#x203a;" u2="&#x2e;" k="175" />
<hkern u1="&#x203a;" u2="&#x2c;" k="175" />
<hkern u1="&#x203a;" u2="&#x2a;" k="41" />
<hkern u1="&#x203a;" u2="&#x27;" k="41" />
<hkern u1="&#x203a;" u2="&#x26;" k="69" />
<hkern u1="&#x203a;" u2="&#x22;" k="41" />
<hkern u1="&#x2122;" u2="&#x2206;" k="180" />
<hkern u1="&#x2122;" u2="&#x203a;" k="41" />
<hkern u1="&#x2122;" u2="&#x2039;" k="41" />
<hkern u1="&#x2122;" u2="&#x2026;" k="45" />
<hkern u1="&#x2122;" u2="&#x2022;" k="41" />
<hkern u1="&#x2122;" u2="&#x201e;" k="45" />
<hkern u1="&#x2122;" u2="&#x201a;" k="45" />
<hkern u1="&#x2122;" u2="&#x2014;" k="41" />
<hkern u1="&#x2122;" u2="&#x2013;" k="41" />
<hkern u1="&#x2122;" u2="&#x178;" k="-43" />
<hkern u1="&#x2122;" u2="&#x153;" k="98" />
<hkern u1="&#x2122;" u2="&#x119;" k="98" />
<hkern u1="&#x2122;" u2="&#x107;" k="98" />
<hkern u1="&#x2122;" u2="&#x105;" k="98" />
<hkern u1="&#x2122;" u2="&#x104;" k="180" />
<hkern u1="&#x2122;" u2="&#xf8;" k="98" />
<hkern u1="&#x2122;" u2="&#xf6;" k="98" />
<hkern u1="&#x2122;" u2="&#xf5;" k="98" />
<hkern u1="&#x2122;" u2="&#xf4;" k="98" />
<hkern u1="&#x2122;" u2="&#xf3;" k="98" />
<hkern u1="&#x2122;" u2="&#xf2;" k="98" />
<hkern u1="&#x2122;" u2="&#xf0;" k="98" />
<hkern u1="&#x2122;" u2="&#xeb;" k="98" />
<hkern u1="&#x2122;" u2="&#xea;" k="98" />
<hkern u1="&#x2122;" u2="&#xe9;" k="98" />
<hkern u1="&#x2122;" u2="&#xe8;" k="98" />
<hkern u1="&#x2122;" u2="&#xe7;" k="98" />
<hkern u1="&#x2122;" u2="&#xe6;" k="98" />
<hkern u1="&#x2122;" u2="&#xe5;" k="98" />
<hkern u1="&#x2122;" u2="&#xe4;" k="98" />
<hkern u1="&#x2122;" u2="&#xe3;" k="98" />
<hkern u1="&#x2122;" u2="&#xe2;" k="98" />
<hkern u1="&#x2122;" u2="&#xe1;" k="98" />
<hkern u1="&#x2122;" u2="&#xe0;" k="98" />
<hkern u1="&#x2122;" u2="&#xdd;" k="-43" />
<hkern u1="&#x2122;" u2="&#xc6;" k="180" />
<hkern u1="&#x2122;" u2="&#xc5;" k="180" />
<hkern u1="&#x2122;" u2="&#xc4;" k="180" />
<hkern u1="&#x2122;" u2="&#xc3;" k="180" />
<hkern u1="&#x2122;" u2="&#xc2;" k="180" />
<hkern u1="&#x2122;" u2="&#xc1;" k="180" />
<hkern u1="&#x2122;" u2="&#xc0;" k="180" />
<hkern u1="&#x2122;" u2="&#xbb;" k="41" />
<hkern u1="&#x2122;" u2="&#xb7;" k="41" />
<hkern u1="&#x2122;" u2="&#xab;" k="41" />
<hkern u1="&#x2122;" u2="q" k="98" />
<hkern u1="&#x2122;" u2="o" k="98" />
<hkern u1="&#x2122;" u2="e" k="98" />
<hkern u1="&#x2122;" u2="d" k="98" />
<hkern u1="&#x2122;" u2="c" k="98" />
<hkern u1="&#x2122;" u2="a" k="98" />
<hkern u1="&#x2122;" u2="\" k="-48" />
<hkern u1="&#x2122;" u2="Y" k="-43" />
<hkern u1="&#x2122;" u2="W" k="-34" />
<hkern u1="&#x2122;" u2="V" k="-48" />
<hkern u1="&#x2122;" u2="A" k="180" />
<hkern u1="&#x2122;" u2="&#x2f;" k="180" />
<hkern u1="&#x2122;" u2="&#x2e;" k="45" />
<hkern u1="&#x2122;" u2="&#x2d;" k="41" />
<hkern u1="&#x2122;" u2="&#x2c;" k="45" />
<hkern u1="&#x2122;" u2="&#x26;" k="180" />
<hkern u1="&#x2206;" u2="&#x203a;" k="69" />
<hkern u1="&#x2206;" u2="&#x2039;" k="69" />
<hkern u1="&#x2206;" u2="&#x2022;" k="69" />
<hkern u1="&#x2206;" u2="&#x201d;" k="196" />
<hkern u1="&#x2206;" u2="&#x201c;" k="196" />
<hkern u1="&#x2206;" u2="&#x2019;" k="196" />
<hkern u1="&#x2206;" u2="&#x2018;" k="196" />
<hkern u1="&#x2206;" u2="&#x2014;" k="69" />
<hkern u1="&#x2206;" u2="&#x2013;" k="69" />
<hkern u1="&#x2206;" u2="&#x178;" k="171" />
<hkern u1="&#x2206;" u2="&#x152;" k="60" />
<hkern u1="&#x2206;" u2="&#x106;" k="60" />
<hkern u1="&#x2206;" u2="&#xff;" k="60" />
<hkern u1="&#x2206;" u2="&#xfd;" k="60" />
<hkern u1="&#x2206;" u2="&#xdd;" k="171" />
<hkern u1="&#x2206;" u2="&#xdc;" k="41" />
<hkern u1="&#x2206;" u2="&#xdb;" k="41" />
<hkern u1="&#x2206;" u2="&#xda;" k="41" />
<hkern u1="&#x2206;" u2="&#xd9;" k="41" />
<hkern u1="&#x2206;" u2="&#xd8;" k="60" />
<hkern u1="&#x2206;" u2="&#xd6;" k="60" />
<hkern u1="&#x2206;" u2="&#xd5;" k="60" />
<hkern u1="&#x2206;" u2="&#xd4;" k="60" />
<hkern u1="&#x2206;" u2="&#xd3;" k="60" />
<hkern u1="&#x2206;" u2="&#xd2;" k="60" />
<hkern u1="&#x2206;" u2="&#xc7;" k="60" />
<hkern u1="&#x2206;" u2="&#xbb;" k="69" />
<hkern u1="&#x2206;" u2="&#xba;" k="196" />
<hkern u1="&#x2206;" u2="&#xb7;" k="69" />
<hkern u1="&#x2206;" u2="&#xb0;" k="196" />
<hkern u1="&#x2206;" u2="&#xae;" k="60" />
<hkern u1="&#x2206;" u2="&#xab;" k="69" />
<hkern u1="&#x2206;" u2="&#xaa;" k="196" />
<hkern u1="&#x2206;" u2="&#xa9;" k="60" />
<hkern u1="&#x2206;" u2="y" k="60" />
<hkern u1="&#x2206;" u2="w" k="48" />
<hkern u1="&#x2206;" u2="v" k="60" />
<hkern u1="&#x2206;" u2="t" k="71" />
<hkern u1="&#x2206;" u2="\" k="141" />
<hkern u1="&#x2206;" u2="Y" k="171" />
<hkern u1="&#x2206;" u2="W" k="104" />
<hkern u1="&#x2206;" u2="V" k="141" />
<hkern u1="&#x2206;" u2="U" k="41" />
<hkern u1="&#x2206;" u2="T" k="151" />
<hkern u1="&#x2206;" u2="Q" k="60" />
<hkern u1="&#x2206;" u2="O" k="60" />
<hkern u1="&#x2206;" u2="J" k="-73" />
<hkern u1="&#x2206;" u2="G" k="60" />
<hkern u1="&#x2206;" u2="C" k="60" />
<hkern u1="&#x2206;" u2="&#x40;" k="60" />
<hkern u1="&#x2206;" u2="&#x2d;" k="69" />
<hkern u1="&#x2206;" u2="&#x2a;" k="196" />
<hkern u1="&#x2206;" u2="&#x27;" k="196" />
<hkern u1="&#x2206;" u2="&#x22;" k="196" />
</font>
</defs></svg> 