-- 维护 daily_statistics 表的触发器
-- 这些触发器用于实时更新统计数据

-- 当充值订单状态变为已支付时，更新 daily_statistics 表的充值相关字段
DELIMITER //

DROP TRIGGER IF EXISTS trg_after_deposit_order_paid_statistics_cn;

CREATE TRIGGER trg_after_deposit_order_paid_statistics_cn
AFTER UPDATE ON deposit_orders
FOR EACH ROW
BEGIN
    DECLARE today_date DATE;
    DECLARE is_first_deposit BOOLEAN;
    DECLARE agent_id_val INT UNSIGNED;

    -- 只在支付状态从非已支付变为已支付时执行
    IF NEW.payment_status = 1 AND OLD.payment_status != 1 AND NEW.player_id IS NOT NULL THEN
        SET today_date = CURDATE();
        SET agent_id_val = NEW.agent_id;

        -- 检查是否为首充
        SELECT COUNT(*) = 0 INTO is_first_deposit
        FROM deposit_orders
        WHERE deposit_orders.player_id = NEW.player_id
          AND deposit_orders.payment_status = 1
          AND deposit_orders.id != NEW.id;

        -- 更新或插入统计记录
        INSERT INTO daily_statistics
            (stat_date, agent_id, total_deposit, total_deposit_count,
             first_deposit_amount, repeat_deposit_amount,
             first_deposit_players, repeat_deposit_players,
             updated_at)
        VALUES
            (today_date, agent_id_val, NEW.amount, 1,
             IF(is_first_deposit, NEW.amount, 0), IF(is_first_deposit, 0, NEW.amount),
             IF(is_first_deposit, 1, 0), IF(is_first_deposit, 0, 1),
             NOW())
        ON DUPLICATE KEY UPDATE
            total_deposit = total_deposit + NEW.amount,
            total_deposit_count = total_deposit_count + 1,
            first_deposit_amount = first_deposit_amount + IF(is_first_deposit, NEW.amount, 0),
            repeat_deposit_amount = repeat_deposit_amount + IF(is_first_deposit, 0, NEW.amount),
            first_deposit_players = first_deposit_players + IF(is_first_deposit, 1, 0),
            repeat_deposit_players = repeat_deposit_players + IF(is_first_deposit, 0, 1),
            updated_at = NOW();
    END IF;
END //

DELIMITER ;

-- 当提现订单状态变为订单完成时，更新 daily_statistics 表的提现相关字段
DELIMITER //

DROP TRIGGER IF EXISTS trg_after_withdraw_order_completed_statistics_cn;

CREATE TRIGGER trg_after_withdraw_order_completed_statistics_cn
AFTER UPDATE ON withdraw_orders
FOR EACH ROW
BEGIN
    DECLARE today_date DATE;
    DECLARE agent_id_val INT UNSIGNED;

    -- 只在审核状态从非订单完成变为订单完成时执行
    IF NEW.audit_status = 4 AND OLD.audit_status != 4 AND NEW.player_id IS NOT NULL THEN
        SET today_date = CURDATE();

        -- 获取玩家的业务员ID
        SELECT agent_id INTO agent_id_val
        FROM players
        WHERE players.id = NEW.player_id
        LIMIT 1;

        -- 更新或插入统计记录
        INSERT INTO daily_statistics
            (stat_date, agent_id, total_withdraw, total_withdraw_count, withdraw_players, updated_at)
        VALUES
            (today_date, agent_id_val, NEW.amount, 1, 1, NOW())
        ON DUPLICATE KEY UPDATE
            total_withdraw = total_withdraw + NEW.amount,
            total_withdraw_count = total_withdraw_count + 1,
            withdraw_players = withdraw_players + 1,
            updated_at = NOW();
    END IF;
END //

DELIMITER ;

-- 当 gm_balance_adjustments 表状态变为已审核时，更新 daily_statistics 表的手动上下分字段
DELIMITER //

DROP TRIGGER IF EXISTS trg_after_gm_balance_adjustment_approved_statistics_cn;

CREATE TRIGGER trg_after_gm_balance_adjustment_approved_statistics_cn
AFTER UPDATE ON gm_balance_adjustments
FOR EACH ROW
BEGIN
    DECLARE today_date DATE;
    DECLARE agent_id_val INT UNSIGNED;

    -- 只在状态从非已审核变为已审核时执行
    IF NEW.status = 1 AND OLD.status != 1 AND NEW.player_id IS NOT NULL THEN
        SET today_date = CURDATE();

        -- 获取玩家的业务员ID
        SELECT agent_id INTO agent_id_val
        FROM players
        WHERE players.id = NEW.player_id
        LIMIT 1;

        -- 根据调整类型更新不同的字段
        IF NEW.adjustment_type = 1 THEN
            -- 上分
            INSERT INTO daily_statistics
                (stat_date, agent_id, manual_increase, updated_at)
            VALUES
                (today_date, agent_id_val, NEW.amount, NOW())
            ON DUPLICATE KEY UPDATE
                manual_increase = manual_increase + NEW.amount,
                updated_at = NOW();
        ELSEIF NEW.adjustment_type = 2 THEN
            -- 下分
            INSERT INTO daily_statistics
                (stat_date, agent_id, manual_decrease, updated_at)
            VALUES
                (today_date, agent_id_val, NEW.amount, NOW())
            ON DUPLICATE KEY UPDATE
                manual_decrease = manual_decrease + NEW.amount,
                updated_at = NOW();
        END IF;
    END IF;
END //

DELIMITER ;

-- 当 gm_balance_adjustments 表插入已审核记录时，更新 daily_statistics 表的手动上下分字段
DELIMITER //

DROP TRIGGER IF EXISTS trg_after_gm_balance_adjustment_inserted_statistics_cn;

CREATE TRIGGER trg_after_gm_balance_adjustment_inserted_statistics_cn
AFTER INSERT ON gm_balance_adjustments
FOR EACH ROW
BEGIN
    DECLARE today_date DATE;
    DECLARE agent_id_val INT UNSIGNED;

    -- 只处理状态为已审核的新记录
    IF NEW.status = 1 AND NEW.player_id IS NOT NULL THEN
        SET today_date = CURDATE();

        -- 获取玩家的业务员ID
        SELECT agent_id INTO agent_id_val
        FROM players
        WHERE players.id = NEW.player_id
        LIMIT 1;

        -- 根据调整类型更新不同的字段
        IF NEW.adjustment_type = 1 THEN
            -- 上分
            INSERT INTO daily_statistics
                (stat_date, agent_id, manual_increase, updated_at)
            VALUES
                (today_date, agent_id_val, NEW.amount, NOW())
            ON DUPLICATE KEY UPDATE
                manual_increase = manual_increase + NEW.amount,
                updated_at = NOW();
        ELSEIF NEW.adjustment_type = 2 THEN
            -- 下分
            INSERT INTO daily_statistics
                (stat_date, agent_id, manual_decrease, updated_at)
            VALUES
                (today_date, agent_id_val, NEW.amount, NOW())
            ON DUPLICATE KEY UPDATE
                manual_decrease = manual_decrease + NEW.amount,
                updated_at = NOW();
        END IF;
    END IF;
END //

DELIMITER ;

-- 当 game_records 表插入新记录时，更新 daily_statistics 表的游戏相关字段
DELIMITER //

DROP TRIGGER IF EXISTS trg_after_game_record_inserted_statistics_cn;

CREATE TRIGGER trg_after_game_record_inserted_statistics_cn
AFTER INSERT ON game_records
FOR EACH ROW
BEGIN
    DECLARE today_date DATE;
    DECLARE agent_id_val INT UNSIGNED;
    DECLARE game_profit DECIMAL(18,2);

    -- 计算游戏盈亏
    SET game_profit = NEW.bet_amount - NEW.win_amount;
    SET today_date = CURDATE();

    -- 获取玩家的业务员ID
    SELECT agent_id INTO agent_id_val
    FROM players
    WHERE players.id = NEW.player_id
    LIMIT 1;

    -- 更新或插入统计记录
    INSERT INTO daily_statistics
        (stat_date, agent_id, total_bet, total_win, total_game_profit, updated_at)
    VALUES
        (today_date, agent_id_val, NEW.bet_amount, NEW.win_amount, game_profit, NOW())
    ON DUPLICATE KEY UPDATE
        total_bet = total_bet + NEW.bet_amount,
        total_win = total_win + NEW.win_amount,
        total_game_profit = total_game_profit + game_profit,
        updated_at = NOW();
END //

DELIMITER ;

-- 当 players 表插入新记录时，更新 daily_statistics 表的新增玩家数
DELIMITER //

DROP TRIGGER IF EXISTS trg_after_player_inserted_statistics_cn;

CREATE TRIGGER trg_after_player_inserted_statistics_cn
AFTER INSERT ON players
FOR EACH ROW
BEGIN
    DECLARE today_date DATE;
    SET today_date = CURDATE();

    -- 更新或插入统计记录
    INSERT INTO daily_statistics
        (stat_date, agent_id, new_players, updated_at)
    VALUES
        (today_date, NEW.agent_id, 1, NOW())
    ON DUPLICATE KEY UPDATE
        new_players = new_players + 1,
        updated_at = NOW();
END //

DELIMITER ;

-- 当玩家登录时，更新 daily_statistics 表的活跃玩家数
DELIMITER //

DROP TRIGGER IF EXISTS trg_after_player_login_statistics_cn;

CREATE TRIGGER trg_after_player_login_statistics_cn
AFTER INSERT ON player_login_logs
FOR EACH ROW
BEGIN
    DECLARE today_date DATE;
    DECLARE agent_id_val INT UNSIGNED;
    DECLARE is_first_login_today INT;

    SET today_date = CURDATE();

    -- 检查这是否是玩家今天的第一次登录
    SELECT COUNT(*) = 1 INTO is_first_login_today
    FROM player_login_logs
    WHERE player_login_logs.player_id = NEW.player_id
      AND DATE(player_login_logs.created_at) = today_date
    LIMIT 1;

    -- 只有当这是玩家今天的第一次登录时，才更新活跃玩家计数
    IF is_first_login_today THEN
        -- 获取玩家的业务员ID
        SELECT agent_id INTO agent_id_val
        FROM players
        WHERE players.id = NEW.player_id
        LIMIT 1;

        -- 更新或插入统计记录
        INSERT INTO daily_statistics
            (stat_date, agent_id, active_players, updated_at)
        VALUES
            (today_date, agent_id_val, 1, NOW())
        ON DUPLICATE KEY UPDATE
            active_players = active_players + 1,
            updated_at = NOW();
    END IF;
END //

DELIMITER ;
