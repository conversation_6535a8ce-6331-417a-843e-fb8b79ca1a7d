-- 宝箱奖励触发器
-- 当充值订单支付成功时，自动检查并创建宝箱奖励记录

DELIMITER //

-- 删除已存在的触发器（如果有）
DROP TRIGGER IF EXISTS trg_after_deposit_order_paid_treasure_box_cn //

-- 创建触发器：当充值订单状态变为成功时，检查并创建宝箱奖励记录
CREATE TRIGGER trg_after_deposit_order_paid_treasure_box_cn
AFTER UPDATE ON deposit_orders
FOR EACH ROW
BEGIN
    DECLARE current_player_id BIGINT UNSIGNED;
    DECLARE referrer_id BIGINT UNSIGNED;
    DECLARE valid_subordinates INT DEFAULT 0;
    DECLARE record_exists INT DEFAULT 0;
    DECLARE min_deposit_amount DECIMAL(18,2) DEFAULT 25.00; -- 最低充值金额要求，默认为25
    DECLARE is_first_deposit BOOLEAN DEFAULT FALSE; -- 是否为首充

    -- 游标相关变量
    DECLARE done INT DEFAULT FALSE;
    DECLARE cur_config_id SMALLINT UNSIGNED;
    DECLARE cur_reward_amount DECIMAL(18,2);

    -- 只在支付状态从非成功变为成功时执行
    IF NEW.payment_status = 1 AND OLD.payment_status != 1 AND NEW.player_id IS NOT NULL THEN
        -- 获取充值玩家ID
        SET current_player_id = NEW.player_id;

        -- 判断是否为首充（第一次充值且金额大于25）
        SELECT COUNT(*) = 0 INTO is_first_deposit
        FROM deposit_orders do
        WHERE do.player_id = NEW.player_id
          AND do.payment_status = 1
          AND do.id != NEW.id;

        -- 只有首充且金额大于等于最低充值金额要求时才处理
        IF is_first_deposit = TRUE AND NEW.amount >= min_deposit_amount THEN
            -- 从player_relations表获取一级上线ID
            SELECT pr.ancestor_id INTO referrer_id
            FROM player_relations pr
            WHERE pr.player_id = NEW.player_id
            AND pr.relation_level = 1
            LIMIT 1;

            -- 如果从player_relations表获取不到，则从players表获取
            IF referrer_id IS NULL THEN
                SELECT p.referrer_id INTO referrer_id
                FROM players p
                WHERE p.id = NEW.player_id;
            END IF;

            -- 如果有推荐人，则处理推荐人的宝箱奖励
            IF referrer_id IS NOT NULL THEN
                -- 获取推荐人的有效下属数量（首充且金额大于等于25的不重复玩家数量）
                SELECT COUNT(DISTINCT pr.player_id) INTO valid_subordinates
                FROM player_relations pr
                JOIN deposit_orders do ON do.player_id = pr.player_id
                WHERE pr.ancestor_id = referrer_id
                  AND pr.relation_level = 1
                  AND do.payment_status = 1
                  AND do.amount >= min_deposit_amount
                  AND (
                      SELECT COUNT(*)
                      FROM deposit_orders do2
                      WHERE do2.player_id = pr.player_id
                        AND do2.payment_status = 1
                        AND do2.id != do.id
                  ) = 0;

                -- 遍历所有符合条件的宝箱配置（invite_count小于等于有效下属数量）
                -- 为每个符合条件且尚未创建记录的配置创建宝箱记录
                BEGIN
                    DECLARE cur CURSOR FOR
                        SELECT tbc.id, tbc.reward_amount
                        FROM treasure_box_config tbc
                        WHERE tbc.invite_count <= valid_subordinates
                        AND tbc.is_active = 1
                        ORDER BY tbc.invite_count ASC;
                    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

                    OPEN cur;

                    read_loop: LOOP
                        FETCH cur INTO cur_config_id, cur_reward_amount;
                        IF done THEN
                            LEAVE read_loop;
                        END IF;

                        -- 检查是否已经存在该配置的记录
                        SELECT COUNT(*) INTO record_exists
                        FROM treasure_box_records tbr
                        WHERE tbr.player_id = referrer_id
                        AND tbr.config_id = cur_config_id;

                        -- 如果不存在记录，则创建新记录
                        IF record_exists = 0 THEN
                            INSERT INTO treasure_box_records (
                                player_id,
                                config_id,
                                reward_amount,
                                is_claimed
                            ) VALUES (
                                referrer_id,
                                cur_config_id,
                                cur_reward_amount,
                                0 -- 默认未领取
                            );
                        END IF;
                    END LOOP;

                    CLOSE cur;
                END;
            END IF;
        END IF;
    END IF;
END //

-- 删除已存在的触发器（如果有）
DROP TRIGGER IF EXISTS trg_after_treasure_box_claimed_cn //

-- 创建触发器：当宝箱奖励记录被领取时，更新玩家余额并记录余额变动日志
CREATE TRIGGER trg_after_treasure_box_claimed_cn
AFTER UPDATE ON treasure_box_records
FOR EACH ROW
BEGIN
    DECLARE player_balance DECIMAL(18,2);
    DECLARE balance_after DECIMAL(18,2);
    DECLARE transaction_type_id TINYINT UNSIGNED DEFAULT 12; -- 代理宝箱奖励
    DECLARE betting_multiplier DECIMAL(3,1) DEFAULT 0.0;
    DECLARE additional_betting_amount DECIMAL(18,2) DEFAULT 0.0;
    DECLARE betting_task_exists INT DEFAULT 0;

    -- 只在is_claimed从0变为1时执行（即宝箱被领取时）
    IF NEW.is_claimed = 1 AND OLD.is_claimed = 0 THEN
        -- 获取玩家当前余额
        SELECT p.balance INTO player_balance
        FROM players p
        WHERE p.id = NEW.player_id;

        -- 计算领取奖励后的余额
        SET balance_after = player_balance + NEW.reward_amount;

        -- 更新玩家余额
        UPDATE players p
        SET p.balance = balance_after
        WHERE p.id = NEW.player_id;

        -- 记录余额变动日志
        INSERT INTO player_balance_logs (
            player_id,
            amount,
            balance_before,
            balance_after,
            balance_type,
            transaction_type,
            remark,
            created_at
        ) VALUES (
            NEW.player_id,
            NEW.reward_amount,
            player_balance,
            balance_after,
            1, -- 账户余额
            transaction_type_id, -- 代理宝箱奖励
            CONCAT('宝箱奖励领取: 配置ID=', NEW.config_id, ', 奖励金额=', NEW.reward_amount, ', 余额从', player_balance, '变为', balance_after),
            NOW()
        );

        -- 更新玩家打码任务
        -- 获取打码倍数配置
        SELECT btc.betting_multiplier INTO betting_multiplier
        FROM betting_task_config btc
        WHERE btc.type_id = transaction_type_id -- 代理宝箱奖励
        LIMIT 1;

        -- 如果找到对应的打码倍数配置，且打码倍数大于0，则更新打码任务
        IF betting_multiplier > 0 THEN
            -- 计算需要增加的打码任务量
            SET additional_betting_amount = NEW.reward_amount * betting_multiplier;

            -- 查询玩家是否已有打码任务记录
            SELECT COUNT(*) INTO betting_task_exists
            FROM player_betting_tasks pbt
            WHERE pbt.player_id = NEW.player_id;

            IF betting_task_exists > 0 THEN
                -- 更新现有记录
                UPDATE player_betting_tasks pbt
                SET pbt.required_betting_amount = pbt.required_betting_amount + additional_betting_amount,
                    pbt.updated_at = NOW()
                WHERE pbt.player_id = NEW.player_id;
            ELSE
                -- 创建新记录
                INSERT INTO player_betting_tasks (
                    player_id,
                    required_betting_amount,
                    completed_betting_amount,
                    created_at,
                    updated_at
                ) VALUES (
                    NEW.player_id,
                    additional_betting_amount,
                    0,
                    NOW(),
                    NOW()
                );
            END IF;
        END IF;
    END IF;
END //

DELIMITER ;
