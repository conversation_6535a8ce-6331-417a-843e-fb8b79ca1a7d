-- 触发器：当充值订单状态变为成功时，计算并更新最多三级上级玩家的佣金
DELIMITER //

DROP TRIGGER IF EXISTS trg_after_deposit_order_paid_commission_cn; //

CREATE TRIGGER trg_after_deposit_order_paid_commission_cn
AFTER UPDATE ON deposit_orders
FOR EACH ROW
BEGIN
    DECLARE level1_id BIGINT UNSIGNED;
    DECLARE level2_id BIGINT UNSIGNED;
    DECLARE level3_id BIGINT UNSIGNED;
    DECLARE level1_rate DECIMAL(18,2);
    DECLARE level2_rate DECIMAL(18,2);
    DECLARE level3_rate DECIMAL(18,2);
    DECLARE level1_max DECIMAL(18,2);
    DECLARE level2_max DECIMAL(18,2);
    DECLARE level3_max DECIMAL(18,2);
    DECLARE min_deposit DECIMAL(18,2);
    DECLARE commission_amount DECIMAL(18,2);
    DECLARE balance_before DECIMAL(18,2);
    DECLARE balance_after DECIMAL(18,2);
    DECLARE config_exists INT DEFAULT 0;

    -- 检查支付状态是否从非成功变为成功 (payment_status = 1)
    IF NEW.payment_status = 1 AND OLD.payment_status != 1 AND NEW.player_id IS NOT NULL THEN

        -- 获取存款玩家的最多三级上级ID
        SELECT
            MAX(CASE WHEN relation_level = 1 THEN ancestor_id ELSE NULL END),
            MAX(CASE WHEN relation_level = 2 THEN ancestor_id ELSE NULL END),
            MAX(CASE WHEN relation_level = 3 THEN ancestor_id ELSE NULL END)
        INTO
            level1_id, level2_id, level3_id
        FROM player_relations
        WHERE player_relations.player_id = NEW.player_id
        GROUP BY player_relations.player_id;

        -- 处理一级上级佣金
        IF level1_id IS NOT NULL THEN
            SELECT COUNT(*), level1_rate, level1_max_amount, min_deposit_amount
            INTO config_exists, level1_rate, level1_max, min_deposit
            FROM player_commission_configs
            WHERE player_commission_configs.player_id = level1_id
            LIMIT 1;

            IF config_exists > 0 AND level1_rate IS NOT NULL AND level1_rate > 0 AND NEW.amount >= min_deposit THEN
                SET commission_amount = NEW.amount * level1_rate / 100;
                IF level1_max IS NOT NULL AND level1_max > 0 AND commission_amount > level1_max THEN
                    SET commission_amount = level1_max;
                END IF;

                IF commission_amount > 0 THEN
                    SELECT balance INTO balance_before FROM players WHERE players.id = level1_id;
                    SET balance_after = balance_before + commission_amount;
                    UPDATE players SET balance = balance_after WHERE players.id = level1_id;
                    INSERT INTO player_balance_logs (player_id, amount, balance_before, balance_after, balance_type, transaction_type, remark, created_at)
                    VALUES (level1_id, commission_amount, balance_before, balance_after, 1, 14, CONCAT('一级邀请充值佣金，来源玩家ID: ', NEW.player_id, ', 订单ID: ', NEW.id), NOW());
                END IF;
            END IF;
        END IF;

        -- 处理二级上级佣金
        IF level2_id IS NOT NULL THEN
            -- 重置变量以供二级查询使用
            SET config_exists = 0;
            SET level2_rate = NULL;
            SET level2_max = NULL;
            SET min_deposit = NULL;

            SELECT COUNT(*), level2_rate, level2_max_amount, min_deposit_amount
            INTO config_exists, level2_rate, level2_max, min_deposit
            FROM player_commission_configs
            WHERE player_commission_configs.player_id = level2_id
            LIMIT 1;

            IF config_exists > 0 AND level2_rate IS NOT NULL AND level2_rate > 0 AND NEW.amount >= min_deposit THEN
                SET commission_amount = NEW.amount * level2_rate / 100;
                IF level2_max IS NOT NULL AND level2_max > 0 AND commission_amount > level2_max THEN
                    SET commission_amount = level2_max;
                END IF;

                IF commission_amount > 0 THEN
                    SELECT balance INTO balance_before FROM players WHERE players.id = level2_id;
                    SET balance_after = balance_before + commission_amount;
                    UPDATE players SET balance = balance_after WHERE players.id = level2_id;
                    INSERT INTO player_balance_logs (player_id, amount, balance_before, balance_after, balance_type, transaction_type, remark, created_at)
                    VALUES (level2_id, commission_amount, balance_before, balance_after, 1, 15, CONCAT('二级邀请充值佣金，来源玩家ID: ', NEW.player_id, ', 订单ID: ', NEW.id), NOW());
                END IF;
            END IF;
        END IF;

        -- 处理三级上级佣金
        IF level3_id IS NOT NULL THEN
            -- 重置变量以供三级查询使用
            SET config_exists = 0;
            SET level3_rate = NULL;
            SET level3_max = NULL;
            SET min_deposit = NULL;

            SELECT COUNT(*), level3_rate, level3_max_amount, min_deposit_amount
            INTO config_exists, level3_rate, level3_max, min_deposit
            FROM player_commission_configs
            WHERE player_commission_configs.player_id = level3_id
            LIMIT 1;

            IF config_exists > 0 AND level3_rate IS NOT NULL AND level3_rate > 0 AND NEW.amount >= min_deposit THEN
                SET commission_amount = NEW.amount * level3_rate / 100;
                IF level3_max IS NOT NULL AND level3_max > 0 AND commission_amount > level3_max THEN
                    SET commission_amount = level3_max;
                END IF;

                IF commission_amount > 0 THEN
                    SELECT balance INTO balance_before FROM players WHERE players.id = level3_id;
                    SET balance_after = balance_before + commission_amount;
                    UPDATE players SET balance = balance_after WHERE players.id = level3_id;
                    INSERT INTO player_balance_logs (player_id, amount, balance_before, balance_after, balance_type, transaction_type, remark, created_at)
                    VALUES (level3_id, commission_amount, balance_before, balance_after, 1, 16, CONCAT('三级邀请充值佣金，来源玩家ID: ', NEW.player_id, ', 订单ID: ', NEW.id), NOW());
                END IF;
            END IF;
        END IF;

    END IF;
END //

DELIMITER ;