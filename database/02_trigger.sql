

-- 当 game_records 表插入新记录时，更新 players 表的 total_bet 和 total_win 字段的触发器
DELIMITER //

DROP TRIGGER IF EXISTS trg_after_game_record_inserted_cn;

CREATE TRIGGER trg_after_game_record_inserted_cn
AFTER INSERT ON game_records
FOR EACH ROW
BEGIN
    -- 确保 player_id 不为 NULL
    IF NEW.player_id IS NOT NULL THEN
        -- 更新 players 表中的 total_bet 和 total_win 字段
        UPDATE players
        SET
            total_bet = total_bet + NEW.bet_amount,
            total_win = total_win + NEW.win_amount
        WHERE players.id = NEW.player_id;

        -- 更新 player_betting_tasks 表中的 completed_betting_amount 字段
        -- 只有当 completed_betting_amount 小于 required_betting_amount 时才更新
        UPDATE player_betting_tasks
        SET
            completed_betting_amount = LEAST(required_betting_amount, completed_betting_amount + NEW.bet_amount)
        WHERE player_betting_tasks.player_id = NEW.player_id AND player_betting_tasks.completed_betting_amount < player_betting_tasks.required_betting_amount;

        -- 调用存储过程更新 VIP 等级
        CALL update_player_vip_level(NEW.player_id);
    END IF;
END //

DELIMITER ;

-- 当 players 表插入新记录时，自动维护 player_relations 表（最多记录三级关系）的触发器
DELIMITER //

DROP TRIGGER IF EXISTS trg_before_player_inserted_limited_cn;
DROP TRIGGER IF EXISTS trg_before_player_inserted_set_channel;

-- 创建合并后的 BEFORE INSERT 触发器，同时处理设置渠道ID和掉绑逻辑
CREATE TRIGGER trg_before_player_inserted_limited_cn
BEFORE INSERT ON players
FOR EACH ROW
BEGIN
    DECLARE ref_id BIGINT UNSIGNED;
    DECLARE ref_ref_id BIGINT UNSIGNED;
    DECLARE ref_ref_ref_id BIGINT UNSIGNED;
    -- 掉绑设置相关变量
    DECLARE unbind_mode TINYINT DEFAULT 0;
    DECLARE reg_unbind_type TINYINT DEFAULT 0;
    DECLARE reg_unbind_prob TINYINT DEFAULT 0;
    DECLARE reg_unbind_threshold INT DEFAULT 0;
    DECLARE reg_unbind_interval INT DEFAULT 0;
    DECLARE subordinate_count INT DEFAULT 0;
    DECLARE random_value INT DEFAULT 0;
    DECLARE should_unbind TINYINT DEFAULT 0;
    DECLARE no_unbind_flag TINYINT DEFAULT 0;
    -- 业务员和渠道相关变量
    DECLARE referrer_agent_id INT;
    DECLARE agent_channel_id INT;
    DECLARE channel_player_count INT;

    -- 第一部分：设置业务员ID和渠道ID
    -- 如果有上线玩家ID，则获取上线玩家的业务员ID
    IF NEW.referrer_id IS NOT NULL THEN
        SELECT agent_id INTO referrer_agent_id
        FROM players
        WHERE players.id = NEW.referrer_id
        LIMIT 1;

        -- 如果找到了上线玩家的业务员ID，则设置当前玩家的业务员ID
        IF referrer_agent_id IS NOT NULL THEN
            SET NEW.agent_id = referrer_agent_id;
        END IF;
    END IF;

    -- 如果有业务员ID，则获取业务员所属的渠道ID
    IF NEW.agent_id IS NOT NULL THEN
        SELECT channel_id INTO agent_channel_id
        FROM agents
        WHERE agents.id = NEW.agent_id
        LIMIT 1;

        -- 调试日志：记录业务员和渠道信息
        INSERT INTO debug_logs (
            player_id,
            referrer_id,
            agent_id,
            channel_id,
            message
        ) VALUES (
            NEW.id,
            NEW.referrer_id,
            NEW.agent_id,
            agent_channel_id,
            '查询业务员渠道ID'
        );

        -- 如果找到了渠道ID，则设置玩家的渠道ID
        IF agent_channel_id IS NOT NULL THEN
            SET NEW.channel_id = agent_channel_id;
        END IF;
    END IF;

    -- 第二部分：掉绑逻辑
    SET ref_id = NEW.referrer_id;

    -- 调试日志：记录初始变量值
    INSERT INTO debug_logs (
        player_id,
        referrer_id,
        agent_id,
        channel_id,
        message
    ) VALUES (
        NEW.id,
        NEW.referrer_id,
        NEW.agent_id,
        NEW.channel_id,
        '触发器开始执行'
    );

    -- 检查新玩家是否有推荐人ID
    IF ref_id IS NOT NULL THEN
        -- 获取全局掉绑设置
        SELECT aus.unbind_mode, aus.reg_unbind_type, aus.reg_unbind_prob, aus.reg_unbind_threshold, aus.reg_unbind_interval
        INTO unbind_mode, reg_unbind_type, reg_unbind_prob, reg_unbind_threshold, reg_unbind_interval
        FROM player_unbind_settings aus
        WHERE aus.player_id = 0
        LIMIT 1;

        -- 如果推荐人有特定掉绑设置，则覆盖全局设置
        SELECT pus.unbind_mode, pus.reg_unbind_type, pus.reg_unbind_prob, pus.reg_unbind_threshold, pus.reg_unbind_interval
        INTO unbind_mode, reg_unbind_type, reg_unbind_prob, reg_unbind_threshold, reg_unbind_interval
        FROM player_unbind_settings pus
        WHERE pus.player_id = ref_id
        LIMIT 1;

        -- 调试日志：记录掉绑设置
        INSERT INTO debug_logs (
            player_id,
            referrer_id,
            agent_id,
            channel_id,
            unbind_mode,
            reg_unbind_type,
            reg_unbind_prob,
            reg_unbind_threshold,
            reg_unbind_interval,
            message
        ) VALUES (
            NEW.id,
            NEW.referrer_id,
            NEW.agent_id,
            NEW.channel_id,
            unbind_mode,
            reg_unbind_type,
            reg_unbind_prob,
            reg_unbind_threshold,
            reg_unbind_interval,
            '获取掉绑设置完成'
        );

        -- 默认不掉绑
        SET should_unbind = 0;

        -- 根据掉绑模式执行不同的掉绑逻辑
        IF unbind_mode > 0 THEN
            -- 渠道模式 (unbind_mode = 1)
            IF unbind_mode = 1 THEN
                -- 获取推荐人所属的渠道ID
                SELECT channel_id INTO agent_channel_id
                FROM players
                WHERE players.id = ref_id
                LIMIT 1;

                IF agent_channel_id IS NOT NULL AND agent_channel_id > 0 THEN
                    -- 获取该渠道下的玩家总数
                    SELECT COUNT(*) INTO channel_player_count
                    FROM players
                    WHERE players.channel_id = agent_channel_id;

                    -- 如果渠道玩家数量达到阈值，才执行掉绑逻辑
                    IF channel_player_count >= reg_unbind_threshold THEN
                        -- 概率模式 (reg_unbind_type = 2 表示比例模式)
                        IF reg_unbind_type = 2 THEN
                            -- 生成1-100的随机数
                            SET random_value = FLOOR(1 + RAND() * 100);

                            -- 如果随机数小于等于设定的概率，则掉绑
                            IF random_value <= reg_unbind_prob THEN
                                SET should_unbind = 1;
                            END IF;
                        -- 间隔模式 (reg_unbind_type = 1 表示间隔模式)
                        ELSEIF reg_unbind_type = 1 AND reg_unbind_interval > 0 THEN
                            -- 如果渠道玩家数量模除间隔等于0，则掉绑
                            IF (channel_player_count % (reg_unbind_interval + 1)) = 0 THEN
                                SET should_unbind = 1;
                            END IF;
                        END IF;
                    END IF;
                END IF;
            -- 个人模式 (unbind_mode = 2)
            ELSEIF unbind_mode = 2 THEN
                -- 检查推荐人是否设置了不掉绑下级
                SELECT no_unbind_subordinate INTO no_unbind_flag FROM players WHERE players.id = ref_id;

                IF no_unbind_flag = 1 THEN
                    -- 推荐人设置了不掉绑下级，不掉绑
                    SET should_unbind = 0;
                ELSE
                    -- 获取推荐人的下级数量
                    SELECT COUNT(*) INTO subordinate_count
                    FROM players
                    WHERE players.referrer_id = ref_id;

                    -- 如果下级数量达到阈值，才执行掉绑逻辑
                    IF subordinate_count >= reg_unbind_threshold THEN
                        -- 概率模式 (reg_unbind_type = 2 表示比例模式)
                        IF reg_unbind_type = 2 THEN
                            -- 生成1-100的随机数
                            SET random_value = FLOOR(1 + RAND() * 100);

                            -- 如果随机数小于等于设定的概率，则掉绑
                            IF random_value <= reg_unbind_prob THEN
                                SET should_unbind = 1;
                            END IF;
                        -- 间隔模式 (reg_unbind_type = 1 表示间隔模式)
                        ELSEIF reg_unbind_type = 1 AND reg_unbind_interval > 0 THEN
                            -- 如果下级数量模除间隔等于0，则掉绑
                            IF (subordinate_count % (reg_unbind_interval + 1)) = 0 THEN
                                SET should_unbind = 1;
                            END IF;
                        END IF;
                    END IF;
                END IF;
            END IF;
        END IF;

        -- 如果需要掉绑，直接设置NEW的self_unbind_status字段
        IF should_unbind = 1 THEN
            SET NEW.self_unbind_status = 1;
        END IF;

        -- 调试日志：记录最终掉绑决策
        INSERT INTO debug_logs (
            player_id,
            referrer_id,
            agent_id,
            channel_id,
            unbind_mode,
            reg_unbind_type,
            subordinate_count,
            channel_player_count,
            random_value,
            should_unbind,
            no_unbind_flag,
            message
        ) VALUES (
            NEW.id,
            NEW.referrer_id,
            NEW.agent_id,
            NEW.channel_id,
            unbind_mode,
            reg_unbind_type,
            subordinate_count,
            channel_player_count,
            random_value,
            should_unbind,
            no_unbind_flag,
            '掉绑决策完成'
        );
    END IF;
END //

DROP TRIGGER IF EXISTS trg_after_player_inserted_limited_cn;

-- 创建 AFTER INSERT 触发器
CREATE TRIGGER trg_after_player_inserted_limited_cn
AFTER INSERT ON players
FOR EACH ROW
BEGIN
    DECLARE ref_id BIGINT UNSIGNED;
    DECLARE ref_ref_id BIGINT UNSIGNED;
    DECLARE ref_ref_ref_id BIGINT UNSIGNED;

    SET ref_id = NEW.referrer_id;

    -- 检查新玩家是否有推荐人ID
    IF ref_id IS NOT NULL THEN
        -- 1. 插入与直接推荐人的关系 (层级 1)
        INSERT INTO player_relations (player_id, ancestor_id, relation_level, created_at)
        VALUES (NEW.id, ref_id, 1, NOW());

        -- 2. 查找并插入与二级上级的关系 (层级 2)
        SELECT referrer_id INTO ref_ref_id FROM players WHERE players.id = ref_id;
        IF ref_ref_id IS NOT NULL THEN
            INSERT INTO player_relations (player_id, ancestor_id, relation_level, created_at)
            VALUES (NEW.id, ref_ref_id, 2, NOW());

            -- 3. 查找并插入与三级上级的关系 (层级 3)
            SELECT referrer_id INTO ref_ref_ref_id FROM players WHERE players.id = ref_ref_id;
            IF ref_ref_ref_id IS NOT NULL THEN
                INSERT INTO player_relations (player_id, ancestor_id, relation_level, created_at)
                VALUES (NEW.id, ref_ref_ref_id, 3, NOW());
            END IF;
        END IF;
    END IF;
END //

DELIMITER ;

-- 用于更新玩家VIP等级的存储过程
DELIMITER //

DROP PROCEDURE IF EXISTS update_player_vip_level; //

CREATE PROCEDURE update_player_vip_level(IN p_player_id BIGINT UNSIGNED)
BEGIN
    DECLARE v_total_deposit DECIMAL(20, 2) DEFAULT 0;
    DECLARE v_total_bet DECIMAL(20, 2) DEFAULT 0;
    DECLARE v_new_vip_level INT DEFAULT 0;

    -- 获取玩家的总存款和总投注额
    SELECT total_deposit, total_bet
    INTO v_total_deposit, v_total_bet
    FROM players
    WHERE players.id = p_player_id;

    -- 根据 vip_config 查找匹配的最高 VIP 等级
    SELECT MAX(level)
    INTO v_new_vip_level
    FROM vip_config
    WHERE v_total_deposit >= vip_config.deposit_requirement
      AND v_total_bet >= vip_config.turnover_requirement;

    -- 如果没有找到匹配的等级，设置为默认等级 0
    IF v_new_vip_level IS NULL THEN
        SET v_new_vip_level = 0;
    END IF;

    -- 更新玩家的 VIP 等级
    UPDATE players
    SET vip_level = v_new_vip_level
    WHERE players.id = p_player_id;

END //

DELIMITER ;

-- 当 deposit_orders 表更新且支付状态变为成功时，更新 players 表的 total_deposit 并调用存储过程更新 VIP 等级
DELIMITER //

DROP TRIGGER IF EXISTS trg_after_deposit_order_paid_cn; //

CREATE TRIGGER trg_after_deposit_order_paid_cn
AFTER UPDATE ON deposit_orders
FOR EACH ROW
BEGIN
    -- 确保 player_id 不为 NULL 且支付状态从非成功变为成功 (payment_status = 1)
    IF NEW.player_id IS NOT NULL AND NEW.payment_status = 1 AND OLD.payment_status != 1 THEN
        -- 更新 players 表中的 total_deposit 字段
        UPDATE players
        SET
            total_deposit = total_deposit + NEW.amount -- 使用 NEW.amount 获取存款金额
        WHERE players.id = NEW.player_id;

        -- 调用存储过程更新 VIP 等级
        CALL update_player_vip_level(NEW.player_id);
    END IF;
END //

DELIMITER ;
