-- 创建调试数据表
DROP TABLE IF EXISTS debug_logs;
CREATE TABLE debug_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    player_id BIGINT UNSIGNED,
    referrer_id BIGINT UNSIGNED,
    agent_id INT,
    channel_id INT,
    unbind_mode TINYINT,
    reg_unbind_type TINYINT,
    reg_unbind_prob TINYINT,
    reg_unbind_threshold INT,
    reg_unbind_interval INT,
    subordinate_count INT,
    channel_player_count INT,
    random_value INT,
    should_unbind TINYINT,
    no_unbind_flag TINYINT,
    message VARCHAR(255)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
